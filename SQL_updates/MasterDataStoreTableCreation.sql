
CREATE TABLE sch_payinvoice_p2p.master_data_store (
	master_data_type varchar(255) NULL,
	company_code int8 NOT NULL,
	code varchar(255) NULL,
    body jsonb NULL,
	description varchar(255) NULL,
	PRIMARY KEY (company_code, code, master_data_type)
);

Hibernate:
    alter table if exists sch_payinvoice_p2p.master_data_store
       add column created_timestamp timestamp(6) with time zone
Hibernate:
    alter table if exists sch_payinvoice_p2p.master_data_store
       add column creating_user_id bigint
Hibernate:
    alter table if exists sch_payinvoice_p2p.master_data_store
       add column updated_timestamp timestamp(6) with time zone
Hibernate:
    alter table if exists sch_payinvoice_p2p.master_data_store
       add column updating_user_id bigint
