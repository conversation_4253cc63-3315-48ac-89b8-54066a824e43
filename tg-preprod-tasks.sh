sudo aws ecr get-login-password --region ap-south-1 | sudo docker login --username AWS --password-stdin 561014279291.dkr.ecr.ap-south-1.amazonaws.com && sudo docker container rm --force pay-expense-container-tg && sudo docker container rm --force pay-expense-container-staging && sudo docker image rm 561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:latest && sudo docker container run --detach --publish 9001:8080 --env DB_URL='***************************************************' --env DB_USERNAME=pe_backend --env DB_PASSWORD='TaxGenie@2022!@#' --env FILE_UPLOAD_LIMIT=10MB --env JWT_SECRET=1E223539-0D5B-4279-8F9A-B3B13BDC7A29-DA01E40D-6806-40FA-B280-44ACB4902845 --env PAY_EXPENSE_PRODUCT_ID=4 --env DB_DIALECT=org.hibernate.dialect.MySQL5InnoDBDialect --env DB_SHOW_SQL=false --env DB_DDL_AUTO=update --env DB_ENGINE=innodb --env CEM_URL='http://*************:9005/api/v1' --env AWS_ENDPOINT_URL='https://payexpense.s3.ap-south-1.amazonaws.com' --env AWS_REGION='ap-south-1' --env AWS_ACCESS_KEY='********************' --env AWS_SECRET_KEY='afLDRkGxNOd0sCb9ztI4X3ACV1k2z2iFyL5PTWl9' --env AWS_BUCKET_NAME='payexpense' --env ACTIVE_PROFILE='tg-internal' --env AWS_ROOT_DIRECTORY='payexpense' --env ALLOWED_ORIGINS='http://localhost:4200,http://*************:9002,http://*************:10002,https://payexpense-uat.abfldirect.com' --env PAY_EXPENSE_CUSTOMER_ID=12741 --env COMMUNICATION_ENGINE_URL=http://*************:9006 --env SENDBACK_EMAIL_TEMPLATE_ID=pe-sentback-format --env APPROVAL_EMAIL_TEMPLATE_ID=pe-approval-format --env ACKNOWLEDGEMENT_EMAIL_TEMPLATE_ID=pe-acknowledgement-format --env PAY_EXPENSE_SENDER_EMAIL='<EMAIL>' --env SENDBACK_EMAIL_TEMPLATE_ID=pe-sentback-for-creator --env SUBMIT_TO_CREATOR_EMAIL_TEMPLATE_ID=pe-submit-for-creator --env SUBMIT_TO_APPROVER_EMAIL_TEMPLATE_ID=pe-submit-for-approver --env APPROVED_TO_CREATOR_EMAIL_TEMPLATE_ID=pe-approved-for-creator --env APPROVED_TO_NEXT_APPROVER_EMAIL_TEMPLATE_ID=pe-approved-for-approver --env POSTED_EMAIL_TEMPLATE_ID=pe-posted-for-creator --env PAID_EMAIL_TEMPLATE_ID=pe-paid-for-creator --env NEW_USER_WELCOME_EMAIL_TEMPLATE_ID=pe-new-user-welcome --env SENDBACK_REMINDER_EMAIL_TEMPLATE_ID=pe-sent-back-reminder --env APPROVAL_REMINDER_EMAIL_TEMPLATE_ID=pe-approval-reminder --name pay-expense-container-staging 561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:latest && echo "Done: TG Staging" && sudo docker container run --detach --publish 10001:8080 --env DB_URL='***************************************************' --env DB_USERNAME=pe_backend --env DB_PASSWORD='TaxGenie@2022!@#' --env FILE_UPLOAD_LIMIT=10MB --env JWT_SECRET=1E223539-0D5B-4279-8F9A-B3B13BDC7A29-DA01E40D-6806-40FA-B280-44ACB4902845 --env PAY_EXPENSE_PRODUCT_ID=4 --env DB_DIALECT=org.hibernate.dialect.MySQL5InnoDBDialect --env DB_SHOW_SQL=false --env DB_DDL_AUTO=update --env DB_ENGINE=innodb --env CEM_URL='http://*************:9005/api/v1' --env AWS_ENDPOINT_URL='https://payexpense.s3.ap-south-1.amazonaws.com' --env AWS_REGION='ap-south-1' --env AWS_ACCESS_KEY='********************' --env AWS_SECRET_KEY='afLDRkGxNOd0sCb9ztI4X3ACV1k2z2iFyL5PTWl9' --env AWS_BUCKET_NAME='payexpense' --env ACTIVE_PROFILE='tg-internal' --env AWS_ROOT_DIRECTORY='payexpense' --env ALLOWED_ORIGINS='http://localhost:4200,http://*************:9002,http://*************:10002,https://payexpense-uat.abfldirect.com,http://procurement.dev.payinvoice.in' --env PAY_EXPENSE_CUSTOMER_ID=12741 --env COMMUNICATION_ENGINE_URL=https://dev.taxgenie.online/communication-engine --env SENDBACK_EMAIL_TEMPLATE_ID=pe-sentback-format --env APPROVAL_EMAIL_TEMPLATE_ID=pe-approval-format --env ACKNOWLEDGEMENT_EMAIL_TEMPLATE_ID=pe-acknowledgement-format --env PAY_EXPENSE_SENDER_EMAIL='<EMAIL>' --env SENDBACK_EMAIL_TEMPLATE_ID=pe-sentback-for-creator --env SUBMIT_TO_CREATOR_EMAIL_TEMPLATE_ID=pe-submit-for-creator --env SUBMIT_TO_APPROVER_EMAIL_TEMPLATE_ID=pe-submit-for-approver --env APPROVED_TO_CREATOR_EMAIL_TEMPLATE_ID=pe-approved-for-creator --env APPROVED_TO_NEXT_APPROVER_EMAIL_TEMPLATE_ID=pe-approved-for-approver --env POSTED_EMAIL_TEMPLATE_ID=pe-posted-for-creator --env PAID_EMAIL_TEMPLATE_ID=pe-paid-for-creator --env NEW_USER_WELCOME_EMAIL_TEMPLATE_ID=pe-new-user-welcome --env SENDBACK_REMINDER_EMAIL_TEMPLATE_ID=pe-sent-back-reminder --env APPROVAL_REMINDER_EMAIL_TEMPLATE_ID=pe-approval-reminder --name pay-expense-container-tg 561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:latest && echo "Done: TG Preprod"
