spring:
  datasource:
    url: ${DB_URL:jdbc:postgresql://*************:5432/db_payinvoice_p2p} # dev
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:DY1RG;DZ2(n|9URm}
    #    url: ${DB_URL:**************************************************} # local
    #    username: ${DB_USERNAME:postgres}
    #    password: ${DB_PASSWORD:P@ssw0rd}
#    url: ${DB_URL:*******************************************************} # uat
#    username: ${DB_USERNAME:app_user_p2p}
#    password: ${DB_PASSWORD:lziZQH7D*<bV3%RA}
    hikari:
      maximum-pool-size: 150
      minimum-idle: 20
      connection:
        idle-timeout: 90000
  profiles:
    active: ${ACTIVE_PROFILE:tg-internal-gcp}
  jpa:
    properties:
      hibernate:
        dialect: ${DB_DIALECT:org.hibernate.dialect.PostgreSQLDialect}
        ddl-auto: ${DB_DDL_AUTO:update}
        format_sql: true
        default_schema: ${DB_SCHEMA:sch_payinvoice_p2p}
    generate-ddl: true
    show-sql: ${DB_SHOW_SQL:false}
  hibernate:
    dialect:
      storage_engine: ${DB_ENGINE:innodb}
  jackson:
    deserialization:
      fail-on-unknown-properties: false
  servlet:
    multipart:
      max-file-size: ${FILE_UPLOAD_LIMIT:10MB}
  cloud:
    gcp:
      project-id: payinvoice-p2p
      credentials:
        location: classpath:payinvoice-p2p-development-key.json

aws:
  region: ${AWS_REGION:ap-south-1}
  endpoint:
    url: ${AWS_ENDPOINT_URL:https://payexpense.s3.ap-south-1.amazonaws.com}
  access:
    key: ${AWS_ACCESS_KEY:PLACE_HOLDER}
  secret:
    key: ${AWS_SECRET_KEY:PLACE_HOLDER}
  bucket:
    name: ${AWS_BUCKET_NAME:payinvoice-p2p}
  root:
    directory: ${AWS_ROOT_DIRECTORY:payinvoice-p2p}

gcp:
  bucket:
    name: ${GCP_BUCKET:payinvoice-p2p}
  topic:
    name: ${GCP_TOPIC:syndicate_pubsub_p2p_dev}
    #dev:syndicate_pubsub_p2p_dev
    #uat:syndicate_pubsub_p2p_uat

cloud:
  uploadEnv: ${UPLOAD_ENVIRONMENT:development}


multi-tenancy-config:
  db-url: ${DB_URL:jdbc:postgresql://*************:5432/db_payinvoice_p2p} # dev
  db-username: ${DB_USERNAME:postgres}
  db-password: ${DB_PASSWORD:DY1RG;DZ2(n|9URm}
#  db-url: ${DB_URL:*******************************************************} # uat
#  db-username: ${DB_USERNAME:app_user_p2p}
#  db-password: ${DB_PASSWORD:lziZQH7D*<bV3%RA}
## Logging
#logging:
#  level:
#    com:
#      zaxxer:
#        hikari:
#          HikariConfig: DEBUG
#logging:
#  level:
#    org:
#      hibernate:
#        SQL: DEBUG
#        type: TRACE


web-client-config:
  services:
    document-etl-service: ${QR_READER_ENV:https://dev.docproc.taxgenie.online} #dev
#    document-etl-service: ${QR_READER_ENV:https://uat.docproc.payindx.com} #uat
    ocr-service: ${OCR_SERVICE:https://dev.docproc.taxgenie.online} # dev
#    ocr-service: ${OCR_SERVICE:https://uat.docproc.payindx.com} #uat
    cam-service: ${CAM_SERVICE:https://dev.taxgenie.online/cam/api/v1} #dev
#    cam-service: ${CAM_SERVICE:https://uat.taxgenie.online/cam/api/v1} #uat
    mdm-service: ${MDM_SERVICE:https://dev.taxgenie.online/mdm-master} #dev
    apigee-account-service: ${APIGEE_SERVICE:https://apigee-account-service-************.us-central1.run.app}
    api4business-service: ${API4BUSINESS_SERVICE:https://sandbox.api.api4business.com}
    base-url-service: ${BASE_URL_SERVICE:https://dev.payinvoice.online/api/base}

  max-buffer-limit: -1

ALLOWED_ORIGINS: ${ALLOWED_ORIGINS_ENV:http://localhost:4200,http://dev.payinvoice.online,https://dev.payinvoice.online}
JWT_SECRET: ${JWT_SECRET_ENV:1E223539-0D5B-4279-8F9A-B3B13BDC7A29-DA01E40D-6806-40FA-B280-44ACB4902845}

PAY_EXPENSE_CUSTOMER_ID: ${PAY_EXPENSE_CUSTOMER_ID_ENV:0}
PAY_EXPENSE_PRODUCT_ID: ${PAY_EXPENSE_PRODUCT_ID_ENV:27}

COMMUNICATION_ENGINE_URL: ${COMMUNICATION_ENGINE_URL_ENV:https://dev.taxgenie.online/communication-engine}
CEM_URL: ${CEM_URL_ENV:https://dev.taxgenie.online/cem/api/v1} #dev
#CEM_URL: ${CEM_URL_ENV:https://uat.taxgenie.online/cem/api/v1} #uat
API4BUSINESS_URL: ${API4BUSINESS_URL_ENV:https://dev.api.api4business.com}

APPROVAL_EMAIL_TEMPLATE_ID: ${APPROVAL_EMAIL_TEMPLATE_ID_ENV:pe-approval-format}
ACKNOWLEDGEMENT_EMAIL_TEMPLATE_ID: ${ACKNOWLEDGEMENT_EMAIL_TEMPLATE_ID_ENV:pe-acknowledgement-format}
PAY_EXPENSE_SENDER_EMAIL: ${PAY_EXPENSE_SENDER_EMAIL_ENV:<EMAIL>}
SENDBACK_EMAIL_TEMPLATE_ID: ${SENDBACK_EMAIL_TEMPLATE_ID_ENV:pe-sentback-for-creator}
SUBMIT_TO_CREATOR_EMAIL_TEMPLATE_ID: ${SUBMIT_TO_CREATOR_EMAIL_TEMPLATE_ID_ENV:pe-submit-for-creator}
SUBMIT_TO_APPROVER_EMAIL_TEMPLATE_ID: ${SUBMIT_TO_APPROVER_EMAIL_TEMPLATE_ID_ENV:pe-submit-for-approver}
APPROVED_TO_CREATOR_EMAIL_TEMPLATE_ID: ${APPROVED_TO_CREATOR_EMAIL_TEMPLATE_ID_ENV:pe-approved-for-creator}
APPROVED_TO_NEXT_APPROVER_EMAIL_TEMPLATE_ID: ${APPROVED_TO_NEXT_APPROVER_EMAIL_TEMPLATE_ID_ENV:_ENVpe-approved-for-approver}
POSTED_EMAIL_TEMPLATE_ID: ${POSTED_EMAIL_TEMPLATE_ID_ENV:pe-posted-for-creator}
PAID_EMAIL_TEMPLATE_ID: ${PAID_EMAIL_TEMPLATE_ID_ENV:pe-paid-for-creator}
NEW_USER_WELCOME_EMAIL_TEMPLATE_ID: ${NEW_USER_WELCOME_EMAIL_TEMPLATE_ID_ENV:pe-new-user-welcome}
SENDBACK_REMINDER_EMAIL_TEMPLATE_ID: ${SENDBACK_REMINDER_EMAIL_TEMPLATE_ID_ENV:pe-sent-back-reminder}
APPROVAL_REMINDER_EMAIL_TEMPLATE_ID: ${APPROVAL_REMINDER_EMAIL_TEMPLATE_ID_ENV:pe-approval-reminder}
