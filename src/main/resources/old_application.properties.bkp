#   Spring Data JPA
spring.datasource.url=${DB_URL}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.jpa.properties.hibernate.dialect= ${DB_DIALECT}
spring.jpa.generate-ddl=true
spring.jpa.hibernate.ddl-auto=${DB_DDL_AUTO}
spring.jpa.show-sql=${DB_SHOW_SQL}
hibernate.dialect.storage_engine=${DB_ENGINE}
spring.jackson.deserialization.fail-on-unknown-properties=false

#   File upload limit
spring.servlet.multipart.max-file-size=${FILE_UPLOAD_LIMIT}

#   AWS S3
aws.region=${AWS_REGION}
aws.endpoint.url=${AWS_ENDPOINT_URL}
aws.access.key=${AWS_ACCESS_KEY}
aws.secret.key=${AWS_SECRET_KEY}
aws.bucket.name=${AWS_BUCKET_NAME}
aws.root.directory=${AWS_ROOT_DIRECTORY}

# GCP
gcp.bucket.name=${GCP_BUCKET}

#   Profile
spring.profiles.active=${ACTIVE_PROFILE}


# Application connection size
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG
logging.level.com.zaxxer.hikari=TRACE

spring.datasource.hikari.maximum-pool-size=150
spring.datasource.hikari.minimum-idle=20
#spring.datasource.hikari.connection-timeout=90000
