package in.taxgenie.pay_expense_pvv.utils;

import in.taxgenie.pay_expense_pvv.entities.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Month;
import java.util.*;

public class StaticDataRegistry {

    //
    public static Long DEFAULT_TENANT_IDENTIFIER = 0L;

    //HSN start with this will be SERVICE
    public static final String HSN_CODE_SERVICE_START = "9";

    // Dashboard Static Values
    public static final Integer DASHBOARD_SEQUENCE_INCREMENT = 1;

    // PO Static Values
    public static final String PO_ITEM_CANNOT_BE_ADDED = "There was an issue adding this item. Please try another one...";
    public static final String PR_ITEM_AVAILABLE_LESS_THEN_REQUESTED = "The quantity of item requested is less then is currently available";
    public static final String PO_RELEASE_INDICATOR = "R";
    // PR Static Values
    public static final Integer PR_ITEM_COUNT_INIT = 0;

    // Consumed Static Value
    public static final ReportStatus[] ITEM_CONSUMED_STATES_PROCUREMENT = {ReportStatus.SENT_BACK ,ReportStatus.SUBMITTED, ReportStatus.ACCEPTED, ReportStatus.POSTED_TO_DESTINATION_SYSTEM, ReportStatus.VALIDATION_FAILED, ReportStatus.PARKED, ReportStatus.RELEASED, ReportStatus.PAID}; // TODO: remove ReportStatus.VALIDATION_FAILED?
    public static final ReportStatus[] ITEM_CONSUMED_STATES = {ReportStatus.SUBMITTED, ReportStatus.ACCEPTED, ReportStatus.POSTED_TO_DESTINATION_SYSTEM, ReportStatus.PARKED, ReportStatus.RELEASED, ReportStatus.PAID};
    public static final ReportStatus[] CONSUMED = {ReportStatus.ACCEPTED, ReportStatus.PARKED, ReportStatus.POSTED_TO_DESTINATION_SYSTEM, ReportStatus.PAID};
    public static final ReportStatus[] BUDGET_CONSUMED_STATES = {ReportStatus.SUBMITTED, ReportStatus.ACCEPTED, ReportStatus.SENT_BACK, ReportStatus.POSTED_TO_DESTINATION_SYSTEM, ReportStatus.PAID};
    // Vendor Static Values
    public static final String VENDOR_GSTIN_NOT_APPLICABLE = "Not--Applicable";
    public static final String VENDOR_ACTIVE_STATUS = "Active";

    // Lookup Data Values
    public static final String LOOKUP_TYPE_DOCUMENT = "DocumentType";

    public static final String LOOKUP_INVOICE_TYPE = "invoice_type";
    public static final String LOOKUP_INVOICE_SUB_TYPE = "invoice_sub_type";
    public static final String LOOKUP_INVOICE_SUB_TYPE_REGULAR = "Regular";
    public static final String PR_TYPE = "pr_type";
    public static final String PO_TYPE = "po_type";

    public static final String BUDGET_TYPE = "budget_type";
    public static final String PUBLISH_BUDGET = "SUBMIT";

    public static final String LOOKUP_VALUE_BUDGET = "Budget";
    public static final String PAYMENT_TERMS = "payment_terms";

    public static final String PR_TYPE_GENERAL = "General";
    public static final String PO_NON_PR_BASED = "Non-PR based";
    public static final String PO_PR_BASED = "PR based";

    public static final String INVOICE_PO_BASED = "PO based";
    public static final String INVOICE_NON_PO_BASED = "Non PO based";
    public static final String LOOKUP_VALUE_INVOICE = "Invoice";
    public static final String LOOKUP_TYPE_UPLOAD_SOURCE = "UploadSource";
    public static final String LOOKUP_VALUE_JSON = "JSON";
    public static final String LOOKUP_VALUE_PDF = "PDF";

    public static final String LOOKUP_VALUE_PR = "Purchase Requisition";

    public static final String LOOKUP_VALUE_PO = "Purchase Order";
    public static final String LOOKUP_PO_STATUS = "po_status";
    public static final String LOOKUP_PO_STATUS_OPEN = "Open";

    public static final Integer PR_CATEGORY_ID = 2;


    public static final String LOOKUP_CODE_JSON_MATCH = "80b5f77b-ab68-473f-8616-e5ddadc66b83";

    // Generic Error Messages
    public static final String ERROR_MESSAGE_INVOICE_NOT_FOUND = "Invoice not found.";

    public static final String ERROR_MESSAGE_INVOICE_TYPE_NOT_FOUND = "Invoice type not found.";

    public static final String ERROR_MESSAGE_RECORD_NOT_FOUND = "Record not found.";
    public static final String ERROR_MESSAGE_RECORD_NOT_CREATED = "There was an error creating a record";
    public static final String ERROR_MESSAGE_DUPLICATE_NODE = "Duplicate label nodes in hierarchy structure";
    public static final String ERROR_MESSAGE_INVOICE_SAVE_FAILURE = "Invoice could not be saved, please try again later...";

    public static final String ERROR_MESSAGE_INVOICE_GENERATE_SIGNED_URL = "Failed to generate signed url to upload file.";

    public static final String ERROR_MESSAGE_PR_NOT_FOUND = "Purchase Request not found.";
    public static final String ERROR_MESSAGE_PO_NOT_FOUND = "Purchase Order not found.";
    public static final String ERROR_MESSAGE_INVOICE_QUEUE_UNHANDLED = "getInvoiceQueue: There was an unhandled exception";

    public static final String URL_SEPERATOR = "/";


    public static final String DUPLICATE_APPROVER_MESSAGE = "Auto Approved";
    public static final int NEW_ENTITY_ID = 0;
    public static final int DEFAULT_ENTITY_ID = 0;

    public static final long MAX_AMOUNT = 100_000_000;
    //  HTTP Response Codes
    public static final int HTTP_RESPONSE_OK = 200;
    public static final int HTTP_RESPONSE_CREATED = 201;
    public static final int HTTP_RESPONSE_ACCEPTED = 202;
    public static final int HTTP_RESPONSE_NO_CONTENT = 204;
    public static final int HTTP_RESPONSE_BAD_REQUEST = 400;
    public static final int HTTP_RESPONSE_UNAUTHENTICATED = 401;
    public static final int HTTP_RESPONSE_UNAUTHORIZED = 403;
    public static final int HTTP_RESPONSE_NOT_FOUND = 404;
    public static final int HTTP_RESPONSE_SERVICE_ERROR = 503;

    //  HTTP Message Templates
    public static final String HTTP_MESSAGE_OK = "Transmission OK";
    public static final String HTTP_MESSAGE_NOT_FOUND = "Resource not found";
    public static final String HTTP_MESSAGE_VALIDATION = "Validation Error";
    public static final String HTTP_MESSAGE_WORKFLOW = "Workflow restrictions imposed";
    public static final String HTTP_MESSAGE_NOT_PERMITTED = "Action not permitted";
    public static final String HTTP_MESSAGE_NOT_AUTHORIZED = "User is not authorized to carry out this action";
    public static final String HTTP_MESSAGE_NOT_AUTHENTICATED = "User is not not authenticated";
    public static final String HTTP_MESSAGE_SERVICE_ERROR = "Service error. Contact administrator.";

    //  CEM service
    public static final String CEM_SERVICE_EMPLOYEE_FRAGMENT = "employees";

    public static final String CEM_SERVICE_EXPENSE_GROUP_INFO_FRAGMENT = "expense-groups";
    public static final String CEM_SERVICE_EXPENSE_ACCOUNT_FRAGMENT = "expense-accounts";
    public static final String CEM_SERVICE_BY_GROUP_FRAGMENT = "by-group";

    //  Role markers
    public static final String ADMIN_ROLE_MARKER = "admin";
    public static final String APPROVER_ROLE_MARKER = "approver";
    public static final String CHECKER_ROLE_MARKER = "checker";
    public static final String USER_ROLE_MARKER = "user";

    //  String utils
    public static String getUrl(String baseUrl, String separator, Object... fragments) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(baseUrl);

        for (Object fragment : fragments) {
            stringBuilder.append(separator);
            stringBuilder.append(fragment);
        }

        return stringBuilder.toString();
    }

    //  Definition increment factor
    public static final int DEFINITION_LEVEL_INCREMENT_FACTOR = 1;

    //  Document Number
    public static final long START_CURRENT_NUMBER = 1;
    public static final long CURRENT_NUMBER_INCREMENT_FACTOR = 1;

    public static final int COUNT_INCREMENT_FACTOR = 1;
    public static final String PREFIX_SEPARATOR = "-";

    public static <E extends Enum<E>> boolean isEnumNull(E enumValue) {
        return enumValue == null;
    }

    public class CloudStorageConstans {
        public static String CATEGORY_INVOICE = "invoice";
        public static String CATEGORY_PR = "pr";
        public static String CATEGORY_PO = "po";
    }
    public static String getDocumentIdentifier(DocumentIdentifier entity) {
        return entity.getDocumentTypePrefix() +
                StaticDataRegistry.PREFIX_SEPARATOR +
                String.format("%04d", entity.getYear()) +
                String.format("%02d", entity.getMonth()) +
                String.format("%05d", entity.getCurrentIndex());
    }

    //  employee k-v map
    public static Map<String, String> getEmployeeKVMap(DocumentApprovalContainer report) {
        Map<String, String> map = new HashMap<>();
        map.put(report.getKey01(), report.getValue01());
        map.put(report.getKey02(), report.getValue02());
        map.put(report.getKey03(), report.getValue03());
        map.put(report.getKey04(), report.getValue04());
        map.put(report.getKey05(), report.getValue05());
        map.put(report.getKey06(), report.getValue06());
        map.put(report.getKey07(), report.getValue07());
        map.put(report.getKey08(), report.getValue08());
        map.put(report.getKey09(), report.getValue09());
        map.put(report.getKey10(), report.getValue10());

        return map;
    }

    public static Map<String, String> getEmployeeKVMap(BaseApprovalDocument report) {
        Map<String, String> map = new HashMap<>();
        map.put(report.getKey01(), report.getValue01());
        map.put(report.getKey02(), report.getValue02());
        map.put(report.getKey03(), report.getValue03());
        map.put(report.getKey04(), report.getValue04());
        map.put(report.getKey05(), report.getValue05());
        map.put(report.getKey06(), report.getValue06());
        map.put(report.getKey07(), report.getValue07());
        map.put(report.getKey08(), report.getValue08());
        map.put(report.getKey09(), report.getValue09());
        map.put(report.getKey10(), report.getValue10());

        return map;
    }

    //  Checker first or approver first
    public static final boolean CHECKER_BEFORE_APPROVER = true;
    public static final boolean FOREIGN_CURRENCY_SAME_AS_BASE_CURRENCY = true;
    public static final BigDecimal DEFAULT_FX_CONVERSION_RATE = new BigDecimal("1.0");
    public static final String DEFAULT_BASE_CURRENCY = "INR";
    public static final Set<String> CURRENCY_SET = Set.of("AED",
            "AFN",
            "ALL",
            "AMD",
            "ANG",
            "AOA",
            "ARS",
            "AUD",
            "AWG",
            "AZN",
            "BAM",
            "BBD",
            "BDT",
            "BGN",
            "BHD",
            "BIF",
            "BMD",
            "BND",
            "BOB",
            "BOV",
            "BRL",
            "BSD",
            "BTN",
            "BWP",
            "BYN",
            "BZD",
            "CAD",
            "CDF",
            "CHE",
            "CHF",
            "CHW",
            "CLF",
            "CLP",
            "CNY",
            "COP",
            "COU",
            "CRC",
            "CUC",
            "CUP",
            "CVE",
            "CZK",
            "DJF",
            "DKK",
            "DOP",
            "DZD",
            "EGP",
            "ERN",
            "ETB",
            "EUR",
            "FJD",
            "FKP",
            "GBP",
            "GEL",
            "GHS",
            "GIP",
            "GMD",
            "GNF",
            "GTQ",
            "GYD",
            "HKD",
            "HNL",
            "HRK",
            "HTG",
            "HUF",
            "IDR",
            "ILS",
            "INR",
            "IQD",
            "IRR",
            "ISK",
            "JMD",
            "JOD",
            "JPY",
            "KES",
            "KGS",
            "KHR",
            "KMF",
            "KPW",
            "KRW",
            "KWD",
            "KYD",
            "KZT",
            "LAK",
            "LBP",
            "LKR",
            "LRD",
            "LSL",
            "LYD",
            "MAD",
            "MDL",
            "MGA",
            "MKD",
            "MMK",
            "MNT",
            "MOP",
            "MRU",
            "MUR",
            "MVR",
            "MWK",
            "MXN",
            "MXV",
            "MYR",
            "MZN",
            "NAD",
            "NGN",
            "NIO",
            "NOK",
            "NPR",
            "NZD",
            "OMR",
            "PAB",
            "PEN",
            "PGK",
            "PHP",
            "PKR",
            "PLN",
            "PYG",
            "QAR",
            "RON",
            "RSD",
            "RUB",
            "RWF",
            "SAR",
            "SBD",
            "SCR",
            "SDG",
            "SEK",
            "SGD",
            "SHP",
            "SLL",
            "SOS",
            "SRD",
            "SSP",
            "STN",
            "SVC",
            "SYP",
            "SZL",
            "THB",
            "TJS",
            "TMT",
            "TND",
            "TOP",
            "TRY",
            "TTD",
            "TWD",
            "TZS",
            "UAH",
            "UGX",
            "USD",
            "USN",
            "UYI",
            "UYU",
            "UYW",
            "UZS",
            "VED",
            "VES",
            "VND",
            "VUV",
            "WST",
            "XAF",
            "XAG",
            "XAU",
            "XBA",
            "XBB",
            "XBC",
            "XBD",
            "XCD",
            "XDR",
            "XOF",
            "XPD",
            "XPF",
            "XPT",
            "XSU",
            "XTS",
            "XUA",
            "XXX",
            "YER",
            "ZAR",
            "ZMW",
            "ZWL");

    public static final int FIRST_INDEX = 0;
    public static final int FIRST_LEVEL = 1;

    public static final long MAX_DRAFT_REPORTS = 1000; // TODO : set for testing, set to 100 again

    public static String getExpenseMetadataMarker(DocumentMetadata metadata) {
        return String.format("%s > %s", metadata.getDocumentType(), metadata.getDocumentGroup());
    }

    public static String getLocationMasterMarker(Location location) {
        return String.format("%s  |  %s  |  %s", location.getCategory(), location.getCountryCode(), location.getLocation());
    }

    public static String getApprovalDefinitionMarker(ApprovalDefinition definition) {
        return String.format("(%s-%s) %d > %s > %s [%.2f]",
                definition.getDocumentMetadata().getDocumentType(),
                definition.getDocumentMetadata().getDocumentGroup(),
                definition.getLevel(),
                definition.getApprovalMatcher(),
                definition.getApprovalTitle(),
                definition.getLimitAmount()
        );
    }

    public static boolean isNotNullOrEmptyOrWhitespace(String source) {
        if (source == null) {
            return false;
        }

        return source.trim().length() != 0;
    }

    public static boolean isNullOrEmptyOrWhitespace(String source) {
        return !StaticDataRegistry.isNotNullOrEmptyOrWhitespace(source);
    }

    public static final Set<String> frequencySet = Set.of("Monthly", "Quarterly", "Half-yearly", "Yearly");

    public static final Set<String> uploadFileContentTypeSet = Set.of(
            "image/jpeg",
            "application/zip",
            "text/csv",
            "application/x-zip-compressed",
            "image/jpg",
            "image/png",
            "application/pdf",
            "application/msword",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"

    );
    public static final Set<String> uploadFileContentTypeSetByExtension = Set.of(
            "jpeg",
            "jpg",
            "png",
            "pdf",
            "doc",
            "docx",
            "xls",
            "xlsx",
            "zip",
            "csv"
    );

    public static final String DEFAULT_APPROVER_MATCHER = "DEFAULT";
    public static final String DEFAULT_APPROVER_VALUE = "DEFAULT";
    public static final String DEFAULT_APPROVER_TITLE = "DEFAULT";

    public static String getApprovalDelegationMarker(String originalApprover, String delegatedApprover, int level, long delegationId) {
        return String.format("You (%s) have been delegated by %s at level %d for this expense vide delegation id: %d", originalApprover, delegatedApprover, level, delegationId);
    }

    public static final Set<String> locationCategories = Set.of(
            "DOM-CAT-1",
            "DOM-CAT-2",
            "DOM-CAT-3",
            "DOM-CAT-4",
            "DOM-CAT-5",
            "INT-CAT-1",
            "INT-CAT-2",
            "INT-CAT-3",
            "INT-CAT-4",
            "INT-CAT-5"
    );

    public static final int APPROVAL_INCREMENT_FACTOR = 1;
    public static final int ACCEPTED_STATUS_MARKER = 0;
    public static final int DECLINED_STATUS_MARKER = -1;
    public static final int REVOKED_STATUS_MARKER = -2;

    public static String getExpenseMetadataMarkerForRule(DocumentRule rule) {
        return String.format("%s | %s | %s > %s",
                rule.getDocumentSubgroup().getDocumentMetadata().getDocumentType(),
                rule.getDocumentSubgroup().getDocumentMetadata().getDocumentGroup(),
                rule.getDocumentSubgroup().getDocumentSubgroup(),
                rule.getDocumentSubgroup().getDocumentCode()
        );
    }

    public static String getExpenseMetadataMarkerForRule(MetadataLimitRule rule) {
        return String.format("%s | %s",
                rule.getDocumentMetadata().getDocumentType(),
                rule.getDocumentMetadata().getDocumentGroup()
        );
    }

    public static String getExpenseSubgroupMarker(DocumentSubgroup subgroup) {
        return String.format("%s | %s | %s > %s",
                subgroup.getDocumentMetadata().getDocumentType(),
                subgroup.getDocumentMetadata().getDocumentGroup(),
                subgroup.getDocumentSubgroup(),
                subgroup.getDocumentCode()
        );
    }
    public static String generateShortUUID() {
        UUID uuid = UUID.randomUUID();
        // Convert to Base64 and truncate to 16 characters
        return Base64.getUrlEncoder()
                .withoutPadding()
                .encodeToString(uuid.toString().getBytes())
                .substring(0, 16);
    }

    public static String getChangeDeskMarker(String fromApprover, String toApprover) {
        return String.format("Admin has changed desk from (%s) to (%s)", fromApprover, toApprover);
    }

    public static final Month DEFAULT_FY_START_MONTH = Month.APRIL;
    public static final Month DEFAULT_FY_END_MONTH = Month.MARCH;
    public static final LocalDate START_EPOCH = LocalDate.of(2022, 1, 1);

    public static final Set<String> TRANSPORT_DESCRIPTOR_VALUE = Set.of("ROAD", "RAIL", "AIR", "TAXI", "METERED_TAXI", "AUTO", "METRO", "AC_BUS");
    public static final Set<String> MOBILITY_DESCRIPTOR_VALUE = Set.of("HOUSE_BROKERAGE", "SCHOOL_FEES", "DONATION", "TRAVEL");
    public static final Set<String> TRAVEL_DESCRIPTOR_VALUE = Set.of("COMPANY", "SELF");
    public static final String REPORT_PATH = "/var/tmp/pem_reports/";

    public static final List<String> TAT_REPORT_HEADERS = List.of("Claim No.", "Claim Date", "Claim Status", "Submission Date", "Employee Grade", "Pending At", "Expense Type", "Cost Center", "Department", "Location", "Claim Amount", "B1ApproverDate", "TAT1", "B1ApproverName",
            "B2 Approver Date", "TAT2", "B2 Approver Name", "B3 Approver Date", "TAT3", "B3 Approver Name", "B4 Approver Date", "TAT4", "B4 Approver Name", "B5 Approver Date", "TAT5", "B5 Approver Name", "Total BA TAT", "Checker1 Approver Date", "TAT6", "Checker1 Approver Name", "Checker2 Approver Date", "TAT7", "Checker2 Approver Name", "Payment Date",
            "Payment Reference", "Payment TAT", "Total Checker TAT", "Total TAT", "Entity");

    public static final List<String> EXPENSE_REPORT_HEADERS = List.of("Fiscal Year", "Employee Code", "Employee Name", "Employee Grade", "Title", "Claim No.", "Claim Date", "Claim Amount", "Currency", "Start Date", "End Date", "Expense Type", "Claim Status", "Pending At", "Cost Center", "Location", "Payment Status", "Payment Date", "Posted Date", "Payment Doc No", "Mobile Number", "Limit Exceeded", "SentBack By Code", "SentBack By", "SentBack On(Date)", "SentBack on Level", "No.of Times Sent Back", "Remarks", "Approved By Code", "Approved By", "Approved On (Date)", "Approval Level", "Entity", "Delay (days)");

    public static final List<String> LINE_ITEM_HEADERS = List.of("Fiscal Year", "Claim No", "Claim Date", "Claim Title", "Expense Type", "Expense Name", "Expense Date", "From Location", "To Location", "Emp Code", "Emp Name", "Emp Grade", "Submission Date", "Voucher Status", "Amount", "Currency", "Converted Amount", "A/C Code", "A/C Description", "Payment Date", "Payment Reference", "Start Date", "End Date", "Cost Center", "Emp Location", "Narration", "Entity");

    public class Widget{
        public static final String PURCHASE_REQUEST = "purchaseRequest";
        public static final String PURCHASE_ORDER = "purchaseOrder";
        public static final String INVOICE = "invoice";

        public static final int W_INVOICE_ID = 1;
        public static final int W_PURCHASE_REQUEST_ID = 2;
        public static final int W_PURCHASE_ORDER_ID = 3;
    }
    public class StatusRemarks {
        public static final String BUYER_NOT_FOUND = "Buyer Details Not Found";
        public static final String INVOICE_NOT_READABLE = "Invoice Not Readable";
        public static final String DUPLICATE_INVOICE_NO = "Duplicate Invoice Number";
        public static final String OCR_FAILED = "Invoice Processing failed";
    }

    public class ERPConstants {
        // System identifiers
        public static final String ERP_SYSTEM = "ERP";
        public static final String ERP_SYSTEM_FULL = "ERP_SYSTEM";
        public static final String SYSTEM = "SYSTEM";

        // Error messages and remarks
        public static final String SENT_BACK_DUE_TO_VALIDATION_ERRORS = "Sent back due to validation errors";
        public static final String BUDGET_REVERSAL_SKIPPED = "BUDGET_REVERSAL_SKIPPED";
        public static final String BUDGET_REVERSAL_SKIPPED_FORMAT = "BUDGET_REVERSAL_SKIPPED: [%s] %s";

        // Budget error codes
        public static final String BUDGET_UNKNOWN_ERROR = "BUDGET_UNKNOWN_ERROR";
        public static final String BUDGET_GENERIC_ERROR_NOT_FOUND = "BUDGET_GENERIC_ERROR_NOT_FOUND";
        public static final String BUDGET_INSUFFICIENT_FUNDS = "BUDGET_INSUFFICIENT_FUNDS";
        public static final String BUDGET_LOCKED_ERROR = "BUDGET_LOCKED_ERROR";
        public static final String BUDGET_EXPIRED_ERROR = "BUDGET_EXPIRED_ERROR";
        public static final String BUDGET_GENERIC_ERROR = "BUDGET_GENERIC_ERROR";
        public static final String BUDGET_CONSUMPTION_SYSTEM_ERROR = "BUDGET_CONSUMPTION_SYSTEM_ERROR";
        public static final String BUDGET_NO_DOCUMENT_DATA = "BUDGET_NO_DOCUMENT_DATA";
        public static final String BUDGET_CONSUMPTION_FAILED = "BUDGET_CONSUMPTION_FAILED";
        public static final String BUDGET_UNLOCK_FAILED = "BUDGET_UNLOCK_FAILED";
        public static final String PO_ALREADY_DISCARDED = "PO_ALREADY_DISCARDED";

        // Error message templates
        public static final String ERROR_NO_DOCUMENT_DATA = "No document data found for budget consumption";
        public static final String ERROR_BUDGET_CONSUMPTION_FAILED = "Budget consumption service returned failure";
        public static final String ERROR_UNEXPECTED_BUDGET_CONSUMPTION = "Unexpected error during budget consumption for document: %d";
        public static final String ERROR_FAILED_VALIDATION_FLOW = "Failed to process validation failed flow for document %d. Reason: %s";
        public static final String ERROR_FAILED_INJECT_STATUS = "Failed to inject validation error state: %s";

        // Separators and formatting
        public static final String REMARKS_SEPARATOR = "; ";
        public static final String COLON_SPACE = ": ";

        // Revoke operation error messages
        public static final String ERROR_NO_DOCUMENT_DATA_FOR_REVOKE = "No document data found for budget consumption reversal. Document ID: %d";
        public static final String ERROR_BUDGET_SERVICE_NULL_RESPONSE = "Budget service returned null for document: %d";
        public static final String ERROR_BUDGET_SERVICE_FAILURE = "Budget service returned failure for document: %d";
        public static final String ERROR_BUDGET_SYSTEM_DURING_REVERSAL = "Budget system error during reversal for document %d: %s";
        public static final String ERROR_DATABASE_DURING_BUDGET_REVERSAL = "Database error during budget reversal for document %d: %s";
        public static final String ERROR_UNEXPECTED_BUDGET_REVERSAL = "Unexpected error during budget consumption reversal for document %d: %s";
        public static final String ERROR_CONSUMPTION_SERVICE_REVERSAL = "Consumption service error during reversal for document %d: %s";
        public static final String ERROR_DATABASE_DURING_CONSUMPTION_REVERSAL = "Database error during consumption reversal for document %d: %s";
        public static final String ERROR_UNEXPECTED_CONSUMPTION_REVERSAL = "Unexpected error during consumption service reversal for document %d: %s";
        public static final String ERROR_REVOKE_BUDGET_FAILURE = "Cannot revoke document %d due to budget consumption reversal failure: %s";
        public static final String ERROR_REVOKE_DATABASE_SAVE = "Failed to save revoked document %d to database: %s";
        public static final String ERROR_DOCUMENT_DATA_RETRIEVAL = "Failed to retrieve document data for budget consumption reversal. Document ID: %d. Error: %s";
    }
}
