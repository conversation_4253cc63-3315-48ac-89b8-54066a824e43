package in.taxgenie.pay_expense_pvv.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.taxgenie.utils.api.MultiServiceClient;
import in.taxgenie.pay_expense_pvv.entities.ExternalServicesIdentifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.HashMap;
import java.util.Map;

@Component
public class MultiServiceClientFactory {

    @Value("${web-client-config.services.document-etl-service}")
    private String documentEtlUrl;

    @Value("${CEM_URL}")
    private String cemService;

    @Value("${web-client-config.services.cam-service}")
    private String camService;

    @Value("${web-client-config.services.ocr-service}")
    private String ocrService;

    @Value("${web-client-config.services.mdm-service}")
    private String mdmService;

    @Value("${web-client-config.max-buffer-limit}" )
    private Integer maxBuffer;

    @Value("${web-client-config.services.apigee-account-service}")
    private String apigeeAccountService;

    @Value("${web-client-config.services.api4business-service}")
    private String api4businessService;

    private Map<Integer, WebClient> clients = new HashMap<>();
    public WebClient getClient(int serviceIdentifier) {
        return clients.get(serviceIdentifier);
    }
    public MultiServiceClient createMultiServiceClient(
            WebClient.Builder builder) {

        MultipartExchangeFilterFunction filter = new MultipartExchangeFilterFunction();
        WebClient qrReaderClient = builder.baseUrl(documentEtlUrl).defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(filter)
                .build();

        clients.put(ExternalServicesIdentifier.QR_READER_SERVICE.ordinal(), qrReaderClient);

        WebClient cemClient = builder.baseUrl(cemService).defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(filter)
                .build();
        clients.put(ExternalServicesIdentifier.CEM_SERVICE.ordinal(), cemClient);

        WebClient camClient = builder.baseUrl(camService).defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(filter)
                .build();
        clients.put(ExternalServicesIdentifier.CAM_SERVICE.ordinal(), camClient);

        WebClient ocrClient = builder.baseUrl(ocrService).defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(filter)
                .build();

        clients.put(ExternalServicesIdentifier.OCR_SERVICE.ordinal(), ocrClient);

        WebClient mdmClient = builder.baseUrl(mdmService).defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(filter)
                .build();

        clients.put(ExternalServicesIdentifier.MDM_SERVICE.ordinal(), mdmClient);

        WebClient apigeeAccountServiceClient = builder.baseUrl(apigeeAccountService).defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(filter)
                .build();

        clients.put(ExternalServicesIdentifier.APIGEE_ACCOUNT_SERVICE.ordinal(), apigeeAccountServiceClient);

        WebClient api4businessServiceClient = builder.baseUrl(api4businessService).defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(filter)
                .build();

        clients.put(ExternalServicesIdentifier.API4BUSINESS_SERVICE.ordinal(), api4businessServiceClient);

        // Create and return an instance of MultiServiceClient
        return new MultiServiceClient(clients, maxBuffer, new ObjectMapper());
    }

    public in.taxgenie.pay_expense_pvv.MultiServiceClient createMultiServiceClientV2(
            WebClient.Builder builder) {

        MultipartExchangeFilterFunction filter = new MultipartExchangeFilterFunction();
        WebClient qrReaderClient = builder.baseUrl(documentEtlUrl).defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(filter)
                .build();

        clients.put(ExternalServicesIdentifier.QR_READER_SERVICE.ordinal(), qrReaderClient);

        WebClient cemClient = builder.baseUrl(cemService).defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(filter)
                .build();
        clients.put(ExternalServicesIdentifier.CEM_SERVICE.ordinal(), cemClient);

        WebClient camClient = builder.baseUrl(camService).defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(filter)
                .build();
        clients.put(ExternalServicesIdentifier.CAM_SERVICE.ordinal(), camClient);

        WebClient ocrClient = builder.baseUrl(ocrService).defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(filter)
                .build();

        clients.put(ExternalServicesIdentifier.OCR_SERVICE.ordinal(), ocrClient);

        WebClient apigeeAccountServiceClient = builder.baseUrl(apigeeAccountService).defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(filter)
                .build();

        clients.put(ExternalServicesIdentifier.APIGEE_ACCOUNT_SERVICE.ordinal(), apigeeAccountServiceClient);

        WebClient api4businessServiceClient = builder.baseUrl(api4businessService).defaultHeaders(header -> {
                    header.setContentType(MediaType.APPLICATION_JSON);
                })
                .filter(filter)
                .build();

        clients.put(ExternalServicesIdentifier.API4BUSINESS_SERVICE.ordinal(), api4businessServiceClient);

        // Create and return an instance of MultiServiceClient
        return new in.taxgenie.pay_expense_pvv.MultiServiceClient(clients, maxBuffer, new ObjectMapper());
    }

}