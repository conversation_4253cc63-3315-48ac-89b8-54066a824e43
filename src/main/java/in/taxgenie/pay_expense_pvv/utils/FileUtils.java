package in.taxgenie.pay_expense_pvv.utils;

import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import io.netty.handler.codec.base64.Base64Encoder;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

@Component
public class FileUtils {
    private static final long MAX_FILE_SIZE_BYTES = 5 * 1024 * 1024; // 5 MB
    private static final Pattern VALID_FILENAME_PATTERN = Pattern.compile("^[a-zA-Z0-9._-]+$");

    public static String processFile(MultipartFile file, String environment, long companyCode, String category) {
        // Todo : Add possible exception throwing here.
        if (file.getOriginalFilename() == null) {
            return null;
        }

        String originalFilename = file.getOriginalFilename();
        StringBuilder finalFileName = new StringBuilder(environment);

        getFileContentTypeIfSupported(file);
        UUID uuid = UUID.randomUUID();

        int extensionIndex = originalFilename.lastIndexOf('.');
        finalFileName
                .append(StaticDataRegistry.URL_SEPERATOR)
                .append(companyCode)
                .append(StaticDataRegistry.URL_SEPERATOR)
                .append(category)
                .append(StaticDataRegistry.URL_SEPERATOR)
                .append(originalFilename, 0, extensionIndex)
                .append(uuid)
                .append(originalFilename, extensionIndex, originalFilename.length());
        return finalFileName.toString();
    }

    public static String processFilePath(String fileName, String environment, long companyCode, String category) {

        StringBuilder finalFileName = new StringBuilder(environment);
        int extensionIndex = fileName.lastIndexOf('.');
        checkIfSupportedByExtension(fileName.substring(extensionIndex + 1));
        UUID uuid = UUID.randomUUID();

        finalFileName
                .append(StaticDataRegistry.URL_SEPERATOR)
                .append(companyCode)
                .append(StaticDataRegistry.URL_SEPERATOR)
                .append(category)
                .append(StaticDataRegistry.URL_SEPERATOR)
                .append(fileName, 0, extensionIndex)
                .append(uuid)
                .append(fileName, extensionIndex, fileName.length());
        return finalFileName.toString();
    }

    public static String computeFileHash(MultipartFile file) throws IOException {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] bytes = digest.digest(file.getBytes());
            return Base64.getEncoder().encodeToString(bytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Could not compute file hash", e);
        }
    }
    public static void validateSupportingDocumentFilenames(List<MultipartFile> supportingDocuments) {
        List<String> invalidFileNames = new ArrayList<>();

            for (MultipartFile file : supportingDocuments) {
                String originalFilename = file.getOriginalFilename();
                if (originalFilename == null || !VALID_FILENAME_PATTERN.matcher(originalFilename).matches()) {
                    invalidFileNames.add(originalFilename != null ? originalFilename : "null");
                }
            }


        if (!invalidFileNames.isEmpty()) {
            String errorMessage = "Invalid filenames detected. Filenames must only contain letters, numbers, dot (.), underscore (_), or hyphen (-). Offending files: "
                    + String.join(", ", invalidFileNames);
            throw new DomainInvariantException(errorMessage);
        }
    }
    // Type safe function for file.getContentType()
    public static String getFileContentTypeIfSupported(MultipartFile file){
        String contentType = file.getContentType();
        checkIfSupported(contentType);
        checkIfSupportedByExtension(FilenameUtils.getExtension(file.getOriginalFilename()));
//        validateFileSize(file);
        return contentType;
    }

    private static void checkIfSupported(String contentType) {
        if (!StaticDataRegistry.uploadFileContentTypeSet.contains(contentType)) {
            throw new DomainInvariantException("Unsupported media type for the uploaded file: " + contentType);
        }
    }
    private static void checkIfSupportedByExtension(String extension) {
        if (!StaticDataRegistry.uploadFileContentTypeSetByExtension.contains(extension)) {
            throw new DomainInvariantException("Unsupported media type for the uploaded file: " + extension);
        }
    }
    private static void validateFileSize(MultipartFile file) {
        if (file.getSize() > MAX_FILE_SIZE_BYTES) {
            throw new DomainInvariantException("Uploaded file is too large, greater than 5MB");
        }
    }
    private static String sanitizeFilename(String originalFilename) {
        return originalFilename.replaceAll("[^a-zA-Z0-9._-]", "_");
    }

    public static void validateFileType(MultipartFile file, String expectedType){
        String fileType = FilenameUtils.getExtension(file.getOriginalFilename());
        if (!fileType.equalsIgnoreCase(expectedType)){
            throw new DomainInvariantException("Selected invalid file type, Please select ."+expectedType+" file ");
        }
    }

    public static byte[] readResourceAsBytes(String resourcePath) throws IOException {
        try (InputStream inputStream = Base64Encoder.class.getClassLoader().getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                throw new IOException("Resource not found: " + resourcePath);
            }
            return inputStream.readAllBytes();
        }
    }

    public static String getBase64String(byte[] fileContent) {
        try {
            // Encode the byte array to Base64
            return Base64.getEncoder().encodeToString(fileContent);
        } catch (Exception e) {
            return "";
        }
    }

    public static String convertMultipartFileToBase64(MultipartFile file) {
        try {
            // Get the file content as a byte array
            byte[] fileContent = file.getBytes();
            // Encode the byte array to Base64
            return Base64.getEncoder().encodeToString(fileContent);
        } catch (IOException e) {
            return null; // Handle the exception as needed
        }
    }

}
