package in.taxgenie.pay_expense_pvv.exceptions;

import lombok.Getter;

@Getter
public class MatchConfigurationException extends RuntimeException {
    
    private final String errorCode;
    
    public MatchConfigurationException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public static MatchConfigurationException notFound(Long id) {
        return new MatchConfigurationException(
                String.format("Match configuration with ID %d not found", id),
                "MATCH_CONFIG_001"
        );
    }
    
    public static MatchConfigurationException invalidOperation(String operation) {
        return new MatchConfigurationException(
                String.format("Invalid operation for match configuration: %s", operation),
                "MATCH_CONFIG_002"
        );
    }
    
    public static MatchConfigurationException accessDenied(Long companyCode) {
        return new MatchConfigurationException(
                String.format("Access denied for match configurations of company %d", companyCode),
                "MATCH_CONFIG_003"
        );
    }
}