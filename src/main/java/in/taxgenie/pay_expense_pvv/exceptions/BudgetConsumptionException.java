package in.taxgenie.pay_expense_pvv.exceptions;

/**
 * Custom exception for budget consumption related errors.
 * Extends RuntimeException to maintain existing error handling patterns.
 */
public class BudgetConsumptionException extends RuntimeException {

    private final String errorCode;
    private final long documentId;

    public BudgetConsumptionException(String message) {
        super(message);
        this.errorCode = "BUDGET_CONSUMPTION_ERROR";
        this.documentId = 0L;
    }

    public BudgetConsumptionException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "BUDGET_CONSUMPTION_ERROR";
        this.documentId = 0L;
    }

    public BudgetConsumptionException(String message, String errorCode, long documentId) {
        super(message);
        this.errorCode = errorCode;
        this.documentId = documentId;
    }

    public BudgetConsumptionException(String message, String errorCode, long documentId, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.documentId = documentId;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public long getDocumentId() {
        return documentId;
    }

    /**
     * Check if this is a budget missing/not found error
     */
    public boolean isBudgetMissingError() {
        return errorCode != null && (
            errorCode.contains("BUDGET_GENERIC_ERROR_NOT_FOUND") ||
            errorCode.contains("BUDGET_REVERSAL_SKIPPED") ||
            getMessage().toLowerCase().contains("budget") && getMessage().toLowerCase().contains("not found")
        );
    }

    /**
     * Check if this is a budget reversal skip error
     */
    public boolean isBudgetReversalSkipError() {
        return errorCode != null && errorCode.contains("BUDGET_REVERSAL_SKIPPED");
    }

    @Override
    public String toString() {
        return String.format("BudgetConsumptionException{errorCode='%s', documentId=%d, message='%s'}", 
                           errorCode, documentId, getMessage());
    }
}
