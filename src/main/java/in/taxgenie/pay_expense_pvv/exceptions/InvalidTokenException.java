package in.taxgenie.pay_expense_pvv.exceptions;

public class InvalidTokenException extends RuntimeException {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    public InvalidTokenException() {
        super("Invalid Token For User");
    }

    public InvalidTokenException(String message) {
        super(message);
    }

    public InvalidTokenException(String jwtToken, String userName) {
        super("Invalid Token " + jwtToken + " for user " + userName);
    }

}