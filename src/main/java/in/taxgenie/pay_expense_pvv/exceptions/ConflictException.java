package in.taxgenie.pay_expense_pvv.exceptions;

/**
 * Exception thrown when a request conflicts with the current state of the resource.
 * This exception maps to HTTP 409 Conflict status code.
 */
public class ConflictException extends RuntimeException {

    private final String errorCode;

    /**
     * Constructs a new ConflictException with the specified error code.
     *
     * @param errorCode the specific error code (e.g., PO_ALREADY_DISCARDED)
     */
    public ConflictException(String errorCode) {
        super(errorCode);
        this.errorCode = errorCode;
    }

    /**
     * Constructs a new ConflictException with the specified error code and detail message.
     *
     * @param errorCode the specific error code
     * @param message the detail message
     */
    public ConflictException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * Constructs a new ConflictException with the specified error code, detail message and cause.
     *
     * @param errorCode the specific error code
     * @param message the detail message
     * @param cause the cause of the exception
     */
    public ConflictException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * Gets the error code associated with this exception.
     *
     * @return the error code
     */
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String toString() {
        return String.format("ConflictException{errorCode='%s', message='%s'}", 
                           errorCode, getMessage());
    }
}
