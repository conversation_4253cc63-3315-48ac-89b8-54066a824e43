package in.taxgenie.pay_expense_pvv;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;


@EnableAspectJAutoProxy
@SpringBootApplication
//@EnableScheduling
@EntityScan(basePackages = {"in.taxgenie.pay_expense_pvv.entities"})
public class PayExpensePvvApplication extends SpringBootServletInitializer {
    public static void main(String[] args) {
        SpringApplication.run(PayExpensePvvApplication.class, args);
    }
}