package in.taxgenie.pay_expense_pvv;


import in.taxgenie.pay_expense_pvv.viewmodels.ErrorResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;

public class ClientApiException extends RuntimeException {

    private HttpStatusCode status;
    private String message;
    private ErrorResponse errorResponse;
    private Object errorPayload;

    public ClientApiException(HttpStatusCode status, String errorPayload) {
        super("Error from service: " + status + " " + errorPayload);
        this.status = status;
        this.errorPayload = errorPayload;
    }

    public ClientApiException(String message) {
        super(message);
    }

    public ClientApiException(String message, HttpStatusCode httpStatusCode) {
        super(message);
        this.message = message;
        this.status = httpStatusCode;
    }

    public ClientApiException(String message, HttpStatus status, Throwable cause) {
        super(message, cause);
        this.message = message;
        this.status = status;
    }

    public ClientApiException(HttpStatusCode status, ErrorResponse resp) {
        System.out.println("In client api exception");
        this.status = status;
        this.errorResponse = resp;
    }

    public ClientApiException(String message, Throwable cause) {
        super(message, cause);
    }

}