package in.taxgenie.pay_expense_pvv.validation;

import org.springframework.web.bind.MethodArgumentNotValidException;

import java.time.ZonedDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class ValidationUtils {
    public static ValidationErrorPageViewModel getValidationErrorPage(MethodArgumentNotValidException exception, Class<?> modelClass) {
        List<DomainErrorViewModel> domainErrors = exception
                .getBindingResult()
                .getFieldErrors()
                .stream()
                .map(fe -> new DomainErrorViewModel(fe.getField(), fe.getDefaultMessage()))
                .sorted(Comparator.comparing(DomainErrorViewModel::getField))
                .collect(Collectors.toList());


        return new ValidationErrorPageViewModel(
                getValidationIdentifierValue(modelClass),
                ZonedDateTime.now(),
                domainErrors
        );
    }

    private static String getValidationIdentifierValue(Class<?> modelClass) {
        ValidationIdentifier identifierInstance = modelClass.getAnnotation(ValidationIdentifier.class);

        if (identifierInstance == null) {
            return "Not available";
        }

        return identifierInstance.value();
    }
}
