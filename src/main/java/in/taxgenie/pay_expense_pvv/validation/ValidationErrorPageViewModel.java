package in.taxgenie.pay_expense_pvv.validation;

import java.time.ZonedDateTime;
import java.util.List;

public class ValidationErrorPageViewModel {
    private final String domain;
    private final ZonedDateTime transmitOn;
    private final List<DomainErrorViewModel> domainErrors;

    public ValidationErrorPageViewModel(String entity, ZonedDateTime transmitOn, List<DomainErrorViewModel> domainErrors) {
        this.domain = entity;
        this.transmitOn = transmitOn;
        this.domainErrors = domainErrors;
    }

    public String getDomain() {
        return domain;
    }

    public ZonedDateTime getTransmitOn() {
        return transmitOn;
    }

    public List<DomainErrorViewModel> getDomainErrors() {
        return domainErrors;
    }

}
