package in.taxgenie.pay_expense_pvv.viewmodels.pr;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.BaseContainerViewModel;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Getter
@Setter
public class PurchaseRequestContainerViewModel extends BaseContainerViewModel {

    private Long id;
    private BigDecimal prAmount;
    private BigDecimal balancePrAmount;
    private BigDecimal approvedInPOAmount;
    private BigDecimal inProcessOfBeingApprovedInPOAmount;

    private String description;
    private String prType;

    // Document
    private String originatorDocumentIdentifier;
    private Long documentSubgroupId;
    private String documentGroup;
    private String documentSubGroup;
    private ExpenseType expenseType;
    private String departmentName;

    private String documentDate;
    private Integer supportingDocumentsCount;

    public PurchaseRequestContainerViewModel(Long id, BigDecimal prAmount, BigDecimal balancePrAmount, String documentIdentifier,
                                             ZonedDateTime ZonedDateTime, LocalDate documentDate, ReportStatus reportStatus,
                                             LocalDate submitDate, String currentApproverFirstName,
                                             String currentApproverLastName, String currentApproverSystemIdCode, String currentApproverEmployeeCode,
                                             String firstName, String middleName, String lastName, String employeeCode, String employeeGrade, ZonedDateTime updatedTimestamp,
                                             String originatorDocumentIdentifier, Long documentSubgroupId, String departmentName, LocalDate expectedDeliveryDate,
                                             String purchaseReason, String docNo, String prType, BigDecimal approvedInPOAmount, BigDecimal inProcessOfBeingApprovedInPOAmount, Integer supportingDocumentsCount,
                                             ExpenseType expenseType, String documentGroup, String documentSubGroup) {
        super(id, documentIdentifier, docNo, ZonedDateTime, expectedDeliveryDate, reportStatus, submitDate, updatedTimestamp, currentApproverFirstName,
                currentApproverLastName, currentApproverSystemIdCode, currentApproverEmployeeCode, firstName, middleName, lastName, employeeCode, employeeGrade);
        this.id = id;
        this.prAmount = prAmount;
        this.approvedInPOAmount = approvedInPOAmount ;
        this.inProcessOfBeingApprovedInPOAmount = inProcessOfBeingApprovedInPOAmount;
        this.balancePrAmount = balancePrAmount;
        this.originatorDocumentIdentifier = originatorDocumentIdentifier;
        this.documentSubgroupId = documentSubgroupId;
        this.expenseType = expenseType;
        this.documentGroup = documentGroup;
        this.documentSubGroup = documentSubGroup;
        this.departmentName = departmentName;
        this.description = purchaseReason;
        this.prType = prType;
        this.documentDate = DateTimeUtils.formatDateJPQL(documentDate);
        this.supportingDocumentsCount = supportingDocumentsCount;
    }

}
