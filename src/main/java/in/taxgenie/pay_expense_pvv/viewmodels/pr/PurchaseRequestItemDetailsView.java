package in.taxgenie.pay_expense_pvv.viewmodels.pr;

import com.fasterxml.jackson.annotation.JsonProperty;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseRequestItemDetailsView {

    private Long id;
    private String docNo;
    private Long itemMasterId;

    @JsonProperty("budgetId")
    private Long budgetNodeId;
    private String budgetCode;

    @JsonProperty("itemType")
    private String type;
    @JsonProperty("itemName")
    private String name;
    private String itemCode;
    private Integer unitOfMeasureId;
    private String unitOfMeasure;
    @JsonProperty("itemDescription")
    private String description;
    // private item code - TODO: fetch this
    private String hsn;
    private Double quantity;
    private Double selectedQuantity;
    private BigDecimal unitRate;

    @JsonProperty("gstRate")
    private Double taxPercentage;
    private BigDecimal amountWithoutGst;
    private BigDecimal totalGstAmount;
    private BigDecimal total;
    private BigDecimal igst;
    private BigDecimal cgst;
    private BigDecimal sgst;

    private Integer quotationId;
    private Integer vendorId;
    private String vendorName;
    private String createdBy;
    private String createdAt;

    private Boolean isCatalog;

    private String requesterRemark;

    // For custom repository query
    public PurchaseRequestItemDetailsView(Long id, String docNo, ItemType type, String name, String unitOfMeasure, String description, Integer quantity, BigDecimal unitRate, Double taxPercentage, BigDecimal amountWithoutGst, BigDecimal totalGstAmount, BigDecimal total, Integer quotationId, Integer vendorId, String vendorName) {
        this.id = id;
        this.docNo = docNo;
        this.type = type.getFormattedName();
        this.name = name;
        this.unitOfMeasure = unitOfMeasure;
        this.description = description;
        this.quantity = (quantity != null) ? quantity.doubleValue() : null;
        this.unitRate = unitRate;
        this.taxPercentage = taxPercentage != null ? taxPercentage : 0.0;
        this.amountWithoutGst = amountWithoutGst;
        this.totalGstAmount = totalGstAmount;
        this.total = total;
        this.quotationId = quotationId;
        this.vendorId = vendorId;
        this.vendorName = vendorName;
    }

    // For custom repository query : Todo: Remove this once agg issue solved
    public PurchaseRequestItemDetailsView(Long id, String docNo, ItemType type, String name, String hsn, String itemCode, String unitOfMeasure, String description, Double quantity,
                                          BigDecimal unitRate, Double taxPercentage, BigDecimal amountWithoutGst, BigDecimal totalGstAmount, BigDecimal total,
                                          Integer quotationId, Integer vendorId, String vendorName, Long budgetNodeId, String budgetCode,
                                          String createdBy, LocalDateTime createdAt, String requesterRemark) {
        this.id = id;
        this.docNo = docNo;
        this.type = type.getFormattedName();
        this.name = name;
        this.hsn = hsn;
        this.itemCode = itemCode;
        this.unitOfMeasure = unitOfMeasure;
        this.description = description;
        this.quantity = quantity;
        this.selectedQuantity = quantity;
        this.unitRate = unitRate;
        this.taxPercentage = taxPercentage != null ? taxPercentage : 0.0;
        this.amountWithoutGst = amountWithoutGst;
        this.totalGstAmount = totalGstAmount;
        this.total = total;
        this.quotationId = quotationId;
        this.vendorId = vendorId;
        this.vendorName = vendorName;
        this.budgetNodeId = budgetNodeId;
        this.budgetCode = budgetCode;
        this.createdBy = createdBy;
        this.createdAt = DateTimeUtils.formatDateJPQL(createdAt);
        this.requesterRemark = requesterRemark;
    }
}
