package in.taxgenie.pay_expense_pvv.viewmodels.pr;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.pr.ProcurementStatus;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.BaseContainerViewModel;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Data
public class ProcurementViewModel extends BaseContainerViewModel {

    private Long id;
    private Long prId;
    private BigDecimal prAmount;
    private BigDecimal balancePrAmount;
    private BigDecimal approvedInPOAmount;
    private BigDecimal inProcessOfBeingApprovedInPOAmount;

    private String description;
    private String prType;
    ProcurementStatus procurementStatus;
    ReportStatus reportStatus;

    // Document
    private String originatorDocumentIdentifier;
    private Long documentSubgroupId;

    private String departmentName;

    private String documentDate;

    public ProcurementViewModel(Long id, Long prId, BigDecimal prAmount, BigDecimal approvedInPOAmount, String documentIdentifier,
                                ZonedDateTime createdDate, LocalDate documentDate, ReportStatus reportStatus,
                                             LocalDate submitDate, String currentApproverFirstName,
                                             String currentApproverLastName, String currentApproverSystemIdCode, String currentApproverEmployeeCode,
                                             String firstName, String middleName, String lastName, String employeeCode, String employeeGrade, ZonedDateTime updatedTimestamp,
                                             String originatorDocumentIdentifier, Long documentSubgroupId, String departmentName, LocalDate expectedDeliveryDate,
                                             String purchaseReason, String docNo, String prType, BigDecimal inProcessOfBeingApprovedInPOAmount, BigDecimal balancePrAmount) {
        super(id, documentIdentifier, docNo, createdDate, expectedDeliveryDate, null, submitDate, updatedTimestamp, currentApproverFirstName,
                currentApproverLastName, currentApproverSystemIdCode, currentApproverEmployeeCode, firstName, middleName, lastName, employeeCode, employeeGrade);
        this.id = id;
        this.prId = prId;
        this.prAmount = prAmount;
        this.approvedInPOAmount = approvedInPOAmount ;
        this.inProcessOfBeingApprovedInPOAmount = inProcessOfBeingApprovedInPOAmount;
        this.balancePrAmount = balancePrAmount;
        this.reportStatus = reportStatus;
        this.originatorDocumentIdentifier = originatorDocumentIdentifier;
        this.documentSubgroupId = documentSubgroupId;
        this.departmentName = departmentName;
        this.description = purchaseReason;
        this.prType = prType;
        this.documentDate = DateTimeUtils.formatDateJPQL(documentDate);
    }
}
