package in.taxgenie.pay_expense_pvv.viewmodels.pr;

public class PurchaseRequestItemDetailsAggViewModel {
    long id;
    long itemCount;
    double quantity;

    public PurchaseRequestItemDetailsAggViewModel() {
        this.itemCount = 0;
        this.quantity = 0;
    }

    public PurchaseRequestItemDetailsAggViewModel(long id, long itemCount, double quantity) {
        this.id = id;
        this.itemCount = itemCount;
        this.quantity = quantity;
    }

    public void addQuantity(double quantity) {
        this.quantity += quantity;
    }
    public void incrementCount() {
        this.itemCount += 1;
    }
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getItemCount() {
        return itemCount;
    }

    public void setItemCount(long itemCount) {
        this.itemCount = itemCount;
    }

    public double getQuantity() {
        return quantity;
    }

    public void setQuantity(double quantity) {
        this.quantity = quantity;
    }
}
