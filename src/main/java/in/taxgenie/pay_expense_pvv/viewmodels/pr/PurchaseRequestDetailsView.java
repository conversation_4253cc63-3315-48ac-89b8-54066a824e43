package in.taxgenie.pay_expense_pvv.viewmodels.pr;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseRequestDetailsView {

    private Long id;
    private Integer prTypeId;
    private Integer docTypeId;
    private String docNo;
    private Integer sellerId;
    private Integer buyerId;
    private Integer priorityId;
    private String documentDate;
    private String expectedDeliveryDate;
    private String description;
    private String financialYear;
    private String vendorName;
    private Boolean isPrApproved;
    private String createdAt;
    private String createdBy;
    private String updatedAt;
    private String updatedBy;
    private BigDecimal taxableValue;
    private BigDecimal totalPRValue;
    private String requesterRemark;
}
