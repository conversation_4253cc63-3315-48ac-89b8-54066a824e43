package in.taxgenie.pay_expense_pvv.viewmodels.pr;


public class PRHeaderWithConsumptionCount {
    long id;
    long prItemId;
    double count;

    public PRHeaderWithConsumptionCount() {
    }

    public PRHeaderWithConsumptionCount(long id, long prItemId, double count) {
        this.id = id;
        this.prItemId = prItemId;
        this.count = count;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getPrItemId() {
        return prItemId;
    }

    public void setPrItemId(long prItemId) {
        this.prItemId = prItemId;
    }

    public double getCount() {
        return count;
    }

    public void setCount(double count) {
        this.count = count;
    }
}
