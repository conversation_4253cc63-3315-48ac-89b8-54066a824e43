package in.taxgenie.pay_expense_pvv.viewmodels.pr;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderItemDetailsViewModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PRPreviewViewModel {

    private Long id;
    private String prType;
    private ReportStatus reportStatus;
    private String supplierName;
    private String financialYear;
    private String dateOfDocument;
    private String requesterName;
    private String paymentTerm;
    private String budgetCode;
    private BigDecimal budgetAmount = BigDecimal.ZERO;
    private BigDecimal prAmountWOGst = BigDecimal.ZERO;
    private BigDecimal advanceAmount = BigDecimal.ZERO;
    private BigDecimal tdsRate = BigDecimal.ZERO;
    private BigDecimal tdsAmount = BigDecimal.ZERO;
    private BigDecimal securityDeposit = BigDecimal.ZERO;
    private String costCenter;
    private String profitCenter;
    private String prDescription;
    private String prNumber;
    private BigDecimal totalPRAmount = BigDecimal.ZERO;
    List<PurchaseRequestItemDetailsView> prItems;
}
