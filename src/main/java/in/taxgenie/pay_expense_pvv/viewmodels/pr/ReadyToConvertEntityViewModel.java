package in.taxgenie.pay_expense_pvv.viewmodels.pr;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.erp.ERPGrnViewModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ReadyToConvertEntityViewModel {

    private Long id;
    private Long containerId;
    private Long documentSubgroupId;
    private String policyName;
    private BigDecimal eligiblePrAmount;
    private Long itemCount;
    private String docNo;
    private ReportStatus status;
    private String subgroupFieldsJson;
    private Boolean hasPrecedingDocument;
    private String createdBy;
    private String createdAt;
    List<ERPGrnViewModel> grnViewModels;
    private Map<String, Object> businessDetails;

    public ReadyToConvertEntityViewModel(Long id, Long containerId, String policyName, BigDecimal eligiblePrAmount, Long itemCount, String docNo, String subgroupFieldsJson, String createdBy, ZonedDateTime createdAt, Long documentSubgroupId, Boolean hasPrecedingDocument) {
        this.id = id;
        this.containerId = containerId;
        this.policyName = policyName;
        this.eligiblePrAmount = eligiblePrAmount;
        this.itemCount = itemCount;
        this.docNo = docNo;
        this.subgroupFieldsJson = subgroupFieldsJson;
        this.createdBy = createdBy;
        this.createdAt = DateTimeUtils.formatTimestamp(createdAt);
        this.documentSubgroupId = documentSubgroupId;
        this.hasPrecedingDocument = hasPrecedingDocument;
    }
}
