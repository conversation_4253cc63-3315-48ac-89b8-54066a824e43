package in.taxgenie.pay_expense_pvv.viewmodels.pr;

import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ProcurementStatisticsViewModel {
    private Long readyToConvertCount;
    private BigDecimal readyToConvertAmount;
    private Long partiallyConvertedCount;
    private BigDecimal partiallyConvertedAmount;
    private Long inProgressCount;
    private BigDecimal inProgressAmount;
    private Long sentBackCount;
    private BigDecimal sentBackAmount;
    private Long partiallyClosedCount;
    private BigDecimal partiallyClosedAmount;
    private <PERSON> fullyConvertedCount;
    private BigDecimal fullyConvertedAmount;
    private Long closedCount;
    private BigDecimal closedAmount;
}
