package in.taxgenie.pay_expense_pvv.viewmodels.pr;

import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetDetails;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseRequestCreateViewModel {

    private PurchaseRequestDetailsView prDetails;
    private List<PurchaseRequestItemDetailsView> itemDetails;
    private BudgetDetails budgetDetails;
    private Long containerId;
    private List<Long> lineItemIds;
    private List<Long> uploadedDocIds;

}
