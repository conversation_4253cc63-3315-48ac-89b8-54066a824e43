package in.taxgenie.pay_expense_pvv.viewmodels;

import java.time.LocalDate;

public class ApprovalDelegationViewModel {
    private long id;
    private LocalDate startDate;
    private LocalDate endDate;
    private String fromApprover;
    private String toApprover;
    private String remarks;
    private boolean assignUnapproved;
    private boolean isFrozen;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getFromApprover() {
        return fromApprover;
    }

    public void setFromApprover(String fromApprover) {
        this.fromApprover = fromApprover;
    }

    public String getToApprover() {
        return toApprover;
    }

    public void setToApprover(String toApprover) {
        this.toApprover = toApprover;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public boolean isAssignUnapproved() {
        return assignUnapproved;
    }

    public void setAssignUnapproved(boolean assignUnapproved) {
        this.assignUnapproved = assignUnapproved;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }
}
