package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.entities.PaidStatus;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Getter
@Setter
@NoArgsConstructor
public class AdminQueueViewModelV2 {

    private Long id;
    private Long companyCode;
    private String documentIdentifier;
    private LocalDate createdDate;
    private LocalDate submitDate;
    private LocalDate startDate;
    private LocalDate endDate;
    private String reportTitle;
    private String description;
    private String purpose;
    private BigDecimal reportClaimAmount;
    private ReportStatus reportStatus;
    private String deviationRemarks;
    private Boolean containsDeviation;
    private Boolean containsSentBack;
    private String expenseType;
    private BigDecimal reportSgstAmount;
    private BigDecimal reportCgstAmount;
    private BigDecimal reportIgstAmount;
    private BigDecimal reportTaxableAmount;
    private Boolean acknowledgedByErp;
    private ZonedDateTime erpAcknowledgementTimestamp;
    private Boolean postedToGl;
    private LocalDate glPostingDate;
    private String glDocumentReference;
    private PaidStatus paidStatus;
    private BigDecimal totalPaidAmount;
    private BigDecimal totalTdsAmount;
    private String currentApproverFirstName;
    private String currentApproverLastName;
    private String currentApproverEmployeeCode;
    private String firstName;
    private String middleName;
    private String lastName;
    private String employeeEmail;
    private String employeeCode;
    private Long employeeSystemId;
    private String gender;
    private LocalDate paymentDate;
    private String employeeHrmsCode;
    private String employeeType;
    private String employeeGrade;
    private String employeeBranch;
    private String employeeDepartment;
    private String employeeCostCenter;
    private String employeeGlMainAccountCode;
    private String employeeGlSubAccountCode;
    private String employeeProfitCenter;
    private String dimension01;
    private String dimension02;
    private String dimension03;
    private String dimension04;
    private String dimension05;
    private String dimension06;
    private String dimension07;
    private String dimension08;
    private String dimension09;
    private String dimension10;
    private String key01;
    private String key02;
    private String key03;
    private String key04;
    private String key05;
    private String key06;
    private String key07;
    private String key08;
    private String key09;
    private String key10;
    private String value01;
    private String value02;
    private String value03;
    private String value04;
    private String value05;
    private String value06;
    private String value07;
    private String value08;
    private String value09;
    private String value10;
    private String currentApproverSystemIdCode;
    private String sendBackRemarks;
    private String rejectRemarks;
    private String delegationRemarks;
    private String defaultApproverRemarks;
    private String expenseGroup;
    private Long creatingUserId;
    private Long updatingUserId;
    private Long expenseMetadataId;
    private ExpenseActionStatus status;
    private Integer level;
    private ZonedDateTime updatedTimestamp;


    public AdminQueueViewModelV2(
            String currentApproverEmployeeCode, String currentApproverFirstName, Integer level, String currentApproverLastName,
            Long id, String documentIdentifier, LocalDate createdDate, LocalDate submitDate, LocalDate startDate, LocalDate endDate,
            String reportTitle, String description, String purpose, BigDecimal reportClaimAmount, ReportStatus reportStatus,
            Integer actionLevel, Boolean containsDeviation, String deviationRemarks, BigDecimal reportSgstAmount, BigDecimal reportCgstAmount,
            BigDecimal reportIgstAmount, BigDecimal reportTaxableAmount, String firstName, String middleName, String lastName,
            String employeeEmail, String employeeCode, String employeeGrade, Long employeeSystemId, ExpenseType gender,
            long documentMetadataId, String documentGroup, String documentType, String sendBackRemarks, String rejectRemarks,
            String delegationRemarks, String defaultApproverRemarks, Boolean containsSentBack, LocalDate glPostingDate,
            LocalDate paymentDate, ZonedDateTime updatedTimestamp
    ) {
        this.currentApproverEmployeeCode = currentApproverEmployeeCode;
        this.currentApproverFirstName = currentApproverFirstName;
        this.level = level;
        this.currentApproverLastName = currentApproverLastName;
        this.id = id;
        this.documentIdentifier = documentIdentifier;
        this.createdDate = createdDate;
        this.submitDate = submitDate;
        this.startDate = startDate;
        this.endDate = endDate;
        this.reportTitle = reportTitle;
        this.description = description;
        this.purpose = purpose;
        this.reportClaimAmount = reportClaimAmount;
        this.reportStatus = reportStatus;
//        this.ac = actionLevel;
        this.containsDeviation = containsDeviation;
        this.deviationRemarks = deviationRemarks;
        this.reportSgstAmount = reportSgstAmount;
        this.reportCgstAmount = reportCgstAmount;
        this.reportIgstAmount = reportIgstAmount;
        this.reportTaxableAmount = reportTaxableAmount;
        this.firstName = firstName;
        this.middleName = middleName;
        this.lastName = lastName;
        this.employeeEmail = employeeEmail;
        this.employeeCode = employeeCode;
        this.employeeGrade = employeeGrade;
        this.employeeSystemId = employeeSystemId;
        this.gender = gender.name();
        this.expenseMetadataId = documentMetadataId;
        this.expenseGroup = documentGroup;
        this.expenseType = documentType;
        this.sendBackRemarks = sendBackRemarks;
        this.rejectRemarks = rejectRemarks;
        this.delegationRemarks = delegationRemarks;
        this.defaultApproverRemarks = defaultApproverRemarks;
        this.containsSentBack = containsSentBack;
        this.glPostingDate = glPostingDate;
        this.paymentDate = paymentDate;
        this.updatedTimestamp = updatedTimestamp;
    }
}

