package in.taxgenie.pay_expense_pvv.viewmodels;

import java.math.BigDecimal;

public class ApprovalKeyValuePairsViewModel {
    private Long documentMetadataId;
    private BigDecimal approvalContainerClaimAmount;

    public ApprovalKeyValuePairsViewModel() {
    }

    public ApprovalKeyValuePairsViewModel(Long documentMetadataId, BigDecimal approvalContainerClaimAmount) {
        this.documentMetadataId = documentMetadataId;
        this.approvalContainerClaimAmount = approvalContainerClaimAmount;
    }

    public Long getDocumentMetadataId() {
        return documentMetadataId;
    }

    public void setDocumentMetadataId(Long documentMetadataId) {
        this.documentMetadataId = documentMetadataId;
    }

    public BigDecimal getApprovalContainerClaimAmount() {
        return approvalContainerClaimAmount;
    }

    public void setApprovalContainerClaimAmount(BigDecimal approvalContainerClaimAmount) {
        this.approvalContainerClaimAmount = approvalContainerClaimAmount;
    }
}
