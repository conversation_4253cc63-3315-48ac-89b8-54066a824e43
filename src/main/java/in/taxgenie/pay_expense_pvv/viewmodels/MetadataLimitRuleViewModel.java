package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.IntervalMarker;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MetadataLimitRuleViewModel {
    private long id;
    private BigDecimal limitAmount;
    private IntervalMarker intervalMarker;
    private String branchCode;
    private String departmentCode;
    private String costCenterCode;
    private String employeeType;
    private String employeeGrade;
    private LocalDate startDate;
    private LocalDate endDate;
    private String locationCategory;

    //  Common
    private boolean isFrozen;
    private long documentMetadataId;
    private String metadataMarker;

    private int documentCountLimit;
}
