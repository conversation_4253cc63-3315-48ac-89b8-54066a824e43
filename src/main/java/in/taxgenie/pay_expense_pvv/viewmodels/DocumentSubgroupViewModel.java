package in.taxgenie.pay_expense_pvv.viewmodels;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentSubgroupViewModel {
    private long id;

    private String documentCode;
    private String documentSubgroup;
    private String documentSubgroupPrefix;
    private Integer documentTypeId;
    private String documentGenre;
    private String documentGenrePrefix;
    private boolean isLocationRequired;
    private String glAccountCode;
    private String description;
    private String frequency;

    private boolean merchantRequired;

    // JSONB field for flags
    private String subgroupFieldsJson;

    //  flags
    private boolean isSourceLocationApplicable;
    private boolean isDestinationLocationApplicable;
    private boolean isGstEntryAllowed;
    private boolean isStandardDeductionApplicable;
    private boolean isDateRangeApplicable;
    private boolean isFrequencyApplicable;
    private boolean isExpenseIdentifierApplicable;

    private String applicableExpenseType;

    // Budget Link
    private Long budgetId;
    private Long budgetStructureMasterId;

    private boolean hasPrecedingDocument;

    private boolean isTransportDescriptorApplicable;
    private boolean isMobilityDescriptorApplicable;
    private boolean isTravelDescriptorApplicable;


    //  Common
    private boolean isFrozen;
    private long documentMetadataId;
    private String documentSubgroupMarker;
    private int rulesCount;
}
