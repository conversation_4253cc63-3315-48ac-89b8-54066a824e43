package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.IntervalMarker;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MetadataLimitRuleUpdateViewModel {
    private long id;
    @Positive
    private BigDecimal limitAmount;
    @NotNull
    private IntervalMarker intervalMarker;
    private String branchCode;
    private String departmentCode;
    private String costCenterCode;
    private String employeeType;
    private String employeeGrade;
    private LocalDate startDate;
    private LocalDate endDate;
    private String locationCategory;

    @Min(0)
    @Max(1_000)
    private int documentCountLimit;

    //  Common
    private boolean isFrozen;
}
