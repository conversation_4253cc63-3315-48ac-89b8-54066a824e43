package in.taxgenie.pay_expense_pvv.viewmodels;

public class ValidationStatusViewModel {
    private final boolean isValid;
    private final String validationErrors;

    public ValidationStatusViewModel(boolean isValid, String validationErrors) {
        this.isValid = isValid;
        this.validationErrors = validationErrors;
    }

    public boolean isValid() {
        return isValid;
    }

    public String getValidationErrors() {
        return validationErrors;
    }
}
