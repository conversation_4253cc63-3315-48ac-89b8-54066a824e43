package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.StateChannel;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

public class ApproverCreateViewModel {
    @NotBlank
    @Size(max = 50)
    private String approvalMatcher;
    @NotBlank
    @Size(max = 50)
    private String approvalMatchValue;
    @NotBlank
    @Size(max = 50)
    private String approvalTitle;
    @NotBlank
    @Size(max = 200)
    private String approver;
    @NotNull
    private StateChannel channel;

    public String getApprovalMatcher() {
        return approvalMatcher;
    }

    public void setApprovalMatcher(String approvalMatcher) {
        this.approvalMatcher = approvalMatcher;
    }

    public String getApprovalMatchValue() {
        return approvalMatchValue;
    }

    public void setApprovalMatchValue(String approvalMatchValue) {
        this.approvalMatchValue = approvalMatchValue;
    }

    public String getApprovalTitle() {
        return approvalTitle;
    }

    public void setApprovalTitle(String approvalTitle) {
        this.approvalTitle = approvalTitle;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public StateChannel getChannel() {
        return channel;
    }

    public void setChannel(StateChannel channel) {
        this.channel = channel;
    }
}
