package in.taxgenie.pay_expense_pvv.viewmodels;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentRuleViewModel {
    private long id;
    private String branchCode;
    private String departmentCode;
    private String locationCategory;
    private String employeeType;
    private String employeeGrade;
    private String costCenterCode;

    private boolean isInvoiceRequired;
    private BigDecimal invoiceRequiredThreshold;

    private LocalDate startDate;
    private LocalDate endDate;

    //  regular specific
    private BigDecimal standardDeductionRate;

    //  Other
    private BigDecimal limitAmount;
    private BigDecimal maximumAmount;
    private boolean canExceedLimit;

    private boolean isPerDiemAllowed;
    private BigDecimal perDiemAmount;

    //  Unit rate specific
    private boolean isUnitRateApplicable;
    private String unitOfMeasure;
    private BigDecimal unitRate;
    private String unitRateType;

    //  Common
    private boolean isFrozen;
    private long documentSubgroupId;
    private String documentMetadataMarker;

}
