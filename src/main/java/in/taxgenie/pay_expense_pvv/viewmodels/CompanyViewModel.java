package in.taxgenie.pay_expense_pvv.viewmodels;

import lombok.Data;

@Data
public class CompanyViewModel {
    Integer id;
    String address1;
    String address2;
    String location;
    String pin;
    Integer stateCodeId;
    String gstin;
    String legalName;
    String phoneNumber;
    String email;
    String vendorCode;
    String supplierCompanyName;
    String gstinStatus;
    Boolean isHandshake = Boolean.FALSE;
}
