package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;


@Data
@NoArgsConstructor
public class DocumentApprovalContainerViewModel {
    private long id;

    private String documentIdentifier;
    private String documentCategory;
    private String createdDate;
    private String submitDate;
    private String startDate;
    private String endDate;

    private String approvalContainerTitle;
    private String description;
    private String purpose;
    private Integer level;

    private BigDecimal approvalContainerClaimAmount;
    private ReportStatus reportStatus;
    private int actionLevel;
    private boolean containsDeviation;
    private String deviationRemarks;

    //  GST computation
    private BigDecimal approvalContainerSgstAmount;
    private BigDecimal approvalContainerCgstAmount;
    private BigDecimal approvalContainerIgstAmount;
    private BigDecimal approvalContainerTaxableAmount;

    //  Employee master data
    private String firstName;
    private String middleName;
    private String lastName;
    private String employeeEmail;
    private String employeeCode;

    private String employeeGrade;
    private Long employeeSystemId;
    private String expenseType;

    private long documentMetadataId;
    private String documentType;
    private String documentGroup;
    private String sendBackRemarks;
    private String rejectRemarks;
    private String delegationRemarks;
    private String defaultApproverRemarks;
    private String currentApproverFirstName;
    private String currentApproverLastName;
    private String currentApproverSystemIdCode;
    private String currentApproverEmployeeCode;
    private boolean containsSentBack;

    //  gl related
    private LocalDate glPostingDate;
    private LocalDate paymentDate;
    private String utrNumber;

    // Document
    private Long documentSubgroupId;

    public DocumentApprovalContainerViewModel(long id, String documentIdentifier, String documentCategory, LocalDate createdDate, LocalDate submitDate, LocalDate startDate, LocalDate endDate, String approvalContainerTitle, String description, String purpose, Integer level, BigDecimal approvalContainerClaimAmount, ReportStatus reportStatus, int actionLevel, boolean containsDeviation, String deviationRemarks, BigDecimal approvalContainerSgstAmount, BigDecimal approvalContainerCgstAmount, BigDecimal approvalContainerIgstAmount, BigDecimal approvalContainerTaxableAmount, String firstName, String middleName, String lastName, String employeeEmail, String employeeCode, String employeeGrade, Long employeeSystemId, ExpenseType expenseType, long documentMetadataId, String documentType, String documentGroup, String sendBackRemarks, String rejectRemarks, String delegationRemarks, String defaultApproverRemarks, String currentApproverFirstName, String currentApproverLastName, String currentApproverSystemIdCode, String currentApproverEmployeeCode, boolean containsSentBack, LocalDate glPostingDate, LocalDate paymentDate, Long documentSubgroupId, String utrNumber) {
        this.id = id;
        this.documentIdentifier = documentIdentifier;
        this.documentCategory = documentCategory;
        this.approvalContainerTitle = approvalContainerTitle;
        this.description = description;
        this.purpose = purpose;
        this.level = level;
        this.approvalContainerClaimAmount = approvalContainerClaimAmount;
        this.reportStatus = reportStatus;
        this.actionLevel = actionLevel;
        this.containsDeviation = containsDeviation;
        this.deviationRemarks = deviationRemarks;
        this.approvalContainerSgstAmount = approvalContainerSgstAmount;
        this.approvalContainerCgstAmount = approvalContainerCgstAmount;
        this.approvalContainerIgstAmount = approvalContainerIgstAmount;
        this.approvalContainerTaxableAmount = approvalContainerTaxableAmount;
        this.firstName = firstName;
        this.middleName = middleName;
        this.lastName = lastName;
        this.employeeEmail = employeeEmail;
        this.employeeCode = employeeCode;
        this.employeeGrade = employeeGrade;
        this.employeeSystemId = employeeSystemId;
        this.expenseType = expenseType.name();
        this.documentMetadataId = documentMetadataId;
        this.documentType = documentType;
        this.documentGroup = documentGroup;
        this.sendBackRemarks = sendBackRemarks;
        this.rejectRemarks = rejectRemarks;
        this.delegationRemarks = delegationRemarks;
        this.defaultApproverRemarks = defaultApproverRemarks;
        this.currentApproverFirstName = currentApproverFirstName;
        this.currentApproverLastName = currentApproverLastName;
        this.currentApproverSystemIdCode = currentApproverSystemIdCode;
        this.currentApproverEmployeeCode = currentApproverEmployeeCode;
        this.containsSentBack = containsSentBack;
        this.glPostingDate = glPostingDate;
        this.paymentDate = paymentDate;
        this.documentSubgroupId = documentSubgroupId;
        this.utrNumber = utrNumber;

        // Date conversion
        this.createdDate = DateTimeUtils.formatDateJPQL(createdDate);
        this.submitDate = DateTimeUtils.formatDateJPQL(submitDate);
        this.startDate = DateTimeUtils.formatDateJPQL(startDate);
        this.endDate = DateTimeUtils.formatDateJPQL(endDate);

    }
}
