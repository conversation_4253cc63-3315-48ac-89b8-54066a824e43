package in.taxgenie.pay_expense_pvv.viewmodels;

public class FileResultViewModel {
    private final String identifier;
    private final String uploadUrl;
    private final String fileType;
    private final long expenseId;

    public FileResultViewModel(String identifier, String uploadUrl, String fileType, long expenseId) {
        this.identifier = identifier;
        this.uploadUrl = uploadUrl;
        this.fileType = fileType;
        this.expenseId = expenseId;
    }

    public String getIdentifier() {
        return identifier;
    }

    public String getUploadUrl() {
        return uploadUrl;
    }

    public String getFileType() {
        return fileType;
    }

    public long getExpenseId() {
        return expenseId;
    }
}
