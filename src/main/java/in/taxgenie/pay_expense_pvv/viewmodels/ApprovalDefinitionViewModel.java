package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.StateChannel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalDefinitionViewModel {
    private long id;
    private int level;
    private BigDecimal limitAmount;
    private String approvalMatcher;
    private String approvalTitle;
    private boolean shouldFetchFromEmployeeMaster;
    private boolean isFrozen;
    private long documentMetadataId;
    private String documentMetadataMarker;
    private StateChannel channel;
    private boolean isExternalToCem;
    private boolean forDeviation;

}
