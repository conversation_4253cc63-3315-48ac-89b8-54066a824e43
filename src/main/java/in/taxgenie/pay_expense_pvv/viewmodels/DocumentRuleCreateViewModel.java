package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentRuleCreateViewModel {
    @Size(max = 50)
    private String branchCode;
    @Size(max = 50)
    private String departmentCode;
    @Size(max = 50)
    private String locationCategory;
    @Size(max = 50)
    private String employeeType;
    @Size(max = 50)
    private String employeeGrade;
    @Size(max = 50)
    private String costCenterCode;

    private boolean isInvoiceRequired;
    @Positive
    @Max(StaticDataRegistry.MAX_AMOUNT)
    private BigDecimal invoiceRequiredThreshold;

    @NotNull
    private LocalDate startDate;
    @NotNull
    private LocalDate endDate;

    //  regular specific
    @Positive
    @Max(100)
    private BigDecimal standardDeductionRate;

    //  Other
    @Positive
    @Max(StaticDataRegistry.MAX_AMOUNT)
    private BigDecimal limitAmount;
    @Positive
    @Max(StaticDataRegistry.MAX_AMOUNT)
    private BigDecimal maximumAmount;
    private boolean canExceedLimit;

    private boolean isPerDiemAllowed;
    @Positive
    @Max(StaticDataRegistry.MAX_AMOUNT)
    private BigDecimal perDiemAmount;

    //  Unit rate specific
    private boolean isUnitRateApplicable;
    @Size(max = 50)
    private String unitOfMeasure;
    @Positive
    @Max(StaticDataRegistry.MAX_AMOUNT)
    private BigDecimal unitRate;
    @Size(max = 50)
    private String unitRateType;

    private long documentSubgroupId;
}
