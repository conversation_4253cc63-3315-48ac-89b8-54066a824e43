package in.taxgenie.pay_expense_pvv.viewmodels.company;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
public class AdditionalDetailsViewModel {
    private long id;
    private Long companyCode;
    private String businessType;
    private String address;
    private boolean status;
    private boolean registrationStatus;
    private String narrationOfGstin;
}
