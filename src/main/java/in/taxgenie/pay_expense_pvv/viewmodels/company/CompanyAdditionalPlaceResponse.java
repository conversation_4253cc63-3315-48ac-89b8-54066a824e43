package in.taxgenie.pay_expense_pvv.viewmodels.company;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class CompanyAdditionalPlaceResponse {
    private String gstin;
    private String buildingName;
    private String streetName;
    private String location;
    private String doorNumber;
    private String city;
    private String district;
    private String state;
    private String floorNo;
    private String latitude;
    private String longitude;
    private String pincode;
    private String businessNature;
    private String addressType;
}

