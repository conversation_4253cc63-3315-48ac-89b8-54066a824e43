package in.taxgenie.pay_expense_pvv.viewmodels.company;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GstinDetailViewModel {
    private long id;
    private long companyCode;
    private String gstinNo;
    private String gstinStatus; //3
    private String registrationDate; //5
    private String filingFrequency;
    private String legalName; //1
    private String panNo;
    private String stateCode;
    private Integer stateCodeId;
    private String stringStateCode;
    private String legalTradeName; //2
    private String gstinType; //6

    private String buyerName;
    private String emailId;
    private String address;
    private String contactNumber;
    private String pinCode;

    private String userId;
    private boolean authorise;

    public GstinDetailViewModel() {
    }
}
