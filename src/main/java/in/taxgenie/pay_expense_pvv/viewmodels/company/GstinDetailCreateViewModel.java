package in.taxgenie.pay_expense_pvv.viewmodels.company;

import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class GstinDetailCreateViewModel {

    private Long id;

    @NotNull(message = "GSTIN number cannot be null")
    @NotEmpty(message = "GSTIN number cannot be empty")
    @Size(min = 15, max = 15, message = "GSTIN number must be exactly 15 characters")
    private String gstinNo;

    @NotNull(message = "Registration date cannot be null")
    @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2}", message = "Registration date must be in YYYY-MM-DD format")
    private String registrationDate;

    @NotNull(message = "Filing frequency cannot be null")
    @NotEmpty(message = "Filing frequency cannot be empty")
    private String filingFrequency;

    @NotNull(message = "Legal name cannot be null")
    @NotEmpty(message = "Legal name cannot be empty")
    private String legalName;

    @NotNull(message = "PAN number cannot be null")
    @NotEmpty(message = "PAN number cannot be empty")
    @Size(min = 10, max = 10, message = "PAN number must be exactly 10 characters")
    private String panNo;

    private String legalTradeName;

    @NotNull(message = "Type of Gstin cannot be null")
    @NotEmpty(message = "Type of Gstin cannot be empty")
    @Pattern(regexp = "^(REGULAR|ISD)$", message = "GSTIN Type must be either REGULAR or ISD")
    private String gstinType;

    @NotNull(message = "Buyer name cannot be null")
    @NotEmpty(message = "Buyer name cannot be empty")
    private String buyerName;

    @NotNull(message = "Email ID cannot be null")
    @NotEmpty(message = "Email ID cannot be empty")
    @Email(message = "Email ID should be valid")
    private String emailId;

    @NotNull(message = "Address cannot be null")
    @NotEmpty(message = "Address cannot be empty")
    private String address;

    @NotNull(message = "Contact number cannot be null")
    @NotEmpty(message = "Contact number cannot be empty")
    @Pattern(regexp = "^[0-9]{10}$", message = "Contact number must be a 10-digit number")
    private String contactNumber;

    @NotNull(message = "Pin code cannot be null")
    @NotEmpty(message = "Pin code cannot be empty")
    @Pattern(regexp = "^[0-9]{6}$", message = "Pin code must be a 6-digit number")
    private String pinCode;
}