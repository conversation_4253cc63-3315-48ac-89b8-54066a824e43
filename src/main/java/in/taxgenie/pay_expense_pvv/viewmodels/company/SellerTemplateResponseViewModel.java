package in.taxgenie.pay_expense_pvv.viewmodels.company;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SellerTemplateResponseViewModel {
    @JsonProperty("seller_identifier")
    private List<String> sellerIdentifiers = new ArrayList<>();

        public void addGstin(String gstin) {
            sellerIdentifiers.add(gstin);
        }
}
