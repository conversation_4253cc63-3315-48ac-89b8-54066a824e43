package in.taxgenie.pay_expense_pvv.viewmodels.company;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class CompanyGstinMasterGetDataResponse {
    private String pan;
    private String gstin;
    private String legalBusinessName;
    private String stateCode;
    private String centerCode;
    private String registrationDate;
    private String businessConstitution;
    private String taxType;
    private String businessNature;
    private String gstinStatus;
    private String gstinCancellationDate;
    private String lastUpdatedDate;
    private String stateJurisdiction;
    private String centerJurisdiction;
    private String tradeName;
    private String irnGenerationStatus;
    private String irnGenerationEligibility;
//    private List<CompanyAdditionalPlaceResponse> addresses;
}
