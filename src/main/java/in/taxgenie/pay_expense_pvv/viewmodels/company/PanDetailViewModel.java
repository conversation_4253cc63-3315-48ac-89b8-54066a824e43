package in.taxgenie.pay_expense_pvv.viewmodels.company;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PanDetailViewModel {
    private Long id;
    private Long companyCode;
    private String companyName;
    private String panNumber;
    private String cinNumber;
    private String tanNumber;
    private String msmeNumber;
    private String address;
    private String emailId;
    private String contactNo;
    private String websiteUrl;
    private String typeOfCompany;
    private String termsAndConditions;

    public PanDetailViewModel() {
    }

    public PanDetailViewModel(Long id, Long companyCode, String companyName, String panNumber, String cinNumber, String tanNumber, String msmeNumber, String address, String emailId, String contactNo, String websiteUrl, String typeOfCompany, String termsAndConditions) {
        this.id = id;
        this.companyCode = companyCode;
        this.companyName = companyName;
        this.panNumber = panNumber;
        this.cinNumber = cinNumber;
        this.tanNumber = tanNumber;
        this.msmeNumber = msmeNumber;
        this.address = address;
        this.emailId = emailId;
        this.contactNo = contactNo;
        this.websiteUrl = websiteUrl;
        this.typeOfCompany = typeOfCompany;
        this.termsAndConditions = termsAndConditions;
    }
}
