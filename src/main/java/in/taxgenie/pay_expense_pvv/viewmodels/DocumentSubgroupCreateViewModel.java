package in.taxgenie.pay_expense_pvv.viewmodels;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentSubgroupCreateViewModel {
    @NotBlank
    @Size(max=50)
    private String documentCode;
    @NotBlank
    @Size(max=50)
    private String documentSubgroup;
    @NotBlank
    @Size(max=5)
    private String documentSubgroupPrefix;
    @Size(max = 50)
    private Integer documentTypeId;
    @Size(max=50)
    private String documentGenre;
    @Size(max=5)
    private String documentGenrePrefix;

    private boolean isLocationRequired;
    @Size(max=15)
    private String glAccountCode;
    @Size(max=200)
    private String description;

    @Size(max = 100)
    private String frequency;

    private String applicableExpenseType;
    // Budget
    private Long budgetId;

    // JSONB field for flags
    private String subgroupFieldsJson;

    //  flags
    private boolean isSourceLocationApplicable;
    private boolean isDestinationLocationApplicable;
    private boolean isGstEntryAllowed;
    private boolean isStandardDeductionApplicable;
    private boolean isDateRangeApplicable;
    private boolean isFrequencyApplicable;
    private boolean isExpenseIdentifierApplicable;
    private boolean isTransportDescriptorApplicable;
    private boolean isMobilityDescriptorApplicable;
    private boolean isTravelDescriptorApplicable;

    private boolean hasPrecedingDocument;

    private boolean merchantRequired;

    private long documentMetadataId;
}
