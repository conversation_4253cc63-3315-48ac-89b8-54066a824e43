package in.taxgenie.pay_expense_pvv.viewmodels.invoice;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderItemDetailsViewModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class InvoicePreviewViewModel {

    private Long id; //Done
    private String poType; //Done
    private String supplierName; //Done
    private String financialYear; //Inprogress
    private String dateOfDocument;//Done
    private String description;
    private ReportStatus reportStatus;

    private Boolean isDomestic;
    private Boolean msmeStatus;//Done

    private BigDecimal tdsAmount = BigDecimal.ZERO;
    private BigDecimal taxAmount = BigDecimal.ZERO;
    private BigDecimal invoiceAmount = BigDecimal.ZERO;//Done
    private BigDecimal netPaybleAmount = BigDecimal.ZERO;//Done

    private String gstnReconcilliation;

    List<InvoiceItemDetailsViewModel> itemList;

}