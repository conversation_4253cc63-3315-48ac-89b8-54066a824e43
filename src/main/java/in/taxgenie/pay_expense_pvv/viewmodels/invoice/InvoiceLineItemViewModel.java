package in.taxgenie.pay_expense_pvv.viewmodels.invoice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
public class InvoiceLineItemViewModel {

    List<InvoiceItemDetailsViewModel> itemList;

    public InvoiceLineItemViewModel() {
        this.itemList = new ArrayList<>();
    }
}
