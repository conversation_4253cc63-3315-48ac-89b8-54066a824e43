package in.taxgenie.pay_expense_pvv.viewmodels.invoice;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoicePoMatchResponse {
    
    private Long invoiceId;
    private List<Long> matchedPoIds;
    private List<Long> failedPoIds;
    private List<String> matchingErrors;
    private String message;
    private boolean success;
    private String matchType; // "2-way" or "3-way"
}
