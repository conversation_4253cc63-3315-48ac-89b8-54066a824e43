package in.taxgenie.pay_expense_pvv.viewmodels.invoice;

import com.fasterxml.jackson.annotation.JsonProperty;
import in.taxgenie.pay_expense_pvv.viewmodels.erp.ERPGrnViewModel;
import lombok.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InvoiceItemDetailsViewModel {
    private Long id;
    private Long invoiceHeaderId;
    // Updated to support many-to-many relationship
    private List<Long> poItemIds = new ArrayList<>();
    private List<Long> poIds = new ArrayList<>();
    private String docNo;
    private Long itemMasterId;
    @JsonProperty("budgetId")
    private Long budgetNodeId;
    private String budgetCode;
    private String displayName;

    private String serialNo;
    private Integer slNo;
    @JsonProperty("itemType")
    private String type;
    @JsonProperty("itemName")
    private String name;
    private String itemCode;
    private String hsn;
    private Integer unitOfMeasureId;
    private String unitOfMeasure;
    @JsonProperty("itemDescription")
    private String description;

    private Boolean isService;

    private Double quantity;
    private Double selectedQuantity;
    private BigDecimal unitRate;

    private BigDecimal totalGstAmount;
    @JsonProperty("total")
    private BigDecimal totalWithTax; // = Assessable Amount + CGST Amt + SGST Amt + Cess Amt + CesNonAdvlAmt + StateCesAmt + StateCesNonAdvlAmt + Otherchrg
    private BigDecimal discount;
    private BigDecimal assessableAmount; // total - discount
    private BigDecimal cessAmt;
    private BigDecimal stateCessAmt;
    private BigDecimal cessNonAdvolAmount;
    private BigDecimal stateCessNonAdvolAmount;
    private BigDecimal otherCharges;


    @JsonProperty("gstRate")
    private Double taxPercentage;
    private Double cessPercentage;
    private Double stateCessPercentage;

    @JsonProperty("amountWithoutGst")
    private BigDecimal total;
    private BigDecimal igst;
    private BigDecimal cgst;
    private BigDecimal sgst;
    private BigDecimal utgst;
    private Integer quotationId;
    private Integer vendorId;
    private String vendorName;
    private String createdBy;
    private String createdAt;

    private Long glId;
    private String glName;

    // Updated to support many-to-many relationship with ERP GRNs
    private List<ERPGrnViewModel> erpGrns = new ArrayList<>();
}
