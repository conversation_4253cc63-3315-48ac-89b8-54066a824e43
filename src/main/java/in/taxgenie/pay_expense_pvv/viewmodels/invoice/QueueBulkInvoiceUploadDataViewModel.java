package in.taxgenie.pay_expense_pvv.viewmodels.invoice;

import lombok.Data;

@Data
public class QueueBulkInvoiceUploadDataViewModel {

    Long type;
    Long group;
    Long subGroup;

    String signedUrls;
    String name;
    String filePath;
    String id;
    Boolean isUpload;

    String token;
    String requestId;

    public QueueBulkInvoiceUploadDataViewModel() {}

    public QueueBulkInvoiceUploadDataViewModel(long type, long group, long subGroup, SignedUrlResponseData requestData,
                                               String token) {
        this.type = type;
        this.group = group;
        this.subGroup = subGroup;
        this.signedUrls = requestData.getSignedUrls();
        this.name = requestData.getName();
        this.filePath = requestData.getFilePath();
        this.id = requestData.getId();
        this.isUpload = requestData.getIsUpload();
        this.token = token;
    }
}
