package in.taxgenie.pay_expense_pvv.viewmodels.invoice;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoicePoMatchRequest {
    
    @NotNull(message = "Invoice ID is required for matching")
    private Long invoiceId;
    
    @NotEmpty(message = "At least one PO ID is required for matching")
    private List<Long> poIds;
}
