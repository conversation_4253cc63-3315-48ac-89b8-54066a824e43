package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.IntervalMarker;

import java.math.BigDecimal;

public class ReportLimitConsumptionViewModel {
    private IntervalMarker interval;
    private BigDecimal limitAmount;
    private BigDecimal consumedAmount;

    public IntervalMarker getInterval() {
        return interval;
    }

    public void setInterval(IntervalMarker interval) {
        this.interval = interval;
    }

    public BigDecimal getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(BigDecimal limitAmount) {
        this.limitAmount = limitAmount;
    }

    public BigDecimal getConsumedAmount() {
        return consumedAmount;
    }

    public void setConsumedAmount(BigDecimal consumedAmount) {
        this.consumedAmount = consumedAmount;
    }
}
