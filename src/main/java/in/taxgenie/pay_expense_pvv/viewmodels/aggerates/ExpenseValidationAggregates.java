package in.taxgenie.pay_expense_pvv.viewmodels.aggerates;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

public class ExpenseValidationAggregates {
    Long count;
    BigDecimal sum;
    LocalDate expenseDate;

    public ExpenseValidationAggregates() {
    }
    public ExpenseValidationAggregates(Long count, BigDecimal sum, LocalDate expenseDate) {
        this.count = count;
        this.sum = sum;
        this.expenseDate = expenseDate;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public BigDecimal getSum() {
        return sum;
    }

    public void setSum(BigDecimal sum) {
        this.sum = sum;
    }

    public LocalDate getExpenseDate() {
        return expenseDate;
    }

    public void setExpenseDate(LocalDate expenseDate) {
        this.expenseDate = expenseDate;
    }

    @Override
    public String toString() {
        return "IExpenseValidationAggregates{" +
                "count=" + count +
                ", sum=" + sum +
                ", expenseDate=" + expenseDate +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ExpenseValidationAggregates)) return false;
        ExpenseValidationAggregates that = (ExpenseValidationAggregates) o;
        return Objects.equals(count, that.count) && Objects.equals(sum, that.sum) && Objects.equals(expenseDate, that.expenseDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(count, sum, expenseDate);
    }
}
