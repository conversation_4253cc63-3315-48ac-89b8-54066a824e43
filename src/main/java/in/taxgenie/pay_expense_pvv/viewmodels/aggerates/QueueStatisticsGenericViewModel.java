package in.taxgenie.pay_expense_pvv.viewmodels.aggerates;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;

import java.math.BigDecimal;

public class QueueStatisticsGenericViewModel {

    private Long count;
    private BigDecimal amount;
    private ReportStatus status;
    public QueueStatisticsGenericViewModel() {
    }

    public QueueStatisticsGenericViewModel(Long count, BigDecimal amount, ReportStatus status) {
        this.count = count;
        this.amount = amount;
        this.status = status;
    }
    public QueueStatisticsGenericViewModel(Long count, BigDecimal amount) {
        this.count = count;
        this.amount = amount;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public ReportStatus getStatus() {
        return status;
    }

    public void setStatus(ReportStatus status) {
        this.status = status;
    }
}

