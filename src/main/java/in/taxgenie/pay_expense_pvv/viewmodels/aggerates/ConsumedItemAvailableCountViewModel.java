package in.taxgenie.pay_expense_pvv.viewmodels.aggerates;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import lombok.Data;

@Data
public class ConsumedItemAvailableCountViewModel {
    Long id; //item id
    Long prId;
    Long vendorId;
    Double quantity;
    Double initialQuantity;
    ReportStatus reportStatus;


    public ConsumedItemAvailableCountViewModel(){}
    public ConsumedItemAvailableCountViewModel(Long id, Double quantity) {
        // this constructor is getting called when there is no record for consumed, it means consumed is 0, setting initialQuantity = quantity
        this.id = id;
        this.quantity = quantity;

    }
    public ConsumedItemAvailableCountViewModel(Long id, Double quantity, Double initialQuantity) {
        this.id = id;
        this.quantity = quantity;
        this.initialQuantity = initialQuantity;
    }
    public ConsumedItemAvailableCountViewModel(Long id,  Long prId, Long vendorId, Double quantity, Double initialQuantity, ReportStatus reportStatus) {
        this.id = id;
        this.prId = prId;
        this.vendorId = vendorId;
        this.quantity = quantity;  // consumed for procurement
        this.initialQuantity = initialQuantity;
        this.reportStatus = reportStatus;
    }
    public ConsumedItemAvailableCountViewModel(Long id,  Long prId, Long vendorId, Double quantity, Double initialQuantity) {
        this.id = id;
        this.prId = prId;
        this.vendorId = vendorId;
        this.quantity = quantity;  // consumed for procurement
        this.initialQuantity = initialQuantity;
    }
    public ConsumedItemAvailableCountViewModel(ConsumedItemAvailableCountViewModel other) {
        this.id = other.id;
        this.prId = other.prId;
        this.vendorId = other.vendorId;
        this.quantity = other.quantity;
        this.initialQuantity = other.initialQuantity;
        this.reportStatus = other.reportStatus;
    }

}

