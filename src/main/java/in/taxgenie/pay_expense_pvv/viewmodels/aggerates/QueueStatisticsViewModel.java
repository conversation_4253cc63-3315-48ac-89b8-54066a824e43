package in.taxgenie.pay_expense_pvv.viewmodels.aggerates;

import java.math.BigDecimal;

public class QueueStatisticsViewModel {
    private Long draftCount;
    private Long submittedCount;
    private Long approvedCount;
    private Long rejectedCount;
    private Long paidCount;
    private Long holdCount;
    private Long parkedCount;
    private Long postedCount;
    private <PERSON> convertedPRCount;

    private BigDecimal draftAmount;
    private BigDecimal submittedAmount;
    private BigDecimal approvedAmount;
    private BigDecimal rejectedAmount;
    private BigDecimal paidAmount;
    private BigDecimal holdAmount;
    private BigDecimal parkedAmount;
    private BigDecimal postedAmount;
    private BigDecimal convertedPRAmount;

    public BigDecimal getConvertedPRAmount() {
        return convertedPRAmount;
    }

    public void setConvertedPRAmount(BigDecimal convertedPRAmount) {
        this.convertedPRAmount = convertedPRAmount;
    }

    public Long getConvertedPRCount() {
        return convertedPRCount;
    }

    public void setConvertedPRCount(Long convertedPRCount) {
        this.convertedPRCount = convertedPRCount;
    }

    public QueueStatisticsViewModel() {
    }

    public QueueStatisticsViewModel(Long draftCount, <PERSON> submittedCount, <PERSON> approvedCount, <PERSON> rejectedCount,  Long postedCount, Long paidCount, Long holdCount, Long parkedCount, BigDecimal draftAmount, BigDecimal submittedAmount, BigDecimal approvedAmount, BigDecimal rejectedAmount,  BigDecimal postedAmount, BigDecimal paidAmount, BigDecimal holdAmount, BigDecimal parkedAmount) {
        this.draftCount = draftCount;
        this.submittedCount = submittedCount;
        this.approvedCount = approvedCount;
        this.rejectedCount = rejectedCount;
        this.paidCount = paidCount;
        this.holdCount = holdCount;
        this.postedCount = postedCount;
        this.parkedCount = parkedCount;
        this.draftAmount = draftAmount;
        this.submittedAmount = submittedAmount;
        this.approvedAmount = approvedAmount;
        this.rejectedAmount = rejectedAmount;
        this.paidAmount = paidAmount;
        this.postedAmount = postedAmount;
        this.holdAmount = holdAmount;
        this.parkedAmount = parkedAmount;
    }

    public Long getDraftCount() {
        return draftCount;
    }

    public Long getSubmittedCount() {
        return submittedCount;
    }

    public Long getApprovedCount() {
        return approvedCount;
    }

    public Long getRejectedCount() {
        return rejectedCount;
    }
    public Long getPaidCount() {
        return paidCount;
    }
    public Long getHoldCount() {
        return holdCount;
    }
    public Long getPostedCount() {
        return postedCount;
    }
    public Long getParkedCount() {
        return parkedCount;
    }

    public BigDecimal getDraftAmount() {
        return draftAmount;
    }

    public BigDecimal getSubmittedAmount() {
        return submittedAmount;
    }

    public BigDecimal getApprovedAmount() {
        return approvedAmount;
    }

    public BigDecimal getRejectedAmount() {
        return rejectedAmount;
    }
    public BigDecimal getPaidAmountAmount() {
        return paidAmount;
    }
    public BigDecimal getPostedAmount() {
        return postedAmount;
    }
    public BigDecimal getParkedAmount() {
        return parkedAmount;
    }
    public BigDecimal getHoldAmount() {
        return holdAmount;
    }
}
