package in.taxgenie.pay_expense_pvv.viewmodels;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentMetadataViewModel {
    private long id;
    private long companyCode;
    private String description;
    private String documentType;
    private String documentCategory;
    private Integer documentCategoryId;
    private String documentTypePrefix;
    private String documentGroup;
    private String documentGroupPrefix;
    private int definitionsCount;
    private int subgroupsCount;
    private int rulesCount;
    private boolean isFrozen;
    private boolean isOnBehalf;
    private boolean isOnBehalfOfOther;
    private boolean purposeRequired;
    private String applicableExpenseType;
    private Integer limitDays;
}
