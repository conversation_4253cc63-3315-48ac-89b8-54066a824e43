package in.taxgenie.pay_expense_pvv.viewmodels.pdf;

import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseRequestItemPdfData {
    private String vendorName;
    private String itemDescription;
    private String budgetCode;
    private Double quantity;
    private BigDecimal unitRate;
    private BigDecimal total;
    private Double gstRate;
    private BigDecimal totalGstAmount;
    private BigDecimal lineTotal;
}
