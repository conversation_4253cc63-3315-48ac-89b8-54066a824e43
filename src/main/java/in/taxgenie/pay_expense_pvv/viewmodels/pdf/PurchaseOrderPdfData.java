package in.taxgenie.pay_expense_pvv.viewmodels.pdf;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PurchaseOrderPdfData extends ApproverViewPdfData{
    private String orderNo;
    private String documentDate;
    private String orderValidFrom;
    private String orderValidTo;
    private String poStartDate;
    private String PoEndDate;


    private String supplierCompanyName;
    private String supplierGstin;
    private String supplierAddress;

    private String buyerCompanyName;
    private String buyerGstin;
    private String buyerAddress;
    private String buyerContactPerson;
    private String buyerContactNo;

    private String shippingAddress;
    private String shipToContactPerson;
    private String shipToContactNo;

    private BigDecimal totalPOValue;
    private String figureInWords;
    private String billingAddress;

    private List<PurchaseOrderItemPdfData> itemList;
    private List<SegmentDetailsPdfData> segmentDetails;
    private Double totalSegmentRatio;
    private BigDecimal totalSegmentAmount;

}

