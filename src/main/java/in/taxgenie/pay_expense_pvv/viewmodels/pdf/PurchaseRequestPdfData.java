package in.taxgenie.pay_expense_pvv.viewmodels.pdf;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PurchaseRequestPdfData extends ApproverViewPdfData {
    private String documentDate;
    private String budgetCode;
    private String reasonForPurchase;

    private List<PurchaseRequestItemPdfData> itemDetails;

    private BigDecimal grandTotalWithoutTax;
    private BigDecimal grandTotal;
    private String figureInWords;

    private String expenditureType;
    private String paymentTerms;

    private String approvedBy;
    private String approvedAt;
}

