package in.taxgenie.pay_expense_pvv.viewmodels.dynamic.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class DynamicFormViewResponseViewModel {
    private int sectionId;
    private String sectionName;
    @JsonProperty("isSelected")
    private boolean isSelected;
    private String sectionSelector;
    private List<DynamicFieldViewModel> fields;
}
