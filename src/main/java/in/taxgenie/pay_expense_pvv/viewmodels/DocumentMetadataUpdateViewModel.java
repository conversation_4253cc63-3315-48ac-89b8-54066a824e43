package in.taxgenie.pay_expense_pvv.viewmodels;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentMetadataUpdateViewModel {
    @Positive
    private long id;
    @Size(max = 100)
    private String description;
    @NotBlank
    @Size(max = 100)
    private String documentType;
    @NotBlank
    @Size(max = 3)
    private String documentTypePrefix;
    @NotBlank
    @Size(max = 100)
    private String documentGroup;
    @NotBlank
    @Size(max = 3)
    private String documentGroupPrefix;
    private boolean isFrozen;
    private boolean isOnBehalf;
    private boolean isOnBehalfOfOther;
    private boolean purposeRequired;

    private String applicableExpenseType;
    private Integer limitDays;
}
