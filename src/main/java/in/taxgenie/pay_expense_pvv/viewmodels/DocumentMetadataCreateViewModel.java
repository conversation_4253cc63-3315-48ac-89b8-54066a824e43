package in.taxgenie.pay_expense_pvv.viewmodels;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentMetadataCreateViewModel {
    @Size(max = 100)
    private String description;
    @Size(max = 100)
    private String documentType;
    private Integer documentCategoryId;
    @Size(max = 3)
    private String documentTypePrefix;
    @Size(max = 100)
    private String documentGroup;
    @Size(max = 3)
    private String documentGroupPrefix;

    private String applicableExpenseType;
    @Range(min = 0, max = 365)
    private Integer limitDays;


    private boolean purposeRequired;
}


