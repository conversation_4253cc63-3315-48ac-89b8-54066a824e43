package in.taxgenie.pay_expense_pvv.viewmodels.match;

import in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchConfigurationViewModel {
    private Long id;
    private Boolean is2WayMatch;
    private LookupDataModel performMatchAt;
    private String criteriaName;
    private String criteriaValue;
    private String criteriaMapValue;
    private Boolean isToleranceAllowed;
    private LookupDataModel defaultOperation;
    private Boolean isPercentage;
}