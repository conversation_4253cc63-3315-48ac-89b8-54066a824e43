package in.taxgenie.pay_expense_pvv.viewmodels.match;

import in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchConfigurationCreateRequest {
    
    @NotNull(message = "Two-way match flag is required")
    private Boolean is2WayMatch;
    
    @NotNull(message = "Perform match at lookup is required")
    private LookupDataModel performMatchAt;
    
    @NotBlank(message = "Criteria name is required")
    private String criteriaName;
    
    private String criteriaValue;

    private String criteriaMapValue;

    @NotNull(message = "Tolerance allowed flag is required")
    private Boolean isToleranceAllowed;
    
    private LookupDataModel defaultOperation;
    
    @NotNull(message = "Percentage flag is required")
    private Boolean isPercentage;
}