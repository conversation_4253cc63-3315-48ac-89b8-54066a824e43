package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.entities.StateChannel;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class ReportStateViewModel {

    private long id;
    private int level;
    private String approver;
    private String actionDate;
    private String remarks;
    private ExpenseActionStatus status;
    private ZonedDateTime delegationTimestamp;
    private StateChannel channel;
    private boolean isDelegated;
    private boolean triggeredByDefault;
    private String defaultTriggerRemarks;
    private String delegationRemarks;
    private long expenseReportId;
    private boolean isDeviationAssignment;
    private String deviationRemarks;

    private String approverFirstName;
    private String approverLastName;
    private String approverEmployeeCode;

    private boolean isChangeDesk;
    private String changeDeskRemarks;
    private ZonedDateTime changeDeskTimestamp;
    private String createdTimestamp;

    public ReportStateViewModel(long id, int level, String approver, LocalDate actionDate, String remarks, ExpenseActionStatus status, ZonedDateTime delegationTimestamp, StateChannel channel, boolean isDelegated, boolean triggeredByDefault, String defaultTriggerRemarks, String delegationRemarks, long expenseReportId, boolean isDeviationAssignment, String deviationRemarks, String approverFirstName, String approverLastName, String approverEmployeeCode, boolean isChangeDesk, String changeDeskRemarks, ZonedDateTime changeDeskTimestamp, ZonedDateTime createdTimestamp) {
        this.id = id;
        this.level = level;
        this.approver = approver;
        this.remarks = remarks;
        this.status = status;
        this.delegationTimestamp = delegationTimestamp;
        this.channel = channel;
        this.isDelegated = isDelegated;
        this.triggeredByDefault = triggeredByDefault;
        this.defaultTriggerRemarks = defaultTriggerRemarks;
        this.delegationRemarks = delegationRemarks;
        this.expenseReportId = expenseReportId;
        this.isDeviationAssignment = isDeviationAssignment;
        this.deviationRemarks = deviationRemarks;
        this.approverFirstName = approverFirstName;
        this.approverLastName = approverLastName;
        this.approverEmployeeCode = approverEmployeeCode;
        this.isChangeDesk = isChangeDesk;
        this.changeDeskRemarks = changeDeskRemarks;
        this.changeDeskTimestamp = changeDeskTimestamp;

        // Date formatting
        this.createdTimestamp = DateTimeUtils.formatTimestampToDate(createdTimestamp);
        this.actionDate = DateTimeUtils.formatDate(actionDate);
    }
}
