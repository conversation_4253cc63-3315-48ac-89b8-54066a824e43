package in.taxgenie.pay_expense_pvv.viewmodels.rbac;

import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetMasterTreeDataViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetMasterTreeViewModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@ToString
@Getter
@Setter
public class HierarchyMasterTreeViewModel {
    private Boolean expanded;
    private HierarchyMasterTreeDataViewModel data;
    private List<HierarchyMasterTreeViewModel> children;

    public HierarchyMasterTreeViewModel() {
        this.children = new ArrayList<>();
    }

    public HierarchyMasterTreeViewModel(Boolean expanded, HierarchyMasterTreeDataViewModel data, List<HierarchyMasterTreeViewModel> children) {
        this.expanded = expanded;
        this.data = data;
        this.children = children;
    }
    public void addChildren(HierarchyMasterTreeViewModel child) {
        this.children.add(child);
    }
}
