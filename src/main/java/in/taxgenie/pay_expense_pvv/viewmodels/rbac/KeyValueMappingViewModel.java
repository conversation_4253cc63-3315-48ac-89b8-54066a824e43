package in.taxgenie.pay_expense_pvv.viewmodels.rbac;

import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import lombok.*;

import java.time.ZonedDateTime;

@ToString
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class KeyValueMappingViewModel {
    private Long id;
    private String key;
    private String value;
    private String label;
    private String entryDate;
    private String lastActionAt;
    private String source;
    private boolean isLastInstance = Boolean.FALSE;


    public KeyValueMappingViewModel(Long id, String key, String value, String label, ZonedDateTime entryDate, ZonedDateTime lastActionAt, String source) {
        this.id = id;
        this.key = key;
        this.value = value;
        this.label = label;
        this.entryDate = DateTimeUtils.formatTimestampToDate(entryDate);
        this.lastActionAt = DateTimeUtils.formatTimestampToDate(lastActionAt);;
        this.source = source;
    }
}
