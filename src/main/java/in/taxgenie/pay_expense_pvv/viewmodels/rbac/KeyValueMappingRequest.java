package in.taxgenie.pay_expense_pvv.viewmodels.rbac;

import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
public class KeyValueMappingRequest {
    private Long id;
    private String key;
    private String value;
    private String label;
    private Integer updatedBy;
}

