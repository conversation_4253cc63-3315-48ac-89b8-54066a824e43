package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.StateChannel;

public class ApproverViewModel {
    private long id;
    private String approvalMatcher;
    private String approvalMatchValue;
    private String approvalTitle;
    private String approver;
    private boolean isFrozen;
    private StateChannel channel;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getApprovalMatcher() {
        return approvalMatcher;
    }

    public void setApprovalMatcher(String approvalMatcher) {
        this.approvalMatcher = approvalMatcher;
    }

    public String getApprovalMatchValue() {
        return approvalMatchValue;
    }

    public void setApprovalMatchValue(String approvalMatchValue) {
        this.approvalMatchValue = approvalMatchValue;
    }

    public String getApprovalTitle() {
        return approvalTitle;
    }

    public void setApprovalTitle(String approvalTitle) {
        this.approvalTitle = approvalTitle;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }

    public StateChannel getChannel() {
        return channel;
    }

    public void setChannel(StateChannel channel) {
        this.channel = channel;
    }
}
