package in.taxgenie.pay_expense_pvv.viewmodels.erp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class MessageMasterViewModel extends BaseReportViewModel{
    @JsonProperty("Vendor_No")
    private String vendorCode;

    @JsonProperty("Vendor_Invoice")
    private String vendorInvoice;

    @JsonProperty("Notification_Code")
    private String notificationCode;

    @JsonProperty("Notification_message")
    private String notificationMessage;

    @JsonProperty("doc_id")
    private String documentIdentifier;

    @JsonProperty("entry_date")
    private String entryDate;

    @JsonProperty("entry_time")
    private String entryTime;

}
