package in.taxgenie.pay_expense_pvv.viewmodels.erp;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ERPPoItemViewModel {
    private String lineItemNumber;
    private String plantCode;
    private String hsnCode;
    private String materialGroup;
    private String poRate;
    private String orderedQuantity;
    private String unit;
    private String netValue;
    private String deliveryDate;
    private String incoTerms;
    private String incoTerms2;
    private String deletionFlag;
    private String deliveryCompletionFlag;
    private String glCode;
    private String costCentreCode;
    private String iorerOrder;
    private String taxRate;
    private String taxVal;
    private String materialCode;
    private String materialDesc;
}
