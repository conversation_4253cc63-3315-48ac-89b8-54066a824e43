package in.taxgenie.pay_expense_pvv.viewmodels.erp;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ToString
public class PaymentsDetailsViewModel extends BaseReportViewModel{
    private long companyCode;
    private String lobCode;
    private String sapFiDocumentNo;
    private Long sapFiDocumentYear;
    private String utrNumber;
    private String utrdate;
    private String docId; // document-identifier
    private BigDecimal paymentAmount;
    private BigDecimal tds;
    private BigDecimal tcs;
    private BigDecimal advancePayment;
    private String advancePaymentDate;
    private String advancePaymentUtrNo;
    private String advancePaymentUtrDate;
    private String invoiceNumber;
    private String vendorCode;
    private String vendorName;
    private String currencyCode;
    private String amountInLC;
    private Boolean isActive;
}
