package in.taxgenie.pay_expense_pvv.viewmodels.erp;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class ERPPurchaseOrderHeaderViewModel extends BaseReportViewModel{
    private String orderNumber;
    private String documentIdentifier;
    private String financialYear;
    private String expenditureType;
    private String orderCreationDate;
    private String orderValidFrom;
    private String orderValidTo;
    private String vendorCode;
    private String poStatus;
    private String poCreatedBy;
    private String purchasingDocType;
    private String creditPeriod;
    private String currency;
    private String incoTerms;
    private String purchasingOrganization;
    private String purchasingGroup;
    private String erpCompanyCode;
    private String poUserEmail;
    private String poReleaseIndicator;
    private List<ERPPoItemViewModel> poItemlist;

}
