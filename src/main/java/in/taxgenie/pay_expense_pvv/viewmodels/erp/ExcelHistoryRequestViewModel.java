package in.taxgenie.pay_expense_pvv.viewmodels.erp;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
public class ExcelHistoryRequestViewModel {
    private Long companyCode;
    private Long userId;
    private String sourceJson;
    private String type;
    private String template;
    private String report;
    private String referenceID;

    private String gstin;
    private String returnPeriod;
    private String fileName;
    private String status;
    private Date uploadStart;
    private Date uploadEnd;
}
