package in.taxgenie.pay_expense_pvv.viewmodels.users;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Data
public class UsersViewModel {
    private Long id;
    private Long userId;
    private String emailId;
    private String firstName;
    private String lastName;

    private Long companyCode;
    private String identifierKey01;
    private String identifierValue01;
    private String identifierKey02;
    private String identifierValue02;
    private String identifierKey03;
    private String identifierValue03;
    private String identifierKey04;
    private String identifierValue04;
    private String identifierKey05;
    private String identifierValue05;
    private String identifierKey06;
    private String identifierValue06;
    private String identifierKey07;
    private String identifierValue07;
    private String identifierKey08;
    private String identifierValue08;
    private String identifierKey09;
    private String identifierValue09;
    private String identifierKey10;
    private String identifierValue10;


    public UsersViewModel(Long userId, String emailId) {
        this.userId = userId;
        this.emailId = emailId;
    }
    public UsersViewModel(){}

    // Method to get key-value pairs as a map
    public Map<String, String> getKeyValueMap() {
        Map<String, String> keyValueMap = new HashMap<>();
        if (identifierKey01 != null && identifierValue01 != null) keyValueMap.put(identifierKey01, identifierValue01);
        if (identifierKey02 != null && identifierValue02 != null) keyValueMap.put(identifierKey02, identifierValue02);
        if (identifierKey03 != null && identifierValue03 != null) keyValueMap.put(identifierKey03, identifierValue03);
        if (identifierKey04 != null && identifierValue04 != null) keyValueMap.put(identifierKey04, identifierValue04);
        if (identifierKey05 != null && identifierValue05 != null) keyValueMap.put(identifierKey05, identifierValue05);
        if (identifierKey06 != null && identifierValue06 != null) keyValueMap.put(identifierKey06, identifierValue06);
        if (identifierKey07 != null && identifierValue07 != null) keyValueMap.put(identifierKey07, identifierValue07);
        if (identifierKey08 != null && identifierValue08 != null) keyValueMap.put(identifierKey08, identifierValue08);
        if (identifierKey09 != null && identifierValue09 != null) keyValueMap.put(identifierKey09, identifierValue09);
        if (identifierKey10 != null && identifierValue10 != null) keyValueMap.put(identifierKey10, identifierValue10);

        // Add more key-value pairs if needed (e.g., key3, value3, etc.)
        return keyValueMap;
    }
}
