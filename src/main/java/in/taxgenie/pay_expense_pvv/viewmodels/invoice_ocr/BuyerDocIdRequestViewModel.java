package in.taxgenie.pay_expense_pvv.viewmodels.invoice_ocr;

import com.fasterxml.jackson.annotation.JsonProperty;

public class BuyerDocIdRequestViewModel {

    @JsonProperty("buyerDocId")
    String buyerDocId;

    @JsonProperty("invoiceReceivedId")
    private Long invoiceReceivedId;

    @JsonProperty("hasSubscription")
    private boolean hasSubscription;

    public String getBuyerDocId() {
        return buyerDocId;
    }

    public void setBuyerDocId(String buyerDocId) {
        this.buyerDocId = buyerDocId;
    }

    public Long getInvoiceReceivedId() {
        return invoiceReceivedId;
    }

    public void setInvoiceReceivedId(Long invoiceReceivedId) {
        this.invoiceReceivedId = invoiceReceivedId;
    }

    public boolean isHasSubscription() {
        return hasSubscription;
    }

    public void setHasSubscription(boolean hasSubscription) {
        this.hasSubscription = hasSubscription;
    }

    @Override
    public String toString() {
        return "BuyerDocIdRequestViewModel{" +
                "buyerDocId='" + buyerDocId + '\'' +
                ", invoiceReceivedId=" + invoiceReceivedId +
                ", hasSubscription=" + hasSubscription +
                '}';
    }
}
