package in.taxgenie.pay_expense_pvv.viewmodels.invoice_ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
public class UploadInternalViewModel {

    @JsonProperty("companyId")
    String companyId;

    @JsonProperty("accessToken")
    String accessToken;

    @JsonProperty("invoiceReceivedId")
    Long invoiceReceivedId;

    @JsonProperty("url")
    String url;

    @JsonProperty("document_identifier")
    String documentIdentifier;

}
