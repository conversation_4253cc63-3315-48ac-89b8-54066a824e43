package in.taxgenie.pay_expense_pvv.viewmodels.document_matcher;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchedLineItem {
    private Integer id;
    private String itemCode;
    private String description;
    private String quantity;
    private String unit;
    private String unitPrice;
    private String hsn;
    private List<MatchData> matchData;
}
