package in.taxgenie.pay_expense_pvv.viewmodels.document_matcher;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchedLineItem {

    @JsonProperty("ItemNo")
    private Integer ItemNo;

    @JsonProperty("SlNo")
    private Integer SlNo;

    private List<MatchData> matchData;
}
