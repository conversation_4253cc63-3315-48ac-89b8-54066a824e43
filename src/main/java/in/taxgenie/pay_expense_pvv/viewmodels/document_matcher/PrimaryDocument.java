package in.taxgenie.pay_expense_pvv.viewmodels.document_matcher;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrimaryDocument {
    private String documentId;
    @JsonProperty("ItemList")
    private List<MatchedLineItem> ItemList;
}
