package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.viewmodels.pr.PurchaseRequestItemDetailsView;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class BudgetAssociatedDocumentDetailViewModel {
    private String prNo;
    private String status;
    private String createdDate;
    private Long id;
    private String firstName;
    private String middleName;
    private String lastName;
    private BigDecimal availableAmount;
    private List<PurchaseRequestItemDetailsView> itemDetails;
}
