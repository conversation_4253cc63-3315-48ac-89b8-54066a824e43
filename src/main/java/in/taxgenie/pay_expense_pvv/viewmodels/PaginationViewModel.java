package in.taxgenie.pay_expense_pvv.viewmodels;

import jakarta.validation.constraints.Positive;

public class PaginationViewModel {

	private Integer totalElements = 0;

	@Positive
	private Integer pages = 0;

	public Integer getPages() {
		return pages;
	}

	public void setPages(Integer pages) {
		this.pages = pages;
	}

	public Integer getTotalElements() {
		return totalElements;
	}

	public void setTotalElements(Integer totalElements) {
		this.totalElements = totalElements;
	}

}
