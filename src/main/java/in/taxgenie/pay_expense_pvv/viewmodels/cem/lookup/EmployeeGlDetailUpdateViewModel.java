package in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup;

public class EmployeeGlDetailUpdateViewModel {
    private String employeeSystemIdCode;
    private String glVendorCode;
    private String costCenterCode;
    private String profitCenterCode;
    private String segmentCode;
    private boolean isModified;

    public String getEmployeeSystemIdCode() {
        return employeeSystemIdCode;
    }

    public void setEmployeeSystemIdCode(String employeeSystemIdCode) {
        this.employeeSystemIdCode = employeeSystemIdCode;
    }

    public String getGlVendorCode() {
        return glVendorCode;
    }

    public void setGlVendorCode(String glVendorCode) {
        this.glVendorCode = glVendorCode;
    }

    public String getCostCenterCode() {
        return costCenterCode;
    }

    public void setCostCenterCode(String costCenterCode) {
        this.costCenterCode = costCenterCode;
    }

    public String getProfitCenterCode() {
        return profitCenterCode;
    }

    public void setProfitCenterCode(String profitCenterCode) {
        this.profitCenterCode = profitCenterCode;
    }

    public String getSegmentCode() {
        return segmentCode;
    }

    public void setSegmentCode(String segmentCode) {
        this.segmentCode = segmentCode;
    }

    public boolean isModified() {
        return isModified;
    }

    public void setModified(boolean modified) {
        isModified = modified;
    }
}
