package in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup;

public class BranchViewModel {
    private String branchCode;
    private String branchName;
    private String cityCode;
    private String address;
    private String branchHead;
    private boolean isFrozen;

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBranchHead() {
        return branchHead;
    }

    public void setBranchHead(String branchHead) {
        this.branchHead = branchHead;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }
}
