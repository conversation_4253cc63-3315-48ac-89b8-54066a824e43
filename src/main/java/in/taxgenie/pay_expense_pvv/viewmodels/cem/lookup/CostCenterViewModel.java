package in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup;

public class CostCenterViewModel {
    private String costCenterCode;
    private String description;
    private String branchCode;
    private String cityCode;
    private String departmentCode;
    private String dimensionLevel01;
    private String dimensionLevel02;
    private String dimensionLevel03;
    private String dimensionLevel04;
    private String dimensionLevel05;
    private String dimensionLevel06;
    private String dimensionLevel07;
    private String dimensionLevel08;
    private String dimensionLevel09;
    private String dimensionLevel10;
    private boolean isFrozen;

    public String getCostCenterCode() {
        return costCenterCode;
    }

    public void setCostCenterCode(String costCenterCode) {
        this.costCenterCode = costCenterCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDimensionLevel01() {
        return dimensionLevel01;
    }

    public void setDimensionLevel01(String dimensionLevel01) {
        this.dimensionLevel01 = dimensionLevel01;
    }

    public String getDimensionLevel02() {
        return dimensionLevel02;
    }

    public void setDimensionLevel02(String dimensionLevel02) {
        this.dimensionLevel02 = dimensionLevel02;
    }

    public String getDimensionLevel03() {
        return dimensionLevel03;
    }

    public void setDimensionLevel03(String dimensionLevel03) {
        this.dimensionLevel03 = dimensionLevel03;
    }

    public String getDimensionLevel04() {
        return dimensionLevel04;
    }

    public void setDimensionLevel04(String dimensionLevel04) {
        this.dimensionLevel04 = dimensionLevel04;
    }

    public String getDimensionLevel05() {
        return dimensionLevel05;
    }

    public void setDimensionLevel05(String dimensionLevel05) {
        this.dimensionLevel05 = dimensionLevel05;
    }

    public String getDimensionLevel06() {
        return dimensionLevel06;
    }

    public void setDimensionLevel06(String dimensionLevel06) {
        this.dimensionLevel06 = dimensionLevel06;
    }

    public String getDimensionLevel07() {
        return dimensionLevel07;
    }

    public void setDimensionLevel07(String dimensionLevel07) {
        this.dimensionLevel07 = dimensionLevel07;
    }

    public String getDimensionLevel08() {
        return dimensionLevel08;
    }

    public void setDimensionLevel08(String dimensionLevel08) {
        this.dimensionLevel08 = dimensionLevel08;
    }

    public String getDimensionLevel09() {
        return dimensionLevel09;
    }

    public void setDimensionLevel09(String dimensionLevel09) {
        this.dimensionLevel09 = dimensionLevel09;
    }

    public String getDimensionLevel10() {
        return dimensionLevel10;
    }

    public void setDimensionLevel10(String dimensionLevel10) {
        this.dimensionLevel10 = dimensionLevel10;
    }

    public boolean isFrozen() {
        return isFrozen;
    }

    public void setFrozen(boolean frozen) {
        isFrozen = frozen;
    }
}
