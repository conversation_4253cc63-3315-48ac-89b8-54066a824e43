package in.taxgenie.pay_expense_pvv.viewmodels.comeng;

public class ApprovalPendingLineViewModel {
    private String lineMarker;
    private String employeeName;
    private int level;
    private String startDate;
    private String endDate;
    private String reportClaimAmount;
    private String defaultRemarks;
    private String delegationRemarks;
    private String deviationRemarks;

    private String approverName;

    public String getLineMarker() {
        return lineMarker;
    }

    public void setLineMarker(String lineMarker) {
        this.lineMarker = lineMarker;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getReportClaimAmount() {
        return reportClaimAmount;
    }

    public void setReportClaimAmount(String reportClaimAmount) {
        this.reportClaimAmount = reportClaimAmount;
    }

    public String getDefaultRemarks() {
        return defaultRemarks;
    }

    public void setDefaultRemarks(String defaultRemarks) {
        this.defaultRemarks = defaultRemarks;
    }

    public String getDelegationRemarks() {
        return delegationRemarks;
    }

    public void setDelegationRemarks(String delegationRemarks) {
        this.delegationRemarks = delegationRemarks;
    }

    public String getDeviationRemarks() {
        return deviationRemarks;
    }

    public void setDeviationRemarks(String deviationRemarks) {
        this.deviationRemarks = deviationRemarks;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }
}
