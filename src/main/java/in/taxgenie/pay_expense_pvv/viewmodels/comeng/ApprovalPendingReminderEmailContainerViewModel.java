package in.taxgenie.pay_expense_pvv.viewmodels.comeng;

import java.util.List;

public class ApprovalPendingReminderEmailContainerViewModel {
    private final String approverName;
    private final String approverEmail;
    private final List<ApprovalPendingLineViewModel> pendingReports;


    public ApprovalPendingReminderEmailContainerViewModel(String approverName, String approverEmail, List<ApprovalPendingLineViewModel> pendingReports) {
        this.approverName = approverName;
        this.approverEmail = approverEmail;
        this.pendingReports = pendingReports;
    }

    public String getApproverName() {
        return approverName;
    }

    public String getApproverEmail() {
        return approverEmail;
    }

    public List<ApprovalPendingLineViewModel> getPendingReports() {
        return pendingReports;
    }
}
