package in.taxgenie.pay_expense_pvv.viewmodels.comeng;

import java.util.ArrayList;
import java.util.List;

public class ReportEmailViewModel {
    private String employeeName;
    private String companyName;
    private String reportMarker;
    private String documentIdentifier;
    private String startDate;
    private String endDate;
    private String description;
    private String delegationRemarks;
    private String sentBackRemarks;
    private String totalClaim;
    private String lastApprover;

    private String currentApprover;

    private long totalPending;

    private List<ApprovalPendingLineViewModel> pendingLines = new ArrayList<>();

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getReportMarker() {
        return reportMarker;
    }

    public void setReportMarker(String reportMarker) {
        this.reportMarker = reportMarker;
    }

    public String getDocumentIdentifier() {
        return documentIdentifier;
    }

    public void setDocumentIdentifier(String documentIdentifier) {
        this.documentIdentifier = documentIdentifier;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDelegationRemarks() {
        return delegationRemarks;
    }

    public void setDelegationRemarks(String delegationRemarks) {
        this.delegationRemarks = delegationRemarks;
    }

    public String getTotalClaim() {
        return totalClaim;
    }

    public void setTotalClaim(String totalClaim) {
        this.totalClaim = totalClaim;
    }

    public String getLastApprover() {
        return lastApprover;
    }

    public void setLastApprover(String lastApprover) {
        this.lastApprover = lastApprover;
    }

    public String getCurrentApprover() {
        return currentApprover;
    }

    public void setCurrentApprover(String currentApprover) {
        this.currentApprover = currentApprover;
    }

    public String getSentBackRemarks() {
        return sentBackRemarks;
    }

    public void setSentBackRemarks(String sentBackRemarks) {
        this.sentBackRemarks = sentBackRemarks;
    }

    public long getTotalPending() {
        return totalPending;
    }

    public void setTotalPending(long totalPending) {
        this.totalPending = totalPending;
    }

    public List<ApprovalPendingLineViewModel> getPendingLines() {
        return pendingLines;
    }

    public void setPendingLines(List<ApprovalPendingLineViewModel> pendingLines) {
        this.pendingLines = pendingLines;
    }
}
