package in.taxgenie.pay_expense_pvv.viewmodels.gl_master;

import in.taxgenie.pay_expense_pvv.entities.gl_master.GLStatus;
import in.taxgenie.pay_expense_pvv.entities.gl_master.PLGroup;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class GLMasterViewModel {
    private Long id;
    private long companyCode;
    private PLGroup plGroup;
    private String parent;
    private String glName;
    private String unary;
    private String gl;
    private String hsnCode;
    private String validFrom;
    private String validTo;
    private GLStatus status;

    public GLMasterViewModel(Long id, long companyCode, PLGroup plGroup, String parent, String glName, String unary, String gl, String hsnCode, LocalDate validFrom, LocalDate validTo, GLStatus status) {
        this.id = id;
        this.companyCode = companyCode;
        this.plGroup = plGroup;
        this.parent = parent;
        this.glName = glName;
        this.unary = unary;
        this.gl = gl;
        this.hsnCode = hsnCode;
        this.validFrom = DateTimeUtils.formatDateJPQL(validFrom);
        this.validTo = DateTimeUtils.formatDateJPQL(validTo);
        this.status = status;
    }
}
