package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.StateChannel;

import java.util.ArrayList;
import java.util.List;

public class ApprovalMatcherTitleViewModel {
    private String matcher;
    private StateChannel channel;
    private List<String> titles = new ArrayList<>();

    public ApprovalMatcherTitleViewModel(String matcher, StateChannel channel) {
        this.matcher = matcher;
        this.channel = channel;
    }

    public String getMatcher() {
        return matcher;
    }

    public List<String> getTitles() {
        return titles;
    }

    public void setMatcher(String matcher) {
        this.matcher = matcher;
    }

    public void setTitles(List<String> titles) {
        this.titles = titles;
    }

    public StateChannel getChannel() {
        return channel;
    }

    public void setChannel(StateChannel channel) {
        this.channel = channel;
    }
}
