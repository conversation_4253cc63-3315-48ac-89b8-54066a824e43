package in.taxgenie.pay_expense_pvv.viewmodels;

import lombok.*;

import java.util.Arrays;
import java.util.stream.Stream;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OtherDepartmentKeyValuePairsViewModel {
    // Key-value pairs
    
    private String key01;
    
    private String key02;
    
    private String key03;
    
    private String key04;
    
    private String key05;
    
    private String key06;
    
    private String key07;
    
    private String key08;
    
    private String key09;
    
    private String key10;

    
    private String value01;
    
    private String value02;
    
    private String value03;
    
    private String value04;
    
    private String value05;
    
    private String value06;
    
    private String value07;
    
    private String value08;
    
    private String value09;
    
    private String value10;

    public boolean isEmpty() {
        // Combine keys and values into a single stream
        Stream<String> allFields = Stream.concat(
                Arrays.stream(new String[]{key01, key02, key03, key04, key05, key06, key07, key08, key09, key10}),
                Arrays.stream(new String[]{value01, value02, value03, value04, value05, value06, value07, value08, value09, value10})
        );

        // Check if any field is not null or empty using anyMatch with short-circuiting
        return allFields.noneMatch(field -> field != null && !field.isEmpty());
    }
}
