package in.taxgenie.pay_expense_pvv.viewmodels.validation;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class ValidationErrorsViewModel {
    private boolean isValidationError = true;
    private Map<String, List<String>> validationErrorsWithKeys = new HashMap<>();
    private List<String> validationErrors = new ArrayList<>();

    /**
     * Adds an error message to a specific field.
     *
     * @param key     The field name.
     * @param message The error message.
     */
    public void addError(String key, String message) {
        validationErrorsWithKeys
                .computeIfAbsent(key, k -> new ArrayList<>())
                .add(message);
        validationErrors.add(message);
    }

    /**
     * Adds a general error message not associated with a specific field.
     *
     * @param message The error message.
     */
    public void addError(String message) {
        validationErrors.add(message);
    }
}