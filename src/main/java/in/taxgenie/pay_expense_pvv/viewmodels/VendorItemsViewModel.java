package in.taxgenie.pay_expense_pvv.viewmodels;

import com.fasterxml.jackson.annotation.JsonProperty;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VendorItemsViewModel {
    private Long id;
    private Long vendorId;
    private String vendorName;
    private Integer productId;
    private Integer companyId;
    private Integer budgetId;
    private String description;
    private String itemCode;
    private ItemType type;// Lookup?
    private Double quantity;
    private BigDecimal rate;
    @JsonProperty("hsnOrSac")
    private String hsn;
    private boolean status;
    private Double initialStock;
    private String source;
    private String currency;
    private Double gstRate;
    private String unitOfMeasure;
    private Timestamp createdAt;
    private Integer createdBy;
    private Timestamp updatedAt;
    private Integer updatedBy;
}
