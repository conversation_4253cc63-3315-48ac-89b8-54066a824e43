package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.StateChannel;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalDefinitionCreateViewModel {
    @Positive
    private int level;

    @Positive
    private BigDecimal limitAmount;

    @Size(max = 50)
    private String approvalMatcher;

    @Size(max = 50)
    private String approvalTitle;

    private boolean shouldFetchFromEmployeeMaster;

    private long documentMetadataId;

    @NotNull
    private StateChannel channel;

    private boolean isExternalToCem;

    private boolean forDeviation;
}
