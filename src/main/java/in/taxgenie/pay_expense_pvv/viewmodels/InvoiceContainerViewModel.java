package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.ProcessMethod;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Data
public class InvoiceContainerViewModel extends BaseContainerViewModel {
    // DocumentApprovalContainer
    private Long id;
    private BigDecimal approvalContainerClaimAmount;
    private String documentDate;
    // Document
    private String originatorDocumentIdentifier;
    private Long documentSubgroupId;
    private String documentSubgroup;
    private String documentGroup;
    private String source;
    private String docType;
    private ExpenseType expenseType;

    // Invoice Header
    private String invoiceNumber;

    // Document -> Invoice Header -> Company (Vendor details)
    private String vendorCode;
    private String vendorGstin;
    private String gstinStatus;
    private Boolean msmeStatus;
//    private boolean isEinvoiceApplicable;
    private String gst;
    private String legalName;
    private String addr1;
    private String addr2;
    private String loc;
    private String pin;
//    private StateCodes stateCode;
    private String phoneNo;
    private String emailId;
    private Integer supportingDocumentsCount;

    private String erpRemarks;
    // ocr
    private String requestId;
    private Boolean isOcrDone;
    private String ocrStatus;
    private String signedUrl;
    private Long invoiceReceivedId;

    private String statusRemarks;
    private String processMethod;
    private String utrNumber;
    private String paymentDate;

    // Here we are using a custom constructor as the formatted datetime value will not be taken directly from the database
    public InvoiceContainerViewModel(Long id, BigDecimal approvalContainerClaimAmount, String documentIdentifier, ZonedDateTime createdDate, String createdBy, ReportStatus reportStatus, LocalDate submitDate, LocalDate startDate, LocalDate endDate, String currentApproverFirstName, String currentApproverLastName, String currentApproverSystemIdCode, String currentApproverEmployeeCode, String firstName, String middleName, String lastName, String employeeCode, String employeeGrade, ZonedDateTime updatedTimestamp,
                                     // Document
                                     String originatorDocumentIdentifier, Long documentSubgroupId, String source,
                                     // Invoice Header
                                     String invoiceNumber,
                                     // Company
                                     String vendorCode, String gstinStatus, Boolean msmeStatus, String gst, String legalName, String addr1, String addr2, String loc, String pin, String phoneNo, String emailId,
                                     String documentSubgroup,Integer supportingDocumentsCount, ExpenseType expenseType, String docType, String documentGroup, String vendorGstin, Long invoiceReceivedId, String erpRemarks, String utrNumber, String statusRemarks, ProcessMethod processMethod, LocalDate paymentDate) {

        super(id, documentIdentifier, invoiceNumber, createdDate, null, reportStatus, submitDate, updatedTimestamp, currentApproverFirstName,
                currentApproverLastName, currentApproverSystemIdCode, currentApproverEmployeeCode, firstName, middleName, lastName, employeeCode, employeeGrade, createdBy);

        this.id = id;
        this.approvalContainerClaimAmount = approvalContainerClaimAmount;

        // Document Date : Standard invoices will have equal startDate and end dates because they arrived from the singular document date in the UI
//        if (startDate != null && startDate.equals(endDate)) {
//            this.documentDate = DateTimeUtils.formatDateJPQL(startDate);
//        }
        // startDate = documentDate form Document
        this.documentDate = DateTimeUtils.formatDateJPQL(startDate);

        // Document
        this.originatorDocumentIdentifier = originatorDocumentIdentifier;
        this.documentSubgroupId = documentSubgroupId;
        this.source = source;
        this.docType = docType;
        this.expenseType = expenseType;

        // Vendor details
        this.vendorCode = vendorCode;
        this.vendorGstin = vendorGstin;
        this.gstinStatus = gstinStatus;
        this.msmeStatus = msmeStatus;
//        this.isEinvoiceApplicable = isEinvoiceApplicable;
        this.gst = gst;
        this.legalName = legalName;
        this.addr1 = addr1;
        this.addr2 = addr2;
        this.loc = loc;
        this.pin = pin;
//        this.stateCode = stateCode;
        this.phoneNo = phoneNo;
        this.emailId = emailId;

        this.erpRemarks = erpRemarks;
        this.statusRemarks = statusRemarks;

        this.processMethod = processMethod != null ? processMethod.name() : null;
        // InvoiceHeader
        this.invoiceNumber = invoiceNumber;
        this.documentSubgroup = documentSubgroup;
        this.documentGroup = documentGroup;
        this.supportingDocumentsCount = supportingDocumentsCount;

        this.invoiceReceivedId = invoiceReceivedId;
        this.utrNumber = utrNumber;

        this.paymentDate = DateTimeUtils.formatDate(paymentDate);
    }

}
