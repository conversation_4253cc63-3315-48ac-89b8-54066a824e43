package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

@Data
@NoArgsConstructor
public class BaseContainerViewModel {
    private Long id;
    private String documentIdentifier;
    private String docNo;
    private String createdDate;

    private String expectedDeliveryDate;
    private ReportStatus reportStatus;
    private String submitDate;
    private String updatedTimestamp;
    private String lastActionedAt;

    private String currentApproverFirstName;
    private String currentApproverLastName;
    private String currentApproverSystemIdCode;
    private String currentApproverEmployeeCode;

    // Employee Details
    private String firstName;
    private String middleName;
    private String lastName;
    private String employeeCode;
    private String employeeGrade;

    // Custom fields for the frontend
    private String createdBy;
    private String lastApprovedBy;

    public BaseContainerViewModel(Long id, String documentIdentifier, String docNo, ZonedDateTime createdTimestamp, LocalDate expectedDeliveryDate,
                                  ReportStatus reportStatus, LocalDate submitDate, ZonedDateTime updatedTimestamp,
                                  String currentApproverFirstName, String currentApproverLastName, String currentApproverSystemIdCode,
                                  String currentApproverEmployeeCode, String firstName, String middleName, String lastName,
                                  String employeeCode, String employeeGrade) {
        this.id = id;
        this.documentIdentifier = documentIdentifier;
        this.docNo = docNo;
        this.reportStatus = reportStatus;

        this.createdDate = DateTimeUtils.formatTimestamp(createdTimestamp);

        this.expectedDeliveryDate = DateTimeUtils.formatDateJPQL(expectedDeliveryDate);
        this.submitDate = DateTimeUtils.formatDateJPQL(submitDate);

        this.updatedTimestamp = DateTimeUtils.formatTimestamp(updatedTimestamp);
        this.lastActionedAt = DateTimeUtils.formatTimestamp(updatedTimestamp);


        this.currentApproverFirstName = currentApproverFirstName;
        this.currentApproverLastName = currentApproverLastName;
        this.currentApproverSystemIdCode = currentApproverSystemIdCode;
        this.currentApproverEmployeeCode = currentApproverEmployeeCode;
        this.firstName = firstName;
        this.middleName = middleName;
        this.lastName = lastName;
        this.employeeCode = employeeCode;
        this.employeeGrade = employeeGrade;
        this.createdBy = this.firstName + " " + this.lastName;
    }

    public BaseContainerViewModel(Long id, String documentIdentifier, String docNo, ZonedDateTime createdTimestamp, LocalDate expectedDeliveryDate,
                                  ReportStatus reportStatus, LocalDate submitDate, ZonedDateTime updatedTimestamp,
                                  String currentApproverFirstName, String currentApproverLastName, String currentApproverSystemIdCode,
                                  String currentApproverEmployeeCode, String firstName, String middleName, String lastName,
                                  String employeeCode, String employeeGrade, String createdBy) {
        this.id = id;
        this.documentIdentifier = documentIdentifier;
        this.docNo = docNo;
        this.reportStatus = reportStatus;

        this.createdDate = DateTimeUtils.formatTimestamp(createdTimestamp);

        this.expectedDeliveryDate = DateTimeUtils.formatDateJPQL(expectedDeliveryDate);
        this.submitDate = DateTimeUtils.formatDateJPQL(submitDate);

        this.updatedTimestamp = DateTimeUtils.formatTimestamp(updatedTimestamp);
        this.lastActionedAt = DateTimeUtils.formatTimestamp(updatedTimestamp);


        this.currentApproverFirstName = currentApproverFirstName;
        this.currentApproverLastName = currentApproverLastName;
        this.currentApproverSystemIdCode = currentApproverSystemIdCode;
        this.currentApproverEmployeeCode = currentApproverEmployeeCode;
        this.firstName = firstName;
        this.middleName = middleName;
        this.lastName = lastName;
        this.employeeCode = employeeCode;
        this.employeeGrade = employeeGrade;
        this.createdBy = createdBy;
    }
}
