package in.taxgenie.pay_expense_pvv.viewmodels;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

public class LocationCreateViewModel {
    @NotBlank
    @Size(max = 50)
    private String location;

    @NotBlank
    @Size(max = 50)
    private String category;

    @NotBlank
    @Size(min = 2, max = 3)
    private String countryCode;

    @Size(max = 200)
    private String description;

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
