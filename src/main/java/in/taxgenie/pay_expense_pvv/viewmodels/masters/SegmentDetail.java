package in.taxgenie.pay_expense_pvv.viewmodels.masters;

import lombok.*;

import java.math.BigDecimal;
import java.util.List;

@ToString
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SegmentDetail {
    private Double ratio;
    private BigDecimal amount;
    private List<SegmentRatioData> masters;

 public SegmentDetail(Double ratio, List<SegmentRatioData> masters) {
    this.ratio = ratio;
    this.masters = masters;
 }

}
