package in.taxgenie.pay_expense_pvv.viewmodels.masters;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
public class SupplierMasterCombinedViewModel extends BaseSupplierMasterViewModel{
    private Long id; // company id from company
    private String pan;
    private Boolean isSupplierConnectRequestEnable;
    private String supplierConnectRequestDate;
    private String paymentsTerms;
    private String cinNo;
    private String cinStatus;
    private String tanNo;
    private String tds;
    private String gstin;
    private String gstinStatus;
    private Boolean invoiceApplicable;
    private Double complianceRating;
    private String itcRiskAmount;
    private String bankName;
    private String bankAccountNo;
    private String ifscCode;
    private String status;

    public SupplierMasterCombinedViewModel(Long id, String pan, Boolean isSupplierConnectRequestEnable, String supplierConnectRequestDate, String paymentsTerms,
                                           String cinNo, String cinStatus, String tanNo, String tds, String gstin, String gstinStatus, Boolean invoiceApplicable, Double complianceRating,
                                           String itcRiskAmount, String bankName, String bankAccountNo, String ifscCode, String status,
                                           String vendorName, String vendorCode, Boolean msmeStatus, String contactPersonEmail, String contactPersonName,
                                           String contactPersonPhone, String address, String createdBy, String lastApprovedBy, String createdAt, String lastUpdatedAt){

        super(vendorName, vendorCode, msmeStatus, contactPersonEmail, contactPersonName, contactPersonPhone, address, createdBy, lastApprovedBy, createdAt, lastUpdatedAt);

        this.id = id;
        this.pan = pan;
        this.isSupplierConnectRequestEnable = isSupplierConnectRequestEnable;
        this.supplierConnectRequestDate = supplierConnectRequestDate;
        this.paymentsTerms = paymentsTerms;
        this.cinNo = cinNo;
        this.cinStatus = cinStatus;
        this.tanNo = tanNo;
        this.tds = tds;
        this.gstin = gstin;
        this.gstinStatus = gstinStatus;
        this.invoiceApplicable = invoiceApplicable;
        this.complianceRating = complianceRating;
        this.itcRiskAmount = itcRiskAmount;
        this.bankName = bankName;
        this.bankAccountNo = bankAccountNo;
        this.ifscCode = ifscCode;
        this.status = status;
    }
}
