package in.taxgenie.pay_expense_pvv.viewmodels.masters;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
public class SupplierMastersGstinViewModel extends BaseSupplierMasterViewModel {
    private Long id;
    private String gstin;
    private String gstinStatus;
    private Boolean invoiceApplicable;
    private Double complianceRating;
    private String itcRiskAmount;
    private String bankName;
    private String bankAccountNo;
    private String ifscCode;
    private String status;
    private String paymentTerms;

    public SupplierMastersGstinViewModel(Long id, String gstin, String gstinStatus, Boolean invoiceApplicable, Double complianceRating, String itcRiskAmount, String bankName, String bankAccountNo, String ifscCode, String status,
                                         String vendorName, String vendorCode, Boolean msmeStatus, String contactPersonEmail, String contactPersonName, String contactPersonPhone, String address1, String address2, String createdBy, String lastApprovedBy, String createdAt, String lastUpdatedAt, String paymentTerms) {
        super(vendorName, vendorCode, msmeStatus, contactPersonEmail, contactPersonName, contactPersonPhone, String.join("", address1 == null ? "" : address1, address2 == null ? "" : address2), createdBy, lastApprovedBy, createdAt, lastUpdatedAt);

        this.id = id;
        this.gstin = gstin;
        this.gstinStatus = gstinStatus;
        this.invoiceApplicable = invoiceApplicable;
        this.complianceRating = complianceRating;
        this.itcRiskAmount = itcRiskAmount;
        this.bankName = bankName;
        this.bankAccountNo = bankAccountNo;
        this.ifscCode = ifscCode;
        this.status = status;
        this.paymentTerms = paymentTerms;
    }
}
