package in.taxgenie.pay_expense_pvv.viewmodels.masters;

import com.fasterxml.jackson.annotation.JsonProperty;
import in.taxgenie.pay_expense_pvv.entities.masters.RatioCategoryMaster;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@ToString
@Getter
@Setter
public class RatioCategoryViewModel {
    private Integer id;
    @NotNull @NotEmpty
    private String name;
    @NotNull @NotEmpty
    private Double ratio;
    @NotNull
    @JsonProperty("masters")
    private List<SegmentRatioData> masterJson;
    private String createdAt;
    private String updatedAt;
}
