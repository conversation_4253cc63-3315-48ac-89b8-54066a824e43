package in.taxgenie.pay_expense_pvv.viewmodels.masters;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class BaseSupplierMasterViewModel {
    private String vendorName;
    private String vendorCode;
    private Boolean msmeStatus;
    private String contactPersonEmail;
    private String contactPersonName;
    private String contactPersonPhone;
    private String address;
    private String createdBy;
    private String lastUpdatedBy;
    private String createdAt;
    private String lastUpdatedAt;

    public BaseSupplierMasterViewModel(String vendorName, String vendorCode, Boolean msmeStatus, String contactPersonEmail, String contactPersonName, String contactPersonPhone, String address, String createdBy, String lastUpdatedBy, String createdAt, String lastUpdatedAt) {
        this.vendorName = vendorName;
        this.vendorCode = vendorCode;
        this.msmeStatus = msmeStatus;
        this.contactPersonEmail = contactPersonEmail;
        this.contactPersonName = contactPersonName;
        this.contactPersonPhone = contactPersonPhone;
        this.address = address;
        this.createdBy = createdBy;
        this.lastUpdatedBy = lastUpdatedBy;
        this.createdAt = createdAt;
        this.lastUpdatedAt = lastUpdatedAt;
    }
}
