package in.taxgenie.pay_expense_pvv.viewmodels.masters;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
public class SupplierMasterSyncRequestViewModel {
        private Long id;
        private String buyerContactNo;
        private String buyerEmailId;
        private String buyerFirstName;
        private String buyerLastName;
        private String buyerPan;
        private Long buyerParentCompId;
        private Long companyCode;
        private String complianceSellerRating;
        private String createdAt;
        private Integer createdBy;
        private Integer groupId;
        private LocalDateTime gstCancellationDate;
        private Integer gstr1DiffDate;
        private LocalDateTime gstr1DueDate;
        private LocalDateTime gstr1FilingDate;
        private String gstr1FilingMonth;
        private String gstr1Rating;
        private Integer gstr3BDiffDate;
        private LocalDateTime gstr3BDueDate;
        private LocalDateTime gstr3BFilingDate;
        private String gstr3BFilingMonth;
        private String gstr3BRating;
        private Integer hsnMandatory;
        private Integer itcRisk;
        private Boolean msmeStatus;
        private String reason;
        private String sellerAccName;
        private String sellerAccNo;
        private String sellerAddress1;
        private String sellerAddress2;
        private String sellerCinNo;
        private String sellerCinStatus;
        private String sellerCity;
        private String sellerCode;
        private String sellerCompanyName;
        private String sellerContact1;
        private String sellerContact2;
        private String sellerContact3;
        private String sellerCountry;
        private Integer sellerEinvoiceStatus;
        private String sellerEmail1;
        private String sellerEmail2;
        private String sellerEmail3;
        private String sellerFirstName;
        private String sellerGstin;
        private String sellerGstinStatus;
        private Integer sellerId;
        private String sellerIfsCode;
        private String sellerLastName;
        private String sellerPan;
        private Integer sellerPanAadharLink;
        private String sellerPaymentTerm;
        private Integer sellerPortalStatus;
        private String sellerState;
        private String sellerStateCode;
        @JsonProperty("sellerBlocked")
        private Boolean isSellerBlocked;
        private String sellerTanNo;
        private String sellerZipCode;
        private String status;
        private String supplierLegalName;
        private String taxPayerType;
        private String updatedAt;
        private Integer updatedBy;
}
