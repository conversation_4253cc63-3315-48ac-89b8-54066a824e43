package in.taxgenie.pay_expense_pvv.viewmodels.masters;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;


public class MasterDataStoreViewModel {
    private String code;
    private String description;

    private String address;
    private String contactDetails;
    private String source;
    private String masterDataType;
    // JSONB field for body
    // Let <PERSON> parse the JSON into a Map
    private Map<String, Object> body;

    public MasterDataStoreViewModel() {
    }

    public MasterDataStoreViewModel(String code, String description, String masterDataType, Map<String, Object> body) {
        this.code = code;
        this.description = description;
        this.masterDataType = masterDataType;
        this.body = body;
    }


    public MasterDataStoreViewModel(String code, String description, String address, String contactDetails, String source, String masterDataType, Map<String, Object> body) {
        this.code = code;
        this.description = description;
        this.address = address;
        this.contactDetails = contactDetails;
        this.source = source;
        this.masterDataType = masterDataType;
        this.body = body;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMasterDataType() {
        return masterDataType;
    }

    public void setMasterDataType(String masterDataType) {
        this.masterDataType = masterDataType;
    }

    public String getBodyString() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        // Convert the map to a JSON string
        return objectMapper.writeValueAsString(body);
    }

    public Map<String, Object> getBody() {
        return body;
    }

    public void setBody(Map<String, Object> body) {
        this.body = body;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getContactDetails() {
        return contactDetails;
    }

    public void setContactDetails(String contactDetails) {
        this.contactDetails = contactDetails;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
}
