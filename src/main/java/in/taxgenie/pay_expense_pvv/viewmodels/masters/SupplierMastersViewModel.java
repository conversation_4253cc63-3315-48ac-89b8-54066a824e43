package in.taxgenie.pay_expense_pvv.viewmodels.masters;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class SupplierMastersViewModel extends BaseSupplierMasterViewModel{

    private Long id;
    private String pan;
    private Integer gstinCount;
    private Boolean isSupplierConnectRequestEnable;
    private String supplierConnectRequestDate;
    private String paymentsTerms;
    private String cinNo;
    private String cinStatus;
    private String tanNo;
    private String tds;
    private Boolean isSellerBlocked;

    public SupplierMastersViewModel(Long id, String pan, Integer gstinCount, Boolean isSupplierConnectRequestEnable, String supplierConnectRequestDate, String paymentsTerms, String cinNo, String cinStatus, String tanNo, String tds,
                                    String vendorName, String vendorCode, Boolean msmeStatus, String contactPersonEmail, String contactPersonName, String contactPersonPhone, String address, String createdBy, String lastUpdatedBy, String createdAt, String lastUpdatedAt) {
        super(vendorName, vendorCode, msmeStatus, contactPersonEmail, contactPersonName, contactPersonPhone, address, createdBy, lastUpdatedBy, createdAt, lastUpdatedAt);
        this.id = id;
        this.pan = pan;
        this.gstinCount = gstinCount;
        this.isSupplierConnectRequestEnable = isSupplierConnectRequestEnable;
        this.supplierConnectRequestDate = supplierConnectRequestDate;
        this.paymentsTerms = paymentsTerms;
        this.cinNo = cinNo;
        this.cinStatus = cinStatus;
        this.tanNo = tanNo;
        this.tds = tds;
    }
}
