package in.taxgenie.pay_expense_pvv.viewmodels;

import java.util.List;

public class EmployeeMasterLookupViewModel {
    private final List<String> designations;
    private final List<String> types;
    private final List<String> grades;
    private final List<String> branches;
    private final List<String> departments;
    private final List<String> costCenters;

    public EmployeeMasterLookupViewModel(List<String> designations, List<String> types, List<String> grades, List<String> branches, List<String> departments, List<String> costCenters) {
        this.designations = designations;
        this.types = types;
        this.grades = grades;
        this.branches = branches;
        this.departments = departments;
        this.costCenters = costCenters;
    }

    public List<String> getDesignations() {
        return designations;
    }

    public List<String> getTypes() {
        return types;
    }

    public List<String> getGrades() {
        return grades;
    }

    public List<String> getBranches() {
        return branches;
    }

    public List<String> getDepartments() {
        return departments;
    }

    public List<String> getCostCenters() {
        return costCenters;
    }
}
