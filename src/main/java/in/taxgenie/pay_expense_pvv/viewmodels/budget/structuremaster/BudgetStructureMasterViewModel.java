package in.taxgenie.pay_expense_pvv.viewmodels.budget.structuremaster;

import in.taxgenie.pay_expense_pvv.entities.budget.BudgetStructureMaster;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BudgetStructureMasterViewModel extends BudgetStructureMasterViewModelBase {
    private Long id;
    private Long companyCode;
    private String expenditureType;
    private String createdUserName;
    private String createdTimestamp;

    private String updatedUserName;
    private String updatedTimestamp;

    private String deletedUserName;
    private String deletedTimestamp;



}
