package in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Setter
@Getter
public class BudgetTotalsViewModel {
    private BigDecimal total;
    private BigDecimal treasury;

    private BigDecimal softLocked;
    private BigDecimal locked;
    private BigDecimal consumed;

    public BudgetTotalsViewModel(BigDecimal total, BigDecimal treasury,
                                 BigDecimal softLocked, BigDecimal locked, BigDecimal consumed) {
        this.total = total;
        this.treasury = treasury;

        this.softLocked = softLocked;
        this.locked = locked;
        this.consumed = consumed;

    }

    public BudgetTotalsViewModel() {
    }

}
