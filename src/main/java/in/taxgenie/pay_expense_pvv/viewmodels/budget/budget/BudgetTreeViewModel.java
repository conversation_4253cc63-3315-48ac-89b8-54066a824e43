package in.taxgenie.pay_expense_pvv.viewmodels.budget.budget;

import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels.BudgetSyncToBudgetMappingMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels.BudgetToBudgetMappingMasterViewModel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class BudgetTreeViewModel {
    private static final Boolean expanded = true;
    private static final Boolean selectable = false;
    private BudgetDataViewModel data;
    private List<BudgetTreeViewModel> children;

    public BudgetTreeViewModel(BudgetDataViewModel data, List<BudgetTreeViewModel> children) {
        this.data = data;
        this.children = children;
    }

    public BudgetTreeViewModel(Budget node, long budgetMasterId, String budgetMasterName,
                               long budgetSyncMasterId, String budgetSyncMasterName,
                               long budgetMappingMasterId, long budgetStructureMasterId, String budgetCode) {

        Budget parent = node.getParent();
        Long parentId = parent == null ? null : parent.getId();
        this.data = new BudgetDataViewModel(budgetMappingMasterId, parentId, node.getId(), budgetMasterId, budgetMasterName, budgetSyncMasterId, budgetSyncMasterName, budgetStructureMasterId, budgetCode);
        this.children = new ArrayList<>();
    }

    public BudgetTreeViewModel(Budget node,  Map<Long, BudgetSyncToBudgetMappingMasterViewModel> budgetSyncMap, Map<Long, BudgetToBudgetMappingMasterViewModel> budgetMasterMap) {
        Budget parent = node.getParent();
        this.data = new BudgetDataViewModel(
                budgetMasterMap.get(node.getId()).getBudgetMappingMasterId(),
                parent == null ? null : budgetMasterMap.get(node.getParent().getId()).getBudgetMappingMasterId(),
                node.getId(),
                budgetSyncMap.get(node.getBudgetSyncMasterId()).getMasterId(),
                budgetSyncMap.get(node.getBudgetSyncMasterId()).getMasterName(),
                budgetSyncMap.get(node.getBudgetSyncMasterId()).getSyncId(),
                budgetSyncMap.get(node.getBudgetSyncMasterId()).getSyncName(),
                node.getBudgetStructureMasterId(),
                node.getBudgetCode()
        );
        this.children = new ArrayList<>();
    }

    public void addChildren(BudgetTreeViewModel child) {
        this.children.add(child);
    }

    public Boolean getExpanded() {
        return expanded;
    }

    public Boolean getSelectable() {
        return selectable;
    }

    public BudgetDataViewModel getData() {
        return data;
    }

    public List<BudgetTreeViewModel> getChildren() {
        return children;
    }
}
