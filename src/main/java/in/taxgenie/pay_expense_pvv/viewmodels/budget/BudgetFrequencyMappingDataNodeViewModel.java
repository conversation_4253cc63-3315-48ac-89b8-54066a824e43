package in.taxgenie.pay_expense_pvv.viewmodels.budget;

import lombok.*;

import java.math.BigDecimal;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BudgetFrequencyMappingDataNodeViewModel {
    private Long id;
    private Long budgetId;
    private Integer interval;

    private BigDecimal total;
    private BigDecimal treasury;

    private BigDecimal softLocked;
    private BigDecimal locked;
    private BigDecimal consumed;

}
