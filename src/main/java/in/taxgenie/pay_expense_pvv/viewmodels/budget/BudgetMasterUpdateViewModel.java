package in.taxgenie.pay_expense_pvv.viewmodels.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMaster;
import lombok.*;

import java.time.ZonedDateTime;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BudgetMasterUpdateViewModel {

    private Long id;
    private String name;
    private boolean isActive;

    public BudgetMaster getUpdatedBudgetMaster(BudgetMaster budgetMaster, IAuthContextViewModel auth){

        budgetMaster.setName(this.name);
        budgetMaster.setActive(this.isActive());

        budgetMaster.setUpdatedTimestamp(ZonedDateTime.now());
        budgetMaster.setUpdatingUserId(auth.getUserId());

        return budgetMaster;
    }
}
