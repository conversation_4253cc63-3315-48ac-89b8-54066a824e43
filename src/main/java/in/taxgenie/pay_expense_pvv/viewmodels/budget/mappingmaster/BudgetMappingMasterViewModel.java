package in.taxgenie.pay_expense_pvv.viewmodels.budget.mappingmaster;

public class BudgetMappingMasterViewModel extends BudgetMappingMasterViewModelBase {
    private long id;
    private long companyCode;
    private Integer level;

    public BudgetMappingMasterViewModel(long budgetStructureMasterId, long budgetMasterId, String displayName) {
        super(budgetStructureMasterId, budgetMasterId, displayName);
    }

    public BudgetMappingMasterViewModel(long budgetStructureMasterId, long budgetMasterId, String displayName, long id, long companyCode, Integer level) {
        super(budgetStructureMasterId, budgetMasterId, displayName);
        this.id = id;
        this.companyCode = companyCode;
        this.level = level;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(long companyCode) {
        this.companyCode = companyCode;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
}
