package in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels;

public class BudgetToBudgetMappingMasterViewModel {
    private long budgetId;
    private long budgetMappingMasterId;

    public BudgetToBudgetMappingMasterViewModel() {
    }

    public BudgetToBudgetMappingMasterViewModel(long budgetId, long budgetMappingMasterId) {
        this.budgetId = budgetId;
        this.budgetMappingMasterId = budgetMappingMasterId;
    }

    public long getBudgetId() {
        return budgetId;
    }

    public void setBudgetId(long budgetId) {
        this.budgetId = budgetId;
    }

    public long getBudgetMappingMasterId() {
        return budgetMappingMasterId;
    }

    public void setBudgetMappingMasterId(long budgetMappingMasterId) {
        this.budgetMappingMasterId = budgetMappingMasterId;
    }
}
