package in.taxgenie.pay_expense_pvv.viewmodels.budget.frequencymapping;

import java.math.BigDecimal;

public class NodeUpdateViewModel {
    private Long id;
    private Long budgetId;
    private String budgetCode;
    private Integer interval;

    private BigDecimal total;
    private BigDecimal treasury;

    private BigDecimal softLocked;
    private BigDecimal locked;
    private BigDecimal consumed;


    public NodeUpdateViewModel() {
    }

    public NodeUpdateViewModel(Long id, Long budgetId, String budgetCode, Integer interval, BigDecimal total, BigDecimal softLocked, BigDecimal locked, BigDecimal treasury, BigDecimal consumed) {
        this.id = id;
        this.budgetId = budgetId;
        this.budgetCode = budgetCode;
        this.interval = interval;
        this.total = total;
        this.treasury = treasury;

        this.softLocked = softLocked;
        this.locked = locked;
        this.consumed = consumed;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBudgetId() {
        return budgetId;
    }

    public void setBudgetId(Long budgetId) {
        this.budgetId = budgetId;
    }

    public String getBudgetCode() {
        return budgetCode;
    }

    public void setBudgetCode(String budgetCode) {
        this.budgetCode = budgetCode;
    }

    public Integer getInterval() {
        return interval;
    }

    public void setInterval(Integer interval) {
        this.interval = interval;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public BigDecimal getSoftLocked() {
        return softLocked;
    }

    public void setSoftLocked(BigDecimal softLocked) {
        this.softLocked = softLocked;
    }

    public BigDecimal getLocked() {
        return locked;
    }

    public void setLocked(BigDecimal locked) {
        this.locked = locked;
    }

    public BigDecimal getConsumed() {
        return consumed;
    }

    public void setConsumed(BigDecimal consumed) {
        this.consumed = consumed;
    }

    public BigDecimal getTreasury() {
        return treasury;
    }

    public void setTreasury(BigDecimal treasury) {
        this.treasury = treasury;
    }
}
