package in.taxgenie.pay_expense_pvv.viewmodels.budget.budget;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BudgetAddLessViewModel {
    private Long id; // budgetDocumentActionId

    private String orgHierarchy;
    private String financialYear;
    private String expenditureType;

    @JsonProperty("policy")
    private Long documentMetadataId;

    @JsonProperty("submitPolicy")
    private Long submitDocumentMetadataId;

    @JsonProperty("budgetCodeId")
    private Long budgetId;
    private Long structureId;
    private BigDecimal amountAvailable;
    private BigDecimal addLessAmount;

    private String reason;
}
