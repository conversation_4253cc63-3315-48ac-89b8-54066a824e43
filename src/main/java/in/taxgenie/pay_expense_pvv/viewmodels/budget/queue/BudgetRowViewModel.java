package in.taxgenie.pay_expense_pvv.viewmodels.budget.queue;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;

import java.math.BigDecimal;

public class BudgetRowViewModel {
    private Long id;
    private Boolean isApproved;
    private String status;
    private String structureCode;
    private String applicableExpenseType;
    private String description;
    private BigDecimal totalAmount;
    private BigDecimal balanceAmount;
    private BigDecimal consumedAmount;
    private String createdBy;
    private String createdDate;
    private String createdTime;
    private String lastUpdatedBy;
    private String lastUpdateDate;
    private String lastUpdateTime;

    public BudgetRowViewModel() {
    }

    public BudgetRowViewModel(Long id, String structureCode, BigDecimal totalAmount, BigDecimal balanceAmount, BigDecimal consumedAmount, String createdBy, String createdDate, String createdTime, String lastUpdatedBy, String lastUpdateDate, String lastUpdateTime, Boolean isApproved, ReportStatus status,ExpenseType applicableExpenseType) {
        this.id = id;
        this.structureCode = structureCode;
        this.totalAmount = totalAmount;
        this.balanceAmount = balanceAmount;
        this.consumedAmount = consumedAmount;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
        this.createdTime = createdTime;
        this.lastUpdatedBy = lastUpdatedBy;
        this.lastUpdateDate = lastUpdateDate;
        this.lastUpdateTime = lastUpdateTime;
        this.isApproved = isApproved;
        this.status = status.name();
        this.applicableExpenseType = applicableExpenseType.name();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Boolean getApproved() {
        return isApproved;
    }

    public void setApproved(Boolean approved) {
        isApproved = approved;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    public String getApplicableExpenseType() {
        return applicableExpenseType;
    }

    public void setApplicableExpenseType(String applicableExpenseType) {
        this.applicableExpenseType = applicableExpenseType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getBalanceAmount() {
        return balanceAmount;
    }

    public void setBalanceAmount(BigDecimal balanceAmount) {
        this.balanceAmount = balanceAmount;
    }

    public BigDecimal getConsumedAmount() {
        return consumedAmount;
    }

    public void setConsumedAmount(BigDecimal consumedAmount) {
        this.consumedAmount = consumedAmount;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(String lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(String lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}
