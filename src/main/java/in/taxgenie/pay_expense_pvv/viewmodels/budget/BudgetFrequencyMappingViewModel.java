package in.taxgenie.pay_expense_pvv.viewmodels.budget;

import lombok.*;

import java.math.BigDecimal;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BudgetFrequencyMappingViewModel {
    private long id;
    private long budgetId;
    private long interval;

    private BigDecimal total;
    private BigDecimal treasury;

    private BigDecimal softLocked;
    private BigDecimal consumed;
    private BigDecimal locked;


}
