package in.taxgenie.pay_expense_pvv.viewmodels.budget.budget;

import java.util.List;

public class BudgetCreateViewModel {
    private static final Boolean expanded = true;
    private static final Boolean selectable = false;
    private BudgetCreateDataViewModel data;
    private List<BudgetCreateViewModel> children;

    public BudgetCreateViewModel() {
    }

    public BudgetCreateViewModel(BudgetCreateDataViewModel data, List<BudgetCreateViewModel> children) {
        this.data = data;
        this.children = children;
    }

    public Boolean getExpanded() {
        return expanded;
    }

    public Boolean getSelectable() {
        return selectable;
    }

    public BudgetCreateDataViewModel getData() {
        return data;
    }

    public void setData(BudgetCreateDataViewModel data) {
        this.data = data;
    }

    public List<BudgetCreateViewModel> getChildren() {
        return children;
    }

    public void setChildren(List<BudgetCreateViewModel> children) {
        this.children = children;
    }
}
