package in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels;

import in.taxgenie.pay_expense_pvv.utils.budget.StaticDataRegistry;

public class MonthFinancialYearModel {

    private final String month;
    private final int monthIndex;
    private final int year;

    public MonthFinancialYearModel(int monthIndex, int year) {
        this.monthIndex = monthIndex % 12;  // Ensure index is within 0-11
        this.year = year;
        this.month = StaticDataRegistry.BUDGET_MONTHS.get(this.monthIndex);
    }

    public int getMonthIndex() {
        return monthIndex;
    }

    public int getYear() {
        return year;
    }

    public MonthFinancialYearModel add(int months) {
        int totalMonths = this.monthIndex + months;
        int yearsToAdd = totalMonths / 12;  // Number of years to add due to rollover
        int newMonthIndex = totalMonths % 12;  // New month index within 0-11
        return new MonthFinancialYearModel(newMonthIndex, this.year + yearsToAdd);
    }

    public MonthFinancialYearModel subtract(int months) {
        int totalMonths = this.monthIndex - months;
        int yearsToSubtract = (totalMonths < 0) ? (Math.abs(totalMonths) / 12) + 1 : 0;
        // Handle negative remainders for rollover (avoid negative months)
        int newMonthIndex = (totalMonths + 12) % 12;  // Add 12 to ensure positive modulo
        return new MonthFinancialYearModel(newMonthIndex, this.year - yearsToSubtract);
    }

    public MonthFinancialYearModel getMonthInclusive() {
        return new MonthFinancialYearModel(this.monthIndex, this.year);
    }

    public MonthFinancialYearModel getMonthExclusive() {
        MonthFinancialYearModel newMonth = this.subtract(1);
        return new MonthFinancialYearModel(newMonth.monthIndex, newMonth.year);
    }

    public String getMonthName() {
        return this.month;
    }


}
