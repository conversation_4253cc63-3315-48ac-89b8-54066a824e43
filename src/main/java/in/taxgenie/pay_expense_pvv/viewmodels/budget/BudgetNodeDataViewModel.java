package in.taxgenie.pay_expense_pvv.viewmodels.budget;

import lombok.*;

import java.util.ArrayList;
import java.util.List;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BudgetNodeDataViewModel {
    private String label;
    private String budgetCode;
    private List<BudgetFrequencyMappingNodeViewModel> frequencyData = new ArrayList<>();
    private Long id;
    private Long parentId;
}
