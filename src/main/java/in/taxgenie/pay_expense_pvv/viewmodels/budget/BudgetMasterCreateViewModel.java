package in.taxgenie.pay_expense_pvv.viewmodels.budget;

import java.time.ZonedDateTime;
import java.util.UUID;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMaster;
import lombok.*;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BudgetMasterCreateViewModel {
    private String name;
    private boolean isActive;
    
    public BudgetMaster getBudgetMaster(IAuthContextViewModel auth){
        BudgetMaster budgetMaster = new BudgetMaster();
        budgetMaster.setName(this.name);
        budgetMaster.setActive(this.isActive());

        budgetMaster.setUuid(UUID.randomUUID());
        budgetMaster.setCompanyCode(auth.getCompanyCode());

        budgetMaster.setCreatedTimestamp(ZonedDateTime.now());
        budgetMaster.setCreatingUserId(auth.getUserId());


        return budgetMaster;
    }
}
