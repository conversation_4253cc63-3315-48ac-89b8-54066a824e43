package in.taxgenie.pay_expense_pvv.viewmodels.budget;

import lombok.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BudgetSheetViewModel {
    private long structureId;
    // Singleton list
    private List<BudgetNodeViewModel> treeData = new ArrayList<>();
    private List<BudgetColumnsViewModel> columns = new ArrayList<>();

    private Integer frequencyId;
    private BigDecimal totalBudgetAmount;
    private BigDecimal balanceBudgetAmount;
    private String budgetCode;
}
