package in.taxgenie.pay_expense_pvv.viewmodels.budget.syncmaster;

import java.time.ZonedDateTime;
import java.util.UUID;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMaster;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetSyncMaster;
import lombok.*;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BudgetSyncMasterCreateViewModel {
	
	private String code;
    private String description;
    private String address;
    private String contactDetails;
    private String sourceId;
    private String source;
    private boolean isActive;
    private long budgetMasterId;

}
