package in.taxgenie.pay_expense_pvv.viewmodels.budget.syncmaster;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetSyncMaster;
import lombok.*;

import java.time.ZonedDateTime;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BudgetSyncMasterUpdateViewModel {

	private Long id;
    private String code;
    private String description;
    private String address;
    private String contactDetails;
    private boolean isActive;

}
