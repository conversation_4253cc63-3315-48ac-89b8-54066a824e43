package in.taxgenie.pay_expense_pvv.viewmodels.budget;

import java.util.ArrayList;
import java.util.List;

public class BudgetMasterTreeViewModel {
    private Boolean expanded;
    private BudgetMasterTreeDataViewModel data;
    private List<BudgetMasterTreeViewModel> children;

    public BudgetMasterTreeViewModel() {
        this.children = new ArrayList<>();
    }

    public BudgetMasterTreeViewModel(Boolean expanded, BudgetMasterTreeDataViewModel data, List<BudgetMasterTreeViewModel> children) {
        this.expanded = expanded;
        this.data = data;
        this.children = children;
    }

    public void addChildren(BudgetMasterTreeViewModel child) {
        this.children.add(child);
    }

    public Boolean getExpanded() {
        return expanded;
    }

    public void setExpanded(Boolean expanded) {
        this.expanded = expanded;
    }

    public BudgetMasterTreeDataViewModel getData() {
        return data;
    }

    public void setData(BudgetMasterTreeDataViewModel data) {
        this.data = data;
    }

    public List<BudgetMasterTreeViewModel> getChildren() {
        return children;
    }

    public void setChildren(List<BudgetMasterTreeViewModel> children) {
        this.children = children;
    }
}
