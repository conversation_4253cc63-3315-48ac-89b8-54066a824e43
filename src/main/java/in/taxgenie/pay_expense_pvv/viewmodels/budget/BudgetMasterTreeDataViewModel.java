package in.taxgenie.pay_expense_pvv.viewmodels.budget;

public class BudgetMasterTreeDataViewModel {
    private Long id;
    private Long budgetMasterId;
    private String displayName;
    private Long parentId;


    public BudgetMasterTreeDataViewModel() {
    }

    public BudgetMasterTreeDataViewModel(Long id, Long budgetMasterId, String displayName, Long parentId) {
        this.id = id;
        this.budgetMasterId = budgetMasterId;
        this.displayName = displayName;
        this.parentId = parentId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBudgetMasterId() {
        return budgetMasterId;
    }

    public void setBudgetMasterId(Long budgetMasterId) {
        this.budgetMasterId = budgetMasterId;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
}
