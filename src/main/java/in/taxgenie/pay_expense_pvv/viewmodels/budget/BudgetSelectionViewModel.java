package in.taxgenie.pay_expense_pvv.viewmodels.budget;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public class BudgetSelectionViewModel {
    private Long id;
    private Long budgetStructureMasterId;
    @JsonProperty("code")
    private String budgetCode;
    private String description;
    private String displayName;
    private BigDecimal total;
    private Boolean isBudgetLocked = Boolean.FALSE;

    public Boolean getBudgetLocked() {
        return isBudgetLocked;
    }

    public void setBudgetLocked(Boolean budgetLocked) {
        isBudgetLocked = budgetLocked;
    }

    public BudgetSelectionViewModel() {
    }

    public BudgetSelectionViewModel(Long id, String code, String description, String displayName) {
        this.id = id;
        this.budgetCode = code;
        this.description = description;
        this.displayName = displayName;
    }

    public BudgetSelectionViewModel(Long id, String code, String description, Long budgetStructureMasterId) {
        this.id = id;
        this.budgetStructureMasterId = budgetStructureMasterId;
        this.budgetCode = code;
        this.description = description;
    }
    public BudgetSelectionViewModel(Long id, String code, String description, Long budgetStructureMasterId, String displayName) {
        this.id = id;
        this.budgetStructureMasterId = budgetStructureMasterId;
        this.budgetCode = code;
        this.description = description;
        this.displayName = displayName;
    }

    public BudgetSelectionViewModel(Long id, Long budgetStructureMasterId, String code, String description, Boolean isBudgetLocked) {
        this.id = id;
        this.budgetStructureMasterId = budgetStructureMasterId;
        this.budgetCode = code;
        this.description = description;
        this.isBudgetLocked = isBudgetLocked;
    }
    public BudgetSelectionViewModel(Long id, Long budgetStructureMasterId, String code, String description, Boolean isBudgetLocked, String displayName) {
        this.id = id;
        this.budgetStructureMasterId = budgetStructureMasterId;
        this.budgetCode = code;
        this.description = description;
        this.isBudgetLocked = isBudgetLocked;
        this.displayName = displayName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBudgetStructureMasterId() {
        return budgetStructureMasterId;
    }

    public void setBudgetStructureMasterId(Long budgetStructureMasterId) {
        this.budgetStructureMasterId = budgetStructureMasterId;
    }

    public String getBudgetCode() {
        return budgetCode;
    }

    public void setBudgetCode(String budgetCode) {
        this.budgetCode = budgetCode;
    }

    public String getDescription() {
        return description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }
}
