package in.taxgenie.pay_expense_pvv.viewmodels.budget.mappingmaster;

import java.util.List;

public class BudgetMappingCreateViewModelWrapper {
    private boolean expanded;
    private BudgetMappingMasterCreateViewModel data;
    private List<BudgetMappingCreateViewModelWrapper> children;

    public BudgetMappingCreateViewModelWrapper() {
    }

    public BudgetMappingCreateViewModelWrapper(boolean expanded, BudgetMappingMasterCreateViewModel data, List<BudgetMappingCreateViewModelWrapper> children) {
        this.expanded = expanded;
        this.data = data;
        this.children = children;
    }

    public boolean isExpanded() {
        return expanded;
    }

    public void setExpanded(boolean expanded) {
        this.expanded = expanded;
    }

    public BudgetMappingMasterCreateViewModel getData() {
        return data;
    }

    public void setData(BudgetMappingMasterCreateViewModel data) {
        this.data = data;
    }

    public List<BudgetMappingCreateViewModelWrapper> getChildren() {
        return children;
    }

    public void setChildren(List<BudgetMappingCreateViewModelWrapper> children) {
        this.children = children;
    }
}
