package in.taxgenie.pay_expense_pvv.viewmodels.budget.budget;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@Data
public class BudgetRecordViewModel {

    private String organisation;
    private String budgetCode;
    private Long uuid1;
    private Long uuid2;

    private Map<String, BigDecimal> monthlyBudgets; // Stores month as key and budget as value

    public BudgetRecordViewModel(String organisation, String budgetCode, Long uuid1, Long uuid2, Map<String, BigDecimal> monthlyBudgets) {
        this.organisation = organisation;
        this.budgetCode = budgetCode;
        this.uuid1 = uuid1;
        this.uuid2 = uuid2;
        this.monthlyBudgets = monthlyBudgets;
    }

}
