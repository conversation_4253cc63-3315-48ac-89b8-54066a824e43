package in.taxgenie.pay_expense_pvv.viewmodels.budget;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.BaseContainerViewModel;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Data
public class BudgetContainerViewModel extends BaseContainerViewModel {

    private Long id;
    private BigDecimal amount;
    private BigDecimal balanceAmount;

    private String description;
    private String type;

    // Document
    private String originatorDocumentIdentifier;
    private Long documentSubgroupId;

    private String departmentName;

    private String documentDate;

    private Long structureId;
    private String budgetCode;

    private String financialYear;
    private String structureName;

    private Integer startMonthIndex;
    private String budgetFrequency;
    private Boolean isApproved;


    public BudgetContainerViewModel(Long id, BigDecimal amount, BigDecimal balanceAmount, String documentIdentifier,
                                    ZonedDateTime createdDate, ReportStatus reportStatus,
                                    LocalDate submitDate, String currentApproverFirstName,
                                    String currentApproverLastName, String currentApproverSystemIdCode, String currentApproverEmployeeCode,
                                    String firstName, String middleName, String lastName, String employeeCode, String employeeGrade, ZonedDateTime updatedTimestamp,
                                    String originatorDocumentIdentifier, Long documentSubgroupId, String departmentName,
                                    String docNo, String type, Long structureId, String budgetCode, String financialYear, String structureName, Integer startMonthIndex, Boolean isApproved) {
        super(id, documentIdentifier, docNo, createdDate, null, reportStatus, submitDate, updatedTimestamp, currentApproverFirstName,
                currentApproverLastName, currentApproverSystemIdCode, currentApproverEmployeeCode, firstName, middleName, lastName, employeeCode, employeeGrade);
        this.id = id;
        this.amount = amount;
        this.balanceAmount = balanceAmount;
        this.originatorDocumentIdentifier = originatorDocumentIdentifier;
        this.documentSubgroupId = documentSubgroupId;
        this.departmentName = departmentName;
        // Removed because of self-assigment issue in sonar qube
//        this.description = description;
        this.type = type;
        this.documentDate = DateTimeUtils.formatTimestampToDate(createdDate);
        this.structureId = structureId;
        this.budgetCode = budgetCode;
        this.financialYear = financialYear;
        this.structureName = structureName;
        this.startMonthIndex = startMonthIndex;
        this.isApproved = isApproved;
    }

}
