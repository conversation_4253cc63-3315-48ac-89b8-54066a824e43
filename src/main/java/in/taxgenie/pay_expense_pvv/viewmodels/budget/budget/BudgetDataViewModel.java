package in.taxgenie.pay_expense_pvv.viewmodels.budget.budget;

public class BudgetDataViewModel {
    private long id;
    private Long parentId;
    private long budgetId;
    private long budgetMasterId;
    private String budgetMasterName;
    private long budgetSyncMasterId;
    private String budgetSyncMasterName;
    private long budgetStructureMasterId;
    private String budgetCode;

    public BudgetDataViewModel() {
    }

    public BudgetDataViewModel(long id, Long parentId, long budgetId, long budgetMasterId, String budgetMasterName,
                               long budgetSyncMasterId, String budgetSyncMasterName,
                               long budgetStructureMasterId, String budgetCode) {
        this.id = id;
        this.parentId = parentId;
        this.budgetId = budgetId;
        this.budgetMasterId = budgetMasterId;
        this.budgetMasterName = budgetMasterName;
        this.budgetSyncMasterId = budgetSyncMasterId;
        this.budgetSyncMasterName = budgetSyncMasterName;
        this.budgetStructureMasterId = budgetStructureMasterId;
        this.budgetCode = budgetCode;
    }


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public long getBudgetId() {
        return budgetId;
    }

    public void setBudgetId(long budgetId) {
        this.budgetId = budgetId;
    }

    public long getBudgetMasterId() {
        return budgetMasterId;
    }

    public void setBudgetMasterId(long budgetMasterId) {
        this.budgetMasterId = budgetMasterId;
    }

    public String getBudgetMasterName() {
        return budgetMasterName;
    }

    public void setBudgetMasterName(String budgetMasterName) {
        this.budgetMasterName = budgetMasterName;
    }

    public long getBudgetSyncMasterId() {
        return budgetSyncMasterId;
    }

    public void setBudgetSyncMasterId(long budgetSyncMasterId) {
        this.budgetSyncMasterId = budgetSyncMasterId;
    }

    public String getBudgetSyncMasterName() {
        return budgetSyncMasterName;
    }

    public void setBudgetSyncMasterName(String budgetSyncMasterName) {
        this.budgetSyncMasterName = budgetSyncMasterName;
    }

    public long getBudgetStructureMasterId() {
        return budgetStructureMasterId;
    }

    public void setBudgetStructureMasterId(long budgetStructureMasterId) {
        this.budgetStructureMasterId = budgetStructureMasterId;
    }

    public String getBudgetCode() {
        return budgetCode;
    }

    public void setBudgetCode(String budgetCode) {
        this.budgetCode = budgetCode;
    }
}
