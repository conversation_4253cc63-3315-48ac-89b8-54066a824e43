package in.taxgenie.pay_expense_pvv.viewmodels.budget.syncmaster;

import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMaster;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetSyncMaster;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import lombok.*;

import java.time.ZonedDateTime;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BudgetSyncMasterViewModel {

    private Long id;
    private UUID uuid;
    private long companyCode;
    
    private String code;
    private String description;
    private String address;
    private String contactDetails;
    private String sourceId;
    private String source;
    private long budgetMasterId;
    
    private boolean isActive;


    private String createdUserName;
    private String createdTimestamp;

    private String updatedUserName;
    private String updatedTimestamp;

    private String deletedUserName;
    private String deletedTimestamp;

    public BudgetSyncMasterViewModel(Long id, UUID uuid, long companyCode, String code, String description, String address, String contactDetails, String sourceId, String source, long budgetMasterId, boolean isActive, String createdUserName, ZonedDateTime createdTimestamp, String updatedUserName, ZonedDateTime updatedTimestamp, String deletedUserName, ZonedDateTime deletedTimestamp) {
        this.id = id;
        this.uuid = uuid;
        this.companyCode = companyCode;
        this.code = code;
        this.description = description;
        this.address = address;
        this.contactDetails = contactDetails;
        this.sourceId = sourceId;
        this.source = source;
        this.budgetMasterId = budgetMasterId;
        this.isActive = isActive;
        this.createdUserName = createdUserName;
        this.createdTimestamp = DateTimeUtils.formatTimestampToDate(createdTimestamp);
        this.updatedUserName = updatedUserName;
        this.updatedTimestamp  = DateTimeUtils.formatTimestampToDate(updatedTimestamp);
        this.deletedUserName = deletedUserName;
        this.deletedTimestamp  = DateTimeUtils.formatTimestampToDate(deletedTimestamp);
    }
 
    public void setBudgetSyncMaster( BudgetSyncMaster budgetSyncMaster) {
		
    	this.setId(budgetSyncMaster.getId());
    	this.setUuid(budgetSyncMaster.getUuid());
    	this.setCompanyCode(budgetSyncMaster.getCompanyCode());
    	
    	this.setCode(budgetSyncMaster.getCode());
    	this.setDescription(budgetSyncMaster.getDescription());
    	this.setAddress(budgetSyncMaster.getAddress());
    	this.setContactDetails(budgetSyncMaster.getContactDetails());
    	this.setSource(budgetSyncMaster.getSource());
    	this.setSourceId(budgetSyncMaster.getSourceId());
    	this.setBudgetMasterId(budgetSyncMaster.getBudgetMasterId());
    	
    	this.setActive(budgetSyncMaster.isActive());

        this.setCreatedTimestamp(DateTimeUtils.formatTimestampToDate(budgetSyncMaster.getCreatedTimestamp()));
        if(null != budgetSyncMaster.getCreatingUser()) {
            this.setCreatedUserName(budgetSyncMaster.getCreatingUser().getFirstName() + " " + budgetSyncMaster.getCreatingUser().getLastName());
        }

        this.setUpdatedTimestamp(DateTimeUtils.formatTimestampToDate(budgetSyncMaster.getUpdatedTimestamp()));
        if(null != budgetSyncMaster.getUpdatingUser()) {
            this.setUpdatedUserName(budgetSyncMaster.getUpdatingUser().getFirstName() + " " + budgetSyncMaster.getUpdatingUser().getLastName());
        }
        this.setDeletedTimestamp(DateTimeUtils.formatTimestamp(budgetSyncMaster.getDeletedTimestamp()));
        if(null != budgetSyncMaster.getDeletingUser()) {
            this.setDeletedUserName(budgetSyncMaster.getDeletingUser().getFirstName() + " " + budgetSyncMaster.getDeletingUser().getLastName());
        }
	}
}
