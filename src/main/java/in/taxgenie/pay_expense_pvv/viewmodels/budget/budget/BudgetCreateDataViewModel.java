package in.taxgenie.pay_expense_pvv.viewmodels.budget.budget;

public class BudgetCreateDataViewModel {
    private Long budgetId;
    private long budgetMasterId;
    private String budgetMasterName;
    private long budgetSyncMasterId;
    private long getBudgetSyncMasterName;
    private long budgetStructureMasterId;
    public BudgetCreateDataViewModel() {
    }

    public BudgetCreateDataViewModel(Long id, long budgetMasterId, String budgetMasterName,
                                     long budgetSyncMasterId, long getBudgetSyncMasterName,
                                     long budgetStructureMasterId) {

        this.budgetId = id;
        this.budgetMasterId = budgetMasterId;
        this.budgetMasterName = budgetMasterName;
        this.budgetSyncMasterId = budgetSyncMasterId;
        this.getBudgetSyncMasterName = getBudgetSyncMasterName;
        this.budgetStructureMasterId = budgetStructureMasterId;
    }

    public long getBudgetMasterId() {
        return budgetMasterId;
    }

    public void setBudgetMasterId(long budgetMasterId) {
        this.budgetMasterId = budgetMasterId;
    }

    public String getBudgetMasterName() {
        return budgetMasterName;
    }

    public void setBudgetMasterName(String budgetMasterName) {
        this.budgetMasterName = budgetMasterName;
    }

    public long getBudgetSyncMasterId() {
        return budgetSyncMasterId;
    }

    public void setBudgetSyncMasterId(long budgetSyncMasterId) {
        this.budgetSyncMasterId = budgetSyncMasterId;
    }

    public long getGetBudgetSyncMasterName() {
        return getBudgetSyncMasterName;
    }

    public void setGetBudgetSyncMasterName(long getBudgetSyncMasterName) {
        this.getBudgetSyncMasterName = getBudgetSyncMasterName;
    }

    public long getBudgetStructureMasterId() {
        return budgetStructureMasterId;
    }

    public void setBudgetStructureMasterId(long budgetStructureMasterId) {
        this.budgetStructureMasterId = budgetStructureMasterId;
    }

    public Long getBudgetId() {
        return budgetId;
    }

    public void setBudgetId(Long budgetId) {
        this.budgetId = budgetId;
    }
}
