package in.taxgenie.pay_expense_pvv.viewmodels.budget;

import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMaster;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import lombok.*;

import java.time.ZonedDateTime;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BudgetMasterViewModel {

    private Long id;
    private UUID uuid;
    private long companyCode;
    private String name;
    private boolean isActive;

    private String createdUserName;
    private String createdTimestamp;

    private String updatedUserName;
    private String updatedTimestamp;

    private String deletedUserName;
    private String deletedTimestamp;
    
 
    public void setBudgetMaster( BudgetMaster budgetMaster) {
		
    	this.setId(budgetMaster.getId());
    	this.setUuid(budgetMaster.getUuid());
    	this.setCompanyCode(budgetMaster.getCompanyCode());
    	this.setName(budgetMaster.getName());
    	this.setActive(budgetMaster.isActive());
    	
    	this.setCreatedUserName("FirstName LastName");
    	this.setCreatedTimestamp(DateTimeUtils.formatTimestamp(budgetMaster.getCreatedTimestamp()));

    	this.setUpdatedUserName("FirstName LastName");
    	this.setUpdatedTimestamp(DateTimeUtils.formatTimestamp(budgetMaster.getUpdatedTimestamp()));
    	
    	this.setDeletedUserName("FirstName LastName");
    	this.setDeletedTimestamp(DateTimeUtils.formatTimestamp(budgetMaster.getDeletedTimestamp()));
    	
	}
}
