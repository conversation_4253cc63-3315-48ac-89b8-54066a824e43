package in.taxgenie.pay_expense_pvv.viewmodels.budget;

import lombok.*;

import java.util.ArrayList;
import java.util.List;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BudgetViewModel {
    private long id;
    private String label;
    private Boolean expanded = true;
    private BudgetDataViewModel data;
    private List<BudgetFrequencyMappingViewModel> budgetByFrequencyMapList;
    private List<BudgetViewModel> children = new ArrayList<>();

    public void addChildren(BudgetViewModel child) {
        this.children.add(child);
    }
}
