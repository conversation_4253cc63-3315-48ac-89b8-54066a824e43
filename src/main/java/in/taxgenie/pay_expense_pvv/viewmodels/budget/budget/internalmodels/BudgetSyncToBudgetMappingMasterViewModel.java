package in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels;

public class BudgetSyncToBudgetMappingMasterViewModel {
    private long syncId;
    private String syncName;
    private long masterId;
    private String masterName;

    public BudgetSyncToBudgetMappingMasterViewModel() {
    }

    public BudgetSyncToBudgetMappingMasterViewModel(long syncId, String syncName, long masterId, String masterName) {
        this.syncId = syncId;
        this.syncName = syncName;
        this.masterId = masterId;
        this.masterName = masterName;
    }

    public long getSyncId() {
        return syncId;
    }

    public void setSyncId(long syncId) {
        this.syncId = syncId;
    }

    public String getSyncName() {
        return syncName;
    }

    public void setSyncName(String syncName) {
        this.syncName = syncName;
    }

    public long getMasterId() {
        return masterId;
    }

    public void setMasterId(long masterId) {
        this.masterId = masterId;
    }

    public String getMasterName() {
        return masterName;
    }

    public void setMasterName(String masterName) {
        this.masterName = masterName;
    }
}
