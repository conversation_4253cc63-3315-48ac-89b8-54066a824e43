package in.taxgenie.pay_expense_pvv.viewmodels.budget.structuremaster;

import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
public class BudgetStructureMasterViewModelBase {

    private Long id;
    private UUID uuid;

    private String structureName;
    private Integer startMonthIndex;
    private String financialYear;
    private Integer currentStep;
    private Long documentMetadataId;
    private boolean isActive;
    private Boolean isApproved;
    private String status;

}
