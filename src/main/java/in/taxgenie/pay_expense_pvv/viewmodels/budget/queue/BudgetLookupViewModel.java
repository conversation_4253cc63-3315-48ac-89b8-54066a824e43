package in.taxgenie.pay_expense_pvv.viewmodels.budget.queue;

import java.math.BigDecimal;

public class BudgetLookupViewModel {

    private Long id;
    private String structureCode;
    private String structureName;

    public BudgetLookupViewModel(Long id, String structureCode, String structureName) {
        this.id = id;
        this.structureCode = structureCode;
        this.structureName = structureName;
    }
    public BudgetLookupViewModel(Long id, String structureName) {
        this.id = id;
        this.structureName = structureName;
    }

    public BudgetLookupViewModel() {}

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    public String getStructureName() {
        return structureName;
    }

    public void setStructureName(String structureName) {
        this.structureName = structureName;
    }
}
