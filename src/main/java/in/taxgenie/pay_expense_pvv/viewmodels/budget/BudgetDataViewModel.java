package in.taxgenie.pay_expense_pvv.viewmodels.budget;

import lombok.*;

import java.math.BigDecimal;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BudgetDataViewModel {
    long id;
    long budgetSyncMasterId;
    private String budgetSyncMasterValue;
    private BigDecimal total;
    private BigDecimal unallocated;
    private BigDecimal softLocked;
    private BigDecimal locked;
    private BigDecimal treasury;
}
