package in.taxgenie.pay_expense_pvv.viewmodels.budget;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

@Data
public class ConsumptionDetailsViewModel {
    private Long id;
    private String documentIdentifier;
    private String docNo;
    private Boolean hasPrecedingDocument;
    private String createdDate;
    private String reportStatus;
    private String submitDate;
    private String lastActionedAt;
    private BigDecimal amount;
    private BigDecimal balanceAmount;
    private Long documentSubgroupId;
    private String source;
    private String vendorName;
    private String vendorCode;
    private String gstin;
    private String pan;
    private Boolean msmeStatus;
    private String gstinStatus;
    private String documentDate;

    public ConsumptionDetailsViewModel(Long id, String documentIdentifier, String docNo, Boolean hasPrecedingDocument, LocalDate createdDate, ReportStatus reportStatus, LocalDate submitDate, ZonedDateTime lastActionedAt, BigDecimal amount, BigDecimal balanceAmount, Long documentSubgroupId, String source, String vendorName, String vendorCode, String gstin, Boolean msmeStatus, String gstinStatus, LocalDate documentDate) {
        this.id = id;
        this.documentIdentifier = documentIdentifier;
        this.docNo = docNo;
        this.hasPrecedingDocument = hasPrecedingDocument;
        this.createdDate = DateTimeUtils.formatDateJPQL(createdDate);
        this.reportStatus = reportStatus.name();
        this.submitDate = DateTimeUtils.formatDateJPQL(submitDate);
        this.lastActionedAt =  lastActionedAt != null ?  lastActionedAt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null;
        this.amount = amount;
        this.balanceAmount = balanceAmount;
        this.documentSubgroupId = documentSubgroupId;
        this.source = source;
        this.vendorName = vendorName;
        this.vendorCode = vendorCode;
        this.gstin = gstin;
        this.pan = this.gstin == null || this.gstin.length() != 15 ? null : this.gstin.substring(2, 12);
        this.msmeStatus = msmeStatus;
        this.gstinStatus = gstinStatus;
        this.documentDate = DateTimeUtils.formatDateJPQL(documentDate);
    }

    public ConsumptionDetailsViewModel() {

    }
}
