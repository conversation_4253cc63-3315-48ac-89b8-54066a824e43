package in.taxgenie.pay_expense_pvv.viewmodels.budget.mappingmaster;

public class BudgetMappingMasterViewModelBase {
    private long budgetStructureMasterId;
    private long budgetMasterId;
    private String displayName;

    public BudgetMappingMasterViewModelBase() {}

    public BudgetMappingMasterViewModelBase(long budgetStructureMasterId, long budgetMasterId, String displayName) {
        this.budgetStructureMasterId = budgetStructureMasterId;
        this.budgetMasterId = budgetMasterId;
        this.displayName = displayName;
    }

    public BudgetMappingMasterViewModelBase(long budgetMasterId, String displayName) {
        this.budgetMasterId = budgetMasterId;
        this.displayName = displayName;
    }

    public long getBudgetStructureMasterId() {
        return budgetStructureMasterId;
    }

    public void setBudgetStructureMasterId(long budgetStructureMasterId) {
        this.budgetStructureMasterId = budgetStructureMasterId;
    }

    public long getBudgetMasterId() {
        return budgetMasterId;
    }

    public void setBudgetMasterId(long budgetMasterId) {
        this.budgetMasterId = budgetMasterId;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
}
