package in.taxgenie.pay_expense_pvv.viewmodels.item_master;

import com.querydsl.core.types.dsl.BooleanPath;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ItemMasterQueueViewModel {


    private String itemCode;

    private String name;
    private ItemType type;

    private String unitOfMeasure;
    private String description;

    private String hsn;
    private String hsnOrSac;

    private String gstRate;
    private BigDecimal rate;

    private Double quantity;
    private String itemUuid;

    private Boolean status;

    public ItemMasterQueueViewModel(
                                    String itemCode, String name, ItemType type,
                                    String unitOfMeasure, String description,
                                    String hsn, String hsnOrSac,
                                    Double gstRate, BigDecimal rate, Double quantity, String itemUuid, Boolean status) {

        this.itemCode = itemCode;
        this.name = name;
        this.type = type;
        this.unitOfMeasure = unitOfMeasure;
        this.description = description;
        this.hsn = hsn;
        this.hsnOrSac = hsnOrSac;
        this.gstRate = String.format("%.0f", gstRate);
        this.rate = rate;
        this.quantity = quantity;
        this.itemUuid = itemUuid;
        this.status = status;
    }
}
