package in.taxgenie.pay_expense_pvv.viewmodels.item_master;

import com.fasterxml.jackson.annotation.JsonProperty;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ItemMaster;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SupplierViewModel {

    private Long id;
    private String itemCode;

    @JsonProperty("supplier_id")
    private Long vendorId;

    private String supplierName;
    private String supplierItemCode;

    private String rate;
    private Double quantity;


    public void getViewModel(ItemMaster itemMaster) {

        this.id = itemMaster.getId();
        this.itemCode = itemMaster.getItemCode();


        this.vendorId = itemMaster.getVendorId();
        this.supplierName = itemMaster.getSupplierName();
        this.supplierItemCode = itemMaster.getSupplierItemCode();

        this.rate = String.valueOf(itemMaster.getSupplierRate());
        this.quantity = itemMaster.getSupplierQuantity();

    }

    public ItemMaster getUpdateEntityFromViewModel(ItemMaster itemMaster, IAuthContextViewModel auth) {

        itemMaster.setVendorId(this.getVendorId());

        itemMaster.setSupplierName(this.supplierName);
        itemMaster.setSupplierItemCode(this.supplierItemCode);

        itemMaster.setSupplierRate(new BigDecimal(this.rate));
        itemMaster.setSupplierQuantity(this.quantity);

        itemMaster.setUpdatedBy((int) auth.getUserId());
        itemMaster.setUpdatedAt(DateTimeUtils.getCurrentTimestamp());

        itemMaster.setStatus(true);

        return itemMaster;

    }

    public ItemMaster getCreateEntityFromViewModel(ItemMaster itemMasterOld, IAuthContextViewModel auth) {

        ItemMaster newItemMaster = new ItemMaster();

        //Item Property
        newItemMaster.setProductId(itemMasterOld.getProductId());
        newItemMaster.setCompanyId(itemMasterOld.getCompanyId());
        newItemMaster.setBudgetId(itemMasterOld.getBudgetId());
        newItemMaster.setName(itemMasterOld.getName());
        newItemMaster.setItemCode(itemMasterOld.getItemCode());
        newItemMaster.setDescription(itemMasterOld.getDescription());
        newItemMaster.setType(itemMasterOld.getType());
        newItemMaster.setQuantity(itemMasterOld.getQuantity());
        newItemMaster.setRate(itemMasterOld.getRate());
        newItemMaster.setHsn(itemMasterOld.getHsn());
        newItemMaster.setHsnOrSac(itemMasterOld.getHsnOrSac());
        newItemMaster.setInitialStock(itemMasterOld.getInitialStock());
        newItemMaster.setSource(itemMasterOld.getSource());
        newItemMaster.setCurrency(itemMasterOld.getCurrency());
        newItemMaster.setGstRate(itemMasterOld.getGstRate());
        newItemMaster.setUnitOfMeasure(itemMasterOld.getUnitOfMeasure());
        newItemMaster.setCompanyCode(auth.getCompanyCode());
        newItemMaster.setItemUuid(itemMasterOld.getItemUuid());

        newItemMaster.setCreatedBy((int) auth.getUserId());
        newItemMaster.setCreatedAt(DateTimeUtils.getCurrentTimestamp());

        newItemMaster.setVendorId(this.vendorId);

        newItemMaster.setSupplierName(this.supplierName);
        newItemMaster.setSupplierItemCode(this.supplierItemCode);

        newItemMaster.setSupplierRate(BigDecimal.valueOf(Long.parseLong(this.rate)));
        newItemMaster.setSupplierQuantity(this.quantity);

        newItemMaster.setStatus(true);

        return newItemMaster;
    }
}
