package in.taxgenie.pay_expense_pvv.viewmodels.item_master;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ItemMaster;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.UUID;

@Data
public class CreateItemMasterViewModel {

    private String type;
    private String itemCode;
    private String unitOfMeasure;

    private String hsn;
    private String hsnOrSac;
    private Double quantity;
    private String gstRate;

    private BigDecimal rate;

    private String currency;
    private String description;
    private String name;

    private String uuid;

    public CreateItemMasterViewModel() {
    }

    public CreateItemMasterViewModel(String type, String itemCode, String unitOfMeasure, String hsn, String hsnOrSac,
                                     Double quantity, String gstRate, BigDecimal rate, String currency,
                                     String description, String name, String itemUuid) {
        this.type = type;
        this.itemCode = itemCode;
        this.unitOfMeasure = unitOfMeasure;
        this.hsn = hsn;
        this.hsnOrSac = hsnOrSac;
        this.quantity = quantity;
        this.gstRate = gstRate;
        this.rate = rate;
        this.currency = currency;
        this.description = description;
        this.name = name;
        this.uuid = itemUuid;
    }

    public ItemMaster getCreateEntityFromViewModel(IAuthContextViewModel auth){
        ItemMaster itemMaster = new ItemMaster();

        itemMaster.setType(ItemType.fromString(this.type));
        itemMaster.setItemCode(this.itemCode);
        itemMaster.setUnitOfMeasure(this.unitOfMeasure);

        itemMaster.setHsn(this.hsn);
        itemMaster.setHsnOrSac(this.hsnOrSac);
        itemMaster.setQuantity(this.quantity);
        itemMaster.setGstRate( this.gstRate != null ?Double.valueOf(this.gstRate) : 0.0);

        itemMaster.setRate(this.rate);
        itemMaster.setCurrency(this.currency);
        itemMaster.setDescription(this.description);
        itemMaster.setName(this.name);

        itemMaster.setCompanyCode(auth.getCompanyCode());

        itemMaster.setStatus(true);

        itemMaster.setCreatedBy((int) auth.getUserId());
        itemMaster.setCreatedAt(DateTimeUtils.getCurrentTimestamp());

        UUID uuid = UUID.randomUUID();
        itemMaster.setItemUuid(uuid.toString());

        return itemMaster;
    }

    public ItemMaster getUpdateEntityFromViewModel(ItemMaster itemMaster, IAuthContextViewModel auth){

        itemMaster.setType(ItemType.fromString(this.type));
        itemMaster.setItemCode(this.itemCode);
        itemMaster.setUnitOfMeasure(this.unitOfMeasure);

        itemMaster.setHsn(this.hsn);
        itemMaster.setHsnOrSac(this.hsnOrSac);
        itemMaster.setQuantity(this.quantity);
        itemMaster.setGstRate(Double.valueOf(this.gstRate));

        itemMaster.setRate(this.rate);
        itemMaster.setCurrency(this.currency);
        itemMaster.setDescription(this.description);
        itemMaster.setName(this.name);

        itemMaster.setStatus(true);

        itemMaster.setUpdatedBy((int) auth.getUserId());
        itemMaster.setUpdatedAt(DateTimeUtils.getCurrentTimestamp());

        itemMaster .setItemUuid(this.uuid);
        return itemMaster;
    }

    public void getViewModel(ItemMaster itemMaster) {

        this.type = itemMaster.getType().getFormattedName();
        this.itemCode = itemMaster.getItemCode();
        this.unitOfMeasure = itemMaster.getUnitOfMeasure();

        this.hsn = itemMaster.getHsn();
        this.hsnOrSac = itemMaster.getHsnOrSac();
        this.quantity = itemMaster.getQuantity();
        this.gstRate = String.format("%.0f", itemMaster.getGstRate());

        this.rate = itemMaster.getRate();
        this.currency = itemMaster.getCurrency();
        this.description = itemMaster.getDescription();
        this.name = itemMaster.getName();

        this.uuid = itemMaster.getItemUuid();
    }
}
