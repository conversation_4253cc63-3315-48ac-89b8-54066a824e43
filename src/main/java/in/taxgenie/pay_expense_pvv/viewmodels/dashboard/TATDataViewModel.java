package in.taxgenie.pay_expense_pvv.viewmodels.dashboard;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class TATDataViewModel {

    private String ageing;
    private Long noOfInvoices;
    private BigDecimal totalInvoiceAmount;
    private BigDecimal mix;
    private String indicator;

    public TATDataViewModel(String ageing, String indicator, Long noOfInvoices, BigDecimal totalInvoiceAmount, BigDecimal mix) {
        this.ageing = ageing;
        this.indicator = indicator;
        this.noOfInvoices = noOfInvoices;
        this.totalInvoiceAmount = totalInvoiceAmount;
        this.mix = mix;
    }
}
