package in.taxgenie.pay_expense_pvv.viewmodels.dashboard;

import java.util.ArrayList;
import java.util.List;

public class EmployeeDashboardViewModel {
    private EmployeeDetailViewModel employeeDetail;
    private List<EmployeeMonthlyClaimLine> monthlyClaimLines = new ArrayList<>();
    private EmployeeStatisticsViewModel statistics;

    public EmployeeDetailViewModel getEmployeeDetail() {
        return employeeDetail;
    }

    public void setEmployeeDetail(EmployeeDetailViewModel employeeDetail) {
        this.employeeDetail = employeeDetail;
    }

    public List<EmployeeMonthlyClaimLine> getMonthlyClaimLines() {
        return monthlyClaimLines;
    }

    public void addMonthlyClaim(EmployeeMonthlyClaimLine line) {
        this.monthlyClaimLines.add(line);
    }

    public EmployeeStatisticsViewModel getStatistics() {
        return statistics;
    }

    public void setStatistics(EmployeeStatisticsViewModel statistics) {
        this.statistics = statistics;
    }
}
