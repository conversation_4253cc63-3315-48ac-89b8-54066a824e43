package in.taxgenie.pay_expense_pvv.viewmodels.dashboard;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.time.ZonedDateTime;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DashboardConfigurationViewModel {
    private Long id;

    private Integer widgetId;
    private String name;

    private Integer sequenceId;
    private Long companyCode; // can add userId if user or role based;

    private Boolean isActive;
    private Boolean isMandatory;
    @JsonIgnore
    private ZonedDateTime subscriptionChangeDate;
    @JsonIgnore
    private Long creatingUserId;

    public DashboardConfigurationViewModel(Long id, Integer widgetId, String name, Integer sequenceId, Boolean isActive, Boolean isMandatory, Long companyCode){
        this.id =  id;
        this.widgetId = widgetId;
        this.name = name;
        this.sequenceId = sequenceId;
        this.isActive = isActive;
        this.isMandatory = isMandatory;
        this.companyCode = companyCode;
    }
}
