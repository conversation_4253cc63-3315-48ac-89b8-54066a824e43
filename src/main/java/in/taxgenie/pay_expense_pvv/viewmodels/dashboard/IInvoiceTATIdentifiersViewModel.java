package in.taxgenie.pay_expense_pvv.viewmodels.dashboard;

import java.util.List;

public class IInvoiceTATIdentifiersViewModel {
    private Integer minTat;
    private Integer maxTat;
    private String approverType;
    private List<String> documentIdentifiers;

    public IInvoiceTATIdentifiersViewModel() {
    }

    public IInvoiceTATIdentifiersViewModel(Integer minTat, Integer maxTat, String approverType, List<String> documentIdentifiers) {
        this.minTat = minTat;
        this.maxTat = maxTat;
        this.approverType = approverType;
        this.documentIdentifiers = documentIdentifiers;
    }

    public Integer getMinTat() {
        return minTat;
    }

    public void setMinTat(Integer minTat) {
        this.minTat = minTat;
    }

    public Integer getMaxTat() {
        return maxTat;
    }

    public void setMaxTat(Integer maxTat) {
        this.maxTat = maxTat;
    }

    public String getApproverType() {
        return approverType;
    }

    public void setApproverType(String approverType) {
        this.approverType = approverType;
    }

    public List<String> getDocumentIdentifiers() {
        return documentIdentifiers;
    }

    public void setDocumentIdentifiers(List<String> documentIdentifiers) {
        this.documentIdentifiers = documentIdentifiers;
    }
}
