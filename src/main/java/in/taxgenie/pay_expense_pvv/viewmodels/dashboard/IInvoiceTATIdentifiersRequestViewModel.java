package in.taxgenie.pay_expense_pvv.viewmodels.dashboard;

import java.util.Map;

public class IInvoiceTATIdentifiersRequestViewModel {
    private Integer minTat;
    private Integer maxTat;
    private String approverType;
    private Map<String, String> metadataFilters;

    public IInvoiceTATIdentifiersRequestViewModel() {
    }

    public IInvoiceTATIdentifiersRequestViewModel(Integer minTat, Integer maxTat, String approverType, Map<String, String> metadataFilters) {
        this.minTat = minTat;
        this.maxTat = maxTat;
        this.approverType = approverType;
        this.metadataFilters = metadataFilters;
    }

    public Integer getMinTat() {
        return minTat;
    }

    public void setMinTat(Integer minTat) {
        this.minTat = minTat;
    }

    public Integer getMaxTat() {
        return maxTat;
    }

    public void setMaxTat(Integer maxTat) {
        this.maxTat = maxTat;
    }

    public String getApproverType() {
        return approverType;
    }

    public void setApproverType(String approverType) {
        this.approverType = approverType;
    }

    public Map<String, String> getMetadataFilters() {
        return metadataFilters;
    }

    public void setMetadataFilters(Map<String, String> metadataFilters) {
        this.metadataFilters = metadataFilters;
    }

    @Override
    public String toString() {
        return "IInvoiceTATIdentifiersRequestViewModel{" +
                "minTat=" + minTat +
                ", maxTat=" + maxTat +
                ", approverType='" + approverType + '\'' +
                ", metadataFilters=" + metadataFilters +
                '}';
    }

    public boolean isValid() {
        return this.approverType.isEmpty();
    }
}
