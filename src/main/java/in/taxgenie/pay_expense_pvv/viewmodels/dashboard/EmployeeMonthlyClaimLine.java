package in.taxgenie.pay_expense_pvv.viewmodels.dashboard;

import java.math.BigDecimal;

public class EmployeeMonthlyClaimLine {
    private int month;
    private String monthCode;
    private int year;
    private BigDecimal totalClaim;
    private BigDecimal totalPaid;
    private BigDecimal totalTds;

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public String getMonthCode() {
        return monthCode;
    }

    public void setMonthCode(String monthCode) {
        this.monthCode = monthCode;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public BigDecimal getTotalClaim() {
        return totalClaim;
    }

    public void setTotalClaim(BigDecimal totalClaim) {
        this.totalClaim = totalClaim;
    }

    public BigDecimal getTotalPaid() {
        return totalPaid;
    }

    public void setTotalPaid(BigDecimal totalPaid) {
        this.totalPaid = totalPaid;
    }

    public BigDecimal getTotalTds() {
        return totalTds;
    }

    public void setTotalTds(BigDecimal totalTds) {
        this.totalTds = totalTds;
    }
}
