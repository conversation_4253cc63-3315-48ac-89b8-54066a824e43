package in.taxgenie.pay_expense_pvv.viewmodels.dashboard;

import in.taxgenie.pay_expense_pvv.cem.employee.implementations.EmployeeStatus;

import java.time.LocalDate;

public class EmployeeDetailViewModel {
    private String firstName;
    private String lastName;
    private LocalDate dateOfJoining;
    private String employeeCode;
    private String systemIdCode;
    private String glMainAccountCode;
    private String gender;
    private String email;
    private String mobileNumber;
    private LocalDate dateOfBirth;
    private String grade;
    private String type;
    private String address;
    private String stateCode;
    private String cityCode;
    private String branchCode;
    private String departmentCode;
    private String costCentre;
    private String profitCentre;
    private EmployeeStatus status;
    private String reportingManager;
    private boolean canBeDelegated;
    private String branchName;
    private String departmentName;
    private String manager;
    private String managerEmail;
    private String managerEmployeeCode;

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public LocalDate getDateOfJoining() {
        return dateOfJoining;
    }

    public void setDateOfJoining(LocalDate dateOfJoining) {
        this.dateOfJoining = dateOfJoining;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    public String getGlMainAccountCode() {
        return glMainAccountCode;
    }

    public void setGlMainAccountCode(String glMainAccountCode) {
        this.glMainAccountCode = glMainAccountCode;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getStateCode() {
        return stateCode;
    }

    public void setStateCode(String stateCode) {
        this.stateCode = stateCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getCostCentre() {
        return costCentre;
    }

    public void setCostCentre(String costCentre) {
        this.costCentre = costCentre;
    }

    public String getProfitCentre() {
        return profitCentre;
    }

    public void setProfitCentre(String profitCentre) {
        this.profitCentre = profitCentre;
    }

    public EmployeeStatus getStatus() {
        return status;
    }

    public void setStatus(EmployeeStatus status) {
        this.status = status;
    }

    public String getReportingManager() {
        return reportingManager;
    }

    public void setReportingManager(String reportingManager) {
        this.reportingManager = reportingManager;
    }

    public boolean isCanBeDelegated() {
        return canBeDelegated;
    }

    public void setCanBeDelegated(boolean canBeDelegated) {
        this.canBeDelegated = canBeDelegated;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public String getManagerEmail() {
        return managerEmail;
    }

    public void setManagerEmail(String managerEmail) {
        this.managerEmail = managerEmail;
    }

    public String getManagerEmployeeCode() {
        return managerEmployeeCode;
    }

    public void setManagerEmployeeCode(String managerEmployeeCode) {
        this.managerEmployeeCode = managerEmployeeCode;
    }

    public String getSystemIdCode() {
        return systemIdCode;
    }

    public void setSystemIdCode(String systemIdCode) {
        this.systemIdCode = systemIdCode;
    }
}
