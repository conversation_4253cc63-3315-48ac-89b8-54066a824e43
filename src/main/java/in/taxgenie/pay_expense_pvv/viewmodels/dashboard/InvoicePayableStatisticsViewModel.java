package in.taxgenie.pay_expense_pvv.viewmodels.dashboard;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class InvoicePayableStatisticsViewModel {
    private Long totalVendorCount;
    private String totalVendorInsight;
    private Long totalInvoiceCount;
    private String totalInvoiceCountInsight;
    private BigDecimal totalTaxableAmount;
    private String totalTaxableAmountInsight;
    private BigDecimal totalInvoiceAmount;
    private String totalInvoiceAmountInsight;
    private BigDecimal totalTaxAmount;
    private String totalTaxAmountInsight;

    public InvoicePayableStatisticsViewModel(Long totalVendorCount, Long totalInvoiceCount, BigDecimal totalTaxableAmount, BigDecimal totalInvoiceAmount, BigDecimal totalTaxAmount) {
        this.totalVendorCount = totalVendorCount;
        this.totalInvoiceCount = totalInvoiceCount;
        this.totalTaxableAmount = totalTaxableAmount;
        this.totalInvoiceAmount = totalInvoiceAmount;
        this.totalTaxAmount = totalTaxAmount;
    }

    public InvoicePayableStatisticsViewModel(Long totalVendorCount, String totalVendorInsight, Long totalInvoiceCount, String totalInvoiceCountInsight, BigDecimal totalTaxableAmount, String totalTaxableAmountInsight, BigDecimal totalInvoiceAmount, String totalInvoiceAmountInsight, BigDecimal totalTaxAmount, String totalTaxAmountInsight) {
        this.totalVendorCount = totalVendorCount;
        this.totalVendorInsight = totalVendorInsight;
        this.totalInvoiceCount = totalInvoiceCount;
        this.totalInvoiceCountInsight = totalInvoiceCountInsight;
        this.totalTaxableAmount = totalTaxableAmount;
        this.totalTaxableAmountInsight = totalTaxableAmountInsight;
        this.totalInvoiceAmount = totalInvoiceAmount;
        this.totalInvoiceAmountInsight = totalInvoiceAmountInsight;
        this.totalTaxAmount = totalTaxAmount;
        this.totalTaxAmountInsight = totalTaxAmountInsight;
    }
}

