package in.taxgenie.pay_expense_pvv.viewmodels.dashboard;

import java.time.LocalDate;

public class EmployeeStatisticsViewModel {
    private long draftReports;
    private long acceptedReports;
    private long sentBackReports;
    private long submittedReports;
    private long discardedReports;
    private long paidReports;
    private LocalDate startDate;
    private LocalDate endDate;

    public long getDraftReports() {
        return draftReports;
    }

    public void setDraftReports(long draftReports) {
        this.draftReports = draftReports;
    }

    public long getAcceptedReports() {
        return acceptedReports;
    }

    public void setAcceptedReports(long acceptedReports) {
        this.acceptedReports = acceptedReports;
    }

    public long getSentBackReports() {
        return sentBackReports;
    }

    public void setSentBackReports(long sentBackReports) {
        this.sentBackReports = sentBackReports;
    }

    public long getSubmittedReports() {
        return submittedReports;
    }

    public void setSubmittedReports(long submittedReports) {
        this.submittedReports = submittedReports;
    }

    public long getDiscardedReports() {
        return discardedReports;
    }

    public void setDiscardedReports(long discardedReports) {
        this.discardedReports = discardedReports;
    }

    public long getPaidReports() {
        return paidReports;
    }

    public void setPaidReports(long paidReports) {
        this.paidReports = paidReports;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }
}
