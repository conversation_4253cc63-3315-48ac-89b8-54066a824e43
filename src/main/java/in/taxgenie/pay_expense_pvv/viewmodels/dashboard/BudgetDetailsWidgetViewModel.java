package in.taxgenie.pay_expense_pvv.viewmodels.dashboard;

import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BudgetDetailsWidgetViewModel {
    private Long budgetId;
    private Long structureMasterId;
    private BigDecimal amount;
    private BigDecimal balanceAmount;
    private String budgetCode;
    private String type;
    private Long purchaseOrderCount;
    private Long invoiceCount;

    public BudgetDetailsWidgetViewModel(Long budgetId, Long structureMasterId, BigDecimal amount, BigDecimal balanceAmount, String budgetCode, ExpenseType type) {
        this.budgetId = budgetId;
        this.structureMasterId = structureMasterId;
        this.amount = amount;
        this.balanceAmount = balanceAmount;
        this.budgetCode = budgetCode;
        this.type = null == type ? null :type.name();
    }
}
