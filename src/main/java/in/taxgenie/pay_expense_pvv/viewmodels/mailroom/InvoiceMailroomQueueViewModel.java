package in.taxgenie.pay_expense_pvv.viewmodels.mailroom;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
public class InvoiceMailroomQueueViewModel {

    private Long id;

    private String supplierName;
    private String irn;
    private String invoiceNumber;
    private BigDecimal totalAmount;
    private String invoiceType;


    public InvoiceMailroomQueueViewModel(){}

    public InvoiceMailroomQueueViewModel(Long id, String supplierName, String irn, String invoiceNumber, BigDecimal totalAmount, String invoiceType) {
        this.id = id;
        this.supplierName = supplierName;
        this.irn = irn;
        this.invoiceNumber = invoiceNumber;
        this.totalAmount = totalAmount;
        this.invoiceType = invoiceType;
    }
}
