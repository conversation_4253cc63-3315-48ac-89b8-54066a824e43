package in.taxgenie.pay_expense_pvv.viewmodels.mailroom;

import lombok.Data;

@Data
public class InvoiceSyncReportQueueViewModel {

    private String requestId;
    private int irnCount;
    private String gstin;
    private String financialYear;
    private String message;
    private String status;
    private String syncedBy;

    String syncedDate;
    String syncedTime;

    public InvoiceSyncReportQueueViewModel(String requestId,
                             int irnCount, String gstin,
                             String financialYear,
                             String message, String status, String syncedBy,
                                           String syncedDate, String syncedTime) {
        this.requestId = requestId;
        this.irnCount = irnCount;
        this.gstin = gstin;
        this.financialYear = financialYear;
        this.message = message;
        this.status = status;
        this.syncedBy = syncedBy;
        this.syncedDate = syncedDate;
        this.syncedTime = syncedTime;
    }

}
