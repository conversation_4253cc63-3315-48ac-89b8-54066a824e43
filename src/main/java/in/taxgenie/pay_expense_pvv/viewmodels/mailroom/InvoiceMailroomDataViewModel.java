package in.taxgenie.pay_expense_pvv.viewmodels.mailroom;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
public class InvoiceMailroomDataViewModel {

    private Long id;

    private String supplierName;
    private String supplierCode;
    private String gstinNo;
    private String activeStatus;
    private Boolean isMSME;
    private Boolean isEInvoice;

    private String irn;
    private String invoiceNumber;
    private String description;
    private LocalDate documentDate;
    private BigDecimal totalAmount;
    private LocalDate syncDate;

    private String invoiceType;


    public InvoiceMailroomDataViewModel(){}

    public InvoiceMailroomDataViewModel(
            Long id, String supplierName, String supplierCode,
            String gstinNo, String activeStatus, Boolean isMSME,
            Boolean isEInvoice, String irn, String invoiceNumber,
            String description, LocalDate documentDate,
            BigDecimal totalAmount, LocalDate syncDate,
            String invoiceType) {
        this.id = id;
        this.supplierName = supplierName;
        this.supplierCode = supplierCode;
        this.gstinNo = gstinNo;
        this.activeStatus = activeStatus;
        this.isMSME = isMSME;
        this.isEInvoice = isEInvoice;
        this.irn = irn;
        this.invoiceNumber = invoiceNumber;
        this.description = description;
        this.documentDate = documentDate;
        this.totalAmount = totalAmount;
        this.syncDate = syncDate;
        this.invoiceType = invoiceType;
    }
}
