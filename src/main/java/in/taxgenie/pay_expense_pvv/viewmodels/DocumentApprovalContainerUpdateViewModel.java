package in.taxgenie.pay_expense_pvv.viewmodels;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentApprovalContainerUpdateViewModel {
    private long id;

    private String documentIdentifier;

    private LocalDate createdDate;
    private LocalDate startDate;
    private LocalDate endDate;

    @NotBlank
    @Size(max = 200)
    private String reportTitle;

    @Size(max = 200)
    private String description;

    @Size(max = 100)
    private String purpose;

    @Positive
    private BigDecimal approvalContainerClaimAmount;


    //  GST computation
    @Positive
    private BigDecimal approvalContainerSgstAmount;
    @Positive
    private BigDecimal approvalContainerCgstAmount;
    @Positive
    private BigDecimal approvalContainerIgstAmount;
    @Positive
    private BigDecimal approvalContainerTaxableAmount;

    private long documentMetadataId;

}
