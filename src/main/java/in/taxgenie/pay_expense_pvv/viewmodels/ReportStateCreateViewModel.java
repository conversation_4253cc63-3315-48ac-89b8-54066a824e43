package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;

public class ReportStateCreateViewModel {
    @Positive
    private int level;

    @Size(max = 200)
    private String remarks;

    @NotNull
    private ExpenseActionStatus status;

    @Positive
    private long expenseReportId;


    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public ExpenseActionStatus getStatus() {
        return status;
    }

    public void setStatus(ExpenseActionStatus status) {
        this.status = status;
    }

    public long getExpenseReportId() {
        return expenseReportId;
    }

    public void setExpenseReportId(long expenseReportId) {
        this.expenseReportId = expenseReportId;
    }
}
