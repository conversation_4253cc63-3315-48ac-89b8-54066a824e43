package in.taxgenie.pay_expense_pvv.viewmodels.po;

import in.taxgenie.pay_expense_pvv.entities.Location;
import in.taxgenie.pay_expense_pvv.entities.LookupData;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SegmentDetailsViewModel {

    private LookupData ratioCategoryId;
    private Location locationId;
    List<SegmentsViewModel> segments;

}
