package in.taxgenie.pay_expense_pvv.viewmodels.po;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class POPreviewViewModel {

    private Long id;
    private boolean hasPrecedingDocument;
    private String poType;
    private String supplierName;
    private String financialYear;
    private String dateOfDocument;
    private String requestorName;
    private BigDecimal totalPRAmount = BigDecimal.ZERO;
    private String paymentTerm;
    private ReportStatus reportStatus;
//    private String budgetCode;
//    private BigDecimal budgetAmount = BigDecimal.ZERO;
    private BigDecimal poAmountWOGst = BigDecimal.ZERO;
    private BigDecimal advanceAmount = BigDecimal.ZERO;
    private String costCenter;
    private String profitCenter;
    private String prDescription;
    private String docNo;
    private String subgroupFieldsJson;
    private BigDecimal totalPOAmount = BigDecimal.ZERO;;

    List<PurchaseOrderItemDetailsViewModel> poItems;
}
