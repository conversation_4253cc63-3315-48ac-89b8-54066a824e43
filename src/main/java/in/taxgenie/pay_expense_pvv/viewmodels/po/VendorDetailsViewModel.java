package in.taxgenie.pay_expense_pvv.viewmodels.po;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class VendorDetailsViewModel {
    private Long id;
    private Long vendorId;
    private String vendorName;
    private String vendorCode;
    private Integer productId;
    private Integer companyId;
    private String gstin;
    private String pan;
    private String address;
    private boolean status;
    private String contact;
    private String email;
    //TODO: item list against vendor?
}
