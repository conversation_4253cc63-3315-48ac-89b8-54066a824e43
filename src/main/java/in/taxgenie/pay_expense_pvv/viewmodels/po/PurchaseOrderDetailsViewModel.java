package in.taxgenie.pay_expense_pvv.viewmodels.po;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderDetailsViewModel {
    private Long id;
    private Boolean hasPrecedingDocument;
    private Integer docTypeId;
    private Integer stateId;
    private String orderNo;
    private Integer poStatus;
    private Integer paymentTermsId;
    private String documentDate;
    private String deliveryDate;
    @Size(max = 255, message = "Description cannot exceed 255 characters.")
    private String description;
    private Integer financialYearId;
    private String vendorName;
    private String createdAt;
    private String createdBy;
    private String updatedAt;
    private String updatedBy;
    private Boolean isRatioBasedPO;
    private BigDecimal taxableValue;
    private Integer segmentMasterId;
    private BigDecimal totalPOValue;
    private String orderValidFrom;
    private String orderValidTo;
    private String startDate;
    private String endDate;
    private String poDate;

    // PO sync
    private String currency;
    private String expenditureType;
    private String purchasingDocType;
    private String documentIdentifier;
}
