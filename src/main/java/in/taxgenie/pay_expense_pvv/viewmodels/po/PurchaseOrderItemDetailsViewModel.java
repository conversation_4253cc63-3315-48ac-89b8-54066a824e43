package in.taxgenie.pay_expense_pvv.viewmodels.po;

import com.fasterxml.jackson.annotation.JsonProperty;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.MasterDataForERPViewModel;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderItemDetailsViewModel {

    private Long id;
    private Long prItemId;
    private Long itemMasterId;
    private String docNo;
    @JsonProperty("budgetId")
    private Long budgetNodeId;
    private String budgetCode;
    private String displayName;
    @JsonProperty("itemType")
    private String type;
    @JsonProperty("itemName")
    private String name;
    private String itemCode;
    private String hsn;
    private Integer unitOfMeasureId;
    private String unitOfMeasure;
    @Size(max = 500, message = "Item Description cannot exceed 500 characters.")
    @JsonProperty("itemDescription")
    private String description;
    private Double quantity;
    private Double selectedQuantity;
    private BigDecimal unitRate;
    private String currency;

    @JsonProperty("gstRate")
    private Double taxPercentage;
    private Double cessPercentage;
    private Double stateCessPercentage;

    private BigDecimal amountWithoutGst; // no value in invoice
    private BigDecimal totalGstAmount; // totalWithTax in invoice


    private BigDecimal total; // amountWithoutGst in invoice

    private BigDecimal discount;
    private BigDecimal assessableAmount; // total - discount
    private BigDecimal cessAmt;
    private BigDecimal stateCessAmt;
    private BigDecimal cessNonAdvolAmount;
    private BigDecimal stateCessNonAdvolAmount;
    private BigDecimal otherCharges;

    private BigDecimal igst;
    private BigDecimal cgst;
    private BigDecimal sgst;
    private Integer quotationId;
    private Integer vendorId;
    private String vendorName;
    private String createdBy;
    private String createdAt;

    private Boolean isCatalog;
    private String requesterRemark;

    private List<MasterDataForERPViewModel> masters;

    private Long glId;
    private String glCode;
    private String glName;

    private Long purchaseOrderHeaderId;

    public PurchaseOrderItemDetailsViewModel(Long id, String docNo, ItemType type, String name, String hsn, String itemCode, String description, Double quantity,
                                          BigDecimal unitRate, Double taxPercentage, BigDecimal amountWithoutGst, BigDecimal totalGstAmount, BigDecimal total,
                                          Integer quotationId, Integer vendorId, String vendorName, Long budgetNodeId, String budgetCode,
                                          String createdBy, LocalDateTime createdAt, String requesterRemark, Long glId, String glCode, String glName, String unitOfMeasure, Long purchaseOrderHeaderId) {
        this.id = id;
        this.docNo = docNo;
        this.type = type.getFormattedName();
        this.name = name;
        this.hsn = hsn;
        this.itemCode = itemCode;
        // Removed because of self-assigment issue in sonar qube
        //this.unitOfMeasureId = unitOfMeasureId;
        this.unitOfMeasure = unitOfMeasure;
        this.description = description;
        this.quantity = quantity;
        this.selectedQuantity = quantity;
        this.unitRate = unitRate;
        this.taxPercentage = taxPercentage;
        this.amountWithoutGst = amountWithoutGst;
        this.totalGstAmount = totalGstAmount;
        this.total = total;
        this.quotationId = quotationId;
        this.vendorId = vendorId;
        this.vendorName = vendorName;
        this.budgetNodeId = budgetNodeId;
        this.budgetCode = budgetCode;
        this.createdBy = createdBy;
        this.createdAt = DateTimeUtils.formatDateJPQL(createdAt);
        this.requesterRemark = requesterRemark;

        this.glId = glId;
        this.glCode = glCode;
        this.glName = glName;
        this.purchaseOrderHeaderId = purchaseOrderHeaderId;
    }

}