package in.taxgenie.pay_expense_pvv.viewmodels.po;

import in.taxgenie.pay_expense_pvv.entities.LookupData;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.BaseContainerViewModel;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Data
public class PurchaseOrderContainerViewModel extends BaseContainerViewModel {
    // Purchase Order
    private String orderCreationTime;
    private BigDecimal poAmount;
    private BigDecimal balancePoAmount;
    private String deliveryDate;
    private String poStatus;

    // Budget
    private String budgetCode;


    // Document
    private String originatorDocumentIdentifier;
    private Long documentSubgroupId;
    private String source;
    private String docType;

    // group subgroup
    private String group;
    private String subGroup;
    private ExpenseType expenseType;

    // Document -> Purchase Order Header -> Company (Vendor details)
    private String vendorName;
    private String vendorCode;
    private String gstin;
    private String pan;
    private Boolean msmeStatus;
    private String gstinStatus;
    private String documentDate;
    private Integer supportingDocumentsCount;

    private String erpRemarks;
    private String statusRemarks;

    public PurchaseOrderContainerViewModel(
            // Base Approval Document
            Long id, String documentIdentifier, ZonedDateTime createdTimestamp, LocalDate documentDate, ReportStatus reportStatus, LocalDate submitDate, String currentApproverFirstName, String currentApproverLastName, String currentApproverSystemIdCode, String currentApproverEmployeeCode, String firstName, String middleName, String lastName, String employeeCode, String employeeGrade, ZonedDateTime updatedTimestamp, String docNo, LocalDate deliveryDate,
            // Purchase order
            Timestamp orderCreationTime, BigDecimal poAmount, BigDecimal balancePoAmount, LookupData poStatus,
            // Budget
            String budgetCode,
            // Document
            String originatorDocumentIdentifier, Long documentSubgroupId, String source,
            // Company
            String vendorName, String vendorCode, String gstin, Boolean msmeStatus, String gstinStatus, Integer supportingDocumentsCount,
            String group, String subGroup, ExpenseType expenseType, String docType, String erpRemarks, String statusRemarks
    ) {
        super(id, documentIdentifier, docNo, createdTimestamp, deliveryDate, reportStatus, submitDate, updatedTimestamp, currentApproverFirstName, currentApproverLastName, currentApproverSystemIdCode, currentApproverEmployeeCode, firstName, middleName, lastName, employeeCode, employeeGrade);
        this.orderCreationTime = null == orderCreationTime ? null : DateTimeUtils.formatDateJPQL(orderCreationTime.toLocalDateTime());
        this.poAmount = poAmount;
        this.balancePoAmount = balancePoAmount;
        this.poStatus = null == poStatus ? null : poStatus.getValue();
        this.budgetCode = budgetCode;
        this.originatorDocumentIdentifier = originatorDocumentIdentifier;
        this.documentSubgroupId = documentSubgroupId;
        this.group = group;
        this.subGroup = subGroup;
        this.expenseType = expenseType;
        this.source = source;
        this.vendorName = vendorName;
        this.vendorCode = vendorCode;
        this.gstin = gstin;
        this.pan = this.gstin == null || this.gstin.length() != 15 ? null : this.gstin.substring(2, 12);
        this.msmeStatus = msmeStatus;
        this.gstinStatus = gstinStatus;
        this.documentDate = DateTimeUtils.formatDateJPQL(documentDate);
        this.supportingDocumentsCount = supportingDocumentsCount;
        this.docType = docType;
        this.erpRemarks = erpRemarks;
        this.statusRemarks = statusRemarks;
    }

}
