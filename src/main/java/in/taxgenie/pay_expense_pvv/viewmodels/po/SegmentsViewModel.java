package in.taxgenie.pay_expense_pvv.viewmodels.po;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SegmentsViewModel {
    private Integer id;
    private String segmentCode;
    private String costCenterCode;
    private String profitCenterCode;
    private Double ratio;
    private BigDecimal amount;
}
