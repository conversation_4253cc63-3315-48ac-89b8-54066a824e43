package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.govrndto.InvoiceDTO;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.document_matcher.MatchingCriteria;
import lombok.Data;

import java.util.List;

@Data
public class DocumentMatcherRequestViewModel {
    InvoiceDTO primaryDocument;
    List<PurchaseOrderCreateViewModel> secondaryDocuments;
    List<MatchingCriteria> criteria;
}
