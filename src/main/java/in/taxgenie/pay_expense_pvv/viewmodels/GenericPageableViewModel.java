package in.taxgenie.pay_expense_pvv.viewmodels;

import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.QueueStatisticsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.ProcurementStatisticsViewModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class GenericPageableViewModel<T> extends PaginationViewModel {
	List<T> data;
	QueueStatisticsViewModel stats;
	ProcurementStatisticsViewModel newStats;
}
