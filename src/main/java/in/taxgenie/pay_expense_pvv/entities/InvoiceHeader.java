package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import in.taxgenie.pay_expense_pvv.entities.company.GstinDetail;
import in.taxgenie.pay_expense_pvv.entities.invoice.InvoiceItem;
import in.taxgenie.pay_expense_pvv.invoice.repository.IStateCodeRepo;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.List;

@Builder
@Getter
@Setter
@Entity
@Table(name = "invoice_header")
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "invoiceHeaderId")
public class InvoiceHeader {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "invoice_header_id")
    private Long invoiceHeaderId;

    @JsonIgnore
    @OneToOne(mappedBy = "invoiceHeader")
    private InvoiceReceived invoiceReceived;

    @JsonIgnore
    @OneToOne
    @JoinColumn(name = "document_id")
    private Document document;

    @Column(name = "version")
    private String version;

    @Column(name = "tax_schema")
    private String taxSchema;

    @Column(name = "transaction_catogory")
    private String supTyp;

    @Column(name = "reverse_charge")
    private Boolean regRev;

    @Column(name = "gstin_of_operator")
    private String ecmGstin;

    @Column(name = "igst_on_intra")
    private Boolean igstOnIntra;

    private Boolean hasPrecedingDocument;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private String dynamicFieldsJson;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "doc_type_id", insertable = false, updatable = false)
    private LookupData docType;
    @Column(name = "doc_type_id")
    private Integer docTypeId;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "invoice_document_type_id", insertable = false, updatable = false)
    private LookupData invoiceDocumentType;
    @Column(name = "invoice_document_type_id")
    private Integer invoiceDocumentTypeId;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "invoice_type_id", insertable = false, updatable = false)
    private LookupData invoiceType;
    @Column(name = "invoice_type_id")
    private Integer invoiceTypeId;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "invoice_sub_type_id", insertable = false, updatable = false)
    private LookupData invoiceSubType;
    @Column(name = "invoice_sub_type_id")
    private Integer invoiceSubTypeId;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sellerId", insertable = false, updatable = false)
    private Company supplier;
    private Integer sellerId;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "buyerGstinId", insertable = false, updatable = false)
    private GstinDetail buyerGstin;
    private Long buyerGstinId;



    @Column(name = "dispatch_name")
    private String dispatchName;

    @Column(name = "dispatch_address_1")
    private String dispatchAddress1;

    @Column(name = "dispatch_address_2")
    private String dispatchAddress2;

    @Column(name = "dispatch_location")
    private String dispatchLocation;

    @Column(name = "dispatch_pin")
    private String dispatchPin;

    @Column(name = "dispatch_phone_number")
    private String dispatchPhoneNumber;

    @Column(name = "dispatch_email")
    private String dispatchEmail;

    @Column(name = "is_dispatch_address_same")
    private Boolean isDispatchAddressSame;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "dispatch_state_code")
    private StateCodes dispatchStateCode;

    @Column(name = "ship_gstin")
    private String shipGstin;

    @Column(name = "ship_legal_name")
    private String shipLegalName;

    @Column(name = "ship_tradename")
    private String shipTradename;

    @Column(name = "ship_address_1")
    private String shipAddress1;

    @Column(name = "ship_address_2")
    private String shipAddress2;

    @Column(name = "ship_location")
    private String shipLocation;

    @Column(name = "ship_pin")
    private String shipPin;

    @Column(name = "ship_phone_number")
    private String shipPhoneNumber;

    @Column(name = "ship_email")
    private String shipEmail;

    @Column(name = "isDeliveryAddressSame")
    private Boolean isDeliveryAddressSame;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "ship_state_code")
    private StateCodes shipStateCode1;

//    @Column(name = "total_cess_value")
//    private BigDecimal totalCessValue; // Todo: Replace with otherCharges
//
//    @Column(name = "total_state_cess_value")
//    private BigDecimal totalStateCessValue; // Todo: otherCharges
//
//    @Column(name = "invoice_discount")
//    private BigDecimal invoiceDiscount; // Todo: Replace with Document Discount
//
//    @Column(name = "invoice_other_charges")
//    private BigDecimal invoiceOtherCharges; // Todo: Replace with Document otherCharges

    @Column(name = "roundoff_amount")
    private BigDecimal roundoffAmount;

//    private BigDecimal totalAmount; // Todo: Replace with Document Applicable

    @Column(name = "grn_srn_status")
    private String grnSrnStatus;

    @Column(name = "payee_name")
    private String payeeName;

    @Column(name = "mode_of_payment")
    private String modeOfPayment;

    @Column(name = "ifsc_code")
    private String finInsBr;

    @Column(name = "payment_term")
    private String paymentTerm;

    @Column(name = "payment_instruction")
    private String paymentInstruction;

    @Column(name = "credit_transfer")
    private String creditTransfer;

    @Column(name = "direct_debit")
    private String directDebit;

    @Column(name = "credit_days")
    private Integer creditDays;

//    @Column(name = "paid_amount")
//    private BigDecimal paidAmount; // Todo: Replace with Document consumed
//
//    @Column(name = "payment_due")
//    private BigDecimal paymentDue; // Todo: Replace with a calculated field

    @Column(name = "account_details")
    private String accountDetails;

    @Column(name = "invoice_remarks")
    private String invRm;

    @Column(name = "invoice_start_date")
    private LocalDate invoiceStartDate;

    @Column(name = "invoice_end_date")
    private LocalDate invoiceEndDate;

    @Column(name = "is_preceding_doc")
    private Boolean isPrecedingDoc;

    @Column(name = "preceding_invoice_no")
    private String precedingInvoiceNo;

    @Column(name = "preceding_invoice_date")
    private LocalDate precedingInvoiceDate;

    @Column(name = "other_refe")
    private String otherRef;

    @Column(name = "recept_advice_no")
    private String receiptAdviceNo;

    @Column(name = "date_of_receipt_advice")
    private LocalDate dateOfReceiptAdvice;

    @Column(name = "lot_ref_no")
    private String tendRef;

    @Column(name = "contract_ref_no")
    private String contractRefNo;

    @Column(name = "any_other_ref")
    private String extRef;

    @Column(name = "project_ref")
    private String projectRef;

    @Column(name = "po_ref_no")
    private String poRefNo;

    @Column(name = "po_ref_date")
    private LocalDate poRefDate;

    @Column(name = "shipping_bill_no")
    private String shipBNo;

    @Column(name = "shipping_bill_date")
    private LocalDate shipBDt;

    @ManyToOne
    @JoinColumn(name = "port_code")
    private Ports port;

    @Column(name = "refund_claim")
    private Boolean refClm;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "foreign_currency")
    private LookupData forCur;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "country_code")
    private Countries cntCode;

//    @Column(name = "export_duty")
//    private BigDecimal expDuty; // Todo: Replace with Document exportDuty

    @Column(name = "transporter")
    private String transId;

    @Column(name = "transporter_name")
    private String transName;

    @Column(name = "mode_of_transportation")
    private String transMode;

    @Column(name = "dist_of_transportation")
    private Integer distance;

    @Column(name = "transporter_doc_no")
    private String transDocNo;

    @Column(name = "transporter_doc_date")
    private LocalDate transDocDt;

    @Column(name = "vehicle_no")
    private String vehNo;

    @Column(name = "odc_or_regular")
    private Boolean odcOrRegular;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "invoice_status_id")
    private LookupData invoiceStatus;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "additional_parameters", columnDefinition = "jsonb")
    private String additionalParameters;

    @Column(name = "created_at")
    private Timestamp createdAt;

    @Column(name = "updated_at")
    private Timestamp updatedAt;

    @Column(name = "deleted_at")
    private Timestamp deletedAt;

    @Column(name = "hsnSacCode")
    private String hsn;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "no_of_line_items")
    private Integer noOfLineItems;

    @JsonIgnore
    @OneToOne
    @JoinColumn(name = "contact_person_id")
    private ContactPerson contactPerson;

    @JsonIgnore
    @OneToMany(mappedBy = "invoiceHeader", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<InvoiceItem> invoiceItems;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creatingUserId", insertable = false, updatable = false)
    private Users creatingUser;
    private Long creatingUserId;

    // buyer - details
    private String gstin;
    private String buyerLegalName;
    private String buyerTradeName;
    private String email;
    private String address1;
    private String address2;
    private String district;
    private String pin;
    private String streetName;
    private String loc;
    private String phoneNo;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "state_code_id")
    private StateCodes stateCode;

    private String buyerContactPerson;

    // erp-sync
    private int priority = 0;
    private Integer syncAttempt = 0;

    // new -fields
    private String currency = "INR";
    private BigDecimal tdsValue = BigDecimal.ZERO;
    private Double exchangeRate = 1d;
    private String itcType; // isineligible
    private String pos; // stateCode

    
    // ---- CUSTOM SETTERS START ----
    
    public void setBuyerGstinDetails(GstinDetail gstinDetail, IStateCodeRepo stateCodeRepo) {
        if (gstinDetail == null) {
            throw new IllegalArgumentException("GstinDetail cannot be null");
        }

        this.setBuyerGstinId(gstinDetail.getId());
        this.setBuyerGstin(gstinDetail);
        this.setBuyerTradeName(gstinDetail.getLegalTradeName());
        this.setBuyerLegalName(gstinDetail.getLegalName());
        this.setGstin(gstinDetail.getGstinNo());
        this.setEmail(gstinDetail.getEmailId());
        this.setAddress1(gstinDetail.getAddress());
        this.setAddress2(gstinDetail.getAddress());
        this.setPhoneNo(gstinDetail.getContactNumber());
        this.setPin(gstinDetail.getPinCode());
        String normalizedCode = String.format("%02d", Integer.parseInt(gstinDetail.getStateCode()));
        stateCodeRepo.findByCode(normalizedCode).ifPresent(this::setStateCode);
        this.setBuyerContactPerson(gstinDetail.getContactNumber());
    } 

    // ---- CUSTOM SETTERS END ----
}
