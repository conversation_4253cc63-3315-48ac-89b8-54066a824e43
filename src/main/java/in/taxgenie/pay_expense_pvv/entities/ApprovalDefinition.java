package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class ApprovalDefinition {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private long companyCode;
    private int level;
    private BigDecimal limitAmount;
    private String approvalMatcher;
    private String approvalTitle;
    private boolean shouldFetchFromEmployeeMaster;
    private StateChannel channel;
    private boolean isExternalToCem;
    private boolean isFrozen;
    private boolean forDeviation;

    //  Common
    private long creatingUserId;
    private ZonedDateTime createdTimestamp;
    private Long updatingUserId;
    private ZonedDateTime updatedTimestamp;

    //  Mappings
    @JsonIgnore
    @ManyToOne(optional = false)
    @JoinColumn(name = "documentMetadataId", insertable = false, updatable = false)
    private DocumentMetadata documentMetadata;
    private long documentMetadataId;
}
