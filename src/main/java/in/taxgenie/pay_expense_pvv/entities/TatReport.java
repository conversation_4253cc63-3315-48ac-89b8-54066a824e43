package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import jakarta.persistence.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

@Entity
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class TatReport {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id", nullable = false)
    private int id;
    @Basic
    @Column(name = "company_code", nullable = true)
    private Long companyCode;
    @Basic
    @Column(name = "document_identifier", nullable = true, length = 30)
    private String documentIdentifier;
    @Basic
    @Column(name = "created_date", nullable = true)
    private LocalDate createdDate;
    @Basic
    @Column(name = "submit_date", nullable = true)
    private LocalDate submitDate;
    @Basic
    @Column(name = "report_title", nullable = true, length = 200)
    private String reportTitle;
    @Basic
    @Column(name = "report_claim_amount", nullable = true, precision = 0)
    private BigDecimal reportClaimAmount;
    @Basic
    @Column(name = "voucher_amount", nullable = true, precision = 0)
    private BigDecimal voucherAmount;
    @Basic
    @Column(name = "paid_status", nullable = true)
    private Integer paidStatus;
    @Basic
    @Column(name = "total_paid_amount", nullable = true, precision = 0)
    private BigDecimal totalPaidAmount;
    @Basic
    @Column(name = "employee_cost_center", nullable = true, length = 30)
    private String employeeCostCenter;
    @Basic
    @Column(name = "employee_grade", nullable = true, length = 30)
    private String employeeGrade;
    @Basic
    @Column(name = "employee_department", nullable = true, length = 30)
    private String employeeDepartment;
    @Basic
    @Column(name = "metadata_id", nullable = true)
    private Integer metadataId;
    @Basic
    @Column(name = "expense_type", nullable = true, length = 30)
    private String expenseType;
    @Basic
    @Column(name = "current_approver_employee_code", nullable = true, length = 30)
    private String currentApproverEmployeeCode;
    @Basic
    @Column(name = "contains_send_back", nullable = true)
    private Boolean containsSendBack;
    @Basic
    @Column(name = "pending_at", nullable = true, length = 255)
    private String pendingAt;
    @Basic
    @Column(name = "start_date", nullable = true)
    private LocalDate startDate;
    @Basic
    @Column(name = "end_date", nullable = true)
    private LocalDate endDate;
    @Basic
    @Column(name = "payment_date", nullable = true)
    private LocalDate paymentDate;
    @Basic
    @Column(name = "payment_reference", nullable = true, length = 255)
    private String paymentReference;
    @Basic
    @Column(name = "payment_tat", nullable = true)
    private Long paymentTat;
    @Basic
    @Column(name = "entity", nullable = true, length = 30)
    private String entity;
    @Basic
    @Column(name = "expense_report_id", nullable = true)
    private Long expenseReportId;
    @Basic
    @Column(name = "report_status", nullable = true)
    private ReportStatus reportStatus;
    @Basic
    @Column(name = "employee_branch", nullable = true, length = 50)
    private String employeeBranch;
    @Basic
    @Column(name = "approver_name", nullable = true, length = 30)
    private String approverName;
    @Basic
    @Column(name = "approver_employee_code2", nullable = true, length = 30)
    private String approverEmployeeCode2;
    @Basic
    @Column(name = "approved_date", nullable = true)
    private LocalDate approvedDate;
    @Basic
    @Column(name = "approver_level", nullable = true, length = 100)
    private String approverLevel;
    @Basic
    @Column(name = "b1_approver_date", nullable = true)
    private LocalDate b1ApproverDate;
    @Basic
    @Column(name = "tat_1", nullable = true)
    private Long tat1;
    @Basic
    @Column(name = "b1_approver_name", nullable = true, length = 255)
    private String b1ApproverName;
    @Basic
    @Column(name = "b2_approver_date", nullable = true)
    private LocalDate b2ApproverDate;
    @Basic
    @Column(name = "tat_2", nullable = true)
    private Long tat2;
    @Basic
    @Column(name = "b2_approver_name", nullable = true, length = 255)
    private String b2ApproverName;
    @Basic
    @Column(name = "b3_approver_date", nullable = true)
    private LocalDate b3ApproverDate;
    @Basic
    @Column(name = "tat_3", nullable = true)
    private Long tat3;
    @Basic
    @Column(name = "b3_approver_name", nullable = true, length = 255)
    private String b3ApproverName;
    @Basic
    @Column(name = "b4_approver_date", nullable = true)
    private LocalDate b4ApproverDate;
    @Basic
    @Column(name = "tat_4", nullable = true)
    private Long tat4;
    @Basic
    @Column(name = "b4_approver_name", nullable = true, length = 255)
    private String b4ApproverName;
    @Basic
    @Column(name = "b5_approver_date", nullable = true)
    private LocalDate b5ApproverDate;
    @Basic
    @Column(name = "tat_5", nullable = true)
    private Long tat5;
    @Basic
    @Column(name = "b5_approver_name", nullable = true, length = 255)
    private String b5ApproverName;
    @Basic
    @Column(name = "checker_1_approver_date", nullable = true)
    private LocalDate checker1ApproverDate;
    @Basic
    @Column(name = "tat_6", nullable = true)
    private Long tat6;
    @Basic
    @Column(name = "b6_checker_name", nullable = true, length = 255)
    private String b6CheckerName;
    @Basic
    @Column(name = "checker_2_approver_date", nullable = true)
    private LocalDate checker2ApproverDate;
    @Basic
    @Column(name = "tat_7", nullable = true)
    private Long tat7;
    @Basic
    @Column(name = "b7_checker_name", nullable = true, length = 255)
    private String b7CheckerName;
    @Basic
    @Column(name = "total_batat", nullable = true)
    private Long totalBatat;
    @Basic
    @Column(name = "total_checker_tat", nullable = true)
    private Long totalCheckerTat;
    @Basic
    @Column(name = "total_tat", nullable = true)
    private Long totalTat;
    @Basic
    @Column(name = "voucher_status", nullable = true, length = 30)
    private String voucherStatus;
    @Basic
    @Column(name = "total_expenses", nullable = true, precision = 0)
    private BigDecimal totalExpenses;
    @Basic
    @Column(name = "location", nullable = true, length = 255)
    private String location;
    @Basic
    @Column(name = "current_approver_name", nullable = true, length = 255)
    private String currentApproverName;
    @Basic
    @Column(name = "all_report_state_data_for_report", nullable = true, length = -1)
    private String allReportStateDataForReport;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(Long companyCode) {
        this.companyCode = companyCode;
    }

    public String getDocumentIdentifier() {
        return documentIdentifier;
    }

    public void setDocumentIdentifier(String documentIdentifier) {
        this.documentIdentifier = documentIdentifier;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDate getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDate submitDate) {
        this.submitDate = submitDate;
    }

    public String getReportTitle() {
        return reportTitle;
    }

    public void setReportTitle(String reportTitle) {
        this.reportTitle = reportTitle;
    }

    public BigDecimal getReportClaimAmount() {
        return reportClaimAmount;
    }

    public void setReportClaimAmount(BigDecimal reportClaimAmount) {
        this.reportClaimAmount = reportClaimAmount;
    }

    public BigDecimal getVoucherAmount() {
        return voucherAmount;
    }

    public void setVoucherAmount(BigDecimal voucherAmount) {
        this.voucherAmount = voucherAmount;
    }

    public Integer getPaidStatus() {
        return paidStatus;
    }

    public void setPaidStatus(Integer paidStatus) {
        this.paidStatus = paidStatus;
    }

    public BigDecimal getTotalPaidAmount() {
        return totalPaidAmount;
    }

    public void setTotalPaidAmount(BigDecimal totalPaidAmount) {
        this.totalPaidAmount = totalPaidAmount;
    }

    public String getEmployeeCostCenter() {
        return employeeCostCenter;
    }

    public void setEmployeeCostCenter(String employeeCostCenter) {
        this.employeeCostCenter = employeeCostCenter;
    }

    public String getEmployeeGrade() {
        return employeeGrade;
    }

    public void setEmployeeGrade(String employeeGrade) {
        this.employeeGrade = employeeGrade;
    }

    public String getEmployeeDepartment() {
        return employeeDepartment;
    }

    public void setEmployeeDepartment(String employeeDepartment) {
        this.employeeDepartment = employeeDepartment;
    }

    public Integer getMetadataId() {
        return metadataId;
    }

    public void setMetadataId(Integer metadataId) {
        this.metadataId = metadataId;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getCurrentApproverEmployeeCode() {
        return currentApproverEmployeeCode;
    }

    public void setCurrentApproverEmployeeCode(String currentApproverEmployeeCode) {
        this.currentApproverEmployeeCode = currentApproverEmployeeCode;
    }

    public Boolean getContainsSendBack() {
        return containsSendBack;
    }

    public void setContainsSendBack(Boolean containsSendBack) {
        this.containsSendBack = containsSendBack;
    }

    public String getPendingAt() {
        return pendingAt;
    }

    public void setPendingAt(String pendingAt) {
        this.pendingAt = pendingAt;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public LocalDate getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDate paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public Long getPaymentTat() {
        return paymentTat;
    }

    public void setPaymentTat(Long paymentTat) {
        this.paymentTat = paymentTat;
    }

    public String getEntity() {
        return entity;
    }

    public void setEntity(String entity) {
        this.entity = entity;
    }

    public Long getExpenseReportId() {
        return expenseReportId;
    }

    public void setExpenseReportId(Long expenseReportId) {
        this.expenseReportId = expenseReportId;
    }

    public ReportStatus getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(ReportStatus reportStatus) {
        this.reportStatus = reportStatus;
    }

    public String getEmployeeBranch() {
        return employeeBranch;
    }

    public void setEmployeeBranch(String employeeBranch) {
        this.employeeBranch = employeeBranch;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public String getApproverEmployeeCode2() {
        return approverEmployeeCode2;
    }

    public void setApproverEmployeeCode2(String approverEmployeeCode2) {
        this.approverEmployeeCode2 = approverEmployeeCode2;
    }

    public LocalDate getApprovedDate() {
        return approvedDate;
    }

    public void setApprovedDate(LocalDate approvedDate) {
        this.approvedDate = approvedDate;
    }

    public String getApproverLevel() {
        return approverLevel;
    }

    public void setApproverLevel(String approverLevel) {
        this.approverLevel = approverLevel;
    }

    public LocalDate getB1ApproverDate() {
        return b1ApproverDate;
    }

    public void setB1ApproverDate(LocalDate b1ApproverDate) {
        this.b1ApproverDate = b1ApproverDate;
    }

    public Long getTat1() {
        return tat1;
    }

    public void setTat1(Long tat1) {
        this.tat1 = tat1;
    }

    public String getB1ApproverName() {
        return b1ApproverName;
    }

    public void setB1ApproverName(String b1ApproverName) {
        this.b1ApproverName = b1ApproverName;
    }

    public LocalDate getB2ApproverDate() {
        return b2ApproverDate;
    }

    public void setB2ApproverDate(LocalDate b2ApproverDate) {
        this.b2ApproverDate = b2ApproverDate;
    }

    public Long getTat2() {
        return tat2;
    }

    public void setTat2(Long tat2) {
        this.tat2 = tat2;
    }

    public String getB2ApproverName() {
        return b2ApproverName;
    }

    public void setB2ApproverName(String b2ApproverName) {
        this.b2ApproverName = b2ApproverName;
    }

    public LocalDate getB3ApproverDate() {
        return b3ApproverDate;
    }

    public void setB3ApproverDate(LocalDate b3ApproverDate) {
        this.b3ApproverDate = b3ApproverDate;
    }

    public Long getTat3() {
        return tat3;
    }

    public void setTat3(Long tat3) {
        this.tat3 = tat3;
    }

    public String getB3ApproverName() {
        return b3ApproverName;
    }

    public void setB3ApproverName(String b3ApproverName) {
        this.b3ApproverName = b3ApproverName;
    }

    public LocalDate getB4ApproverDate() {
        return b4ApproverDate;
    }

    public void setB4ApproverDate(LocalDate b4ApproverDate) {
        this.b4ApproverDate = b4ApproverDate;
    }

    public Long getTat4() {
        return tat4;
    }

    public void setTat4(Long tat4) {
        this.tat4 = tat4;
    }

    public String getB4ApproverName() {
        return b4ApproverName;
    }

    public void setB4ApproverName(String b4ApproverName) {
        this.b4ApproverName = b4ApproverName;
    }

    public LocalDate getB5ApproverDate() {
        return b5ApproverDate;
    }

    public void setB5ApproverDate(LocalDate b5ApproverDate) {
        this.b5ApproverDate = b5ApproverDate;
    }

    public Long getTat5() {
        return tat5;
    }

    public void setTat5(Long tat5) {
        this.tat5 = tat5;
    }

    public String getB5ApproverName() {
        return b5ApproverName;
    }

    public void setB5ApproverName(String b5ApproverName) {
        this.b5ApproverName = b5ApproverName;
    }

    public LocalDate getChecker1ApproverDate() {
        return checker1ApproverDate;
    }

    public void setChecker1ApproverDate(LocalDate checker1ApproverDate) {
        this.checker1ApproverDate = checker1ApproverDate;
    }

    public Long getTat6() {
        return tat6;
    }

    public void setTat6(Long tat6) {
        this.tat6 = tat6;
    }

    public String getB6CheckerName() {
        return b6CheckerName;
    }

    public void setB6CheckerName(String b6CheckerName) {
        this.b6CheckerName = b6CheckerName;
    }

    public LocalDate getChecker2ApproverDate() {
        return checker2ApproverDate;
    }

    public void setChecker2ApproverDate(LocalDate checker2ApproverDate) {
        this.checker2ApproverDate = checker2ApproverDate;
    }

    public Long getTat7() {
        return tat7;
    }

    public void setTat7(Long tat7) {
        this.tat7 = tat7;
    }

    public String getB7CheckerName() {
        return b7CheckerName;
    }

    public void setB7CheckerName(String b7CheckerName) {
        this.b7CheckerName = b7CheckerName;
    }

    public Long getTotalBatat() {
        return totalBatat;
    }

    public void setTotalBatat(Long totalBatat) {
        this.totalBatat = totalBatat;
    }

    public Long getTotalCheckerTat() {
        return totalCheckerTat;
    }

    public void setTotalCheckerTat(Long totalCheckerTat) {
        this.totalCheckerTat = totalCheckerTat;
    }

    public Long getTotalTat() {
        return totalTat;
    }

    public void setTotalTat(Long totalTat) {
        this.totalTat = totalTat;
    }

    public String getVoucherStatus() {
        return voucherStatus;
    }

    public void setVoucherStatus(String voucherStatus) {
        this.voucherStatus = voucherStatus;
    }

    public BigDecimal getTotalExpenses() {
        return totalExpenses;
    }

    public void setTotalExpenses(BigDecimal totalExpenses) {
        this.totalExpenses = totalExpenses;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getCurrentApproverName() {
        return currentApproverName;
    }

    public void setCurrentApproverName(String currentApproverName) {
        this.currentApproverName = currentApproverName;
    }

    public String getAllReportStateDataForReport() {
        return allReportStateDataForReport;
    }

    public void setAllReportStateDataForReport(String allReportStateDataForReport) {
        this.allReportStateDataForReport = allReportStateDataForReport;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TatReport tatReport = (TatReport) o;
        return id == tatReport.id && Objects.equals(companyCode, tatReport.companyCode) && Objects.equals(documentIdentifier, tatReport.documentIdentifier) && Objects.equals(createdDate, tatReport.createdDate) && Objects.equals(submitDate, tatReport.submitDate) && Objects.equals(reportTitle, tatReport.reportTitle) && Objects.equals(reportClaimAmount, tatReport.reportClaimAmount) && Objects.equals(voucherAmount, tatReport.voucherAmount) && Objects.equals(paidStatus, tatReport.paidStatus) && Objects.equals(totalPaidAmount, tatReport.totalPaidAmount) && Objects.equals(employeeCostCenter, tatReport.employeeCostCenter) && Objects.equals(employeeGrade, tatReport.employeeGrade) && Objects.equals(employeeDepartment, tatReport.employeeDepartment) && Objects.equals(metadataId, tatReport.metadataId) && Objects.equals(expenseType, tatReport.expenseType) && Objects.equals(currentApproverEmployeeCode, tatReport.currentApproverEmployeeCode) && Objects.equals(containsSendBack, tatReport.containsSendBack) && Objects.equals(pendingAt, tatReport.pendingAt) && Objects.equals(startDate, tatReport.startDate) && Objects.equals(endDate, tatReport.endDate) && Objects.equals(paymentDate, tatReport.paymentDate) && Objects.equals(paymentReference, tatReport.paymentReference) && Objects.equals(paymentTat, tatReport.paymentTat) && Objects.equals(entity, tatReport.entity) && Objects.equals(expenseReportId, tatReport.expenseReportId) && Objects.equals(reportStatus, tatReport.reportStatus) && Objects.equals(employeeBranch, tatReport.employeeBranch) && Objects.equals(approverName, tatReport.approverName) && Objects.equals(approverEmployeeCode2, tatReport.approverEmployeeCode2) && Objects.equals(approvedDate, tatReport.approvedDate) && Objects.equals(approverLevel, tatReport.approverLevel) && Objects.equals(b1ApproverDate, tatReport.b1ApproverDate) && Objects.equals(tat1, tatReport.tat1) && Objects.equals(b1ApproverName, tatReport.b1ApproverName) && Objects.equals(b2ApproverDate, tatReport.b2ApproverDate) && Objects.equals(tat2, tatReport.tat2) && Objects.equals(b2ApproverName, tatReport.b2ApproverName) && Objects.equals(b3ApproverDate, tatReport.b3ApproverDate) && Objects.equals(tat3, tatReport.tat3) && Objects.equals(b3ApproverName, tatReport.b3ApproverName) && Objects.equals(b4ApproverDate, tatReport.b4ApproverDate) && Objects.equals(tat4, tatReport.tat4) && Objects.equals(b4ApproverName, tatReport.b4ApproverName) && Objects.equals(b5ApproverDate, tatReport.b5ApproverDate) && Objects.equals(tat5, tatReport.tat5) && Objects.equals(b5ApproverName, tatReport.b5ApproverName) && Objects.equals(checker1ApproverDate, tatReport.checker1ApproverDate) && Objects.equals(tat6, tatReport.tat6) && Objects.equals(b6CheckerName, tatReport.b6CheckerName) && Objects.equals(checker2ApproverDate, tatReport.checker2ApproverDate) && Objects.equals(tat7, tatReport.tat7) && Objects.equals(b7CheckerName, tatReport.b7CheckerName) && Objects.equals(totalBatat, tatReport.totalBatat) && Objects.equals(totalCheckerTat, tatReport.totalCheckerTat) && Objects.equals(totalTat, tatReport.totalTat) && Objects.equals(voucherStatus, tatReport.voucherStatus) && Objects.equals(totalExpenses, tatReport.totalExpenses) && Objects.equals(location, tatReport.location) && Objects.equals(currentApproverName, tatReport.currentApproverName) && Objects.equals(allReportStateDataForReport, tatReport.allReportStateDataForReport);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, companyCode, documentIdentifier, createdDate, submitDate, reportTitle, reportClaimAmount, voucherAmount, paidStatus, totalPaidAmount, employeeCostCenter, employeeGrade, employeeDepartment, metadataId, expenseType, currentApproverEmployeeCode, containsSendBack, pendingAt, startDate, endDate, paymentDate, paymentReference, paymentTat, entity, expenseReportId, reportStatus, employeeBranch, approverName, approverEmployeeCode2, approvedDate, approverLevel, b1ApproverDate, tat1, b1ApproverName, b2ApproverDate, tat2, b2ApproverName, b3ApproverDate, tat3, b3ApproverName, b4ApproverDate, tat4, b4ApproverName, b5ApproverDate, tat5, b5ApproverName, checker1ApproverDate, tat6, b6CheckerName, checker2ApproverDate, tat7, b7CheckerName, totalBatat, totalCheckerTat, totalTat, voucherStatus, totalExpenses, location, currentApproverName, allReportStateDataForReport);
    }
}
