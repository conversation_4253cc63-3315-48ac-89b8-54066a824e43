package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import jakarta.persistence.*;
import lombok.*;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Builder
@Getter
@Setter
@Entity
@Table(name="company_master")
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "companyMasterId")
public class CompanyMaster {
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "company_master_id")
	private Integer companyMasterId;

	@Column(name = "vendor_code")
	private String vendorCode;

	@Column(name = "msme_status")
	private Boolean msmeStatus;

	@Column(name= "gstin")
	private String gstin;
	
	@Column(name= "vat_number")
	private String vatNumber;
	
	@Column(name= "legal_name")
	private String legalName;
	
	@Column(name= "trade_name")
	private String tradeName;
	
	@Column(name= "point_of_sale")
	private String pos;
	
	@Column(name= "address_1")
	private String address1;
	
	@Column(name= "address_2")
	private String address2;
	
	@Column(name= "location")
	private String location;
	
	@Column(name= "zipcode")
	private String zipcode;
	
	@Column(name= "city")
	private String city;
	
	@Column(name= "state")
	private String state;
	
	@Column(name= "country")
	private String country;
	
	@Column(name= "pin")
	private Integer pin;
	
	@Column(name= "state_code_id")
	private Integer stateCodeId;
	
	@Column(name= "phone_no")
	private String phoneNo;

	@Column(name= "contact_person1")
	private String contactPerson1;

	@Column(name= "contact_person2")
	private String contactPerson2;

	@Column(name= "email_id1")
	private String emailId1;
	
	@Column(name= "email_id2")
	private String emailId2;
	
	@Column(name= "contact_no1")
	private String contactNo1;
	
	@Column(name= "contact_no2")
	private String contactNo2;
	
	@Column(name= "pan_number")
	private String panNumber;

	@Column(name= "tds")
	private String tds;
	
	@Column(name= "payment_terms")
	private String paymentTerms;
	
	@Column(name= "bank_name")
	private String bankName;
	
	@Column(name= "bank_account_number")
	private String bankAccountNumber;
	
	@Column(name= "ifsc_code")
	private String ifscCode;

	@Column(name= "tan")
	private String tan;

	@Column(name= "cin_no")
	private String cinNo;

	@Column(name= "cin_status")
	private String cinStatus;

	@Column(name = "is_in_sync_with_erp")
	private Boolean isInSyncWithErp = Boolean.FALSE;
	
	@Column(name= "created_by")
	private Integer createdBy;
	
	@Column(name= "updated_by")
	private Integer updatedBy;
	
	@Column(name= "created_at")
	private Timestamp createdAt;
	
	@Column(name= "updated_at")
	private Timestamp updatedAt;
	
	@Column(name= "deleted_at")
	private Timestamp deletedAt;
	
	@OneToMany(mappedBy = "companyMaster")
	private List<Company> companyList=new ArrayList<>();
	
}
