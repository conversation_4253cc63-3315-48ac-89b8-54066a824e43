package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class MetadataLimitRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private long companyCode;
    private BigDecimal limitAmount;
    private IntervalMarker intervalMarker;
    private String branchCode;
    private String departmentCode;
    private String costCenterCode;
    private String employeeType;
    private String employeeGrade;
    private LocalDate startDate;
    private LocalDate endDate;
    @Column(length = 50)
    private String locationCategory;

    private int documentCountLimit;

    //  Common
    private boolean isFrozen;
    private long creatingUserId;
    private ZonedDateTime createdTimestamp;
    private Long updatingUserId;
    private ZonedDateTime updatedTimestamp;

    //  Mapping
    @JsonBackReference
    @ManyToOne(optional = false)
    @JoinColumn(name = "documentMetadataId", insertable = false, updatable = false)
    private DocumentMetadata documentMetadata;
    private long documentMetadataId;

}
