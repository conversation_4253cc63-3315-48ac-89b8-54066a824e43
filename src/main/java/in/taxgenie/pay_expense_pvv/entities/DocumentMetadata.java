package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.*;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "document_metadata")
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class DocumentMetadata {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Long companyCode;
//    @NotBlank
    private String documentCategory;
//    @NotNull
    private Integer documentCategoryId;
    private String description;
    private String documentType;
    private String documentTypePrefix;
    private String documentGroup;
    private String documentGroupPrefix;

    private boolean purposeRequired;
    @Enumerated(EnumType.STRING)
    private ExpenseType applicableExpenseType;
    private Integer limitDays;

    private int definitionsCount;
    private int subgroupsCount;
    private int rulesCount;

    private boolean isFrozen;
    @Column(name = "is_on_behalf")
    private boolean isOnBehalf;
    @Column(name = "is_on_behalf_of_other")
    private boolean isOnBehalfOfOther;
    private long creatingUserId;
    private ZonedDateTime createdTimestamp;
    private Long updatingUserId;
    private ZonedDateTime updatedTimestamp;

    private String uuid;

    @Enumerated(EnumType.STRING)
    private ActionType actionType = ActionType.SUBMIT;

    //  mappings

    @JsonManagedReference
    @OneToMany(mappedBy = "documentMetadata")
    private List<ApprovalDefinition> approvalDefinitions = new ArrayList<>();

    @JsonManagedReference
    @OneToMany(mappedBy = "documentMetadata")
    private List<DocumentSubgroup> documentSubgroups = new ArrayList<>();

    @JsonManagedReference
    @OneToMany(mappedBy = "documentMetadata")
    private List<DocumentApprovalContainer> documentApprovalContainers = new ArrayList<>();

    @JsonManagedReference
    @OneToMany(mappedBy = "documentMetadata")
    private List<MetadataLimitRule> limitRules = new ArrayList<>();

}
