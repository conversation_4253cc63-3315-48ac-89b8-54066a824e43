package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.sql.Timestamp;
import java.util.Set;

@Builder
@Getter
@Setter
@Entity
@Table(name="invoice_upload")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "invoiceReceivedId")
public class InvoiceReceived {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "invoice_received_id")
	private Long invoiceReceivedId;
	
	@Column(name = "company_id")
	private Long companyId;
	
	@Column(name = "purchase_order_id")
	private Integer purchaseOrderId;
	
	@Column(name= "gstin")
	private String gstin;
	
	@Column(name= "irn")
	private String irn;
	
	@Column(name= "ack_no")
	private Long ackNo;
	
	@Column(name= "ack_date")
	private Timestamp ackDate;
	
	@Column(name= "signed_invoice")
	private String signedInvoice;
	
	@Column(name= "signed_qr_code")
	private String signedQrCode;
	
	@JdbcTypeCode(SqlTypes.JSON)
	@Column(name= "json", columnDefinition = "jsonb")
	private String govtJson;

	@Enumerated(EnumType.STRING)
	@Column(name= "json_source")
	private JsonSource jsonSource;//OCR, GOV_JSON

	@Column(name= "document_url")
	private String documentUrl;
	
	@JdbcTypeCode(SqlTypes.JSON)
	@Column(name= "iq_json", columnDefinition = "jsonb")
	private String iqJson;

	@ManyToOne
	@JoinColumn(name="source_id")
	private LookupData invoiceSource;

	@ManyToOne
	@JoinColumn(name="json_pdf_matched_status")
	private LookupData jsonPdfMatchedStatus;

	@JdbcTypeCode(SqlTypes.JSON)
	@Column(name= "irp_json", columnDefinition = "jsonb")
	private String irpJson;
	
	@Column(name= "irp_error_msg")
	private String irpErrorMsg;
	
	@Column(name= "is_po_invoice")
	private Boolean isPoInvoice;
	
	@Column(name= "created_at")
	private Timestamp createdAt;
	
	@Column(name= "updated_at")
	private Timestamp updatedAt;
	
	@Column(name= "deleted_at")
	private Timestamp deletedAt;
	
	@Column(name= "document_type")
	private String documentType;
	
	@Column(name = "is_deleted")
	private Boolean isDeleted;
	
	@OneToMany(mappedBy = "invoiceReceived")
	private Set<AdditionalSupportingDocumentsToInvoice> additionDocumentList;

	@OneToOne(cascade = CascadeType.PERSIST)
	@JoinColumn(name = "invoice_header_id")
	private InvoiceHeader invoiceHeader;

	@Column(name= "dmr_reference_id")
	public String dmrReferenceId;

	@Column(name= "is_ocr_done")
	public Boolean isOcrDone = Boolean.FALSE;

	@Column(name= "ocr_status")
	public String ocrStatus;

	@Column(name= "dmr_created_at")
	private Timestamp dmrCreatedAt;
	
}
