package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.ZonedDateTime;

@Getter
@Setter
@Entity
@Table(name = "document_storage")
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class DocumentStorage {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Long documentId;
    private String uuid;
    private String name;
    private String path;
    private String contentType;
    private Long fileSize;
    private String source;
    private Long entityId;
    private Integer categoryId;

    private String fileHash;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "creatingUserId", insertable = false, updatable = false)
    private Users createdBy;
    private Long creatingUserId;

    private ZonedDateTime createdAt;
}
