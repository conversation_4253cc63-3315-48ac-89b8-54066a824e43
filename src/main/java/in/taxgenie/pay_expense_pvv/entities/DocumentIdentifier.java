package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import jakarta.persistence.*;
import lombok.*;

import java.time.ZonedDateTime;
import java.util.concurrent.atomic.AtomicLong;


@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class DocumentIdentifier {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    private long companyCode;
    private String documentType;
    private String documentTypePrefix;
    private String documentGroup;
    private String documentGroupPrefix;
    private int year;
    private int month;
    private long currentIndex;

    private ZonedDateTime createdTimestamp;
    private ZonedDateTime updatedTimestamp;

    @Version // Enables optimistic locking
    private int version;

    @PrePersist
    protected void onCreate() {
        this.createdTimestamp = ZonedDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedTimestamp = ZonedDateTime.now();
    }
}
