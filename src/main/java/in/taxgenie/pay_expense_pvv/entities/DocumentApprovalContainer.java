package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import jakarta.persistence.*;
import lombok.*;

import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "document_approval_container")
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class DocumentApprovalContainer extends BaseApprovalDocument {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	// Core fields
	@Column(length = 50)
	private String documentIdentifier;

	private LocalDate createdDate;
	private LocalDate submitDate;
	private LocalDate startDate;
	private LocalDate endDate;

	@Column(length = 200)
	private String approvalContainerTitle;
	@Column(length = 200)
	private String description;
	@Column(length = 100)
	private String purpose;

	private BigDecimal approvalContainerClaimAmount = new BigDecimal("0.0");

	// GST computation
	private BigDecimal approvalContainerSgstAmount = new BigDecimal("0.0");
	private BigDecimal approvalContainerCgstAmount = new BigDecimal("0.0");
	private BigDecimal approvalContainerIgstAmount = new BigDecimal("0.0");
	private BigDecimal approvalContainerTaxableAmount = new BigDecimal("0.0");

	// GL Transmission and Posting
	private boolean isPostedToGl;
	private LocalDate glPostingDate;
	private String glDocumentReference;

	// erp-sync
	private Boolean acknowledgedByErp = Boolean.FALSE;
	private Boolean sentToErp = Boolean.FALSE;
	private ZonedDateTime approvedDate;
	private ZonedDateTime erpAcknowledgementTimestamp;
	private Boolean isErpValidated;
	private ZonedDateTime erpValidatedDate;
	private Boolean isParked;
	private ZonedDateTime parkingDate;
	private Boolean isHold;
	private ZonedDateTime holdingDate;

	private PaidStatus paidStatus;
	private BigDecimal totalPaidAmount = new BigDecimal("0.0");
	private BigDecimal totalTdsAmount = new BigDecimal("0.0");

	// Employee master data
	@Column(length = 100)
	private String firstName;
	@Column(length = 100)
	private String middleName;
	@Column(length = 100)
	private String lastName;

	@Column(length = 200)
	private String employeeEmail;
	@Column(length = 50)
	private String employeeCode;
	@Column(columnDefinition = "bigint")
	private Long employeeSystemId;
	@Enumerated(EnumType.STRING)
	private ExpenseType expenseType;

	private LocalDate paymentDate;

	@Column(length = 50)
	private String employeeHrmsCode;
	@Column(length = 50)
	private String employeeType;
	@Column(length = 50)
	private String employeeGrade;
	@Column(length = 50)
	private String employeeBranch;
	@Column(length = 50)
	private String employeeDepartment;
	@Column(length = 50)
	private String employeeCostCenter;
	@Column(length = 50)
	private String employeeGlMainAccountCode;
	@Column(length = 50)
	private String employeeGlSubAccountCode;
	@Column(length = 50)
	private String employeeProfitCenter;

	// Dimensions
	@Column(length = 50)
	private String dimension01;
	@Column(length = 50)
	private String dimension02;
	@Column(length = 50)
	private String dimension03;
	@Column(length = 50)
	private String dimension04;
	@Column(length = 50)
	private String dimension05;
	@Column(length = 50)
	private String dimension06;
	@Column(length = 50)
	private String dimension07;
	@Column(length = 50)
	private String dimension08;
	@Column(length = 50)
	private String dimension09;
	@Column(length = 50)
	private String dimension10;

	private String currentApproverSystemIdCode;

	@Column(name="last_actioned_at", nullable = true, updatable = true)
	@UpdateTimestamp
	private LocalDateTime lastActionedAt;

	// Common
//	@Column(name = "document_approval_code", length = 16, updatable = false, nullable = false, unique = true).
//	TODO : for 16 chars - change @Column and remove generic generator and use type String and use @PrePersist strategy
	@Column(name = "document_approval_code", columnDefinition = "UUID", updatable = false)
	private UUID documentApprovalCode;

	@Column(columnDefinition = "bigint")
	private long creatingUserId;
	private ZonedDateTime createdTimestamp;
	@Column(columnDefinition = "bigint")
	private Long updatingUserId;
	private ZonedDateTime updatedTimestamp;

	@Enumerated(EnumType.STRING)
	private ProcessMethod processMethod;

	private Boolean isOnBehalf = Boolean.FALSE;

	// Mapping
	@ManyToOne(optional = false)
	@JoinColumn(name = "documentMetadataId", insertable = false, updatable = false)
	private DocumentMetadata documentMetadata;
	@Column(columnDefinition = "bigint")
	private long documentMetadataId;

	@OneToMany(mappedBy = "documentApprovalContainer")
	private List<Document> documents = new ArrayList<>();

	@OneToMany(mappedBy = "documentApprovalContainer")
	private List<ReportState> reportStates = new ArrayList<>();

	@OneToMany(mappedBy = "documentApprovalContainer")
	private List<Payment> paymentAdvices = new ArrayList<>();

	@PrePersist
	protected void onCreate() {
		ZonedDateTime now = ZonedDateTime.now();
		this.createdTimestamp = now;
		this.updatedTimestamp = now;
		if (documentApprovalCode == null) {
			documentApprovalCode = UUID.randomUUID();;
		}
	}

	@PreUpdate
	protected void onUpdate() {
		this.updatedTimestamp = ZonedDateTime.now();
	}

}
