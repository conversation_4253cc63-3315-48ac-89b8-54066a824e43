package in.taxgenie.pay_expense_pvv.entities;


import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDate;
import java.time.LocalDateTime;


@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "predefined_ageing")
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class PredefinedAgeing {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING) // Correct way to map PostgreSQL ENUM
    @Column(name = "approver_type")
    private ApproverType approverType;

    @Column(nullable = false)
    private String aging;

    @Column(nullable = false)
    private String indicator;

    @Column(nullable = false)
    private Integer sort_order;

    @Column(nullable = false)
    private Integer lower_bound;

    @Column(nullable = false)
    private Integer upper_bound;
}
