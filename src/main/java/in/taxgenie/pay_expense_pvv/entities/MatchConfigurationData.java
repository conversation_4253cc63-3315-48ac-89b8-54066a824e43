package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel;
import in.taxgenie.pay_expense_pvv.viewmodels.match.MatchConfigurationViewModel;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.BatchSize;

import java.time.ZonedDateTime;

@Builder
@Getter
@Setter
@Entity
@Table(name = "match_configuration_data", indexes = {
    @Index(name = "idx_match_config_company_code", columnList = "company_code"),
    @Index(name = "idx_match_config_criteria_name", columnList = "criteria_name")
})
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class MatchConfigurationData {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "is_2_way_match", nullable = false)
    private Boolean is2WayMatch;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "lookup_perform_match_at")
    @BatchSize(size = 20)
    private LookupData lookupPerformMatchAt;
    
    @Column(name = "criteria_name", nullable = false)
    private String criteriaName;
    
    @Column(name = "criteria_value")
    private String criteriaValue;

    @Column(name = "criteria_map_value")
    private String criteriaMapValue;

    @Column(name = "is_tolerance_allowed", nullable = false)
    private Boolean isToleranceAllowed = false;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "lookup_default_operation")
    @BatchSize(size = 20)
    private LookupData lookupDefaultOperation;
    
    @Column(name = "is_percentage", nullable = false)
    private Boolean isPercentage = false;
    
    @Column(name = "company_code")
    private Long companyCode;
    
    @Column(name = "created_timestamp")
    private ZonedDateTime createdTimestamp;
    
    @Column(name = "creating_user_id")
    private Long creatingUserId;
    
    @Column(name = "updated_timestamp")
    private ZonedDateTime updatedTimestamp;
    
    @Column(name = "updating_user_id")
    private Long updatingUserId;
    
    public MatchConfigurationViewModel toViewModel() {
        return MatchConfigurationViewModel.builder()
                .id(this.id)
                .is2WayMatch(this.is2WayMatch)
                .performMatchAt(this.lookupPerformMatchAt != null ? 
                        new LookupDataModel(
                                this.lookupPerformMatchAt.getId(),
                                this.lookupPerformMatchAt.getValue()) : null)
                .criteriaName(this.criteriaName)
                .criteriaValue(this.criteriaValue)
                .criteriaMapValue(this.criteriaMapValue)
                .isToleranceAllowed(this.isToleranceAllowed)
                .defaultOperation(this.lookupDefaultOperation != null ?
                        new LookupDataModel(
                                this.lookupDefaultOperation.getId(),
                                this.lookupDefaultOperation.getValue()) : null)
                .isPercentage(this.isPercentage)
                .build();
    }
}
