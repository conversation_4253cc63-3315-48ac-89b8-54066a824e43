package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import jakarta.persistence.*;
import lombok.*;

import java.sql.Timestamp;

@Builder
@Getter
@Setter
@Entity
@Table(name="additional_supporting_documents_to_invoice")
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "additionalSupportingDocumentId")
public class AdditionalSupportingDocumentsToInvoice {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "additional_supporting_document_id")
	private Integer additionalSupportingDocumentId;
	
	@ManyToOne
	@JoinColumn(name = "invoice_upload_id")
	private InvoiceReceived invoiceReceived;

	@Column(name= "doc_type")
	private String docType;
	
	@OneToOne
	@JoinColumn(name= "company_id")
	private Company company;
	
	@Column(name= "upload_url")
	private String uploadUrl;
	
	@Column(name= "remark")
	private String remark;
	
	@Column(name= "created_at")
	private Timestamp createdAt;
	
	@Column(name= "created_by")
	private Integer createdBy;
	
	@Column(name= "updated_at")
	private Timestamp updatedAt;
	
	@Column(name= "updated_by")
	private Integer updatedBy;
	
	@Column(name= "source")
	private String source;
	
	@Column(name= "original_doc_name")
	private String originalDocName;
	
	@OneToOne
	@JoinColumn(name= "supporting_document_category_id")
	private SupportingDocumentCategory supportingDocumentCategory;
	
}
