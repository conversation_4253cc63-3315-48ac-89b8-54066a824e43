package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import jakarta.persistence.*;

@Entity
@Table(uniqueConstraints = @UniqueConstraint(name = "HeaderKey", columnNames ={ "companyCode", "jobBand", "entitlementHeader"}))
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class EntitlementDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    private long companyCode;
    private String jobBand;

    private String entitlementHeader;

    @Column(length = 2000)
    private String entitlementInformation;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getJobBand() {
        return jobBand;
    }

    public void setJobBand(String jobBand) {
        this.jobBand = jobBand;
    }

    public long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(long companyCode) {
        this.companyCode = companyCode;
    }

    public String getEntitlementHeader() {
        return entitlementHeader;
    }

    public void setEntitlementHeader(String entitlementHeader) {
        this.entitlementHeader = entitlementHeader;
    }

    public String getEntitlementInformation() {
        return entitlementInformation;
    }

    public void setEntitlementInformation(String entitlementInformation) {
        this.entitlementInformation = entitlementInformation;
    }
}
