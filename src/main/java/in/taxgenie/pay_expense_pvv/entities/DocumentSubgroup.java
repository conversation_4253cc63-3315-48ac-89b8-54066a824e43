package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class DocumentSubgroup {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private long companyCode;

    private String documentCode;
    private String documentSubgroup;
    private String documentSubgroupPrefix;
    private String documentGenre;
    private String documentGenrePrefix;
    private boolean isLocationRequired;
    private String glAccountCode;
    private String description;

    private boolean merchantRequired;

    //  flags
    //  Should be moved to a separate entity called Expense Descriptor
    //  This is a tentative solution
    private boolean isSourceLocationApplicable;
    private boolean isDestinationLocationApplicable;
    private boolean isGstEntryAllowed;
    private boolean isStandardDeductionApplicable;
    private boolean isDateRangeApplicable;
    private boolean isDocumentIdentifierApplicable;
    private boolean isTransportDescriptorApplicable;
    private boolean isMobilityDescriptorApplicable;
    private boolean isTravelDescriptorApplicable;

    private int rulesCount;

    private String frequency;
    @Enumerated(EnumType.STRING)
    private ExpenseType applicableExpenseType;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "document_type_id", insertable = false, updatable = false)
    private LookupData documentType;
    @Column(name = "document_type_id")
    private Integer documentTypeId;


    // Foreign key from Budget microservice hence is nullable
    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "budgetId", insertable = false, updatable = false)
    private Budget budget;
    private Long budgetId;

    // Generic flag for presence of preceding document. For example PO has PR, Invoice has PO and so on
    private Boolean hasPrecedingDocument;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private String subgroupFieldsJson;

    //  Common
    private boolean isFrozen;
    private long creatingUserId;
    private ZonedDateTime createdTimestamp;
    private Long updatingUserId;
    private ZonedDateTime updatedTimestamp;

    @JsonIgnore
    //  Mapping
    @ManyToOne(optional = false)
    @JoinColumn(name = "documentMetadataId", insertable = false, updatable = false)
    private DocumentMetadata documentMetadata;
    private long documentMetadataId;

    @JsonIgnore
    @OneToMany(mappedBy = "documentSubgroup")
    private List<DocumentRule> rules = new ArrayList<>();

    @JsonIgnore
    @OneToMany(mappedBy = "documentSubgroup")
    private List<Document> documents = new ArrayList<>();
}
