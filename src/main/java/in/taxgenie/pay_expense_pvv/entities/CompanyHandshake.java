package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import jakarta.persistence.*;
import lombok.*;

import java.sql.Timestamp;

@Builder
@Getter
@Setter
@Entity
@Table(name="company_handshake")
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class CompanyHandshake {	
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
 	@Column(name = "company_handshake")
    private Integer id;
 	
 	//for supplier relationship with buyer
    @ManyToOne
    @JoinColumn(name = "supplier_id")
    private Company supplier;

    //for buyer relationship with supplier
    @ManyToOne
    @JoinColumn(name = "buyer_id")
    private Company buyer;

    @Column(name = "supplier_code")
    private String supplierCode="SPP1";
    
    @Column(name = "buyer_code")
    private String buyerCode="BUY1";
    
    @Column(name = "is_handshaked")
    private Boolean isHandShaked=false;
    
  
    @OneToOne
    @JoinColumn(name = "verification_status")
    private LookupData varificationStatus;

    @ManyToOne
    @JoinColumn(name = "request_acceptance_status")
    private LookupData requestAcceptanceStatus;
    
    @Column(name = "request_date")
    private Timestamp requestDate=new Timestamp(System.currentTimeMillis());
    
    @Column(name = "crerated_at")
    private Timestamp createdAt=new Timestamp(System.currentTimeMillis());;
    
    @Column(name = "updated_at")
    private Timestamp updatedAt=new Timestamp(System.currentTimeMillis());;
    
    @Column(name = "deleted_at")
    private Timestamp deletedAt=new Timestamp(System.currentTimeMillis());;
}
