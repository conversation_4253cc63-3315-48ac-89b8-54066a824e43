package in.taxgenie.pay_expense_pvv.entities.invoice;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import in.taxgenie.pay_expense_pvv.entities.BaseItemEntity;
import in.taxgenie.pay_expense_pvv.entities.Company;
import in.taxgenie.pay_expense_pvv.entities.InvoiceHeader;
import in.taxgenie.pay_expense_pvv.entities.Users;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetSyncMaster;
import in.taxgenie.pay_expense_pvv.entities.erp.ERPGrn;
import in.taxgenie.pay_expense_pvv.entities.gl_master.GlMaster;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderItem;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.invoice.message.PODetails;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice.InvoiceItemDetailsViewModel;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@SuperBuilder
@Getter
@Setter
@ToString
@Entity
@EntityListeners(EntityAuditListener.class)
public class InvoiceItem extends BaseItemEntity {

    @JsonIgnore
    public InvoiceItem() {
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @JsonIgnore
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "vendorId", insertable = false, updatable = false)
    private Company vendor;
    private Long vendorId;

    @Column(nullable = false)
    private Double quantity;

    @Column(nullable = false)
    private BigDecimal total; // Todo: Rename to subtotal TotAmt -> (Unit Price * Quantity) -> amountWithoutGst
    private BigDecimal totalWithTax; // Todo: Rename to total = Assessable Amount + CGST Amt + SGST Amt + Cess Amt + CesNonAdvlAmt + StateCesAmt + StateCesNonAdvlAmt + Otherchrg
    private BigDecimal discount;
    private BigDecimal assessableAmount; // total - discount
    private BigDecimal cessAmt; // Todo : Rename and group into otherCharges
    private BigDecimal stateCessAmt;
    private BigDecimal cessNonAdvolAmount;
    private BigDecimal stateCessNonAdvolAmount;
    private BigDecimal otherCharges;

    private BigDecimal igst;
    private BigDecimal cgst;
    private BigDecimal sgst;

    private Double taxPercentage; // GstRt
    private Double cessPercentage;
    private Double stateCessPercentage;


    @JsonProperty("ItemNo")
    private String serialNo;
    private Integer slNo;
    private Boolean isService;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "invoiceHeaderId", nullable = false, insertable = false, updatable = false)
    private InvoiceHeader invoiceHeader;
    private Long invoiceHeaderId;

    @JsonIgnore
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "invoice_item_purchase_order_item_mapping",
        joinColumns = @JoinColumn(name = "invoice_item_id"),
        inverseJoinColumns = @JoinColumn(name = "purchase_order_item_id")
    )
    private Set<PurchaseOrderItem> purchaseOrderItems = new HashSet<>();

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "budget_node_id", insertable = false, updatable = false)
    private Budget budgetNode;

    @Column(name = "budget_node_id")
    private Long budgetNodeId;

    @Basic
    private ItemType type;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private String dynamicFieldsJson;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "gl_master_id", insertable = false, updatable = false)
    private GlMaster glMaster;

    @Column(name = "gl_master_id")
    private Long glMasterId;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "erpGrnId", insertable = false, updatable = false)
    private ERPGrn erpGrn;
    private Long erpGrnId;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    @JsonIgnore
    public static InvoiceItemDetailsViewModel getViewModel(InvoiceItem entity) {
        if (null == entity) {
            throw new RecordNotFoundException("The invoice item was null");
        }
        // TODO : revisit - supplier from header or from item
        Company vendor = entity.getInvoiceHeader().getSupplier();

        InvoiceItemDetailsViewModel viewModel = new InvoiceItemDetailsViewModel();
        viewModel.setId(entity.getId());
        viewModel.setInvoiceHeaderId(entity.getInvoiceHeaderId());

        viewModel.setName(entity.getName());
        viewModel.setUnitOfMeasure(entity.getUnitOfMeasure());
        viewModel.setUnitOfMeasureId(null); // Todo: Either remove name fields from viewmodel or add it to entity
        viewModel.setDescription(entity.getDescription());
        viewModel.setHsn(entity.getHsn());
        viewModel.setItemCode(entity.getItemCode());
        if(entity.getType() != null) {
            viewModel.setType(entity.getType().getFormattedName());
        }
        // Handle multiple purchase order items - set the first one for backward compatibility
        if (!entity.getPurchaseOrderItems().isEmpty()) {
            PurchaseOrderItem firstPoItem = entity.getPurchaseOrderItems().iterator().next();
            viewModel.setPoItemId(firstPoItem.getId());
            viewModel.setPoId(firstPoItem.getPurchaseOrderHeaderId());
        } else {
            viewModel.setPoItemId(null);
            viewModel.setPoId(null);
        }
        if (null != entity.getQuantity()) {
            // in case of Non PO based invoice
            viewModel.setQuantity(entity.getQuantity()); // actual set quantity in invoice // for PO based quantity becomes initial PO quantity
            viewModel.setSelectedQuantity(entity.getQuantity());
        }
        viewModel.setUnitRate(entity.getUnitRate());

        // Todo: Discuss tax values
        viewModel.setTotalWithTax(entity.getTotalWithTax());
        viewModel.setDiscount(entity.getDiscount());
        viewModel.setAssessableAmount(entity.getAssessableAmount());
        viewModel.setCessAmt(entity.getCessAmt());
        viewModel.setStateCessAmt(entity.getStateCessAmt());
        viewModel.setTotal(entity.getTotal()); // totalWithoutTax -> amountWithoutGst
        viewModel.setOtherCharges(entity.getOtherCharges());
        if (null != entity.getTaxPercentage()) {
            viewModel.setTotalGstAmount(entity.getAssessableAmount().multiply(BigDecimal.valueOf(entity.getTaxPercentage() * 0.01)));
        } else {
            //TODO: needed?
        }

        viewModel.setTaxPercentage(entity.getTaxPercentage());
        viewModel.setCessPercentage(entity.getCessPercentage());
        viewModel.setStateCessPercentage(entity.getStateCessPercentage());

        if (null != entity.getBudgetNodeId()) {
            viewModel.setBudgetNodeId(entity.getBudgetNodeId());
        }
        if (null != entity.getBudgetNode()) {
            Budget budget = entity.getBudgetNode();
            BudgetSyncMaster bSync = budget.getBudgetSyncMaster();
            viewModel.setBudgetCode(budget.getBudgetCode());
            viewModel.setDisplayName(viewModel.getBudgetCode() + " - " + (null == bSync.getDescription() ? "NA" : bSync.getDescription()));
        }

        // Todo: Discuss quotation id
        viewModel.setQuotationId(null);

        if (null != vendor) {
            viewModel.setVendorId(vendor.getCompanyId());
            viewModel.setVendorName(vendor.getSupplierCompanyName());
        } else {
            viewModel.setVendorName("NA");
        }
        if (null != entity.getInvoiceHeader().getCreatingUser()) {
            Users creatingUser = entity.getInvoiceHeader().getCreatingUser();
            viewModel.setCreatedBy(creatingUser.getFirstName() + " " + creatingUser.getLastName());
        }

        viewModel.setDocNo(entity.getInvoiceHeader().getDocument().getDocNo());
        viewModel.setCreatedAt(DateTimeUtils.formatDateJPQL(entity.getCreatedAt()));

        if(null != entity.getGlMaster()) {
            viewModel.setGlId(entity.getGlMaster().getId());
            viewModel.setGlName(entity.getGlMaster().getGlName());
        }
        return viewModel;
    }

    public List<PODetails> getPoListForJson(){
        List<PODetails> poDetailsList = new ArrayList<>();

        if(null != this.getPurchaseOrderItems() && !this.getPurchaseOrderItems().isEmpty()) {
            for (PurchaseOrderItem poItem : this.getPurchaseOrderItems()) {
                PODetails poDetails = new PODetails();
                poDetails.setPoNo(poItem.getPurchaseOrderHeader().getDocument().getDocNo());
                poDetails.setPoDate(DateTimeUtils.formatDateJPQL(poItem.getPurchaseOrderHeader().getPoDate()));
                poDetails.setPodoc(poItem.getPurchaseOrderHeader().getPurchasingDocTypeFromErp());
                poDetailsList.add(poDetails);
            }
        } else {
            // Add empty PODetails for backward compatibility
            poDetailsList.add(new PODetails());
        }
        return poDetailsList;
    }
}

