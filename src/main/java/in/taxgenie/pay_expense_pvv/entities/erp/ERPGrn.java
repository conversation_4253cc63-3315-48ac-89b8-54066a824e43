package in.taxgenie.pay_expense_pvv.entities.erp;

import in.taxgenie.pay_expense_pvv.entities.invoice.InvoiceItem;
import in.taxgenie.pay_expense_pvv.viewmodels.erp.ERPGrnItemViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.erp.ERPGrnViewModel;
import jakarta.persistence.*;
import lombok.*;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Entity
@Table(name = "erp_grn")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ERPGrn {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String grnNumber;

    private LocalDate grnDate;

    private String grnReferenceId;

    private BigDecimal grnQty;

    private String grnUnit;

    private String grnStatus;

    private String grnCreatedBy;

    private String grnUserEmail;

    private long companyCode;

    @OneToMany(mappedBy = "grn", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ERPGrnItem> grnItemList;

    @JsonIgnore
    @ManyToMany(mappedBy = "erpGrns", fetch = FetchType.LAZY)
    private Set<InvoiceItem> invoiceItems = new HashSet<>();

    public static ERPGrn mapToEntity(ERPGrnViewModel viewModel) {
        ERPGrn grn = ERPGrn.builder()
                .grnNumber(viewModel.getGrnNumber())
                .grnDate(LocalDate.parse(viewModel.getGrnDate())) // Assumes format: yyyy-MM-dd
                .grnReferenceId(viewModel.getGrnReferenceId())
                .grnQty(new BigDecimal(viewModel.getGrnQty()))
                .grnUnit(viewModel.getGrnUnit())
                .grnStatus(viewModel.getGrnStatus())
                .grnCreatedBy(viewModel.getGrnCreatedBy())
                .grnUserEmail(viewModel.getGrnUserEmail())
                .build();

//        List<ERPGrnItem> items = viewModel.getGrnItemList().stream()
//                .map(item -> mapItemToEntity(item, grn))
//                .collect(Collectors.toList());
//
//        grn.setGrnItemList(items);
        return grn;
    }

    private static ERPGrnItem mapItemToEntity(ERPGrnItemViewModel itemViewModel, ERPGrn grn) {
        return ERPGrnItem.builder()
                .poLineItemNumber(itemViewModel.getPoLineItemNumber())
                .grniLineItemNumber(itemViewModel.getGrniLineItemNumber())
                .grniReceivedQuantity(new BigDecimal(itemViewModel.getGrniReceivedQuantity()))
                .poRate(new BigDecimal(itemViewModel.getPoRate()))
                .grnValue(new BigDecimal(itemViewModel.getGrnValue()))
                .grniUnitOfMeasure(itemViewModel.getGrniUnitOfMeasure())
                .materialCode(itemViewModel.getMaterialCode())
                .grn(grn)
                .build();
    }

}