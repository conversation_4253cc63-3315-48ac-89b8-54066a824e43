package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;

@MappedSuperclass
public class BaseApprovalDocument {

    private long companyCode;
    private DocumentType documentType;
    private int actionLevel = 0;
    private ExpenseActionStatus actionStatus;
    private ReportStatus reportStatus = ReportStatus.DRAFT;
    @Column(length = 250)
    private String currentApproverFirstName;
    @Column(length = 250)
    private String currentApproverLastName;
    @Column(length = 100)
    private String currentApproverEmployeeCode;
    private boolean containsSentBack;
    private String sendBackRemarks;
    private boolean containsDeviation;
    private String deviationRemarks;
    private String defaultApproverRemarks;
    private String delegationRemarks;
    private String rejectRemarks;
    private String erpRemarks;
    private String statusRemarks;

    @Column(length = 200)
    private String employeeEmail;

    // Key-value pairs
    @Column(length = 50)
    private String key01;
    @Column(length = 50)
    private String key02;
    @Column(length = 50)
    private String key03;
    @Column(length = 50)
    private String key04;
    @Column(length = 50)
    private String key05;
    @Column(length = 50)
    private String key06;
    @Column(length = 50)
    private String key07;
    @Column(length = 50)
    private String key08;
    @Column(length = 50)
    private String key09;
    @Column(length = 50)
    private String key10;

    @Column(length = 50)
    private String value01;
    @Column(length = 50)
    private String value02;
    @Column(length = 50)
    private String value03;
    @Column(length = 50)
    private String value04;
    @Column(length = 50)
    private String value05;
    @Column(length = 50)
    private String value06;
    @Column(length = 50)
    private String value07;
    @Column(length = 50)
    private String value08;
    @Column(length = 50)
    private String value09;
    @Column(length = 50)
    private String value10;

    // Common
    private long creatingUserId;
    private ZonedDateTime createdTimestamp;
    private Long updatingUserId;
    private ZonedDateTime updatedTimestamp;


    public long getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(long companyCode) {
        this.companyCode = companyCode;
    }

    public DocumentType getDocumentType() {
        return documentType;
    }

    public void setDocumentType(DocumentType documentType) {
        this.documentType = documentType;
    }

    public int getActionLevel() {
        return actionLevel;
    }

    public void setActionLevel(int actionLevel) {
        this.actionLevel = actionLevel;
    }

    public ExpenseActionStatus getActionStatus() {
        return actionStatus;
    }

    public void setActionStatus(ExpenseActionStatus actionStatus) {
        this.actionStatus = actionStatus;
    }

    public ReportStatus getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(ReportStatus reportStatus) {
        this.reportStatus = reportStatus;
    }

    public String getCurrentApproverFirstName() {
        return currentApproverFirstName;
    }

    public void setCurrentApproverFirstName(String currentApproverFirstName) {
        this.currentApproverFirstName = currentApproverFirstName;
    }

    public String getCurrentApproverLastName() {
        return currentApproverLastName;
    }

    public void setCurrentApproverLastName(String currentApproverLastName) {
        this.currentApproverLastName = currentApproverLastName;
    }

    public String getCurrentApproverEmployeeCode() {
        return currentApproverEmployeeCode;
    }

    public void setCurrentApproverEmployeeCode(String currentApproverEmployeeCode) {
        this.currentApproverEmployeeCode = currentApproverEmployeeCode;
    }

    public boolean isContainsSentBack() {
        return containsSentBack;
    }

    public void setContainsSentBack(boolean containsSentBack) {
        this.containsSentBack = containsSentBack;
    }

    public String getSendBackRemarks() {
        return sendBackRemarks;
    }

    public void setSendBackRemarks(String sendBackRemarks) {
        this.sendBackRemarks = sendBackRemarks;
    }

    public boolean isContainsDeviation() {
        return containsDeviation;
    }

    public void setContainsDeviation(boolean containsDeviation) {
        this.containsDeviation = containsDeviation;
    }

    public String getDeviationRemarks() {
        return deviationRemarks;
    }

    public void setDeviationRemarks(String deviationRemarks) {
        this.deviationRemarks = deviationRemarks;
    }

    public String getDefaultApproverRemarks() {
        return defaultApproverRemarks;
    }

    public void setDefaultApproverRemarks(String defaultApproverRemarks) {
        this.defaultApproverRemarks = defaultApproverRemarks;
    }

    public String getDelegationRemarks() {
        return delegationRemarks;
    }

    public void setDelegationRemarks(String delegationRemarks) {
        this.delegationRemarks = delegationRemarks;
    }

    public String getRejectRemarks() {
        return rejectRemarks;
    }

    public void setRejectRemarks(String rejectRemarks) {
        this.rejectRemarks = rejectRemarks;
    }

    public String getErpRemarks() {return erpRemarks;}

    public void setErpRemarks(String erpRemarks) {this.erpRemarks = erpRemarks;}

    public String getStatusRemarks() {return statusRemarks;}

    public void setStatusRemarks(String statusRemarks) {this.statusRemarks = statusRemarks;}

    public String getEmployeeEmail() {
        return employeeEmail;
    }

    public void setEmployeeEmail(String employeeEmail) {
        this.employeeEmail = employeeEmail;
    }


    public String getKey01() {
        return key01;
    }

    public void setKey01(String key01) {
        this.key01 = key01;
    }

    public String getKey02() {
        return key02;
    }

    public void setKey02(String key02) {
        this.key02 = key02;
    }

    public String getKey03() {
        return key03;
    }

    public void setKey03(String key03) {
        this.key03 = key03;
    }

    public String getKey04() {
        return key04;
    }

    public void setKey04(String key04) {
        this.key04 = key04;
    }

    public String getKey05() {
        return key05;
    }

    public void setKey05(String key05) {
        this.key05 = key05;
    }

    public String getKey06() {
        return key06;
    }

    public void setKey06(String key06) {
        this.key06 = key06;
    }

    public String getKey07() {
        return key07;
    }

    public void setKey07(String key07) {
        this.key07 = key07;
    }

    public String getKey08() {
        return key08;
    }

    public void setKey08(String key08) {
        this.key08 = key08;
    }

    public String getKey09() {
        return key09;
    }

    public void setKey09(String key09) {
        this.key09 = key09;
    }

    public String getKey10() {
        return key10;
    }

    public void setKey10(String key10) {
        this.key10 = key10;
    }

    public String getValue01() {
        return value01;
    }

    public void setValue01(String value01) {
        this.value01 = value01;
    }

    public String getValue02() {
        return value02;
    }

    public void setValue02(String value02) {
        this.value02 = value02;
    }

    public String getValue03() {
        return value03;
    }

    public void setValue03(String value03) {
        this.value03 = value03;
    }

    public String getValue04() {
        return value04;
    }

    public void setValue04(String value04) {
        this.value04 = value04;
    }

    public String getValue05() {
        return value05;
    }

    public void setValue05(String value05) {
        this.value05 = value05;
    }

    public String getValue06() {
        return value06;
    }

    public void setValue06(String value06) {
        this.value06 = value06;
    }

    public String getValue07() {
        return value07;
    }

    public void setValue07(String value07) {
        this.value07 = value07;
    }

    public String getValue08() {
        return value08;
    }

    public void setValue08(String value08) {
        this.value08 = value08;
    }

    public String getValue09() {
        return value09;
    }

    public void setValue09(String value09) {
        this.value09 = value09;
    }

    public String getValue10() {
        return value10;
    }

    public void setValue10(String value10) {
        this.value10 = value10;
    }

    public long getCreatingUserId() {
        return creatingUserId;
    }

    public void setCreatingUserId(long creatingUserId) {
        this.creatingUserId = creatingUserId;
    }

    public ZonedDateTime getCreatedTimestamp() {
        return createdTimestamp;
    }

    public void setCreatedTimestamp(ZonedDateTime createdTimestamp) {
        this.createdTimestamp = createdTimestamp;
    }

    public Long getUpdatingUserId() {
        return updatingUserId;
    }

    public void setUpdatingUserId(Long updatingUserId) {
        this.updatingUserId = updatingUserId;
    }

    public ZonedDateTime getUpdatedTimestamp() {
        return updatedTimestamp;
    }

    public void setUpdatedTimestamp(ZonedDateTime updatedTimestamp) {
        this.updatedTimestamp = updatedTimestamp;
    }

    @JsonIgnore
    public String getKeyValueJson() throws JsonProcessingException {
        Map<String, String> keyValueMap = new HashMap<>();

        // Add key-value pairs if both key and value are not null or empty
        if (key01 != null && value01 != null) keyValueMap.put(key01, value01);
        if (key02 != null && value02 != null) keyValueMap.put(key02, value02);
        if (key03 != null && value03 != null) keyValueMap.put(key03, value03);
        if (key04 != null && value04 != null) keyValueMap.put(key04, value04);
        if (key05 != null && value05 != null) keyValueMap.put(key05, value05);
        if (key06 != null && value06 != null) keyValueMap.put(key06, value06);
        if (key07 != null && value07 != null) keyValueMap.put(key07, value07);
        if (key08 != null && value08 != null) keyValueMap.put(key08, value08);
        if (key09 != null && value09 != null) keyValueMap.put(key09, value09);
        if (key10 != null && value10 != null) keyValueMap.put(key10, value10);

        // Convert the map to a JSON string
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(keyValueMap);
    }
}
