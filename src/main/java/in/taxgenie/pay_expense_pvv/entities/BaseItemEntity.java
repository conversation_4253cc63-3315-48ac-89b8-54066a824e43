package in.taxgenie.pay_expense_pvv.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;


@SuperBuilder
@Getter
@Setter
@ToString
@MappedSuperclass
public abstract class BaseItemEntity {
    public BaseItemEntity(){}

    private String name;
    private String itemCode;
    private String description;
    private String hsn;

    private BigDecimal unitRate;
    private Double gstRate;
    private String unitOfMeasure;

    private Boolean isCatalog = Boolean.FALSE;

}
