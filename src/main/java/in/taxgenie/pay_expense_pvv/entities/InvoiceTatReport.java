package in.taxgenie.pay_expense_pvv.entities;


import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Objects;


@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "invoice_tat_report")
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class InvoiceTatReport {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 255)
    private Long companyCode;

    @Column(nullable = false, length = 255)
    private String approver;

    @Column(name = "entry_date")
    private ZonedDateTime entryDate;

    @Column(name = "tat_time_days")
    private Integer tatTimeDays;

    @Column(length = 50)
    private String status;

    @Column(name = "doc_id", nullable = false)
    private Long docId;

    @ManyToOne
    @JoinColumn(name = "doc_id", referencedColumnName = "id", insertable = false, updatable = false)
    private DocumentApprovalContainer documentApprovalContainer;

    @Column(name = "key", nullable = false, length = 100)
    private String key;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String title;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String value;

    @Enumerated(EnumType.STRING) // Correct way to map PostgreSQL ENUM
    @Column(name = "approver_type")
    private ApproverType approverType;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private String metadata;

    @Column(name = "document_date")
    private LocalDate documentDate;
}
