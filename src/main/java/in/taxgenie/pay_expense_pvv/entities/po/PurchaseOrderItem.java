package in.taxgenie.pay_expense_pvv.entities.po;


import com.fasterxml.jackson.annotation.JsonIgnore;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import in.taxgenie.pay_expense_pvv.entities.BaseItemEntity;
import in.taxgenie.pay_expense_pvv.entities.Company;
import in.taxgenie.pay_expense_pvv.entities.Users;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetSyncMaster;
import in.taxgenie.pay_expense_pvv.entities.gl_master.GlMaster;
import in.taxgenie.pay_expense_pvv.entities.invoice.InvoiceItem;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestItem;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderItemDetailsViewModel;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@SuperBuilder
@Getter
@Setter
@Entity
@EntityListeners(EntityAuditListener.class)
public class PurchaseOrderItem extends BaseItemEntity {

    public PurchaseOrderItem() {
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @JsonIgnore
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "vendorId", insertable = false, updatable = false)
    private Company vendor;
    private Long vendorId;

    @Column(nullable = false)
    private Double quantity;

    @Column(nullable = false)
    private BigDecimal total;

    private BigDecimal discount;
    private BigDecimal assessableAmount; // total - discount
    private BigDecimal cessAmt; // Todo : Rename and group into otherCharges
    private BigDecimal stateCessAmt;
    private BigDecimal cessNonAdvolAmount;
    private BigDecimal stateCessNonAdvolAmount;
    private BigDecimal otherCharges;

    private BigDecimal amountWithoutGst; // Todo: Rename to taxableAmount
    private BigDecimal totalGstAmount; //  Todo: Rename to taxAmount -- // Remove - should be same as per invoice
    // Todo: Add subtotal field

    private Double taxPercentage;
    private Double cessPercentage;
    private Double stateCessPercentage;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "purchaseOrderHeaderId", nullable = false, insertable = false, updatable = false)
    private PurchaseOrderHeader purchaseOrderHeader;
    private Long purchaseOrderHeaderId;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "purchaseRequestItemId", insertable = false, updatable = false)
    private PurchaseRequestItem purchaseRequestItem;
    private Long purchaseRequestItemId;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "budget_node_id", insertable = false, updatable = false)
    private Budget budgetNode;

    @Column(name = "budget_node_id")
    private Long budgetNodeId;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "gl_master_id", insertable = false, updatable = false)
    private GlMaster glMaster;

    @Column(name = "gl_master_id")
    private Long glMasterId;

    @Basic
    private ItemType type;

    @JsonIgnore
    @ManyToMany(mappedBy = "purchaseOrderItems", fetch = FetchType.LAZY)
    private Set<InvoiceItem> invoiceItems = new HashSet<>();

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    @JsonIgnore
    public static PurchaseOrderItemDetailsViewModel getViewModel(PurchaseOrderItem entity) {
        Company vendor = entity.getVendor();

        PurchaseOrderItemDetailsViewModel viewModel = new PurchaseOrderItemDetailsViewModel();
        viewModel.setId(entity.getId()); // Todo: Convert ViewModel to use long
        if(entity.getType() != null) {
            viewModel.setType(entity.getType().getFormattedName());
        }
        viewModel.setName(entity.getName());
        viewModel.setUnitOfMeasure(entity.getUnitOfMeasure());
        viewModel.setUnitOfMeasureId(null); // Todo: Either remove name fields from viewmodel or add it to entity
        viewModel.setDescription(entity.getDescription());
        viewModel.setHsn(entity.getHsn());
        viewModel.setItemCode(entity.getItemCode());

        viewModel.setPrItemId(entity.getPurchaseRequestItemId());
        if (null != entity.getQuantity()) {
            // in case of Non PR based PO
            viewModel.setQuantity(entity.getQuantity()); // Todo: Fix type difference
            viewModel.setSelectedQuantity(entity.getQuantity());
        }
        viewModel.setUnitRate(entity.getUnitRate());

        // Todo: Discuss tax values
        viewModel.setAmountWithoutGst(entity.getAmountWithoutGst());
        viewModel.setTotalGstAmount(entity.getTotalGstAmount());

        if (null != entity.getBudgetNodeId()) {
            viewModel.setBudgetNodeId(entity.getBudgetNodeId());
        }
        if (null != entity.getBudgetNode()) {
            Budget budget = entity.getBudgetNode();
            BudgetSyncMaster bSync = budget.getBudgetSyncMaster();
            viewModel.setBudgetCode(budget.getBudgetCode());
            viewModel.setDisplayName(viewModel.getBudgetCode() + " - " + (null == bSync.getDescription() ? "NA" : bSync.getDescription()));
        }

        viewModel.setTotal(entity.getTotal());

        viewModel.setDiscount(entity.getDiscount());
        viewModel.setAssessableAmount(entity.getAssessableAmount());
        viewModel.setCessAmt(entity.getCessAmt());
        viewModel.setStateCessAmt(entity.getStateCessAmt());
        viewModel.setOtherCharges(entity.getOtherCharges());

        viewModel.setTaxPercentage(entity.getTaxPercentage());
        viewModel.setCessPercentage(entity.getCessPercentage());
        viewModel.setStateCessPercentage(entity.getStateCessPercentage());

        // Todo: Discuss quotation id
        viewModel.setQuotationId(null);

        if (null != vendor) {
            viewModel.setVendorId(vendor.getCompanyId());
            viewModel.setVendorName(vendor.getSupplierCompanyName());
        } else {
            viewModel.setVendorName("NA");
        }
        PurchaseOrderHeader purchaseOrderHeader = entity.getPurchaseOrderHeader();
        viewModel.setCurrency(purchaseOrderHeader.getCurrency());

        if (null != purchaseOrderHeader.getCreatedBy()) {
            Users creatingUser = purchaseOrderHeader.getCreatedBy();
            viewModel.setCreatedBy(creatingUser.getFirstName() + " " + creatingUser.getLastName());
        }

        GlMaster glMaster1 = entity.getGlMaster();
        if(null != glMaster1) {
            viewModel.setGlId(glMaster1.getId());
            viewModel.setGlName(glMaster1.getGlName());
            viewModel.setGlCode(glMaster1.getGl());
        }

        viewModel.setDocNo((null != purchaseOrderHeader.getHasPrecedingDocument() && purchaseOrderHeader.getHasPrecedingDocument()) ? purchaseOrderHeader.getDocument().getDocNo() : "NA");
        viewModel.setCreatedAt(DateTimeUtils.formatDateJPQL(entity.getCreatedAt()));
        viewModel.setIsCatalog(entity.getIsCatalog());
        return viewModel;
    }

    @JsonIgnore
    @Override
    public String toString() {
        return "PurchaseOrderItem{" +
                "id=" + id +
                ", quantity=" + quantity +
                ", total=" + total +
                ", taxPercentage=" + taxPercentage +
                ", amountWithoutGst=" + amountWithoutGst +
                ", totalGstAmount=" + totalGstAmount +
                ", vendor=" + vendorId +
                ", purchaseOrderHeaderId=" + purchaseOrderHeaderId +
                ", purchaseRequestItemId=" + purchaseRequestItemId +
                ", budgetNodeId=" + budgetNodeId +
                ", glMasterId=" + glMasterId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
