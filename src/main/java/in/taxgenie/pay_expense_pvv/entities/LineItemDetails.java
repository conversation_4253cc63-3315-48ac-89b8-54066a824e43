package in.taxgenie.pay_expense_pvv.entities;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import in.taxgenie.pay_expense_pvv.audit_logs.EntityAuditListener;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;

@Builder
@Getter
@Setter
@Entity
@Table(name="item_details")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EntityListeners(EntityAuditListener.class)
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class LineItemDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

//    , nullable = false
    @ManyToOne
    @JoinColumn(name = "invoice_header_id")
    private InvoiceHeader invoiceHeader;

//    , nullable = false
    @Column(name = "slno")
    private Integer slNo;

//    , length = 300
    @Column(name = "product_description")
    private String productDescription;

    @Column(name = "is_service")
    private Boolean isService;

//    , length = 8, nullable = false
    @Column(name = "hsn_code")
    private String hsnCode;

//    , length = 30
    @Column(name = "barode")
    private String barcode;

//    , nullable = false
    @Column(name = "quantity")
    private Integer quantity;

    @Column(name = "free_quantity")
    private Double freeQuantity;

//    , length = 8
    @Column(name = "uqc")
    private String uqc;

//    , nullable = false, precision = 12, scale = 3
    @Column(name = "unit_price")
    private BigDecimal unitPrice;

//    , precision = 12, scale = 2
    @Column(name = "total_amount")
    private BigDecimal totalAmount;

//    , precision = 12, scale = 2
    @Column(name = "discount")
    private BigDecimal discount;

//    , precision = 12, scale = 2
    @Column(name = "pre_tax_value")
    private BigDecimal preTaxValue;

//    , precision = 12, scale = 2
    @Column(name = "assessable_amount")
    private BigDecimal assessableAmount;
//    , precision = 3, scale = 3
    @Column(name = "gst_rate")
    private BigDecimal gstRate;

//    , precision = 12, scale = 2
    @Column(name = "igst_amount")
    private BigDecimal igstAmount;

//    , precision = 12, scale = 2
    @Column(name = "cgst_amount")
    private BigDecimal cgstAmount;

//    , precision = 12, scale = 2
    @Column(name = "sgst_amount")
    private BigDecimal sgstAmount;

//    , precision = 3, scale = 3
    @Column(name = "cess_rate")
    private BigDecimal cessRate;

//    , precision = 12, scale = 2
    @Column(name = "cess_amount")
    private BigDecimal cessAmount;

//    , precision = 12, scale = 2
    @Column(name = "cess_non_advol_amount")
    private BigDecimal cessNonAdvolAmount;

//    , precision = 3, scale = 3
    @Column(name = "state_cess_rate")
    private BigDecimal stateCessRate;

//    , precision = 12, scale = 2
    @Column(name = "state_cess_amount")
    private BigDecimal stateCessAmount;

//    , precision = 12, scale = 2
    @Column(name = "state_cess_non_advol_amount")
    private BigDecimal stateCessNonAdvolAmount;

//    , precision = 12, scale = 2
    @Column(name = "other_charges")
    private BigDecimal otherCharges;

//    , length = 50
    @Column(name = "order_line_ref")
    private String orderLineRef;

    @ManyToOne
    @JoinColumn(name = "origin_country_id")
    private Countries originCountry;

//    , length = 20
    @Column(name = "serial_no")
    private String serialNo;

//    , precision = 12, scale = 2
    @Column(name = "total_item_value")
    private BigDecimal totalItemValue;

//    , length = 20
    @Column(name = "batch_name")
    private String batchName;

//    , length = 10
    @Column(name = "batch_expiry_date")
    private LocalDate batchExpiryDate;

//    , length = 10
    @Column(name = "batch_warranty_date")
    private LocalDate batchWarrantyDate;

    @Column(name = "po_number")
    private Long poNumber;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "additional_parameters", columnDefinition = "jsonb")
    private String additionalParameters;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "budget_node_id", insertable = false, updatable = false)
    private Budget budgetNode;

    @Column(name = "budget_node_id")
    private long budgetNodeId;

    @Column(name = "created_at")
    private Timestamp createdAt;

    @Column(name = "updated_at")
    private Timestamp updatedAt;

    @Column(name = "deleted_at")
    private Timestamp deletedAt;

}
