package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import in.taxgenie.pay_expense_pvv.entities.LookupData;
import in.taxgenie.pay_expense_pvv.utils.DocDeserializer;
import in.taxgenie.pay_expense_pvv.utils.SqlDateDeserializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DocDtlsDTO {

//	@JsonDeserialize(using = DocDeserializer.class)
	@JsonProperty("Typ")
	private String Typ;

	@JsonProperty("No")
	private String No;

//	@JsonDeserialize(using = SqlDateDeserializer.class)
	@JsonProperty("Dt")
	private String Dt;

}
