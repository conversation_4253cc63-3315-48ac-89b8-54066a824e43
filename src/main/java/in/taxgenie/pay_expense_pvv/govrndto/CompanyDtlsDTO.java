package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class CompanyDtlsDTO {
	
	@JsonProperty("Gstin")
	private String gst;

	@JsonProperty("LglNm")
	private String legalName;

	@JsonProperty("TrdNm")
	private String name;

	@JsonProperty("Addr1")
	private String addr1;

	@JsonProperty("Addr2")
	private String addr2;

	@JsonProperty("Loc")
	private String loc;

	@JsonProperty("Pin")
	private String pin;

	@JsonProperty("Stcd")
	private String stcd;
	
	@JsonProperty("Ph")
	private String phoneNo;
	
	@JsonProperty("Pos")
	private String Pos;
	
	@JsonProperty("Em")
	private String emailId;

}
