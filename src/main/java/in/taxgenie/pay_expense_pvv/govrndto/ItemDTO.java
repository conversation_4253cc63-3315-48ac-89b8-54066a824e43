package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import in.taxgenie.pay_expense_pvv.entities.LookupData;
import in.taxgenie.pay_expense_pvv.invoice.message.PODetails;
import in.taxgenie.pay_expense_pvv.utils.BooleanYNDeserializer;
import in.taxgenie.pay_expense_pvv.viewmodels.erp.ERPGrnViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.MasterDataForERPViewModel;
import lombok.*;

import java.util.List;



@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
public class ItemDTO {

	@JsonProperty("ItemNo")
	private Integer serialNo;
	
	@JsonProperty("SlNo")
	private Integer slNo;

	@JsonProperty("PrdDesc")
	private String productDescription;

	@JsonProperty("IsServc")
	@JsonDeserialize(using = BooleanYNDeserializer.class)
	private Boolean isService;

	@JsonProperty("HsnCd")
	private String hsnCode;

	@JsonProperty("glCode")
	private String glCode;

	@JsonProperty("segment")
	private String segment;

	@JsonProperty("Barcde")
	private String barcode;

	@JsonProperty("Qty")
	private Double quantity;

	@JsonProperty("FreeQty")
	private Double freeQuantity;

	@JsonProperty("Unit")
	private String unit;

	@JsonProperty("UnitPrice")
	private Double unitPrice;

	@JsonProperty("TotAmt")
	private Double totalAmount;

	@JsonProperty("Discount")
	private Double discount;

	@JsonProperty("PreTaxVal")
	private Double preTaxValue;

	@JsonProperty("AssAmt")
	private Double assessableAmount;

	@JsonProperty("GstRt")
	private Double gstRate;

	@JsonProperty("IgstAmt")
	private Double igstAmount;

	@JsonProperty("CgstAmt")
	private Double cgstAmount;

	@JsonProperty("SgstAmt")
	private Double sgstAmount;

	@JsonProperty("CesRt")
	private Double cessRate;

	@JsonProperty("CesAmt")
	private Double cessAmount;

	@JsonProperty("CesNonAdvlAmt")
	private Double cessNonAdvolAmount;

	@JsonProperty("StateCesRt")
	private Double stateCessRate;

	@JsonProperty("StateCesAmt")
	private Double stateCessAmount;

	@JsonProperty("StateCesNonAdvlAmt")
	private Double stateCessNonAdvolAmount;

	@JsonProperty("OthChrg")
	private Double otherCharges;

	@JsonProperty("TotItemVal")
	private Double totalItemValue;

	@JsonProperty("OrdLineRef")
	private String orderLineRef;

	@JsonProperty("OrgCntry")
	private LookupData originCountry;

	@JsonProperty("PrdSlNo")
	private String prdSlNo;

	@JsonProperty("BchDtls")
	private BchDtlsDTO bchDtls;

	@JsonProperty("AttribDtls")
	private List<AttribDtlsDTO> attribDtls;

	private List<MasterDataForERPViewModel> masters;

	@JsonProperty("grnList")
	private List<ERPGrnViewModel> grnList;

	@JsonProperty("poList")
	private List<PODetails> poList;
}
