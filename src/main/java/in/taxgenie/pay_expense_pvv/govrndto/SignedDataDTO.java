package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class SignedDataDTO {

	@JsonProperty("AckNo")
	private Long ackNo;

	@JsonProperty("AckDt")
	private String ackDt;

	@JsonProperty("Irn")
	private String irn;

	@JsonProperty("SignedInvoice")
	private String signedInvoice;

	@JsonProperty("SignedQRCode")
	private String signedQRCode;

	@JsonProperty("Status")
	private String status;

}
