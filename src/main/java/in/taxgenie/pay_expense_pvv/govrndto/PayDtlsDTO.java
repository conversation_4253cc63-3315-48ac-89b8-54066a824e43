package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class PayDtlsDTO {

	@JsonProperty("Nm")
	private String payeeName;

	@JsonProperty("AccDet")
	private String accountDetails;

	@JsonProperty("Mode")
	private String modeOfPayment;

	@JsonProperty("FinInsBr")
	private String finInsBr;

	@JsonProperty("PayTerm")
	private String paymentTerm;

	@JsonProperty("PayInstr")
	private String paymentInstruction;

	@JsonProperty("CrTrn")
	private String creditTransfer;

	@JsonProperty("DirDr")
	private String directDebit;

	@JsonProperty("CrDay")
	private Integer creditDays;

	@JsonProperty("PaidAmt")
	private Double paidAmount;

	@JsonProperty("PaymtDue")
	private Double paymentDue;

}
