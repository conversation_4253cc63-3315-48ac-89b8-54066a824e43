package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import in.taxgenie.pay_expense_pvv.utils.BooleanYNDeserializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TranDtlsDTO {
	
	 	@JsonProperty("TaxSch")
	    private String taxSchema;

	    @JsonProperty("SupTyp")
	    private String supTyp;
	    
	    @JsonProperty("IgstOnIntra")
		@JsonDeserialize(using = BooleanYNDeserializer.class)
	    private Boolean igstOnIntra;

	    @JsonProperty("RegRev")
	    @JsonDeserialize(using = BooleanYNDeserializer.class)
	    private Boolean regRev = Boolean.FALSE;

	    @JsonProperty("EcmGstin")
	    private String ecmGstin;

		@JsonProperty("isISD")
		private Boolean isIsd;

}
