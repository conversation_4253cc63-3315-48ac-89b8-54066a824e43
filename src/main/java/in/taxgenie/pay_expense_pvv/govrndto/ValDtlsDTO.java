package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ValDtlsDTO {

	@JsonProperty("AssVal")
	private Double totalAssValue;

	@JsonProperty("IgstVal")
	private Double totalIgstValue;

	@JsonProperty("CgstVal")
	private Double totalCgstValue;

	@JsonProperty("SgstVal")
	private Double totalSgstValue;

	@JsonProperty("CesVal")
	private Double totalCessValue;

	@JsonProperty("StCesVal")
	private Double totalStateCessValue;

	@JsonProperty("tdsValue")
	private BigDecimal tdsValue;

	@JsonProperty("currency")
	private String currency;

	@JsonProperty("exchangeRate")
	private Double exchangeRate;

	@JsonProperty("Discount")
	private Double invoiceDiscount;

	@JsonProperty("OthChrg")
	private Double invoiceOtherCharges;

	@JsonProperty("RndOffAmt")
	private Double roundoffAmount;

	@JsonProperty("TotInvVal")
	private Double totalInvoiceValue;

	@JsonProperty("TotInvValFc")
	private Double totInvValFc;

}
