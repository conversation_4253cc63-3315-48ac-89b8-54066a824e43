package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import in.taxgenie.pay_expense_pvv.utils.SqlDateDeserializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class BchDtlsDTO {
	
	 @JsonProperty("Nm")
     private String batchName;

     @JsonProperty("ExpDt")
     @JsonDeserialize(using = SqlDateDeserializer.class)
     private LocalDate batchExpiryDate;

     @JsonProperty("WrDt")
     @JsonDeserialize(using = SqlDateDeserializer.class)
     private LocalDate batchWarrantyDate;

}
