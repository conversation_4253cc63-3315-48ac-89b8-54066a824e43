package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipDtlsDTO {
	
	@JsonProperty("Gstin")
    private String shipGstin;

    @JsonProperty("LglNm")
    private String shipLegalName;

    @JsonProperty("TrdNm")
    private String shipTradename;

    @JsonProperty("Addr1")
    private String shipAddress1;

    @JsonProperty("Addr2")
    private String shipAddress2;

    @JsonProperty("Loc")
    private String shipLocation;

    @JsonProperty("Pin")
    private Integer shipPin;

    @JsonProperty("Stcd")
    private String shipStateCode1;

    @JsonProperty("Em")
    private String em;

}
