package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class RefDtlsDTO {

	@JsonProperty("InvRm")
	private String invRm;

	@JsonProperty("DocPerdDtls")
	private DocPerdDtlsDTO docPerdDtls;

	@JsonProperty("PrecDocDtls")
	private List<PrecDocDtlsDTO> precDocDtls; // renamed from precDocDtls1

//	@JsonProperty("PrecDocDtls1")
//	private PrecDocDtlsDTO precDocDtls;

	@JsonProperty("ContrDtls")
	private List<ContrDtlsDTO> contrDtls;

}
