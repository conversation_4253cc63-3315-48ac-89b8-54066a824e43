package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ExpDtlsDTO {

	@JsonProperty("ShipBNo")
	private String shipBNo;

	@JsonProperty("ShipBDt")
	private String shipBDt;

	@JsonProperty("Port")
	private String port;

	@JsonProperty("RefClm")
	private String refClm;

	@JsonProperty("ForCur")
	private String forCur;

	@JsonProperty("CntCode")
	private String cntCode;

	@JsonProperty("ExpDuty")
	private Double expDuty;

}
