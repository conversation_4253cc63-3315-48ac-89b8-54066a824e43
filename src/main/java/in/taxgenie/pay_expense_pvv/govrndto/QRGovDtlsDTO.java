package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import in.taxgenie.pay_expense_pvv.viewmodels.QRDetailsBaseViewModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class QRGovDtlsDTO extends QRDetailsBaseViewModel {

    @JsonProperty("SellerGstin")
    private String sellerGstin;
    @JsonProperty("BuyerGstin")
    private String buyerGstin;
    @JsonProperty("DocNo")
    private String docNo;
    @JsonProperty("DocTyp")
    private String docTyp;
    @JsonProperty("DocDt")
    private String docDt;
    @JsonProperty("TotInvVal")
    private Integer totInvVal;
    @JsonProperty("ItemCnt")
    private Integer itemCnt;
    @JsonProperty("MainHsnCode")
    private String mainHsnCode;
    @JsonProperty("Irn")
    private String irn;
    @JsonProperty("IrnDt")
    private String irnDt;
}
