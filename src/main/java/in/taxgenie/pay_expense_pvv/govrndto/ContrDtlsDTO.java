package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ContrDtlsDTO {
	
	@JsonProperty("RecAdvRef")
    private String receiptAdviceNo;

    @JsonProperty("RecAdvDt")
    private String dateOfReceiptAdvice;

    @JsonProperty("TendRef")
    private String tendRef;

    @JsonProperty("ContrRef")
    private String contractRefNo;

    @JsonProperty("ExtRef")
    private String extRef;

    @JsonProperty("ProjRef")
    private String projectRef;

//    @JsonProperty("PORef")
//    private String poRefNo;

    @JsonProperty("PORefDt")
    private String poRefDate;
    
    @JsonProperty("PORefr")
    private String poRefNo;
}
