package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class EwbDtlsDTO {

	@JsonProperty("TransId")
	private String transId;

	@JsonProperty("TransName")
	private String transName;

	@JsonProperty("TransMode")
	private String transMode;

	@JsonProperty("Distance")
	private Integer distance;

	@JsonProperty("TransDocNo")
	private String transDocNo;

	@JsonProperty("TransDocDt")
	private String transDocDt;

	@JsonProperty("VehNo")
	private String vehNo;

	@JsonProperty("VehType")
	private String vehType;
}
