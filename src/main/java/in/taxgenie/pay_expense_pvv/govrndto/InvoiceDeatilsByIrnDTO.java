package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import in.taxgenie.pay_expense_pvv.entities.LookupData;
import in.taxgenie.pay_expense_pvv.utils.DocDeserializer;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class InvoiceDeatilsByIrnDTO {

	@JsonProperty("status")
	@JsonDeserialize(using = DocDeserializer.class)
	private LookupData invoiceStatus;

	@JsonProperty("invoice")
	InvoiceDTO invoiceJson;

	@JsonProperty("customDetails")
	CustomDetails customDetails;
}
