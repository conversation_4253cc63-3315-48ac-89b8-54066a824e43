package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import in.taxgenie.pay_expense_pvv.utils.SqlDateDeserializer;
import lombok.*;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DocPerdDtlsDTO {

	@JsonProperty("InvStDt")
	private String invoiceStartDate;
	
    @JsonProperty("InvEndDt")
    private String invoiceEndDate;

}
