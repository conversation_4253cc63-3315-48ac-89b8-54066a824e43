package in.taxgenie.pay_expense_pvv.govrndto;

import com.fasterxml.jackson.annotation.JsonProperty;
import in.taxgenie.pay_expense_pvv.invoice.message.CustomDetails;
import in.taxgenie.pay_expense_pvv.invoice.message.DispDtls;
import in.taxgenie.pay_expense_pvv.invoice.message.DispatchDetails;
import in.taxgenie.pay_expense_pvv.invoice.message.TGDetails;
import lombok.*;

import java.util.List;
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Data
@ToString
public class InvoiceDTO {

    private long documentId;
	@JsonProperty("AckNo")
	private String AckNo;

	@JsonProperty("Irn")
	private String irn;

	@JsonProperty("AckDt")
	private String AckDt;

    @JsonProperty("Version")
    private String version;

    @JsonProperty("TranDtls")
    private TranDtlsDTO tranDtls;

    @JsonProperty("DocDtls")
    private DocDtlsDTO docDtls;

    @JsonProperty("SellerDtls")
    private CompanyDtlsDTO sellerDtls;

    @JsonProperty("BuyerDtls")
    private CompanyDtlsDTO buyerDtls;

    @JsonProperty("ValDtls")
    private ValDtlsDTO valDtls;

    @JsonProperty("ExpDtls")
    private ExpDtlsDTO expDtls;

    @JsonProperty("EwbDtls")
    private EwbDtlsDTO ewbDtls;

    @JsonProperty("PayDtls")
    private PayDtlsDTO payDtls;

    @JsonProperty("RefDtls")
    private RefDtlsDTO refDtls;

    @JsonProperty("ShipDtls")
    private ShipDtlsDTO shipDtls;

    @JsonProperty("DispDtls")
    private DispDtls dispatchDetails;

    @JsonProperty("AddlDocDtls")
    private List<AddlDocDtlsDTO> addlDocDtls;

    @JsonProperty("ItemList")
    private List<ItemDTO> itemList;

    @JsonProperty("signedData")
    private SignedDataDTO signedData;

    @JsonProperty("TGDtls")
    TGDetails tgDetails;

    @JsonProperty("customDetails")
    CustomDetails customDetails;
}
