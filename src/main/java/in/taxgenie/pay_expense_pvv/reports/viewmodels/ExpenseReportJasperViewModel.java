package in.taxgenie.pay_expense_pvv.reports.viewmodels;


import in.taxgenie.pay_expense_pvv.entities.PaidStatus;

import java.math.BigDecimal;
import java.time.LocalDate;

public class ExpenseReportJasperViewModel {
    private Long id;
    private Long companyCode;
    private Integer fiscalYear;
    private String documentIdentifier;

    private LocalDate createdDate;
    private LocalDate submitDate;
    private String reportTitle;
    private BigDecimal reportClaimAmount;
    private BigDecimal voucherAmount;
    private PaidStatus paidStatus;
    private BigDecimal totalPaidAmount;
    private String currency;
    private Long delay;


    //  Employee master data
    private String firstName;
    private String middleName;
    private String lastName;
    private String employeeCode;
    private String employeeBranch;
    private String employeeName;
    private String employeeCostCenter;
    private String mobileNumber;
    private String employeeGrade;


    private String department;
    private String location;
    private String paymentStatus;
    private Long expenseMetadataId;
    private String expenseType;
    private String expenseGroup;
    private String limitExceeded;


    private String currentApproverName;
    private String currentApproverEmployeeCode;
    private boolean containsSentBack;
    private String approverCode;
    private String approverName;
    private LocalDate approvedDate;
    private String approverLevel;

    private String voucherStatus;
    private String pendingAt;

    private  Integer sentBackTimes;
    private String sentBackByCode;
    private LocalDate sentBackOnDate;
    private String sentBackOnLevel;
    private String sentBackBy;
    private String sentBackRemarks;

    private LocalDate startDate;
    private LocalDate endDate;

    //  gl related
    private LocalDate paymentDate;
    private LocalDate postedDate;
    private String paymentReference;
    private BigDecimal totalExpenses;
    private String entity;

    @Override
    public String toString() {
        return "ExpenseReportJasperViewModel{" +
                "id=" + id +
                ", companyCode=" + companyCode +
                ", fiscalYear=" + fiscalYear +
                ", documentIdentifier='" + documentIdentifier + '\'' +
                ", createdDate=" + createdDate +
                ", submitDate=" + submitDate +
                ", reportTitle='" + reportTitle + '\'' +
                ", reportClaimAmount=" + reportClaimAmount +
                ", voucherAmount=" + voucherAmount +
                ", paidStatus=" + paidStatus +
                ", totalPaidAmount=" + totalPaidAmount +
                ", currency='" + currency + '\'' +
                ", delay=" + delay +
                ", firstName='" + firstName + '\'' +
                ", middleName='" + middleName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", employeeCode='" + employeeCode + '\'' +
                ", employeeBranch='" + employeeBranch + '\'' +
                ", employeeName='" + employeeName + '\'' +
                ", employeeCostCenter='" + employeeCostCenter + '\'' +
                ", mobileNumber='" + mobileNumber + '\'' +
                ", employeeGrade='" + employeeGrade + '\'' +
                ", department='" + department + '\'' +
                ", location='" + location + '\'' +
                ", paymentStatus='" + paymentStatus + '\'' +
                ", expenseMetadataId=" + expenseMetadataId +
                ", expenseType='" + expenseType + '\'' +
                ", expenseGroup='" + expenseGroup + '\'' +
                ", limitExceeded='" + limitExceeded + '\'' +
                ", currentApproverName='" + currentApproverName + '\'' +
                ", currentApproverEmployeeCode='" + currentApproverEmployeeCode + '\'' +
                ", containsSentBack=" + containsSentBack +
                ", approverCode='" + approverCode + '\'' +
                ", approverName='" + approverName + '\'' +
                ", approvedDate=" + approvedDate +
                ", approverLevel='" + approverLevel + '\'' +
                ", voucherStatus='" + voucherStatus + '\'' +
                ", pendingAt='" + pendingAt + '\'' +
                ", sentBackTimes=" + sentBackTimes +
                ", sentBackByCode='" + sentBackByCode + '\'' +
                ", sentBackOnDate=" + sentBackOnDate +
                ", sentBackOnLevel='" + sentBackOnLevel + '\'' +
                ", sentBackBy='" + sentBackBy + '\'' +
                ", sentBackRemarks='" + sentBackRemarks + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", paymentDate=" + paymentDate +
                ", postedDate=" + postedDate +
                ", paymentReference='" + paymentReference + '\'' +
                ", totalExpenses=" + totalExpenses +
                ", entity='" + entity + '\'' +
                '}';
    }

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(Long companyCode) {
		this.companyCode = companyCode;
	}

	public Integer getFiscalYear() {
		return fiscalYear;
	}

	public void setFiscalYear(Integer fiscalYear) {
		this.fiscalYear = fiscalYear;
	}

	public String getDocumentIdentifier() {
		return documentIdentifier;
	}

	public void setDocumentIdentifier(String documentIdentifier) {
		this.documentIdentifier = documentIdentifier;
	}

	public LocalDate getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(LocalDate createdDate) {
		this.createdDate = createdDate;
	}

	public LocalDate getSubmitDate() {
		return submitDate;
	}

	public void setSubmitDate(LocalDate submitDate) {
		this.submitDate = submitDate;
	}

	public String getReportTitle() {
		return reportTitle;
	}

	public void setReportTitle(String reportTitle) {
		this.reportTitle = reportTitle;
	}

	public BigDecimal getReportClaimAmount() {
		return reportClaimAmount;
	}

	public void setReportClaimAmount(BigDecimal reportClaimAmount) {
		this.reportClaimAmount = reportClaimAmount;
	}

	public BigDecimal getVoucherAmount() {
		return voucherAmount;
	}

	public void setVoucherAmount(BigDecimal voucherAmount) {
		this.voucherAmount = voucherAmount;
	}

	public PaidStatus getPaidStatus() {
		return paidStatus;
	}

	public void setPaidStatus(PaidStatus paidStatus) {
		this.paidStatus = paidStatus;
	}

	public BigDecimal getTotalPaidAmount() {
		return totalPaidAmount;
	}

	public void setTotalPaidAmount(BigDecimal totalPaidAmount) {
		this.totalPaidAmount = totalPaidAmount;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public Long getDelay() {
		return delay;
	}

	public void setDelay(Long delay) {
		this.delay = delay;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getMiddleName() {
		return middleName;
	}

	public void setMiddleName(String middleName) {
		this.middleName = middleName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getEmployeeCode() {
		return employeeCode;
	}

	public void setEmployeeCode(String employeeCode) {
		this.employeeCode = employeeCode;
	}

	public String getEmployeeBranch() {
		return employeeBranch;
	}

	public void setEmployeeBranch(String employeeBranch) {
		this.employeeBranch = employeeBranch;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public String getEmployeeCostCenter() {
		return employeeCostCenter;
	}

	public void setEmployeeCostCenter(String employeeCostCenter) {
		this.employeeCostCenter = employeeCostCenter;
	}

	public String getMobileNumber() {
		return mobileNumber;
	}

	public void setMobileNumber(String mobileNumber) {
		this.mobileNumber = mobileNumber;
	}

	public String getEmployeeGrade() {
		return employeeGrade;
	}

	public void setEmployeeGrade(String employeeGrade) {
		this.employeeGrade = employeeGrade;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getPaymentStatus() {
		return paymentStatus;
	}

	public void setPaymentStatus(String paymentStatus) {
		this.paymentStatus = paymentStatus;
	}

	public Long getExpenseMetadataId() {
		return expenseMetadataId;
	}

	public void setExpenseMetadataId(Long expenseMetadataId) {
		this.expenseMetadataId = expenseMetadataId;
	}

	public String getExpenseType() {
		return expenseType;
	}

	public void setExpenseType(String expenseType) {
		this.expenseType = expenseType;
	}

	public String getExpenseGroup() {
		return expenseGroup;
	}

	public void setExpenseGroup(String expenseGroup) {
		this.expenseGroup = expenseGroup;
	}

	public String getLimitExceeded() {
		return limitExceeded;
	}

	public void setLimitExceeded(String limitExceeded) {
		this.limitExceeded = limitExceeded;
	}

	public String getCurrentApproverName() {
		return currentApproverName;
	}

	public void setCurrentApproverName(String currentApproverName) {
		this.currentApproverName = currentApproverName;
	}

	public String getCurrentApproverEmployeeCode() {
		return currentApproverEmployeeCode;
	}

	public void setCurrentApproverEmployeeCode(String currentApproverEmployeeCode) {
		this.currentApproverEmployeeCode = currentApproverEmployeeCode;
	}

	public boolean isContainsSentBack() {
		return containsSentBack;
	}

	public void setContainsSentBack(boolean containsSentBack) {
		this.containsSentBack = containsSentBack;
	}

	public String getApproverCode() {
		return approverCode;
	}

	public void setApproverCode(String approverCode) {
		this.approverCode = approverCode;
	}

	public String getApproverName() {
		return approverName;
	}

	public void setApproverName(String approverName) {
		this.approverName = approverName;
	}

	public LocalDate getApprovedDate() {
		return approvedDate;
	}

	public void setApprovedDate(LocalDate approvedDate) {
		this.approvedDate = approvedDate;
	}

	public String getApproverLevel() {
		return approverLevel;
	}

	public void setApproverLevel(String approverLevel) {
		this.approverLevel = approverLevel;
	}

	public String getVoucherStatus() {
		return voucherStatus;
	}

	public void setVoucherStatus(String voucherStatus) {
		this.voucherStatus = voucherStatus;
	}

	public String getPendingAt() {
		return pendingAt;
	}

	public void setPendingAt(String pendingAt) {
		this.pendingAt = pendingAt;
	}

	public Integer getSentBackTimes() {
		return sentBackTimes;
	}

	public void setSentBackTimes(Integer sentBackTimes) {
		this.sentBackTimes = sentBackTimes;
	}

	public String getSentBackByCode() {
		return sentBackByCode;
	}

	public void setSentBackByCode(String sentBackByCode) {
		this.sentBackByCode = sentBackByCode;
	}

	public LocalDate getSentBackOnDate() {
		return sentBackOnDate;
	}

	public void setSentBackOnDate(LocalDate sentBackOnDate) {
		this.sentBackOnDate = sentBackOnDate;
	}

	public String getSentBackOnLevel() {
		return sentBackOnLevel;
	}

	public void setSentBackOnLevel(String sentBackOnLevel) {
		this.sentBackOnLevel = sentBackOnLevel;
	}

	public String getSentBackBy() {
		return sentBackBy;
	}

	public void setSentBackBy(String sentBackBy) {
		this.sentBackBy = sentBackBy;
	}

	public String getSentBackRemarks() {
		return sentBackRemarks;
	}

	public void setSentBackRemarks(String sentBackRemarks) {
		this.sentBackRemarks = sentBackRemarks;
	}

	public LocalDate getStartDate() {
		return startDate;
	}

	public void setStartDate(LocalDate startDate) {
		this.startDate = startDate;
	}

	public LocalDate getEndDate() {
		return endDate;
	}

	public void setEndDate(LocalDate endDate) {
		this.endDate = endDate;
	}

	public LocalDate getPaymentDate() {
		return paymentDate;
	}

	public void setPaymentDate(LocalDate paymentDate) {
		this.paymentDate = paymentDate;
	}

	public LocalDate getPostedDate() {
		return postedDate;
	}

	public void setPostedDate(LocalDate postedDate) {
		this.postedDate = postedDate;
	}

	public String getPaymentReference() {
		return paymentReference;
	}

	public void setPaymentReference(String paymentReference) {
		this.paymentReference = paymentReference;
	}

	public BigDecimal getTotalExpenses() {
		return totalExpenses;
	}

	public void setTotalExpenses(BigDecimal totalExpenses) {
		this.totalExpenses = totalExpenses;
	}

	public String getEntity() {
		return entity;
	}

	public void setEntity(String entity) {
		this.entity = entity;
	}

    
}
