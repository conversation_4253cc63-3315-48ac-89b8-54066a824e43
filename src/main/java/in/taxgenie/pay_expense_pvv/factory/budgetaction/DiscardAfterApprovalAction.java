package in.taxgenie.pay_expense_pvv.factory.budgetaction;

public class DiscardAfterApprovalAction implements Action{
    private DocumentAction documentAction;

    public DiscardAfterApprovalAction(DocumentAction documentAction) {
        this.documentAction = documentAction;
    }

    @Override
    public FactoryResponse execute(DocumentData documentData) {
        return documentAction.discardAfterApproval(documentData);
    }
}
