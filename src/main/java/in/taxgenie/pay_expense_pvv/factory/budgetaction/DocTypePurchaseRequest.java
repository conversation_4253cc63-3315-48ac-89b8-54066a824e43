package in.taxgenie.pay_expense_pvv.factory.budgetaction;

import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;


public class DocTypePurchaseRequest extends DocTypeBase implements DocumentAction  {

    private final Logger logger;

    public DocTypePurchaseRequest() {
        super();
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public FactoryResponse accepted(DocumentData documentData) {

        logger.info("In accepted method of DocTypePurchaseRequest.");
        FactoryResponse factoryResponse = new FactoryResponse();

        try{

            //Call for base check
            factoryResponse = basicCheck(documentData);
            if(!factoryResponse.getSuccess()){
                return  factoryResponse;
            }

            if(getAmountToBeConsumed().compareTo(getAvailableAmount()) <= 0)
            {
                BigDecimal newSoftLockAmount = getSoftLockedAmount().add(getAmountToBeConsumed());
                BigDecimal newSoftLockBudgetAmount = getSoftLockedBudgetAmount().add(getAmountToBeConsumed());
                if(newSoftLockAmount.compareTo(getTotalAmount()) <= 0 &&
                newSoftLockBudgetAmount.compareTo(getTotalBudgetAmount()) <= 0) {

                    getBudgetFrequencyMapping().setSoftLocked(newSoftLockAmount);
                    getBudget().setSoftLocked(newSoftLockBudgetAmount);

                    factoryResponse.setSuccess(true);
                    factoryResponse.setMessage("Amount SoftLocked Successfully.");

                } else{
                    factoryResponse.setSuccess(false);
                    factoryResponse.setMessage("After calculation soft lock amount is greater than total budget amount");
                    throw new DomainInvariantException("After calculation soft lock amount is greater than total budget amount");
                }

            } else{
                factoryResponse.setSuccess(false);
                factoryResponse.setMessage("PR amount is greater than allocated budget.");
                throw new DomainInvariantException("PR amount is greater than allocated budget.");
            }

        }catch (DomainInvariantException e) {
            throw new DomainInvariantException(e.getMessage());
        }catch (Exception e){
            factoryResponse.setSuccess(false);
            factoryResponse.setMessage("Exception while handling approve action of PR. Exception Message : " + e.getMessage());
            logger.error("Exception while handling approve action of PR. Exception Message : {}", e.getMessage());
        }
        factoryResponse.setBudgetFrequencyMapping(getBudgetFrequencyMapping());
        factoryResponse.setBudget(documentData.getBudget());
        return factoryResponse;
    }


    @Override
    public FactoryResponse declined(DocumentData documentData) {

        logger.info("In declined method of DocTypePurchaseRequest.");
        FactoryResponse factoryResponse = new FactoryResponse();

        try{

            //Call for base check
            factoryResponse = basicCheck(documentData);
            if(!factoryResponse.getSuccess()){
                return  factoryResponse;
            }

            if(getAmountToBeConsumed().compareTo(getAvailableAmount()) <= 0 )
            {

                BigDecimal newSoftLockAmount = getBudgetFrequencyMapping().getSoftLocked().subtract(getAmountToBeConsumed());
                BigDecimal newSoftLockBudgetAmount = getBudget().getSoftLocked().subtract(getAmountToBeConsumed());


                if(newSoftLockAmount.compareTo(getTotalAmount()) <= 0 &&
                        newSoftLockBudgetAmount.compareTo(getTotalBudgetAmount()) <= 0) {

                    getBudgetFrequencyMapping().setSoftLocked(newSoftLockAmount);
                    getBudget().setSoftLocked(newSoftLockBudgetAmount);

                    factoryResponse.setSuccess(true);
                    factoryResponse.setMessage("Amount SoftLocked Successfully.");

                } else{
                    factoryResponse.setSuccess(false);
                    factoryResponse.setMessage("After calculation soft lock amount is greater than total budget amount");
                }

            } else{
                factoryResponse.setSuccess(false);
                factoryResponse.setMessage("PR amount is greater than allocated budget.");

            }

        }catch (Exception e){
            factoryResponse.setSuccess(false);
            factoryResponse.setMessage("Exception while handling declined action of PR. Exception Message : " + e.getMessage());
            logger.error("Exception while handling declined action of PR. Exception Message : {}", e.getMessage());
        }
        factoryResponse.setBudgetFrequencyMapping(getBudgetFrequencyMapping());
        factoryResponse.setBudget(documentData.getBudget());
        return factoryResponse;
    }
    @Override
    public FactoryResponse paid(DocumentData documentData) {
        logger.info("In paid method of DocTypePurchaseRequest. Body for this method is empty.");

        FactoryResponse factoryResponse = new FactoryResponse();
        factoryResponse.setSuccess(true);
        factoryResponse.setMessage("Body for this method is empty.");
        factoryResponse.setBudgetFrequencyMapping(documentData.getBudgetFrequencyMapping());
        factoryResponse.setBudget(documentData.getBudget());

        return factoryResponse;
    }

    @Override
    public FactoryResponse discard(DocumentData documentData) {
        logger.info("In discard method of DocTypePurchaseRequest. Body for this method is empty.");

        FactoryResponse factoryResponse = new FactoryResponse();
        factoryResponse.setSuccess(true);
        factoryResponse.setMessage("Body for this method is empty.");
        factoryResponse.setBudgetFrequencyMapping(documentData.getBudgetFrequencyMapping());
        factoryResponse.setBudget(documentData.getBudget());

        return factoryResponse;
    }
    @Override
    public FactoryResponse discardAfterApproval(DocumentData documentData) {

        logger.info("In discard method of DocTypePurchaseRequest. Body for this method is empty.");
        FactoryResponse factoryResponse = declined(documentData);
        return factoryResponse;
    }
}

