package in.taxgenie.pay_expense_pvv.factory.budgetaction;

import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;


public class DocTypeInvoice extends DocTypeBase implements DocumentAction {
    private final Logger logger;

    public DocTypeInvoice() {
        super();
        this.logger = LoggerFactory.getLogger(this.getClass());
    }


    @Override
    public FactoryResponse accepted(DocumentData documentData) {

        logger.info("In accepted method of DocTypeInvoice.");
        FactoryResponse factoryResponse = new FactoryResponse();
        try {

            factoryResponse = basicCheck(documentData);
            if (!factoryResponse.getSuccess()) {
                return factoryResponse;
            }

            if (documentData.getInvoiceType().equals("CNR") || documentData.getInvoiceType().equals("CNUR")) {
//                BigDecimal newTreasuryAmount = getBudgetFrequencyMapping().getTreasury().add(getAmountToBeConsumed());
//
//                BigDecimal lockAmount = getBudgetFrequencyMapping().getLocked().add(getAmountToBeConsumed());
//                BigDecimal lockBudgetAmount = getBudget().getLocked().add(getAmountToBeConsumed());
//
//                getBudgetFrequencyMapping().setTreasury(newTreasuryAmount);
//
//                if (documentData.getIsBase() && getBaseBudgetFrequencyMapping() != null && getBaseBudget() != null) {
//                    getBaseBudgetFrequencyMapping().setLocked(lockAmount);
//                    getBaseBudget().setLocked(lockBudgetAmount);
//                }
//
//                factoryResponse.setSuccess(true);
//                factoryResponse.setMessage("Amount Removed From Locked Successfully.");
                logger.info("Invoice is of type "+documentData.getInvoiceType()+" Skipping budget consumption");
            } else if (documentData.getIsBase() || getAmountToBeConsumed().compareTo(getAvailableAmount()) <= 0) {

                BigDecimal newConsumedAmount = getConsumedAmount().add(getAmountToBeConsumed());
                BigDecimal newConsumedBudgetAmount = getConsumedBudgetAmount().add(getAmountToBeConsumed());

                BigDecimal newLockAmount = getLockedAmount().subtract(getAmountToBeConsumed());
                BigDecimal newLockBudgetAmount = getLockedBudgetAmount().subtract(getAmountToBeConsumed());

                if (newConsumedAmount.compareTo(getTotalAmount()) <= 0) {

                    getBudgetFrequencyMapping().setConsumed(newConsumedAmount);
                    getBudget().setConsumed(newConsumedBudgetAmount);

                    if (documentData.getIsBase() && getBaseBudgetFrequencyMapping() != null && getBaseBudget() != null) {
                        getBaseBudgetFrequencyMapping().setLocked(newLockAmount);
                        getBaseBudget().setLocked(newLockBudgetAmount);
                    }

                    factoryResponse.setSuccess(true);
                    factoryResponse.setMessage("Amount Locked Successfully.");
                } else {

                    factoryResponse.setSuccess(false);
                    factoryResponse.setMessage("After calculation invoice amount is greater than total");
                    throw new DomainInvariantException("After calculation invoice amount is greater than total");
                }

            } else {
                factoryResponse.setSuccess(false);
                factoryResponse.setMessage("Invoice amount is greater than allocated budget.");
                throw new DomainInvariantException("Invoice amount is greater than allocated budget.");
            }

        } catch (DomainInvariantException e) {
            throw new DomainInvariantException(e.getMessage());
        }
        catch (Exception e) {
            factoryResponse.setSuccess(false);
            factoryResponse.setMessage("Exception while handling approve action of Invoice. Exception Message : " + e.getMessage());
            logger.error("Exception while handling approve action of Invoice. Exception Message : {}", e.getMessage());
        }
        factoryResponse.setBudgetFrequencyMapping(getBudgetFrequencyMapping());
        factoryResponse.setBudget(documentData.getBudget());

        factoryResponse.setBaseBudgetFrequencyMapping(getBaseBudgetFrequencyMapping());
        factoryResponse.setBaseBudget(documentData.getBaseBudget());

        return factoryResponse;
    }


    @Override
    public FactoryResponse declined(DocumentData documentData) {

        logger.info("In declined method of DocTypeInvoice.");
        FactoryResponse factoryResponse = new FactoryResponse();
        try {

            factoryResponse = basicCheck(documentData);
            if (!factoryResponse.getSuccess()) {
                return factoryResponse;
            }

            if (getAmountToBeConsumed().compareTo(getTotalAmount()) <= 0 &&
                    getAmountToBeConsumed().compareTo(getConsumedAmount()) <= 0) {

                BigDecimal newConsumedAmount = getBudgetFrequencyMapping().getConsumed().subtract(getAmountToBeConsumed());
                BigDecimal newConsumedBudgetAmount = getBudget().getConsumed().subtract(getAmountToBeConsumed());

                BigDecimal lockAmount = getBudgetFrequencyMapping().getLocked().add(getAmountToBeConsumed());
                BigDecimal lockBudgetAmount = getBudget().getLocked().add(getAmountToBeConsumed());

                getBudgetFrequencyMapping().setConsumed(newConsumedAmount);
                getBudget().setConsumed(newConsumedBudgetAmount);

                if (documentData.getIsBase() && getBaseBudgetFrequencyMapping() != null && getBaseBudget() != null) {
                    getBaseBudgetFrequencyMapping().setLocked(lockAmount);
                    getBaseBudget().setLocked(lockBudgetAmount);
                }

                factoryResponse.setSuccess(true);
                factoryResponse.setMessage("Amount Removed From Locked Successfully.");

            } else {
                factoryResponse.setSuccess(false);
                factoryResponse.setMessage("Invoice amount is greater than allocated budget.");
            }

        } catch (Exception e) {
            factoryResponse.setSuccess(false);
            factoryResponse.setMessage("Exception while handling declined action of Invoice. Exception Message : " + e.getMessage());
            logger.error("Exception while handling declined action of Invoice. Exception Message : {}", e.getMessage());
        }
        factoryResponse.setBudgetFrequencyMapping(getBudgetFrequencyMapping());
        factoryResponse.setBudget(documentData.getBudget());

        factoryResponse.setBaseBudgetFrequencyMapping(getBaseBudgetFrequencyMapping());
        factoryResponse.setBaseBudget(documentData.getBaseBudget());

        return factoryResponse;
    }

    @Override
    public FactoryResponse paid(DocumentData documentData) {

        logger.info("In paid method of DocTypeInvoice.");
        FactoryResponse factoryResponse = new FactoryResponse();
        try{

            factoryResponse = basicCheck(documentData);
            if(!factoryResponse.getSuccess()){
                return  factoryResponse;
            }

            if(documentData.getInvoiceType().equals("CNR") || documentData.getInvoiceType().equals("CNUR")) {
                BigDecimal newTreasuryAmount = getBudgetFrequencyMapping().getTreasury().add(getAmountToBeConsumed());

                BigDecimal lockAmount = getBudgetFrequencyMapping().getLocked().add(getAmountToBeConsumed());
                BigDecimal lockBudgetAmount = getBudget().getLocked().add(getAmountToBeConsumed());

                getBudgetFrequencyMapping().setTreasury(newTreasuryAmount);

                if (documentData.getIsBase() && getBaseBudgetFrequencyMapping() != null && getBaseBudget() != null) {
                    getBaseBudgetFrequencyMapping().setLocked(lockAmount);
                    getBaseBudget().setLocked(lockBudgetAmount);
                }

                factoryResponse.setSuccess(true);
                factoryResponse.setMessage("Amount Removed From Locked Successfully.");
            } else{
                factoryResponse.setSuccess(true);
                factoryResponse.setMessage("Invoice type is not Credit Note. So, Budget cannot be consumed.");
                return factoryResponse;
            }

        }catch (Exception e){
            factoryResponse.setSuccess(false);
            factoryResponse.setMessage("Exception while handling paid action of Invoice. Exception Message : " + e.getMessage());
            logger.error("Exception while handling paid action of Invoice. Exception Message : {}", e.getMessage());
        }
        factoryResponse.setBudgetFrequencyMapping(getBudgetFrequencyMapping());
        factoryResponse.setBudget(documentData.getBudget());

        factoryResponse.setBaseBudgetFrequencyMapping(getBaseBudgetFrequencyMapping());
        factoryResponse.setBaseBudget(documentData.getBaseBudget());

        return factoryResponse;
    }

    @Override
    public FactoryResponse discard(DocumentData documentData) {

        logger.info("In discard method of DocTypeInvoice. Body for this method is empty.");

        FactoryResponse factoryResponse = new FactoryResponse();
        factoryResponse.setSuccess(true);
        factoryResponse.setMessage("Body for this method is empty.");
        factoryResponse.setBudgetFrequencyMapping(documentData.getBudgetFrequencyMapping());
        factoryResponse.setBudget(documentData.getBudget());

        return factoryResponse;

    }

    @Override
    public FactoryResponse discardAfterApproval(DocumentData documentData) {

        logger.info("In discardAfterApproval method of DocTypeInvoice");
        FactoryResponse factoryResponse = declined(documentData);
        return factoryResponse;
    }
}