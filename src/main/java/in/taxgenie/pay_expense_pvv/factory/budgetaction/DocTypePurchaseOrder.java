package in.taxgenie.pay_expense_pvv.factory.budgetaction;

import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

public class DocTypePurchaseOrder extends DocTypeBase implements DocumentAction {

    private final Logger logger;

    public DocTypePurchaseOrder() {
        super();
        this.logger = LoggerFactory.getLogger(this.getClass());
    }


    @Override
    public FactoryResponse accepted(DocumentData documentData) {

        logger.info("In accepted method of DocTypePurchaseOrder.");

        FactoryResponse factoryResponse = new FactoryResponse();

        try{

            //Call for base check
            factoryResponse = basicCheck(documentData);
            if(!factoryResponse.getSuccess()){
                return  factoryResponse;
            }

            if(getAmountToBeConsumed().compareTo(getAvailableAmount()) <= 0)
            {
                BigDecimal newLockAmount = getLockedAmount().add(getAmountToBeConsumed());
                BigDecimal newLockBudgetAmount = getLockedBudgetAmount().add(getAmountToBeConsumed());

                BigDecimal newSoftLockAmount = getSoftLockedAmount().subtract(getAmountToBeConsumed());
                BigDecimal newSoftLockBudgetAmount = getSoftLockedBudgetAmount().subtract(getAmountToBeConsumed());

                if(newLockAmount.compareTo(getTotalAmount()) <= 0) {

                    getBudgetFrequencyMapping().setLocked(newLockAmount);
                    getBudget().setLocked(newLockBudgetAmount);

                    if(documentData.isBase && getBaseBudgetFrequencyMapping() != null && getBaseBudget() != null) {
                        getBaseBudgetFrequencyMapping().setSoftLocked(newSoftLockAmount);
                        getBaseBudget().setSoftLocked(newSoftLockBudgetAmount);
                    }

                    factoryResponse.setSuccess(true);
                    factoryResponse.setMessage("Amount Locked Successfully.");

                } else{
                    factoryResponse.setSuccess(false);
                    factoryResponse.setMessage("After calculation lock amount is greater than total budget amount");
                    throw new DomainInvariantException("After calculation lock amount is greater than total budget amount");
                }

            } else{
                factoryResponse.setSuccess(false);
                factoryResponse.setMessage("PO amount is greater than allocated budget.");
                throw new DomainInvariantException("PO amount is greater than allocated budget.");
            }

        }catch (DomainInvariantException e) {
            throw new DomainInvariantException(e.getMessage());
        }catch (Exception e){
            factoryResponse.setSuccess(false);
            factoryResponse.setMessage("Exception while handling approve action of PO. Exception Message : " + e.getMessage());
            logger.error("Exception while handling approve action of PO. Exception Message : {}", e.getMessage());
        }
        factoryResponse.setBudgetFrequencyMapping(getBudgetFrequencyMapping());
        factoryResponse.setBudget(documentData.getBudget());

        factoryResponse.setBaseBudgetFrequencyMapping(getBaseBudgetFrequencyMapping());
        factoryResponse.setBaseBudget(documentData.getBaseBudget());

        return factoryResponse;
    }



    @Override
    public FactoryResponse declined(DocumentData documentData) {

        logger.info("In declined method of DocTypePurchaseOrder.");
        FactoryResponse factoryResponse = new FactoryResponse();
        try{

            factoryResponse = basicCheck(documentData);
            if(!factoryResponse.getSuccess()){
                return  factoryResponse;
            }

            if(getAmountToBeConsumed().compareTo(getTotalAmount()) <= 0 &&
                    documentData.getAmount().compareTo(getLockedAmount()) <= 0)
            {

                BigDecimal softLockAmount = getBudgetFrequencyMapping().getSoftLocked().add(getAmountToBeConsumed());
                BigDecimal softLockBudgetAmount = getBudget().getSoftLocked().add(getAmountToBeConsumed());

                BigDecimal lockAmount = getBudgetFrequencyMapping().getLocked().subtract(getAmountToBeConsumed());
                BigDecimal lockBudgetAmount = getBudget().getLocked().subtract(getAmountToBeConsumed());

                getBudgetFrequencyMapping().setLocked(lockAmount);
                getBudget().setLocked(lockBudgetAmount);

                if(documentData.isBase && getBaseBudgetFrequencyMapping() != null && getBaseBudget() != null) {
                    getBaseBudgetFrequencyMapping().setSoftLocked(softLockAmount);
                    getBaseBudget().setSoftLocked(softLockBudgetAmount);
                }

                factoryResponse.setSuccess(true);
                factoryResponse.setMessage("Amount Removed From Locked Successfully.");
                factoryResponse.setBudgetFrequencyMapping(getBudgetFrequencyMapping());
                factoryResponse.setBudget(documentData.getBudget());

            } else{
                factoryResponse.setSuccess(false);
                factoryResponse.setMessage("PO amount is greater than allocated budget.");
                factoryResponse.setBudgetFrequencyMapping(getBudgetFrequencyMapping());
                factoryResponse.setBudget(documentData.getBudget());
            }
            factoryResponse.setBaseBudgetFrequencyMapping(getBaseBudgetFrequencyMapping());
            factoryResponse.setBaseBudget(documentData.getBaseBudget());

        }catch (Exception e){
            factoryResponse.setSuccess(false);
            factoryResponse.setMessage("Exception while handling declined action of PO. Exception Message : " + e.getMessage());
            factoryResponse.setBudgetFrequencyMapping(documentData.getBudgetFrequencyMapping());
            factoryResponse.setBudget(documentData.getBudget());

            logger.error("Exception while handling declined action of PO. Exception Message : " + e.getMessage());
        }
        return factoryResponse;
    }
    @Override
    public FactoryResponse paid(DocumentData documentData) {
        logger.info("In paid method of DocTypePurchaseRequest. Body for this method is empty.");

        FactoryResponse factoryResponse = new FactoryResponse();
        factoryResponse.setSuccess(true);
        factoryResponse.setMessage("Body for this method is empty.");
        factoryResponse.setBudgetFrequencyMapping(documentData.getBudgetFrequencyMapping());
        factoryResponse.setBudget(documentData.getBudget());

        return factoryResponse;
    }

    @Override
    public FactoryResponse discard(DocumentData documentData) {
        logger.info("In discard method of DocTypePurchaseOrder. Body for this method is empty.");

        FactoryResponse factoryResponse = new FactoryResponse();
        factoryResponse.setSuccess(true);
        factoryResponse.setMessage("Body for this method is empty.");
        factoryResponse.setBudgetFrequencyMapping(documentData.getBudgetFrequencyMapping());
        factoryResponse.setBudget(documentData.getBudget());

        return factoryResponse;
    }

    @Override
    public FactoryResponse discardAfterApproval(DocumentData documentData) {

        logger.info("In discard method of DocTypePurchaseOrder. Body for this method is empty.");
        FactoryResponse factoryResponse = declined(documentData);
        return factoryResponse;
    }
}

