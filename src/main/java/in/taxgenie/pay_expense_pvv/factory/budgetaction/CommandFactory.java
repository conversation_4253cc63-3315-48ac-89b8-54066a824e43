package in.taxgenie.pay_expense_pvv.factory.budgetaction;

public class CommandFactory {
    public static Action createCommand(DocumentData documentData) {
        DocumentAction documentAction = switch (documentData.getDocumentType()) {
            case PURCHASE_REQUEST -> new DocTypePurchaseRequest();
            case PURCHASE_ORDER -> new DocTypePurchaseOrder();
            case INVOICE -> new DocTypeInvoice();
            default -> throw new IllegalArgumentException("Unknown document type");
        };

        return switch (documentData.getAction()) {
            case ACCEPTED -> new AcceptedAction(documentAction);
            case DECLINED -> new RevokedAction(documentAction);
            case PAID -> new PaidAction(documentAction);
            case REVOKED -> new DiscardAction(documentAction);
            case CANCELLED, DELETED, VALIDATION_FAILED -> new DiscardAfterApprovalAction(documentAction);
            default -> throw new IllegalArgumentException("Unknown action");
        };
    }
}