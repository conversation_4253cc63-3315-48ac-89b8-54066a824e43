package in.taxgenie.pay_expense_pvv.response.interfaces.infra;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import in.taxgenie.pay_expense_pvv.response.implementations.infra.ServerResponseWithBodyImplementation;

@JsonDeserialize(as = ServerResponseWithBodyImplementation.class)
public interface IServerResponseWithBody<T>
        extends
        IServerResponseWithoutBody {
    T getBody();
}
