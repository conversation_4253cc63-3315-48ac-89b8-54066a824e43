package in.taxgenie.pay_expense_pvv.response.interfaces.infra;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import in.taxgenie.pay_expense_pvv.response.implementations.infra.ServerResponseWithoutBodyImplementation;

@JsonDeserialize(as = ServerResponseWithoutBodyImplementation.class)
public interface IServerResponseWithoutBody {
    int getResponseCode();

    String getMessage();

    boolean isOk();
}
