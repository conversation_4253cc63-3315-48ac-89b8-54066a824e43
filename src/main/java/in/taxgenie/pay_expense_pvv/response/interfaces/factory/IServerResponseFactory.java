package in.taxgenie.pay_expense_pvv.response.interfaces.factory;

import in.taxgenie.pay_expense_pvv.response.interfaces.infra.IServerResponseWithBody;
import in.taxgenie.pay_expense_pvv.response.interfaces.infra.IServerResponseWithoutBody;

public interface IServerResponseFactory {
    IServerResponseWithoutBody getServerResponseWithoutBody(
            int responseCode,
            String message,
            boolean isOk
    );

    <T> IServerResponseWithBody<T> getServerResponseWithBody(
            int responseCode,
            String message,
            boolean isOk,
            T body
    );
}
