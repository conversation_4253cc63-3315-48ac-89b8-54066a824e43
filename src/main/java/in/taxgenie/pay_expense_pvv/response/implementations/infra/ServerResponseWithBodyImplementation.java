package in.taxgenie.pay_expense_pvv.response.implementations.infra;

import in.taxgenie.pay_expense_pvv.response.interfaces.infra.AbstractServerResponseWithBody;

public class ServerResponseWithBodyImplementation<T>
        extends
        AbstractServerResponseWithBody<T> {
    public ServerResponseWithBodyImplementation(
            int responseCode,
            String message,
            boolean isOk,
            T body
    ) {
        super(responseCode, message, isOk, body);
    }
}
