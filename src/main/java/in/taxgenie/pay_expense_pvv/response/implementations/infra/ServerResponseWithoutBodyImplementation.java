package in.taxgenie.pay_expense_pvv.response.implementations.infra;


import in.taxgenie.pay_expense_pvv.response.interfaces.infra.AbstractServerResponseWithoutBody;

public class ServerResponseWithoutBodyImplementation
        extends
        AbstractServerResponseWithoutBody {
    public ServerResponseWithoutBodyImplementation(
            int responseCode,
            String message,
            boolean isOk
    ) {
        super(responseCode, message, isOk);
    }
}
