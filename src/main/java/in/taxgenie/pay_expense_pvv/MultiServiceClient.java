package in.taxgenie.pay_expense_pvv;


import com.fasterxml.jackson.databind.ObjectMapper;

import in.taxgenie.pay_expense_pvv.viewmodels.ErrorResponse;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;


public class MultiServiceClient {

    private final Map<Integer, WebClient> clients;
    private final Integer maxBuffer;
    private final ObjectMapper objectMapper;

    public MultiServiceClient(Map<Integer, WebClient> clients, Integer maxBuffer, ObjectMapper objectMapper) {
        this.clients = clients;
        this.maxBuffer = maxBuffer;
        this.objectMapper = objectMapper;
    }
    Function<ClientResponse, Mono<? extends Throwable>> exception = response -> Mono
            .error(new ClientApiException("External Api Exception ", response.statusCode()));

    // create uri with pathVariables
    private String replaceUriTemplateWithValues(String uriTemplate, Map<String, String> pathVariables) {
        StringBuilder uriBuilder = new StringBuilder(uriTemplate);
        if (pathVariables != null) {
            for (Map.Entry<String, String> entry : pathVariables.entrySet()) {
                uriBuilder.append("/").append(entry.getValue());
            }
        }
        return uriBuilder.toString();
    }

    // create uri with pathVariables
    private String replaceUriTemplateWithValues(String uriTemplate, List<String> pathVariables) {
        StringBuilder uriBuilder = new StringBuilder(uriTemplate);
        if (pathVariables != null) {
            for (String pathVariable : pathVariables) {
                uriBuilder.append("/").append(pathVariable);
            }
        }
        return uriBuilder.toString();
    }

    // create uri with queryParams
    private String replaceUriTemplateWithQueryParams(String uriTemplate, Map<String, String> queryParams) {
        if (queryParams == null || queryParams.isEmpty()) {
            return uriTemplate;
        }
        StringBuilder uriBuilder = new StringBuilder(uriTemplate);
        boolean isFirstParam = true;
        for (Map.Entry<String, String> entry : queryParams.entrySet()) {
            if (isFirstParam) {
                uriBuilder.append("?");
                isFirstParam = false;
            } else {
                uriBuilder.append("&");
            }
            uriBuilder.append(entry.getKey()).append("=").append((entry.getValue()));
        }
        return uriBuilder.toString();
    }

    // request call to respective service
    public <T> Mono<T> makeRequest( String contentType, Integer service,
                                    HttpMethod method,
                                    String uriTemplate,
                                    Optional<Object> body,
                                    Map<String, String> queryParams,
                                    List<String> pathVariables,
                                    Class<T> responseType,
                                    String bearerToken,
                                    Map<String, String> defaultHeader) throws ClientApiException, UnsupportedOperationException{
        // Build the URI with path variables and query parameters
        String uri = replaceUriTemplateWithValues(uriTemplate, pathVariables);
        uri = replaceUriTemplateWithQueryParams(uri, queryParams);
        System.out.println("URL Called: " + uri);

        if(null == contentType){
            contentType = "";
        }
        return switch (method.toString()) {
            case "POST" -> {
                if (body.isEmpty())
                    yield performPostRequestWithoutBody(contentType, responseType, service,uri,bearerToken, defaultHeader);
                else
                    yield performPostRequest(contentType, responseType, service, body, uri, bearerToken, defaultHeader);
            }
            case "PUT" -> {
                if (body.isEmpty()) {
                    yield Mono.error(new ClientApiException("No body found", HttpStatusCode.valueOf(400)));
                }
                yield performPutRequest(contentType, responseType, service, body, uri, bearerToken, defaultHeader);
            }
            case "GET" -> performGetRequest(responseType, service, uri, bearerToken, defaultHeader);
            default -> throw new UnsupportedOperationException("Unsupported HTTP method: " + method);
        };

    }

    private <T> Mono<T> performPostRequest(String contentType,
                                           Class<T> responseType,
                                           Integer service,
                                           Optional<Object> body,
                                           String uri,
                                           String bearerToken,
                                           Map<String, String> defaultHeader) {

        WebClient.RequestHeadersSpec<?> requestSpec = null;
        WebClient webClient = (WebClient)this.clients.get(service);

        if(contentType.equals("application/x-www-form-urlencoded")) {
            requestSpec = ((WebClient.RequestBodySpec)webClient.post().uri(uri, new Object[0])).body(BodyInserters.fromFormData((MultiValueMap<String, String>) body.get()));
        } else if (contentType.equals("multipart/form-data")) {
            requestSpec = ((WebClient.RequestBodySpec) webClient.post().uri(uri, new Object[0])).contentType(MediaType.MULTIPART_FORM_DATA).body(BodyInserters.fromMultipartData((MultiValueMap) body.get()));
        }else {
            requestSpec = ((WebClient.RequestBodySpec)webClient.post().uri(uri, new Object[0])).bodyValue(body);
        }

        requestSpec = requestSpec.headers(headers -> {

            // Conditionally add the bearer token to the request
            if (bearerToken != null && !bearerToken.isEmpty()) {
                headers.setBearerAuth(bearerToken);
            }

            if(null != defaultHeader && !defaultHeader.isEmpty()) {
                for (Map.Entry<String, String> entry : defaultHeader.entrySet()) {
                    headers.add(entry.getKey(), entry.getValue());
                }
                // headers.add("Content-Length", String.valueOf(contentLength));

            }
        });

        return requestSpec
                .exchangeToMono((response) -> {
                    if (response.statusCode().isError()) {
                        return performErrorHandling(response);
                    } else {
                        return response.bodyToMono(responseType);
                    }
                });
    }

    private <T> Mono<T> performPostRequestWithoutBody(String contentType,
                                                      Class<T> responseType,
                                                      Integer service,
                                                      String uri,
                                                      String bearerToken,
                                                      Map<String, String> defaultHeader) {

        WebClient.RequestHeadersSpec<?> requestSpec = clients.get(service).post().uri(uri);
        WebClient webClient = (WebClient)this.clients.get(service);

        if(contentType.equals("application/x-www-form-urlencoded")) {
            requestSpec = ((WebClient.RequestBodySpec)webClient.post().uri(uri));
        } else if (contentType.equals("multipart/form-data")) {
            requestSpec = ((WebClient.RequestBodySpec) webClient.post().uri(uri));
        }else {
            requestSpec = ((WebClient.RequestBodySpec)webClient.post().uri(uri));
        }

        requestSpec = requestSpec.headers(headers -> {

            // Conditionally add the bearer token to the request
            if (bearerToken != null && !bearerToken.isEmpty()) {
                headers.setBearerAuth(bearerToken);
            }

            if(null != defaultHeader && !defaultHeader.isEmpty()) {
                for (Map.Entry<String, String> entry : defaultHeader.entrySet()) {
                    headers.add(entry.getKey(), entry.getValue());
                }
            }
        });

        return requestSpec
                .exchangeToMono(response -> {
                    if (response.statusCode().isError()) {
                        return performErrorHandling(response);
                    } else {
                        return response.bodyToMono(responseType);
                    }
                });
    }

    private <T> Mono<T> performPutRequest(String contentType,
                                          Class<T> responseType,
                                          Integer service,
                                          Optional<Object> body,
                                          String uri,
                                          String bearerToken,
                                          Map<String, String> defaultHeader) {

        WebClient.RequestHeadersSpec<?> requestSpec = null;
        WebClient webClient = (WebClient)this.clients.get(service);

        if(contentType.equals("application/x-www-form-urlencoded")) {
            requestSpec = ((WebClient.RequestBodySpec)webClient.put().uri(uri, new Object[0])).contentType(MediaType.APPLICATION_FORM_URLENCODED)
                    .body(BodyInserters.fromFormData((MultiValueMap<String, String>) body.get()));
        } else if (contentType.equals("multipart/form-data")) {
            requestSpec = ((WebClient.RequestBodySpec) webClient.put().uri(uri, new Object[0])).contentType(MediaType.MULTIPART_FORM_DATA)
                    .body(BodyInserters.fromMultipartData((MultiValueMap) body.get()));
        }else {
            requestSpec = ((WebClient.RequestBodySpec)webClient.put().uri(uri, new Object[0])).bodyValue(body);
        }

        requestSpec = requestSpec.headers(headers -> {

            // Conditionally add the bearer token to the request
            if (bearerToken != null && !bearerToken.isEmpty()) {
                headers.setBearerAuth(bearerToken);
            }

            if(null != defaultHeader && !defaultHeader.isEmpty()) {
                for (Map.Entry<String, String> entry : defaultHeader.entrySet()) {
                    headers.add(entry.getKey(), entry.getValue());
                }
            }
        });

        return requestSpec
                .exchangeToMono(response -> {
                    if (response.statusCode().isError()) {
                        return performErrorHandling(response);
                    } else {
                        return response.bodyToMono(responseType);
                    }
                });
    }

    private <T> Mono<T> performGetRequest(Class<T> responseType,
                                          Integer service,
                                          String uri,
                                          String bearerToken,
                                          Map<String, String> defaultHeader) {

        WebClient.RequestHeadersSpec<?> requestSpec = clients.get(service).get().uri(uri);
        System.out.println("URI GET: " + uri);

        requestSpec = requestSpec.headers(headers -> {

            // Conditionally add the bearer token to the request
            if (bearerToken != null && !bearerToken.isEmpty()) {
                headers.setBearerAuth(bearerToken);
            }

            if(null != defaultHeader && !defaultHeader.isEmpty()) {
                for (Map.Entry<String, String> entry : defaultHeader.entrySet()) {
                    headers.add(entry.getKey(), entry.getValue());
                }
            }
        });
        return requestSpec
//                .headers(headers -> headers.setBearerAuth(bearerToken))
                .exchangeToMono(response -> {
                    if (response.statusCode().isError()) {
                        return performErrorHandling(response);
                    } else {
                        return response.bodyToMono(responseType);
                    }
                });
    }

    private <T> Mono<T> performErrorHandling(ClientResponse response) {
        return (Mono<T>) response.bodyToMono(String.class).flatMap((rawJson) -> {
            ObjectMapper objectMapper = new ObjectMapper();
            ErrorResponse err = null;
            try {
                err = objectMapper.readValue(rawJson, ErrorResponse.class);
                return Mono.error(new ClientApiException(HttpStatusCode.valueOf(400), err));
            } catch (Exception e) {
                return Mono.error(new ClientApiException(HttpStatusCode.valueOf(400), rawJson));
            }

        }).switchIfEmpty(Mono.defer(() -> Mono.error(new ClientApiException(HttpStatusCode.valueOf(400), "Empty response"))));
    }

}