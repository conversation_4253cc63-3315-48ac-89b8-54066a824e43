package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.entities.DocumentApprovalContainer;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.EmployeeCreationConfirmationViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.comeng.ApprovalPendingReminderEmailContainerViewModel;

public interface IEmailNotificationService {
    void transmitSubmitNotification(DocumentApprovalContainer report);
    void transmitApprovedNotification(DocumentApprovalContainer report);
    void transmitPostedNotification(DocumentApprovalContainer report);
    void transmitSendBackNotification(DocumentApprovalContainer report);
    void transmitPaidNotification(DocumentApprovalContainer report);
    void transmitNewUserCreationNotification(EmployeeCreationConfirmationViewModel viewModel);
    void transmitSentBackReminderNotification(DocumentApprovalContainer report);
    void transmitApprovalReminderNotification(ApprovalPendingReminderEmailContainerViewModel viewModel);
}
