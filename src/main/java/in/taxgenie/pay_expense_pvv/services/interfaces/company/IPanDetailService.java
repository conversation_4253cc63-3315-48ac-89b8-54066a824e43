package in.taxgenie.pay_expense_pvv.services.interfaces.company;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.company.PanDetailViewModel;

public interface IPanDetailService {
    PanDetailViewModel getPanDetails(IAuthContextViewModel auth);
    PanDetailViewModel createOrUpdatePanDetails(PanDetailViewModel panDetailViewModel, IAuthContextViewModel auth);
}
