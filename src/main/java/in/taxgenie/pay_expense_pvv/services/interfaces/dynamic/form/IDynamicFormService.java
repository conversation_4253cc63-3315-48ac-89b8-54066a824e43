package in.taxgenie.pay_expense_pvv.services.interfaces.dynamic.form;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dynamic.form.DynamicFormDataRequest;
import in.taxgenie.pay_expense_pvv.viewmodels.dynamic.form.DynamicFormViewResponseViewModel;

import java.util.Map;

public interface IDynamicFormService {

    DynamicFormViewResponseViewModel getDynamicFormData(IAuthContextViewModel auth, DynamicFormDataRequest request);

    Map<String, Object> getBusinessDetailsFromJson(String dynamicFieldsJson);

    Map<String, Object> getBusinessDetailsFromJsonForStaticView(String dynamicFieldsJson, IAuthContextViewModel auth);

    DynamicFormViewResponseViewModel getDynamicFormDataForTypeAndEntity(IAuthContextViewModel auth, String type, Long documentApprovalContainerId);
}
