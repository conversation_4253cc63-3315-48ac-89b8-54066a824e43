package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentRuleCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentRuleUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentRuleViewModel;

import java.util.List;

public interface IDocumentRuleService {
    DocumentRuleViewModel create(DocumentRuleCreateViewModel viewModel, IAuthContextViewModel auth);
    DocumentRuleViewModel update(long id, DocumentRuleUpdateViewModel viewModel, IAuthContextViewModel auth);
    List<DocumentRuleViewModel> getRulesBySubgroup(long subgroupId, IAuthContextViewModel auth);
    DocumentRuleViewModel getRuleById(long id, IAuthContextViewModel auth);
}
