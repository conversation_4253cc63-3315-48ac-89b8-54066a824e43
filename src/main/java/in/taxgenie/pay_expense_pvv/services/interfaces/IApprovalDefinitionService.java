package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ApprovalDefinitionCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ApprovalDefinitionUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ApprovalDefinitionViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.KeyValuePairViewModel;

import java.util.List;

public interface IApprovalDefinitionService {
    ApprovalDefinitionViewModel create(ApprovalDefinitionCreateViewModel viewModel, IAuthContextViewModel authViewModel);
    ApprovalDefinitionViewModel update(long id, ApprovalDefinitionUpdateViewModel viewModel, IAuthContextViewModel authViewModel);
    ApprovalDefinitionViewModel getById(long id, IAuthContextViewModel auth);
    List<ApprovalDefinitionViewModel> getAllByMetadataId(long metadataId, IAuthContextViewModel auth);
    List<KeyValuePairViewModel<Long, String>> getKeyValuePairCollection(IAuthContextViewModel auth);

    //  exists
    boolean existsByLevel(int level, long metadataId, IAuthContextViewModel auth);
    boolean existsByMatcherAndTitle(String matcher, String title, long metadataId, IAuthContextViewModel auth);
}
