package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Document;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URL;

public interface IFileIOService {
    void upload(MultipartFile file, String identifier, int index, Document document);
    ResponseEntity<Resource> getAttachment1(String identifier, long expenseId, IAuthContextViewModel auth);
    ResponseEntity<Resource> getAttachment2(String identifier, long expenseId, IAuthContextViewModel auth);
    ResponseEntity<Resource> getAttachment3(String identifier, long expenseId, IAuthContextViewModel auth);
    void delete(Document document);
    void delete(int index, Document document);

    String handleInvoiceUpload(MultipartFile file, long companyCode) throws IOException;

    String handleMoveInvoice(String tempFilePath, String name, long companyCode) throws IOException;

    String handleDocumentUpload(MultipartFile doc, long companyCode, String category, String fileHash, Integer categoryId, Long entityId) throws IOException;

    URL getSignedUrl(String filename);
}
