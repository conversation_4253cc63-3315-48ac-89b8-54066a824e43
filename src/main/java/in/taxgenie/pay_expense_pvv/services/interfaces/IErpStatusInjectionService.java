package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentApprovalContainerSendBackResultViewModel;

/**
 * Service interface for handling ERP status injection and related operations.
 * This service manages the process of injecting various ERP statuses into the approval workflow.
 *
 * Supported ERP notification codes:
 * - VALIDATION_FAILED (@5C@): Document failed ERP validation, requires sendback flow
 * - PARKED (@5D@): Document parked in ERP
 * - POSTED (@5B@): Document posted to ERP
 * - CANCELLED (@0W@): Document cancelled in ERP
 * - DELETED: Document deleted in ERP
 * - PAID (@): Document paid in ERP
 */
public interface IErpStatusInjectionService {

    /**
     * Injects ERP status into the document approval workflow based on notification code.
     * This method handles the complete flow of ERP status injection including:
     * - Document validation
     * - Status-specific processing (sendback, budget consumption, etc.)
     * - ReportState creation (if needed)
     * - Status updates
     * - Approval flow restart (for sendback scenarios)
     * - Consumption handling
     * - Notifications
     *
     * @param reportId the document approval container ID
     * @param remarks the remarks from ERP
     * @param auth the authentication context
     * @return result view model containing document details (null for non-sendback scenarios)
     * @throws RuntimeException if injection fails
     */
    DocumentApprovalContainerSendBackResultViewModel injectERPStatus(
        long reportId,
        String remarks,
        ExpenseActionStatus expenseActionStatus,
        IAuthContextViewModel auth
    );
}
