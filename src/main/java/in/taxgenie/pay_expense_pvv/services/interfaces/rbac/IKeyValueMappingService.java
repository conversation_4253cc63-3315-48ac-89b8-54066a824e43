package in.taxgenie.pay_expense_pvv.services.interfaces.rbac;

import in.taxgenie.pay_expense_pvv.api.rbac.UnmappedValuesViewModel;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.repositories.rbac.IKeyValueMappingRepository;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.KeyValueMappingRequest;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.KeyValueMappingViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.KeyWithValuesResponseViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.ResponseViewWithWarning;
import org.slf4j.Logger;

import java.util.List;
import java.util.Map;

public interface IKeyValueMappingService {

    GenericPageableViewModel<KeyValueMappingViewModel> getKeyValueMappingQueue(IAuthContextViewModel auth, QueueFilterViewModel filterValues);

    ResponseViewWithWarning save(KeyValueMappingRequest keyValueMappingRequest, IAuthContextViewModel auth);

    List<String> getLabelsByKey(String key, IAuthContextViewModel auth);

    List<LookupDataModel> getValuesByKey(String key, IAuthContextViewModel auth);

    List<LookupDataModel> getKeys(IAuthContextViewModel auth);

    List<UnmappedValuesViewModel> getUnmappedLabelsByKey(String key, boolean isEdit, IAuthContextViewModel auth);

    List<LookupDataModel> getHierarchyLabelsByKey(String key, IAuthContextViewModel auth);

    void updateKeyValues(List<Map.Entry<String, String>> keyValuePairs, Long companyCode);

    List<KeyWithValuesResponseViewModel> getAllKeysAndValues(long documentMetadataId, IAuthContextViewModel auth);
}

