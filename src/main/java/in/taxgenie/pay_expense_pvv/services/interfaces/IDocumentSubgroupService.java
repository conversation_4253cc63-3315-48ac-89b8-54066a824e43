package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentSubgroupCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentSubgroupUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentSubgroupViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.KeyValuePairViewModel;

import java.util.List;

public interface IDocumentSubgroupService {
    DocumentSubgroupViewModel create(DocumentSubgroupCreateViewModel viewModel, IAuthContextViewModel auth);
    DocumentSubgroupViewModel update(long id, DocumentSubgroupUpdateViewModel viewModel, IAuthContextViewModel auth);
    List<DocumentSubgroupViewModel> getAll(IAuthContextViewModel auth);
    List<DocumentSubgroupViewModel> getAllByMetadata(long metadataId, IAuthContextViewModel auth);
    List<DocumentSubgroupViewModel> getDocumentSubroupFilteredBasedOnRulesByMetadata(long metadataId, IAuthContextViewModel auth);

    DocumentSubgroupViewModel getById(long id, IAuthContextViewModel auth);

    DocumentSubgroupViewModel getByDocumentContainerId(long id, IAuthContextViewModel auth);

    List<KeyValuePairViewModel<Long, String>> getKeyValueCollection(IAuthContextViewModel auth);
    List<KeyValuePairViewModel<Long, String>> getKeyValueCollection(long metadataId, IAuthContextViewModel auth);

    //  exists
    boolean existsByExpenseCode(String expenseCode,long metadataId,  IAuthContextViewModel auth);
    boolean existsBySubgroup(String subgroup,long metadataId,  IAuthContextViewModel auth);
    boolean existsByPrefix(String prefix,long metadataId,  IAuthContextViewModel auth);
}
