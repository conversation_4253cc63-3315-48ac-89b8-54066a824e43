package in.taxgenie.pay_expense_pvv.services.interfaces.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ActionType;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.factory.budgetaction.DocumentData;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.*;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetAddLessViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetTreeViewModel;

import java.math.BigDecimal;
import java.util.List;

public interface IBudgetService {

    // Budget
    BudgetViewModel create(BudgetViewModel viewModel, long structureId, IAuthContextViewModel auth);
    BudgetViewModel updateBudgetNodes(MultiBudgetNodeUpdateViewModel viewModel, long structureId, IAuthContextViewModel authContextViewModel);
    BudgetTreeViewModel createTree(BudgetCreateViewModel viewModel, long structureId, Boolean resetStages, IAuthContextViewModel auth);
    BudgetTreeViewModel getTree(long structureId, IAuthContextViewModel auth);
    BudgetContainerViewModel getBudgetDetailsByDocumentContainerId(IAuthContextViewModel auth, long id);

    BudgetAddLessViewModel getAddLessBudgetData(Long structureId, IAuthContextViewModel auth);
    void checkBudgetCodeUnique(Long structureId, String budgetCode, IAuthContextViewModel auth);

    BudgetNodeUpdateViewModel updateBudgetNode(BudgetNodeUpdateViewModel viewModel, IAuthContextViewModel authContextViewModel);
    BudgetSelectionViewModel getBudgetNode(Long budgetId, IAuthContextViewModel auth);

    BudgetSelectionViewModel getBudgetNodeByStructureAndBudgetId(long structureId, long budgetId, IAuthContextViewModel auth);

    List<BudgetSelectionViewModel> getBudgetNodes(IAuthContextViewModel auth, Long structureId);

    BudgetSelectionViewModel getBudgetNodeForCurrentFrequency(Long budgetId, IAuthContextViewModel auth);
    // Testing
    Long getIntervalValue(int startMonth, int endMonth, int intervalSize);

    BudgetDetails getBudgetDetails(Long budgetId, IAuthContextViewModel auth);
    void createNewZeroedBudgetFrequencyRow(Budget node, long userId, long budgetStructureMasterId);
    List<BudgetSelectionViewModel> getBudgetNodesInTree(IAuthContextViewModel auth, long budgetId, String documentType);
    Boolean handleBudgetConsumption(List<DocumentData> documentDataList);


    void addLessBudget(Long budgetId, BigDecimal amount, ActionType actionType);

    BudgetSelectionViewModel getBudgetNodeByBudgetNodeId(Long budgetId, IAuthContextViewModel auth);

    BudgetAddLessViewModel getAddLessBudgetDataByDocumentContainerId(Long documentContainerId, IAuthContextViewModel auth);
}
