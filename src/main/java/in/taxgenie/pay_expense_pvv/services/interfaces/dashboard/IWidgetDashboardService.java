package in.taxgenie.pay_expense_pvv.services.interfaces.dashboard;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.QueueStatisticsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.*;

import java.util.List;
import java.util.Map;

public interface IWidgetDashboardService {

    DocumentWidgetStatsResponse<QueueStatisticsViewModel> getDocumentWidgetStats(Integer widgetId, DashboardWidgetFilterViewModel filterValues, IAuthContextViewModel auth);

    List<DashboardConfigurationViewModel> getDashboardConfigurations(IAuthContextViewModel auth);
    void initializeDashboardConfigurations(IAuthContextViewModel auth);
    void saveDashboardConfigurations(DashboardConfigurationRequest configs, IAuthContextViewModel auth);

    GenericPageableWidgetViewModel<BudgetDetailsWidgetViewModel> getBudgetDetails(QueueFilterViewModel filterValues, IAuthContextViewModel auth);

    DocumentWidgetStatsResponse<InvoicePayableStatisticsViewModel> getInvoicePayableStats(DashboardWidgetFilterViewModel filterValues, IAuthContextViewModel auth);

    public TATResponseViewModel getInvoiceTatReport(Map<String, String> metadataFilters, IAuthContextViewModel auth);
    
    IInvoiceTATIdentifiersViewModel getInvoiceTatReportItems(IInvoiceTATIdentifiersRequestViewModel tatFilters, IAuthContextViewModel auth);

    }
