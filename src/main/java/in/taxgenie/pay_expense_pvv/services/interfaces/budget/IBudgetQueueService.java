package in.taxgenie.pay_expense_pvv.services.interfaces.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericPageableViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.ConsumptionDetailsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.queue.BudgetLookupViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.queue.BudgetRowViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.DocumentWidgetStatsResponse;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface IBudgetQueueService {
    GenericPageableViewModel<BudgetRowViewModel> getQueue(QueueFilterViewModel filterValues, IAuthContextViewModel auth);
    List<BudgetLookupViewModel> getQueueLookup(IAuthContextViewModel auth);

    DocumentWidgetStatsResponse<GenericPageableViewModel<ConsumptionDetailsViewModel>> getBudgetConsumptionDetailsForPurchaseOrder(Long budgetId, QueueFilterViewModel filterValues, IAuthContextViewModel auth);

    DocumentWidgetStatsResponse<GenericPageableViewModel<ConsumptionDetailsViewModel>> getBudgetConsumptionDetailsForInvoice(Long budgetId, QueueFilterViewModel filterValues, IAuthContextViewModel auth);

    ResponseEntity<Resource> getBudgetConsumptionDetailsExcelForInvoice(Long budgetId, QueueFilterViewModel filterValues, IAuthContextViewModel auth);

    List<BudgetLookupViewModel> getBudgetStructureByExpenseType(String expenseType, IAuthContextViewModel auth);
}
