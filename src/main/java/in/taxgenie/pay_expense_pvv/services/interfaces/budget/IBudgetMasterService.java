package in.taxgenie.pay_expense_pvv.services.interfaces.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetMasterCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetMasterUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.MultiBudgetMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.MasterResponseViewModel;

import java.util.List;

public interface IBudgetMasterService {
	
    MultiBudgetMasterViewModel getAllBudgetMaster(IAuthContextViewModel auth, Boolean isActive);
    List<MasterResponseViewModel> getAllBudgetMasterV2(IAuthContextViewModel auth, Boolean isActive);
    BudgetMasterViewModel updateBudgetMaster(BudgetMasterUpdateViewModel budgetMasterUpdateViewModel, IAuthContextViewModel auth);
    BudgetMasterViewModel createBudgetMaster(BudgetMasterCreateViewModel budgetMasterCreateViewModel, IAuthContextViewModel auth);

}
