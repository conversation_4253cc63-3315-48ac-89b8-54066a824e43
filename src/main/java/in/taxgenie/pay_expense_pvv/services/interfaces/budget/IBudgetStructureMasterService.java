package in.taxgenie.pay_expense_pvv.services.interfaces.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.structuremaster.BudgetStructureMasterCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.structuremaster.BudgetStructureMasterUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.structuremaster.BudgetStructureMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.structuremaster.LockScreenViewModel;

public interface IBudgetStructureMasterService {
    BudgetStructureMasterViewModel  create(BudgetStructureMasterCreateViewModel budgetStructureMasterCreateViewModel, IAuthContextViewModel auth);
    BudgetStructureMasterViewModel get(long structureId, IAuthContextViewModel auth);
    BudgetStructureMasterViewModel  put(BudgetStructureMasterUpdateViewModel budgetStructureMasterUpdateViewModel, IAuthContextViewModel auth);

    //Lock screen
    BudgetStructureMasterViewModel  updateLockScreen(
            LockScreenViewModel lockScreenViewModel, IAuthContextViewModel auth);

    LockScreenViewModel getLockScreen(long structureId, IAuthContextViewModel auth);

}
