package in.taxgenie.pay_expense_pvv.services.interfaces.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetStructureMaster;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetMasterCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetMasterUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.MultiBudgetMasterViewModel;

public interface IBudgetStepperService {


    void setLockScrren(boolean resetStages, int currentStep, long structureId, IAuthContextViewModel auth);
    void initiateReset(int startingStep, long structureId, IAuthContextViewModel auth);
    void performReset(int stage, long structureId, IAuthContextViewModel auth);
    int getCurrentLockedStep(long structureId, long companyCode);
}
