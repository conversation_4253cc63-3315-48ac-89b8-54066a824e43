package in.taxgenie.pay_expense_pvv.services.interfaces.pr;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Document;
import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestHeader;
import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestItem;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.PRPreviewViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.PurchaseRequestCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.PurchaseRequestItemDetailsView;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.ReadyToConvertEntityViewModel;

import java.util.List;

public interface IPurchaseRequestService {
    DocumentCreateResponse create(Long metadataId, Long subgroupId, IAuthContextViewModel auth);

    DocumentCreateResponse save(PurchaseRequestCreateViewModel prCreateViewModel, IAuthContextViewModel auth);

    PurchaseRequestItemDetailsView prItemToDto(PurchaseRequestItem prItem);

    PurchaseRequestCreateViewModel getByDocumentContainerId(Long documentContainerId, IAuthContextViewModel auth);

    void deleteLineItem(Long prId, List<Long> lineItemId, IAuthContextViewModel auth);

    GenericPageableViewModel<BudgetAssociatedDocumentDetailViewModel> getSubmittedPRByBudgetId(Long budgetId, QueueFilterViewModel filterValues, IAuthContextViewModel auth);

    List<PurchaseRequestItemDetailsView> getLineItems(Long prId, IAuthContextViewModel auth);
    List<PurchaseRequestItemDetailsView> getLineItemsReadyToConvert(IAuthContextViewModel auth, Integer vendorId);

    Document getDocumentFromDocumentApprovalContainerWithUniquenessChecks(long companyCode, Long documentContainerId);

    PurchaseRequestHeader getPRHeaderFromDocumentWithUniquenessChecks(long companyCode, Document document);

    PRPreviewViewModel getPurchaseRequestPreviewById(IAuthContextViewModel auth, long id, boolean isProcurementView);

    List<PurchaseRequestItemDetailsView> getLineItemsByPrIds(GenericListRequestViewModel<Long> prRequest, IAuthContextViewModel auth);

    List<ReadyToConvertEntityViewModel> getPurchaseRequestsReadyToConvert(IAuthContextViewModel auth, Integer vendorId, GenericListRequestViewModel<Long> prIds);

    List<PurchaseRequestItemDetailsView> getLineItemsReadyToConvertByPR(IAuthContextViewModel auth, Integer vendorId, GenericListRequestViewModel<Long> prIds);
}
