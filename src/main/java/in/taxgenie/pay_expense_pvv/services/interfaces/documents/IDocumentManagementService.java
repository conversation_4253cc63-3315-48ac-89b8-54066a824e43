package in.taxgenie.pay_expense_pvv.services.interfaces.documents;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericListRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.files.DocumentStorageQueueViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.files.DocumentUploadResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IDocumentManagementService {


    DocumentUploadResponse uploadDocuments(List<MultipartFile> supportingDocuments, Long entityId, Integer categoryId, IAuthContextViewModel auth);

    List<DocumentStorageQueueViewModel> getSupportingDocuments(Long entityId, Integer categoryId);
    void deleteSupportingDocument(Integer categoryId, Long entityId, GenericListRequestViewModel<Long> docIds, IAuthContextViewModel auth);
    void performMappingDocumentToSupportingDocs(List<Long> uploadedDocIds, long documentId);
}
