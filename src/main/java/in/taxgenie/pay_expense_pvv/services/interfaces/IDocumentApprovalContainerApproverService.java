package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.DocumentApprovalContainer;
import in.taxgenie.pay_expense_pvv.factory.budgetaction.DocumentData;
import in.taxgenie.pay_expense_pvv.viewmodels.*;

import java.util.List;

public interface IDocumentApprovalContainerApproverService {
    void approve(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth);

    void approveBulk(BulkApproveContainerViewModel viewModel, IAuthContextViewModel auth);
    void reject(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth);
    DocumentApprovalContainerSendBackResultViewModel approverSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth);

    void checkerConsent(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth);
    DocumentApprovalContainerSendBackResultViewModel checkerSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth);

    List<DocumentApprovalContainerViewModel> getApproverQueue(IAuthContextViewModel auth);
    List<DocumentApprovalContainerViewModel> getCheckerQueue(IAuthContextViewModel auth);
    List<DocumentApprovalContainerViewModel> getAdminQueue(IAuthContextViewModel auth);
    GenericPageableViewModel getAdminQueueV2(IAuthContextViewModel auth, QueueFilterViewModel viewModel);
    List<DocumentApprovalContainerViewModel> getSimilarExpenses(long reportId, IAuthContextViewModel auth);

	GenericPageableViewModel getCheckerQueueV2(IAuthContextViewModel auth, QueueFilterViewModel viewModel);

	GenericPageableViewModel getApproverQueueV2(IAuthContextViewModel auth, QueueFilterViewModel viewModel);

    List<DocumentData> getDocumentData(DocumentApprovalContainer report) ;

    GenericPageableViewModel adminQueueViewModelV3(IAuthContextViewModel auth, QueueFilterViewModel viewModel);
}
