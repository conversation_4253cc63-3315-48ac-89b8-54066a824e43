package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ActionType;
import in.taxgenie.pay_expense_pvv.entities.ApproverType;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.factory.budgetaction.DocumentData;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.*;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetAddLessViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetTreeViewModel;

import java.math.BigDecimal;
import java.util.List;

public interface IInvoiceTatReportService {

    Boolean create(ApproverType approverType, Long documentApprovalContainerId, IAuthContextViewModel auth);

}
