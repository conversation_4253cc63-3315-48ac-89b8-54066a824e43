package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.*;

import java.util.List;

public interface IDocumentMetadataService {
    DocumentMetadataViewModel create(DocumentMetadataCreateViewModel viewModel, IAuthContextViewModel auth);

    DocumentMetadataViewModel createSingleMetadata(DocumentMetadataCreateViewModel viewModel, IAuthContextViewModel auth);
    DocumentMetadataViewModel update(long id, DocumentMetadataUpdateViewModel viewModel, IAuthContextViewModel auth);
    List<DocumentMetadataViewModel> getAll(IAuthContextViewModel auth, Integer documentCategoryId, String action);
    List<DocumentMetadataViewModel> getAllByCategoryAndByActionAndByExpenseType(IAuthContextViewModel auth, Integer documentCategoryId, String action, String expenseType);
    List<DocumentMetadataViewModel> getPolicyByMetadata(IAuthContextViewModel auth, GetPolicyDataModel getPolicyDataModel);
    DocumentMetadataViewModel getById(long id, IAuthContextViewModel auth);
    List<KeyValuePairViewModel<Long, String>> getKeyValueCollection(IAuthContextViewModel auth);

    //  exists
    boolean existsByTypeAndGroup(String type, String group, IAuthContextViewModel auth);
    boolean existsByPrefixesOfTypeAndGroup(String typePrefix, String groupPrefix, IAuthContextViewModel auth);

    List<DocumentMetadataViewModel> getBudgetMetadataByExpenseType(String expenseType, IAuthContextViewModel auth);
}
