package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.entities.LookupData;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupViewModel;

import java.util.List;
import java.util.Optional;

public interface ILookupService {
    LookupViewModel lookupById(Integer id);
    List<LookupViewModel> lookupByType(String type);

    Optional<LookupViewModel> lookUpByTypeAndValue(String type, String value);

    List<LookupViewModel> getCountries();

    List<LookupViewModel> getPorts();

    List<LookupViewModel> getStateCodes();

    LookupData lookupByAttribute(String value);

    List<LookupViewModel> getFinancialYears();
}
