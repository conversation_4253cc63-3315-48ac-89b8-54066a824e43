package in.taxgenie.pay_expense_pvv.services.interfaces.budget;

import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetFrequencyMapping;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMappingMaster;

import java.util.List;

public interface ISafeDeletionService {
    List<BudgetMappingMaster> deleteBudgetMappingMasterEntries(List<BudgetMappingMaster> entries);
    List<Budget> deleteBudgetEntries(List<Budget> entries);
    List<BudgetFrequencyMapping> deleteBudgetFrequencyMappingEntries(List<BudgetFrequencyMapping> entries);
}
