package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.govrndto.SignedDataDTO;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericPageableViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.company.ResponseViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.TATResponseViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.mailroom.*;

import java.util.List;
import java.util.Optional;

public interface IInvoiceMailroomService {

    GenericPageableViewModel<InvoiceMailroomQueueViewModel> getQueue(QueueFilterViewModel filterValues, IAuthContextViewModel auth);

    InvoiceMailroomDataViewModel getQueueDataById(long id, IAuthContextViewModel auth);

    TATResponseViewModel getQueueTatData( IAuthContextViewModel auth);

    GenericPageableViewModel<InvoiceSyncReportQueueViewModel> getMailroomSyncReportQueue(QueueFilterViewModel filterValues, IAuthContextViewModel auth);

    ResponseViewModel syncInvoice(InvoiceSyncRequestViewModel requestViewModel, IAuthContextViewModel auth);

    List<MonthObjectViewModel> getMonthLookupData(IAuthContextViewModel auth);

    String downloadJson(Long id, IAuthContextViewModel auth);


    boolean createOrUpdateInvoiceSyncHistory(InvoiceSyncReportRequestViewModel requestViewModel, IAuthContextViewModel auth);

    boolean buyerDataUpload(SignedDataDTO requestViewModel, String requestId, IAuthContextViewModel auth);


}
