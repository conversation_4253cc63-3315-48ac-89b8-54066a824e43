package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.EntitlementDetailsViewModel;

import java.util.List;

public interface IEntitlementDetailsService {

    EntitlementDetailsViewModel create(List<EntitlementDetailsViewModel> viewModel, IAuthContextViewModel auth);
    List<EntitlementDetailsViewModel> findByCompanyCodeAndJobBand(String jobBand, IAuthContextViewModel auth);
    void save(List<EntitlementDetailsViewModel> viewModel, IAuthContextViewModel auth);
}
