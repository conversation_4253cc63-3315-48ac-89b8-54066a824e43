package in.taxgenie.pay_expense_pvv.services.interfaces;


import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cem.employee.interfaces.IEmployeeViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.EmployeeGlDetailUpdateContainerViewModel;

import java.util.List;

public interface IEmployeeMasterDataService {
    IEmployeeViewModel getEmployeeMasterData(String email, IAuthContextViewModel auth);
    IEmployeeViewModel getEmployeeMasterDataByCode(String employeeCode, IAuthContextViewModel auth);
    List<IEmployeeViewModel> getAllEmployeeMasterData(IAuthContextViewModel auth);

    List<IEmployeeViewModel> getAllApproversMasterData(IAuthContextViewModel auth);

    void updateEmployeeGlDetails(EmployeeGlDetailUpdateContainerViewModel viewModel, IAuthContextViewModel auth);
}
