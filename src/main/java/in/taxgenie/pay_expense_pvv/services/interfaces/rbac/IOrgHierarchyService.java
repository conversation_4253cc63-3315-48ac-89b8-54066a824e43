package in.taxgenie.pay_expense_pvv.services.interfaces.rbac;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.rbac.OrgHierarchyStructure;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.HierarchyMasterTreeViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.OrgStructureViewWrapper;

import java.util.List;

public interface IOrgHierarchyService {

    HierarchyMasterTreeViewModel create(String key, HierarchyMasterTreeViewModel hierarchyCreateViewModelWrapper, IAuthContextViewModel auth);

    void createMappingOfValuesAndLabels(String key, IAuthContextViewModel auth, OrgHierarchyStructure orgHierarchyStructure);

    OrgStructureViewWrapper getTree(IAuthContextViewModel auth, Long keyValueMappingId);

    void delete(String key, IAuthContextViewModel auth);

    List<String> getValuesForRbac(Long userId, Long companyCode);
}
