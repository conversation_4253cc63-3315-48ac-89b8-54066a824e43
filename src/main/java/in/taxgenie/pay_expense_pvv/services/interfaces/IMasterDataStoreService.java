package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.MasterDataStoreViewModel;

import java.util.List;

public interface IMasterDataStoreService {


    List<MasterDataStoreViewModel> syncMasterDataStoresV1(IAuthContextViewModel auth, List<MasterDataStoreViewModel> masterDataStores);
}
