package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.users.UsersViewModel;

import java.util.List;

public interface IUsersService {
    void updateUserInfoFromAuth(IAuthContextViewModel auth);

    void saveUserAndKeyValues(List<UsersViewModel> usersViewModel, IAuthContextViewModel auth);
}
