package in.taxgenie.pay_expense_pvv.services.interfaces;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@Service
public class RestCreatorImplementation<T> implements IRestCreator<T> {
    private final RestTemplate restTemplate;
    private final Logger logger;

    public RestCreatorImplementation(RestTemplateBuilder restTemplateBuilder) {
        this.restTemplate = restTemplateBuilder.build();
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public void create(HttpHeaders headers, String url, Class<T> classInfoModel, T body) {
        HttpEntity<T> httpEntity = new HttpEntity<>(body, headers);

        logger.trace("Making POST request to: {}", url);
        this.restTemplate.exchange(url, HttpMethod.POST, httpEntity, classInfoModel);
        logger.trace("POST complete; returning");
    }

    @Override
    public void create(HttpHeaders headers, String url, T body) {
        HttpEntity<T> httpEntity = new HttpEntity<>(body, headers);

        logger.trace("Making POST request to: {}", url);
        try {
            this.restTemplate.exchange(url, HttpMethod.POST, httpEntity, Object.class);
        } catch (RuntimeException re) {
            logger.error("HTTP POST Error: {}", re.getMessage());
        }

        logger.trace("POST complete; returning");
    }

    @Override
    public void exchange(HttpHeaders headers, String url, MultiValueMap<String, Object> formData) {
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(formData, headers);

        logger.trace("Making PUT request to: {}", url);
        try {
            this.restTemplate.exchange(url, HttpMethod.PUT, httpEntity, String.class);
        } catch (RuntimeException re) {
            logger.error("HTTP PUT Error: {}", re.getMessage());
        }

        logger.trace("PUT complete; returning");
    }

}
