package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.*;

import java.util.List;

public interface ICemLookupService {
    List<BranchViewModel> getBranches(IAuthContextViewModel auth);
    List<CostCenterViewModel> getCostCenters(IAuthContextViewModel auth);
    List<DepartmentViewModel> getDepartments(IAuthContextViewModel auth);
    List<EmployeeGradeViewModel> getEmployeeGrades(IAuthContextViewModel auth);
    List<EmployeeTypeViewModel> getEmployeeTypes(IAuthContextViewModel auth);
}
