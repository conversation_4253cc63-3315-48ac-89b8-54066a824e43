package in.taxgenie.pay_expense_pvv.services.interfaces.masters;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericListRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericPageableViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SupplierMasterCombinedViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SupplierMasterSyncRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SupplierMastersGstinViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SupplierMastersViewModel;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface ISupplierMasterService {
    GenericPageableViewModel<SupplierMastersViewModel> getSupplierMastersQueue(IAuthContextViewModel auth, QueueFilterViewModel filterValues);

    List<SupplierMastersGstinViewModel> getAllGSTSuppliersByPan(IAuthContextViewModel auth, String pan);

    ResponseEntity<Resource> getSupplierMastersDetailsExcelReport(IAuthContextViewModel auth);

    List<SupplierMasterCombinedViewModel> getAllSupplierMastersDetailsForExcel(IAuthContextViewModel auth);

    void saveSupplierMaster(List<SupplierMasterSyncRequestViewModel> supplierCreateOrUpdateRequest, IAuthContextViewModel auth);
}
