package in.taxgenie.pay_expense_pvv.services.interfaces.po;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Document;
import in.taxgenie.pay_expense_pvv.entities.DocumentApprovalContainer;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderItem;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentCreateResponse;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericListRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetSelectionViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderItemDetailsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.AddNewLineItemViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.ReadyToConvertEntityViewModel;

import java.util.List;

public interface IPurchaseOrderService {
    DocumentCreateResponse create(Long metadataId, Long subgroupId, Integer prTypeId, IAuthContextViewModel auth);
    PurchaseOrderItemDetailsViewModel addItem(Long poId, AddNewLineItemViewModel viewModel, IAuthContextViewModel auth);
    DocumentCreateResponse save(PurchaseOrderCreateViewModel poCreateViewModel, IAuthContextViewModel auth);

    PurchaseOrderCreateViewModel getByDocumentContainerId(Long documentContainerId, IAuthContextViewModel auth);

    List<ReadyToConvertEntityViewModel> getPurchaseOrdersReadyToConvert(IAuthContextViewModel auth, Integer vendorId);

    List<PurchaseOrderItemDetailsViewModel> getLineItemsReadyToConvertByPO(IAuthContextViewModel auth, Integer vendorId, GenericListRequestViewModel<Long> prIds);

    Document getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks(long companyCode, Long documentContainerId) ;

    PurchaseOrderHeader getPOHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks(long companyCode, Document document);

    PurchaseOrderItemDetailsViewModel poItemToDto(PurchaseOrderItem poItem, IAuthContextViewModel auth);

    public void performApprovalContainerAndDocumentContainerSantityCheck(DocumentApprovalContainer documentApprovalContainer, Long documentContainerId);
    public BudgetSelectionViewModel getBudgetNodeForDocument(Document document, IAuthContextViewModel auth);
    public PurchaseOrderCreateViewModel preparePODetailsResponseData(DocumentApprovalContainer documentApprovalContainer, PurchaseOrderHeader poHeader, Document document, BudgetSelectionViewModel budgetDetails, IAuthContextViewModel auth);
}
