package in.taxgenie.pay_expense_pvv.services.interfaces;


import in.taxgenie.pay_expense_pvv.cem.expense.account.interfaces.IExpenseAccountViewModel;

public interface IExpenseAccountMasterDataService {
    IExpenseAccountViewModel getExpenseAccount(long companyCode, long groupId, long accountId);
    IExpenseAccountViewModel[] getAllExpenseAccounts(long companyCode);
    IExpenseAccountViewModel[] getAllExpenseAccountsByGroup(long companyCode, long groupId);
}
