package in.taxgenie.pay_expense_pvv.services.interfaces.masters;

import in.taxgenie.pay_expense_pvv.entities.masters.MappingSegmentRatioToEntity;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericListRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.*;
import in.taxgenie.pay_expense_pvv.viewmodels.po.SegmentsViewModel;

import java.util.List;

public interface IRatioCategoryMasterService {

    List<SegmentsViewModel> getSegmentMasters(long companyCode);

    void saveRatioCategoryConfig(long companyCode, RatioCategoryConfigRequest request);

    List<MasterResponseViewModel> getConfig(long companyCode);

    List<RatioCategoryViewModel> getAllRatioCategories(long companyCode);

    List<LookupDataModel> getAllCategoriesLookup(long companyCode);

    void saveOrUpdateRatioCategory(long companyCode, RatioCategoryViewModel request);

    void deleteRatioCategory(long companyCode, Long id);

    void deleteRatioCategoryConfig(long companyCode);

    void saveSegmentRatio(long companyCode, SegmentRatioToEntityRequest request);

    SegmentRatioViewModel getSegmentRatio(long companyCode, Integer documentTypeId, Long entityId, GenericListRequestViewModel<Long> entityIds);

    SegmentRatioViewModel getRatioCategoryDetails(long companyCode, Integer id);

    boolean isTotalRatioEqualsTo100(MappingSegmentRatioToEntity entity);
}
