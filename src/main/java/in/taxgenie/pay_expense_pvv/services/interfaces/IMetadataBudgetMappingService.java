package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentCreateResponse;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetAddLessViewModel;

public interface IMetadataBudgetMappingService {
    DocumentCreateResponse createBudgetDocumentForPublishAndSendForApproval(Long structureId, IAuthContextViewModel auth);

    DocumentCreateResponse createBudgetDocumentAction(BudgetAddLessViewModel addLessViewModel, IAuthContextViewModel auth);

    DocumentCreateResponse save(BudgetAddLessViewModel addLessViewModel, IAuthContextViewModel auth);

    DocumentCreateResponse delete(long reportId, IAuthContextViewModel auth);
}
