package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.KeyValuePairViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.LocationCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.LocationUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.LocationViewModel;

import java.util.List;

public interface ILocationService {
    LocationViewModel create(LocationCreateViewModel viewModel, IAuthContextViewModel auth);
    LocationViewModel update(long id, LocationUpdateViewModel viewModel, IAuthContextViewModel auth);
    LocationViewModel getById(long id, IAuthContextViewModel auth);
    List<LocationViewModel> getAll(IAuthContextViewModel auth);
    List<LocationViewModel> getAllByCountryCode( IAuthContextViewModel auth, String countryCode);

    List<KeyValuePairViewModel<Long, String>> getLocationKeyValueCollection(IAuthContextViewModel auth);
    //  exists
    boolean existsByCombination(String countryCode, String location, IAuthContextViewModel auth);
}
