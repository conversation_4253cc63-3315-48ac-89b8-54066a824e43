package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.MatchConfigurationData;
import in.taxgenie.pay_expense_pvv.viewmodels.match.MatchConfigurationViewModel;

import java.util.List;

public interface IMatchConfigurationService {
    List<MatchConfigurationViewModel> getMatchConfigurations(IAuthContextViewModel auth);
    
    MatchConfigurationViewModel getMatchConfigurationById(Long id, IAuthContextViewModel auth);
    
    MatchConfigurationViewModel createMatchConfiguration(MatchConfigurationData matchConfiguration, IAuthContextViewModel auth);

    // Filter by is2WayMatch
    List<MatchConfigurationViewModel> getMatchConfigurationsByIs2WayMatch(Boolean is2WayMatch, IAuthContextViewModel auth);
}
