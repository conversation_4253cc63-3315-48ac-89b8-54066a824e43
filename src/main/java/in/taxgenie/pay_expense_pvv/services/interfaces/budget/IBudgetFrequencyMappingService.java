package in.taxgenie.pay_expense_pvv.services.interfaces.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.LookupData;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetFrequencyMapping;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetSheetViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.frequencymapping.NodeUpdateViewModel;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;


public interface IBudgetFrequencyMappingService {
    BudgetSheetViewModel get(long structureId, IAuthContextViewModel auth);
    BudgetViewModel initaliseFrequencyMapping(long structureId, String period, BigDecimal budgetTotal, Boolean resetStages,IAuthContextViewModel auth);
    BudgetSheetViewModel updateNodes(List<NodeUpdateViewModel> viewModelList, long structureId, IAuthContextViewModel auth);

    BudgetSheetViewModel updateNodesCSV(MultipartFile csvFile, long structureId, IAuthContextViewModel auth);

    Budget getRootNodeForStructure(long structureId, long companyCode);

    LookupData getIntervalCount(String interval) ;
    LookupData getIntervalName(int numberOfIntervals);

    BudgetFrequencyMapping getCurrentFrequency(Budget budget);
}
