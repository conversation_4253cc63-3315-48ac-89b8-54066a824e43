package in.taxgenie.pay_expense_pvv.services.interfaces;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class RestUpdaterImplementation<T> implements IRestUpdater<T> {
    private final RestTemplate restTemplate;
    private final Logger logger;

    public RestUpdaterImplementation(RestTemplateBuilder restTemplateBuilder) {
        this.restTemplate = restTemplateBuilder.build();
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public void update(HttpHeaders headers, String url, Class<T> classInfoModel, T body) {
        HttpEntity<T> httpEntity = new HttpEntity<>(body, headers);

        logger.trace("Making PUT request to: {}", url);
        this.restTemplate.exchange(url, HttpMethod.PUT, httpEntity, classInfoModel);
        logger.trace("PUT complete; returning");
    }
}
