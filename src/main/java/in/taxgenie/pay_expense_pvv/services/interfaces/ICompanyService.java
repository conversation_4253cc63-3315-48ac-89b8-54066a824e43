package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.CompanyRequestBySupplierNameViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel;
import in.taxgenie.pay_expense_pvv.viewmodels.CompanyViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.company.SellerTemplateViewModel;

import java.util.List;

public interface ICompanyService {
    List<LookupDataModel> getAllAvailableGstins(IAuthContextViewModel auth);
    List<SellerTemplateViewModel> getAllAvailableGstinsWithTemplateStatus(IAuthContextViewModel auth);
    List<LookupDataModel> getAllSupplierNames(IAuthContextViewModel auth);
    List<CompanyViewModel> getBySupplierName(IAuthContextViewModel auth, CompanyRequestBySupplierNameViewModel supplierName);
    CompanyViewModel getById(IAuthContextViewModel auth, Integer id);

    List<LookupDataModel> getAllAvailableGstinsBySupplier(Long supplierId, IAuthContextViewModel auth);

//    List<CompanyViewModel> getAllSupplierDetailsByGstin(String gstin, IAuthContextViewModel auth);
    List<CompanyViewModel>  getAllSupplierDetailsByGstin(Long supplierId, String gstin, IAuthContextViewModel auth);
    byte[] getCamCompanyLogo(IAuthContextViewModel auth);
}
