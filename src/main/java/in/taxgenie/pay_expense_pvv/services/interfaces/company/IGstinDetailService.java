package in.taxgenie.pay_expense_pvv.services.interfaces.company;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.company.*;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.concurrent.ForkJoinPool;

public interface IGstinDetailService {
    List<GstinDetailViewModel> getAllGstinDetails(IAuthContextViewModel auth);
    GstinDetailViewModel addGstinDetail(GstinDetailCreateViewModel viewModel, IAuthContextViewModel auth);
    List<GstinDetailViewModel> addGstinDetails(List<GstinDetailCreateViewModel> viewModels, IAuthContextViewModel auth);
    List<GstinDetailViewModel> addGstinDetailsMultiGstinPerState(List<GstinDetailCreateViewModel> viewModels, IAuthContextViewModel auth);

    ResponseViewModel sendOtpForGSTIN(SendOTPRequestViewModel requestViewModel, IAuthContextViewModel auth);

    ResponseViewModel verifyOtpForGSTIN(VerifyOTPRequestViewModel requestViewModel, IAuthContextViewModel auth);

    Mono<OAuthProxyViewModel> getAccessTokenWithKeys(IAuthContextViewModel auth);
}
