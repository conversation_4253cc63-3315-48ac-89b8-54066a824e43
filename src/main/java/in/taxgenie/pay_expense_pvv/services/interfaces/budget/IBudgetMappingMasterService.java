package in.taxgenie.pay_expense_pvv.services.interfaces.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMappingMaster;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetMasterTreeViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.mappingmaster.BudgetMappingCreateViewModelWrapper;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.mappingmaster.BudgetMappingMasterViewModel;

import java.util.List;

public interface IBudgetMappingMasterService {
    BudgetMasterTreeViewModel getTree(long structureId, IAuthContextViewModel auth);
    BudgetMasterTreeViewModel create(long structureId, BudgetMappingCreateViewModelWrapper budgetMappingCreateViewModelWrapper, <PERSON><PERSON><PERSON> resetStages, IAuthContextViewModel auth);
    List<BudgetMappingMasterViewModel> getChildren(long structureId, Long parentId, IAuthContextViewModel auth);
    BudgetMappingMasterViewModel getRoot(long structureId, IAuthContextViewModel auth);

}
