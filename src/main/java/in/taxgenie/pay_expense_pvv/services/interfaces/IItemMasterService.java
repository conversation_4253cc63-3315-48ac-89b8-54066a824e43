package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.item_master.*;

import java.util.List;

public interface IItemMasterService {
    List<ItemMasterViewModel> getItemsByType(String type, IAuthContextViewModel auth);

    CreateItemMasterViewModel getItemsByItemCode(String itemCode, IAuthContextViewModel auth);

    UniqueViewModel checkItemCodeIsUnique(ItemMasterUniqueCheckRequestViewModel request, IAuthContextViewModel auth);

    List<SupplierViewModel> getSupplierByItemCode(String itemCode, IAuthContextViewModel auth);

    boolean deleteSupplierByItemId(Long id, IAuthContextViewModel auth);

    List<ItemMasterViewModel> getItemsByTypeAndVendor(String type, Long vendor, IAuthContextViewModel auth);

    GenericPageableViewModel<ItemMasterQueueViewModel> getQueue(QueueFilterViewModel filterValues, IAuthContextViewModel auth);

    CreateItemMasterViewModel createUpdateItemMaster(CreateItemMasterViewModel createItemMasterViewModel, IAuthContextViewModel auth);
    boolean createUpdateItemSupplier(ListSupplierViewModel createItemMasterViewModel, IAuthContextViewModel auth);
}
