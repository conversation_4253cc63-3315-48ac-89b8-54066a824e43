package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.MetadataLimitRuleCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.MetadataLimitRuleUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.MetadataLimitRuleViewModel;

import java.util.List;

public interface IMetadataLimitRuleService {
    MetadataLimitRuleViewModel create(MetadataLimitRuleCreateViewModel viewModel, IAuthContextViewModel auth);
    MetadataLimitRuleViewModel update(long id, MetadataLimitRuleUpdateViewModel viewModel, IAuthContextViewModel auth);
    List<MetadataLimitRuleViewModel> getRulesByMetadataId(long metadataId, IAuthContextViewModel auth);
    MetadataLimitRuleViewModel getRuleById(long id, IAuthContextViewModel auth);
}
