package in.taxgenie.pay_expense_pvv.services.interfaces.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentApprovalContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericPageableViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.syncmaster.BudgetSyncMasterCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.syncmaster.BudgetSyncMasterOptionsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.syncmaster.BudgetSyncMasterUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.syncmaster.BudgetSyncMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.MultiBudgetSyncMasterViewModel;

import java.util.List;

public interface IBudgetSyncMasterService {

	GenericPageableViewModel<BudgetSyncMasterViewModel> getAllBudgetSyncMaster(IAuthContextViewModel auth, long budgetMasterId, Boolean isActive, QueueFilterViewModel viewModel);

	MultiBudgetSyncMasterViewModel getAllBudgetSyncMaster(IAuthContextViewModel auth, long budgetMasterId, Boolean isActive);

	BudgetSyncMasterViewModel updateBudgetSyncMaster(BudgetSyncMasterUpdateViewModel budgetSyncMasterUpdateViewModel,
			IAuthContextViewModel auth);

	BudgetSyncMasterViewModel createBudgetSyncMaster(BudgetSyncMasterCreateViewModel budgetSyncMasterCreateViewModel,
			IAuthContextViewModel auth);

	List<BudgetSyncMasterOptionsViewModel> getOptions(long budgetMasterId, long structureId, IAuthContextViewModel auth);

	List<LookupDataModel> getBudgetSyncMasterLookupData(IAuthContextViewModel auth, Long masterId, Boolean isActive);

	GenericPageableViewModel<BudgetSyncMasterViewModel> getCustomAllBudgetSyncMaster(IAuthContextViewModel auth, Long budgetMasterId, Boolean isActive, QueueFilterViewModel filterValues);
}
