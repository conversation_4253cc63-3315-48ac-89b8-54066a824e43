package in.taxgenie.pay_expense_pvv.services.interfaces;

import org.springframework.http.HttpHeaders;
import org.springframework.util.MultiValueMap;

public interface IRestCreator<T> {
    void create(HttpHeaders headers, String url, Class<T> classInfoModel, T body);
    void create(HttpHeaders headers, String url, T body);
    void exchange(HttpHeaders headers, String url, MultiValueMap<String, Object> formData);
}
