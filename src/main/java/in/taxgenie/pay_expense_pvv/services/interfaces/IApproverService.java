package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.*;

import java.util.List;

public interface IApproverService {
    ApproverViewModel create(ApproverCreateViewModel viewModel, IAuthContextViewModel auth);
    ApproverViewModel update(long id, ApproverUpdateViewModel viewModel, IAuthContextViewModel auth);
    void startDelegation(DelegationCreateViewModel viewModel, IAuthContextViewModel auth);
    void endDelegation(long id, IAuthContextViewModel auth);

    void startDelegationByAdmin(DelegationCreateViewModel viewModel, IAuthContextViewModel auth);
    void endDelegationByAdmin(long id, IAuthContextViewModel auth);

    List<ApprovalDelegationViewModel> getAllDelegations(IAuthContextViewModel auth);
    List<ApprovalDelegationViewModel> getUserDelegations(IAuthContextViewModel auth);

    List<ApproverViewModel> getAll(IAuthContextViewModel auth);
    ApproverViewModel getById(long id, IAuthContextViewModel auth);

    List<ApprovalMatcherTitleViewModel> getApprovalMatcherTitleList(IAuthContextViewModel auth);

    //  exists
    boolean existsByCriteria(String approvalMatcher, String approvalMatchValue, String approvalTitle, IAuthContextViewModel auth);
    
    void changeDesk(long reportId, String toApproverMailId, IAuthContextViewModel auth);

}
