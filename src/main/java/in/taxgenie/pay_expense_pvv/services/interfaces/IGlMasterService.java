package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.gl_master.GlMaster;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericPageableViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.gl_master.*;

import java.util.List;

public interface IGlMasterService {
    GenericPageableViewModel<GLMasterViewModel> getQueue(QueueFilterViewModel filterValues, IAuthContextViewModel auth);

    GLMasterStatusViewModel syncGlMaster(GlMaster glMaster, IAuthContextViewModel auth);

    List<GlMasterSummary> searchGlMaster(GLMasterRequestViewModel glMasterRequestViewModel, IAuthContextViewModel auth);

    List<GlMaster> fullSearchGlMaster(String searchKey, IAuthContextViewModel auth);

    StoreGlMasterResponseViewModel storeGlMasterData(List<GlMaster> glMasterList, IAuthContextViewModel auth);
}
