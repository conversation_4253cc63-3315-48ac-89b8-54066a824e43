package in.taxgenie.pay_expense_pvv.services;

import java.time.LocalDate;

public class Dummy {
    private boolean isBetween(LocalDate date, LocalDate start, LocalDate end) {
        return (date.isAfter(start) && date.isBefore(end));
    }

    private boolean isSameDateRange(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2) {
        return (start1.isEqual(start2) && end1.isEqual(end2));
    }

    public boolean isDateRangeOverlap(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2) {
        boolean areSame = isSameDateRange(start1, end1, start2, end2);
        if (areSame) return true;

        return isBetween(start1, start2, end2) || isBetween(end1, start2, end2) ||
                isBetween(start2, start1, end1) || isBetween(end2, start1, end1);
    }
}
