package in.taxgenie.pay_expense_pvv.services;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ExternalServicesIdentifier;
import in.taxgenie.pay_expense_pvv.invoice.request.InvoiceDetailsReq;
import in.taxgenie.pay_expense_pvv.utils.MultiServiceClientFactory;
import in.taxgenie.pay_expense_pvv.utils.UrlConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

@Service
public class InvoiceOCRService {
    private final MultiServiceClientFactory multiServiceClientFactory;
    private final Logger logger;

    public InvoiceOCRService(MultiServiceClientFactory multiServiceClientFactory) {
        this.multiServiceClientFactory = multiServiceClientFactory;
        this.logger =  LoggerFactory.getLogger(this.getClass());
    }

    public InvoiceDetailsReq performOcrAndFetchDetails(MultipartFile file, String buyerGstin, IAuthContextViewModel auth) {
        WebClient webClient = multiServiceClientFactory.getClient(ExternalServicesIdentifier.OCR_SERVICE.ordinal());

        try {
            MultipartBodyBuilder bodyBuilder = new MultipartBodyBuilder();
            bodyBuilder.part("file", file.getResource());
            bodyBuilder.part("buyer_gstin", buyerGstin);

            return webClient.post()
                    .uri(UrlConstants.INVOICE_OCR)
                    .contentType(MediaType.MULTIPART_FORM_DATA)
                    .body(BodyInserters.fromMultipartData(bodyBuilder.build()))
                    .retrieve()
                    .bodyToMono(InvoiceDetailsReq.class)
                    .block();
        } catch (WebClientResponseException e) {
            // Handle exception as needed
            e.printStackTrace();
            throw new RuntimeException("Failed to get invoice details", e);
        }
    }
}
