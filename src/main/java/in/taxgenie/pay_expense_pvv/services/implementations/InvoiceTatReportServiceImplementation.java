package in.taxgenie.pay_expense_pvv.services.implementations;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cem.employee.interfaces.IEmployeeViewModel;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.tat_report.IInvoiceTatReportRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmployeeMasterDataService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IInvoiceTatReportService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static java.time.temporal.ChronoUnit.DAYS;


@Service
public class InvoiceTatReportServiceImplementation implements IInvoiceTatReportService {

    private static final String STATUS_UPLOADER = "UPLOADER";
    private static final String STATUS_APPROVER = "APPROVER";
    private static final String STATUS_PAYER = "PAYER";

    private final Logger logger;
    private final IInvoiceTatReportRepository repository;
    private final IDocumentApprovalContainerRepository documentApprovalContainerRepository;
    private final IEmployeeMasterDataService iEmployeeMasterDataService;
    public InvoiceTatReportServiceImplementation(IInvoiceTatReportRepository repository,
                                                 IDocumentApprovalContainerRepository documentApprovalContainerRepository, IEmployeeMasterDataService iEmployeeMasterDataService) {
        this.repository = repository;
        this.documentApprovalContainerRepository = documentApprovalContainerRepository;
        this.iEmployeeMasterDataService = iEmployeeMasterDataService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public Boolean create(ApproverType approverType, Long documentApprovalContainerId, IAuthContextViewModel auth) {
        logger.info("Starting Invoice TAT report creation for container ID: {}", documentApprovalContainerId);

        try {
            DocumentApprovalContainer documentApprovalContainer = documentApprovalContainerRepository
                    .findByCompanyCodeAndId(auth.getCompanyCode(), documentApprovalContainerId)
                    .orElseThrow(() -> new RecordNotFoundException(
                            "No DocumentApprovalContainer found for ID: " + documentApprovalContainerId));

            List<Document> documents = documentApprovalContainer.getDocuments();
            List<ReportState> reportStates = documentApprovalContainer.getReportStates();

            if (documents == null || documents.isEmpty()) {
                throw new DomainInvariantException("No documents found for container ID: " + documentApprovalContainerId);
            }

            if (reportStates == null || reportStates.isEmpty()) {
                logger.warn("No report states found for container ID: {}", documentApprovalContainerId);
            }

            InvoiceTatReport invoiceTatReport = new InvoiceTatReport();
            invoiceTatReport.setApproverType(approverType);
            invoiceTatReport.setApprover(auth.getUserEmail()); // TODO: Set approver logic
            invoiceTatReport.setEntryDate(DateTimeUtils.getCurrentZonedDateTime());
            invoiceTatReport.setDocId(documentApprovalContainerId);
            invoiceTatReport.setDocumentApprovalContainer(documentApprovalContainer);
            invoiceTatReport.setMetadata(documentApprovalContainer.getKeyValueJson());
            invoiceTatReport.setDocumentDate(documents.get(0).getDocumentDate());
            invoiceTatReport.setCompanyCode(auth.getCompanyCode());
            switch (approverType) {
                case UPLOADER -> handleUploader(invoiceTatReport, documentApprovalContainer, documents, reportStates, auth);
                case APPROVER -> handleApprover(invoiceTatReport, reportStates, auth);
                case PAYER -> handlePayer(invoiceTatReport, documentApprovalContainer);
                default -> throw new DomainInvariantException("Unknown ApproverType: " + approverType);
            }

            repository.save(invoiceTatReport);
            logger.info("Successfully created Invoice TAT report for container ID: {}", documentApprovalContainerId);
            return true;

        } catch (RecordNotFoundException e) {
            logger.error("Record not found: {}", e.getMessage());
            throw e;
        } catch (DomainInvariantException e) {
            logger.error("Domain constraint violation: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error while creating Invoice TAT report for container ID: {}", documentApprovalContainerId, e);
            throw new DomainInvariantException("Unexpected error while creating Invoice TAT report.");
        }
    }


    private void handleUploader(InvoiceTatReport invoiceTatReport, DocumentApprovalContainer container, List<Document> documents,
                                List<ReportState> reportStates, IAuthContextViewModel auth) {
        logger.debug("Handling TAT calculation for UPLOADER.");

        // Ensure documents list is not empty
        if (documents.isEmpty()) {
            logger.warn("No documents available for TAT calculation.");
            throw new DomainInvariantException("No documents found for TAT calculation.");
        }

        // Ensure document date and last actioned date are not null before calculating days
        LocalDate documentDate = Optional.ofNullable(documents.get(0).getDocumentDate())
                .orElseThrow(() -> new DomainInvariantException("Document date is missing for TAT calculation."));
        LocalDate lastActionedAt = Optional.ofNullable(container.getSubmitDate())
                .orElseThrow(() -> new DomainInvariantException("Last actioned date is missing for TAT calculation."));

        long days = ChronoUnit.DAYS.between(documentDate, lastActionedAt);

        Optional<InvoiceTatReport> invoiceTatReportOptional = repository.findTopByDocIdAndApproverTypeAndCompanyCodeOrderByIdDesc(invoiceTatReport.getDocId(), invoiceTatReport.getApproverType(), auth.getCompanyCode());
        if(invoiceTatReportOptional.isPresent()){
            invoiceTatReport.setTatTimeDays((int) (invoiceTatReportOptional.get().getTatTimeDays() - days));
        } else{
            invoiceTatReport.setTatTimeDays((int) days);
        }

        invoiceTatReport.setStatus(ExpenseActionStatus.UNACTIONED.name());

//        IEmployeeViewModel employeeMasterData = iEmployeeMasterDataService.getEmployeeMasterData(auth.getUserEmail(), auth);
//        // Find matching report state safely
//        reportStates.stream()
//                .filter(rs -> Objects.equals(rs.getApprover(), auth.getUserEmail()))
//                .findFirst()
//                .ifPresentOrElse(reportState -> {
//                    invoiceTatReport.setKey(Optional.ofNullable(reportState.getApprovalMatcher()).orElse("N/A"));
//                    invoiceTatReport.setValue(Optional.ofNullable(reportState.getApprovalMatchValue()).orElse("N/A"));
//                    invoiceTatReport.setTitle(Optional.ofNullable(reportState.getApprovalTitle()).orElse("N/A"));
//                    invoiceTatReport.setStatus(Optional.ofNullable(reportState.getStatus()).map(Enum::name).orElse("UNKNOWN"));
//                }, () -> {
//                    logger.warn("No matching report state found for user: {}", auth.getUserEmail());
//                    throw new DomainInvariantException("No matching report state found for approver.");
//                });
    }


    private void handleApprover(InvoiceTatReport invoiceTatReport, List<ReportState> reportStates, IAuthContextViewModel auth) {
        logger.debug("Handling TAT calculation for APPROVER.");
        ObjectMapper objectMapper = new ObjectMapper();

        reportStates.stream()
                .filter(rs -> Objects.equals(rs.getApprover(), auth.getUserEmail()))
                .findFirst()
                .ifPresentOrElse(reportState -> {
                    Long tatDays = null;

                    if (reportState.getLevel() == 1) {
                        // Ensure timestamps are not null before calculation
                        if (reportState.getCreatedTimestamp() != null && reportState.getActionDate() != null) {
                            tatDays = ChronoUnit.DAYS.between(reportState.getCreatedTimestamp().toLocalDate(), reportState.getActionDate());
                        } else {
                            logger.warn("Missing timestamps for level 1 ReportState: {}", reportState);
                        }
                    } else {
                        int currentIndex = reportStates.indexOf(reportState);
                        if (currentIndex > 0 && reportStates.size() > 1) { // Ensure previousState exists
                            ReportState previousState = reportStates.get(currentIndex - 1);
                            if (previousState.getActionDate() != null && reportState.getActionDate() != null) {
                                tatDays = ChronoUnit.DAYS.between(previousState.getActionDate(), reportState.getActionDate());
                            } else {
                                logger.warn("Missing action dates for ReportState: {}, previous: {}", reportState, previousState);
                            }
                        } else {
                            logger.warn("Previous state not found or not enough report states for index {}", currentIndex);
                        }
                    }

                    if (tatDays == null) {
                        logger.warn("TAT days could not be calculated. Defaulting to 0.");
                    }

                    invoiceTatReport.setTatTimeDays(tatDays != null ? tatDays.intValue() : 0);

                    invoiceTatReport.setKey(reportState.getApprovalMatcher());
                    invoiceTatReport.setValue(reportState.getApprovalMatchValue());
                    invoiceTatReport.setTitle(reportState.getApprovalTitle());
                    invoiceTatReport.setStatus(reportState.getStatus().name());


                    // Convert existing metadata JSON string to Map<String, String>
                    Map<String, String> metadataMap;
                    try {
                        metadataMap = invoiceTatReport.getMetadata() != null
                                ? objectMapper.readValue(invoiceTatReport.getMetadata(), new TypeReference<Map<String, String>>() {})
                                : new HashMap<>();
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to parse metadata JSON", e);
                    }

                    // Add new key-value pair
                    if(reportState.getApprovalTitle() != null && !reportState.getApprovalTitle().isEmpty() &&
                            reportState.getApprovalMatchValue() != null && !reportState.getApprovalMatchValue().isEmpty()) {
                        metadataMap.put(reportState.getApprovalTitle(), reportState.getApprovalMatchValue());
                    }
                    // Convert updated Map back to JSON string and save
                    try {
                        invoiceTatReport.setMetadata(objectMapper.writeValueAsString(metadataMap));
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to convert metadata to JSON", e);
                    }

                }, () -> {
                    logger.warn("No matching report state found for user: {}", auth.getUserEmail());
                    throw new DomainInvariantException("No matching report state found for approver.");
                });
    }

    private void handlePayer(InvoiceTatReport invoiceTatReport, DocumentApprovalContainer container) {
        logger.debug("Handling TAT calculation for PAYER.");
        ZonedDateTime maxLevelTimestamp = container.getReportStates().stream()
                .max(Comparator.comparingInt(ReportState::getLevel))
                .map(ReportState::getUpdatedTimestamp)
                .orElse(null);
        if (maxLevelTimestamp == null){
            logger.error("No Approved Date for document {} - TAT entry for payment details is failed", container.getId());
            return;
//            throw new DomainInvariantException("No Approved Date for document");
        }
        Integer tatDays = Math.toIntExact(DAYS.between(
                maxLevelTimestamp.toLocalDate(),
                container.getPaymentDate()));
        invoiceTatReport.setTatTimeDays(tatDays);
        invoiceTatReport.setStatus(ExpenseActionStatus.APPROVED.name());
    }
}
