package in.taxgenie.pay_expense_pvv.services.implementations;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Company;
import in.taxgenie.pay_expense_pvv.entities.ExternalServicesIdentifier;
import in.taxgenie.pay_expense_pvv.entities.invoice.Mailroom;
import in.taxgenie.pay_expense_pvv.entities.mailroom.InvoiceSyncReport;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.govrndto.InvoiceDTO;
import in.taxgenie.pay_expense_pvv.govrndto.InvoiceDataDTO;
import in.taxgenie.pay_expense_pvv.govrndto.SignedDataDTO;
import in.taxgenie.pay_expense_pvv.repositories.ICompanyRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomMailroomRepository;
import in.taxgenie.pay_expense_pvv.repositories.mailroom.IInvoiceSyncReportReposiitory;
import in.taxgenie.pay_expense_pvv.repositories.mailroom.IMailRoomReposiitory;
import in.taxgenie.pay_expense_pvv.repositories.tat_report.IInvoiceTatReportRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.*;
import in.taxgenie.pay_expense_pvv.services.interfaces.company.IGstinDetailService;
import in.taxgenie.pay_expense_pvv.utils.MultiServiceClientFactory;
import in.taxgenie.pay_expense_pvv.utils.UrlConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.company.OAuthProxyViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.company.ResponseViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.TATDataViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.TATResponseViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.mailroom.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
public class InvoiceMailroomServiceImplementation implements IInvoiceMailroomService {

    @Value("${web-client-config.services.base-url-service}")
    private String baseUrlService;

    public final CustomMailroomRepository customMailroomRepository;
    public final IInvoiceSyncReportReposiitory iInvoiceSyncReportReposiitory;
    public final IMailRoomReposiitory iMailRoomReposiitory;
    public final IGstinDetailService gstinDetailService;
    private final IInvoiceTatReportRepository iInvoiceTatReportRepository;
    public final ICompanyRepository companyRepository;

    private final com.taxgenie.utils.api.MultiServiceClient webClientHelper;
    private final in.taxgenie.pay_expense_pvv.MultiServiceClient webClientHelperV2;
    private final MultiServiceClientFactory multiServiceClientFactory;

    private final ObjectMapper objectMapper;
    private final Logger logger;

    public InvoiceMailroomServiceImplementation(CustomMailroomRepository customMailroomRepository, IInvoiceSyncReportReposiitory iInvoiceSyncReportReposiitory, IMailRoomReposiitory iMailRoomReposiitory, IGstinDetailService gstinDetailService, IInvoiceTatReportRepository iInvoiceTatReportRepository, ICompanyRepository companyRepository, MultiServiceClientFactory multiServiceClientFactory, ObjectMapper objectMapper) {
        this.iInvoiceSyncReportReposiitory = iInvoiceSyncReportReposiitory;
        this.iMailRoomReposiitory = iMailRoomReposiitory;
        this.gstinDetailService = gstinDetailService;
        this.iInvoiceTatReportRepository = iInvoiceTatReportRepository;
        this.companyRepository = companyRepository;
        this.multiServiceClientFactory = multiServiceClientFactory;
        this.webClientHelper = multiServiceClientFactory.createMultiServiceClient(WebClient.builder());
        this.webClientHelperV2 = multiServiceClientFactory.createMultiServiceClientV2(WebClient.builder());
        this.objectMapper = objectMapper;
        this.logger = LoggerFactory.getLogger(MetadataBudgetMappingServiceImplementation.class);
        this.customMailroomRepository = customMailroomRepository;
    }

    @Override
    public InvoiceMailroomDataViewModel getQueueDataById(long id, IAuthContextViewModel auth) {
        logger.info("Fetching mailroom record data for company: {} with id: {}", auth.getCompanyCode(), id);

        try {
            return customMailroomRepository.getMailroomInvoiceById(auth.getCompanyCode(), id);
        } catch (DataAccessException e) {
            logger.error("Database error while fetching mailroom data for company: {}. Error: {}", auth.getCompanyCode(), e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Unexpected error fetching mailroom data for company: {}. Error: {}", auth.getCompanyCode(), e.getMessage(), e);
        }
        return null;
    }

    @Override
    public TATResponseViewModel getQueueTatData(IAuthContextViewModel auth) {

        TATResponseViewModel response = new TATResponseViewModel();

        try {
            List<Object[]> results = iInvoiceTatReportRepository.findMailroomInvoiceAgeingData(auth.getCompanyCode());

            SummaryViewModel mailroomSummary = iInvoiceSyncReportReposiitory.getMailroomSummary(auth.getCompanyCode());
            // Map the results to the response model
            List<TATDataViewModel> data = results.stream()
                    .map(row -> new TATDataViewModel(
                            (String) row[0], // ageing
                            (String) row[1], // indicator
                            ((Number) row[2]).longValue(), // noOfInvoices
                            row[3] != null ? BigDecimal.valueOf(((Number) row[3]).doubleValue()) : BigDecimal.ZERO, // totalInvoiceAmount
                            BigDecimal.ZERO // mix

                    ))
                    .collect(Collectors.toList());

            // Set the data in the response view model and return
            response.setData(data);
            response.setMailroomSummary(mailroomSummary);

            logger.info("Successfully fetched {} invoice records", data.size());

        } catch (Exception e) {
            // Log the exception with details
            logger.error("Error occurred while fetching invoice TAT report", e);

            // Set a failure response or return a default response
            throw new DomainInvariantException("Failed to fetch invoice TAT report. Please try again later.");
        }
        return response;
    }

    @Override
    public GenericPageableViewModel<InvoiceMailroomQueueViewModel> getQueue(QueueFilterViewModel filterValues, IAuthContextViewModel auth) {

        logger.info("Fetching mailroom display queue for company: {} with page: {} and size: {}",
                auth.getCompanyCode(), filterValues.getPage(), filterValues.getPageSize());

        GenericPageableViewModel<InvoiceMailroomQueueViewModel> pagedQueue = new GenericPageableViewModel<>();

        try {
            // Create Pageable object
            Pageable pageable = PageRequest.of(
                    Math.max(filterValues.getPage() - 1, 0),  // Ensure page index is not negative
                    filterValues.getPageSize()
            );

            // Fetch paginated results
            Page<InvoiceMailroomQueueViewModel> invoiceQueuePage = customMailroomRepository
                    .getMailroomInvoice(auth.getCompanyCode(), filterValues, pageable);

            // Map results to the response
            pagedQueue.setData(invoiceQueuePage.getContent());
            pagedQueue.setPages(invoiceQueuePage.getTotalPages());
            pagedQueue.setTotalElements((int) invoiceQueuePage.getTotalElements());

            logger.info("Queue fetched successfully for company: {}. Total records: {}, Total pages: {}",
                    auth.getCompanyCode(), pagedQueue.getTotalElements(), pagedQueue.getPages());

            return pagedQueue;

        } catch (DataAccessException e) {
            logger.error("Database error while fetching queue for company: {}. Error: {}",
                    auth.getCompanyCode(), e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Unexpected error fetching queue for company: {}. Error: {}",
                    auth.getCompanyCode(), e.getMessage(), e);
        }

        // Return empty paged queue in case of exception
        pagedQueue.setData(Collections.emptyList());
        pagedQueue.setPages(0);
        pagedQueue.setTotalElements(0);

        return pagedQueue;
    }

    @Override
    public GenericPageableViewModel<InvoiceSyncReportQueueViewModel> getMailroomSyncReportQueue(
            QueueFilterViewModel filterValues, IAuthContextViewModel auth) {

        logger.info("Fetching mailroom sync report queue for company: {} with page: {} and size: {}",
                auth.getCompanyCode(), filterValues.getPage(), filterValues.getPageSize());

        GenericPageableViewModel<InvoiceSyncReportQueueViewModel> pagedQueue = new GenericPageableViewModel<>();

        try {
            // Ensure page index is not negative
            Pageable pageable = PageRequest.of(
                    Math.max(filterValues.getPage() - 1, 0),
                    filterValues.getPageSize()
            );

            // Fetch paginated results
            Page<InvoiceSyncReportQueueViewModel> invoiceSyncReportQueue = customMailroomRepository
                    .getInvoiceSyncReportForCompany(auth.getCompanyCode(), filterValues, pageable);

            // Map results to the response
            pagedQueue.setData(invoiceSyncReportQueue.getContent());
            pagedQueue.setPages(invoiceSyncReportQueue.getTotalPages());
            pagedQueue.setTotalElements((int) invoiceSyncReportQueue.getTotalElements());

            logger.info("Queue fetched successfully for company: {}. Total records: {}, Total pages: {}",
                    auth.getCompanyCode(), pagedQueue.getTotalElements(), pagedQueue.getPages());

            return pagedQueue;

        } catch (DataAccessException e) {
            logger.error("Database error while fetching sync report queue for company: {}. Error: {}",
                    auth.getCompanyCode(), e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Unexpected error fetching sync report queue for company: {}. Error: {}",
                    auth.getCompanyCode(), e.getMessage(), e);
        }

        // Return empty paged queue in case of exception
        pagedQueue.setData(Collections.emptyList());
        pagedQueue.setPages(0);
        pagedQueue.setTotalElements(0);

        return pagedQueue;
    }


    @Override
    public ResponseViewModel syncInvoice(InvoiceSyncRequestViewModel requestViewModel, IAuthContextViewModel auth) {

        ResponseViewModel responseViewModel = new ResponseViewModel();

        try {
            logger.info("Starting invoice sync for GSTIN: {}, Rtnprd: {} by company: {}",
                    requestViewModel.getGstinNo(), requestViewModel.getRtnprd(), auth.getCompanyCode());

            // Fetch OAuth token
            OAuthProxyViewModel oAuthProxyViewModel = fetchAccessToken(auth);
            if (oAuthProxyViewModel == null) {
                logger.error("Failed to obtain access token for company: {}", auth.getCompanyCode());
                return new ResponseViewModel(false, "Failed to authenticate. Please contact the administrator.");
            }

            // Prepare request payload
            SyncGovInvoiceRequestViewModel syncRequest = buildSyncRequest(requestViewModel, oAuthProxyViewModel, auth);

            // Sync invoice data
            String syncResponse = syncGovInvoiceData(syncRequest, auth).block();
            logger.info("Sync response: {}", syncResponse);

            // Parse and handle the response
            handleSyncResponse(syncResponse, responseViewModel);

        } catch (Exception e) {
            logger.error("Exception in invoice sync for company: {}. Error: {}", auth.getCompanyCode(), e.getMessage(), e);
            responseViewModel.setStatus(false);
            responseViewModel.setMessage("Something went wrong, please try again later...");
        }

        return responseViewModel;
    }

    /**
     * Fetches OAuth Access Token
     */
    private OAuthProxyViewModel fetchAccessToken(IAuthContextViewModel auth) {
        try {
            OAuthProxyViewModel oAuthProxyViewModel = gstinDetailService.getAccessTokenWithKeys(auth).block();
            if (oAuthProxyViewModel == null) {
                logger.error("Failed to obtain access token");
                return null;
            }
            return oAuthProxyViewModel;
        } catch (Exception e) {
            logger.error("Failed to get access token: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Builds the Sync Request Payload
     */
    private SyncGovInvoiceRequestViewModel buildSyncRequest(
            InvoiceSyncRequestViewModel requestViewModel,
            OAuthProxyViewModel oAuthProxyViewModel,
            IAuthContextViewModel auth) {

        SyncGovInvoiceRequestViewModel syncRequest = new SyncGovInvoiceRequestViewModel();
        syncRequest.setGstinType("buyer");
        syncRequest.setRtnprd(requestViewModel.getRtnprd());
        syncRequest.setSupplyType("B2B");
        syncRequest.setGstin(requestViewModel.getGstinNo());
        syncRequest.setApigeeToken(oAuthProxyViewModel.getTokenType() + " " + oAuthProxyViewModel.getAccessToken());
        syncRequest.setStatusCallBackSUrl(baseUrlService + "invoice_mailroom/invoice_sync_history");
        syncRequest.setCallBackUrl(baseUrlService + "invoice_mailroom/buyer_data_upload");
        syncRequest.setCallBackAuthToken("Bearer " + auth.getToken());

        return syncRequest;
    }

    /**
     * Handles Sync Response
     */
    private void handleSyncResponse(String syncResponse, ResponseViewModel responseViewModel) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(syncResponse);

            // Extract executionId
            String executionId = root.path("executionId").asText();
            JsonNode outputParameters = root.path("outputParameters").path("Response");

            // Extract status and message
            String status = outputParameters.path("status").asText();
            String message = outputParameters.path("message").asText();

            logger.info("Execution ID: {}, Status: {}, Message: {}", executionId, status, message);

            if ("success".equalsIgnoreCase(status)) {
                responseViewModel.setStatus(true);
                responseViewModel.setMessage("Invoice sync successful: " + message);
            } else {
                responseViewModel.setStatus(false);
                responseViewModel.setMessage("Invoice sync failed: " + message);
            }

        } catch (Exception e) {
            logger.error("Failed to parse sync response: {}", e.getMessage(), e);
            responseViewModel.setStatus(false);
            responseViewModel.setMessage("Failed to parse the sync response.");
        }
    }

    private Mono<String> syncGovInvoiceData(SyncGovInvoiceRequestViewModel syncGovInvoiceRequestViewModel, IAuthContextViewModel auth) {


        Map<String, String> headers = Map.of(
                "gstin", syncGovInvoiceRequestViewModel.getGstin(),
                "Authorization", syncGovInvoiceRequestViewModel.getApigeeToken()
        );

        return webClientHelperV2.makeRequest(
                        "application/json",
                        ExternalServicesIdentifier.API4BUSINESS_SERVICE.ordinal(),
                        HttpMethod.POST,
                        UrlConstants.API4_BUSINESS_SYNC_DATA,
                        Optional.of(syncGovInvoiceRequestViewModel),
                        null,
                        null,
                        String.class,
                        null,
                        headers
                )
                .doOnSuccess(response -> logger.info("Sync request submitted Successfully"))
                .doOnError(error -> logger.error("Failed to submit sync request : {}", error.getMessage()));
    }

    @Override
    public List<MonthObjectViewModel> getMonthLookupData(IAuthContextViewModel auth) {
        List<MonthObjectViewModel> monthObjectViewModelsResponse = new ArrayList<>();

        try {
            YearMonth currentMonth = YearMonth.now();
            YearMonth startMonth = currentMonth.minusMonths(6); // Past 6 months including current month

            logger.info("Generating month lookup data from {} to {}",
                    startMonth.format(DateTimeFormatter.ofPattern("MMM yyyy")),
                    currentMonth.format(DateTimeFormatter.ofPattern("MMM yyyy")));

            DateTimeFormatter nameFormatter = DateTimeFormatter.ofPattern("MMM yyyy");

            // Stream-based iteration from past 6 months to the current month
            Stream.iterate(startMonth, month -> month.plusMonths(1))
                    .limit(7)  // 6 past months + current month = 7 months
                    .map(month -> {
                        String name = month.format(nameFormatter);
                        String value = String.format("%02d%d", month.getMonthValue(), month.getYear());
                        return new MonthObjectViewModel(name, value);
                    })
                    .forEach(monthObjectViewModelsResponse::add);

            logger.info("Successfully generated {} months of lookup data.", monthObjectViewModelsResponse.size());

        } catch (Exception exception) {
            logger.error("Failed to get month lookup data for mailroom", exception);
            throw new DomainInvariantException("Something went wrong, please try again later...(Failed to get month lookup data)");
        }

        return monthObjectViewModelsResponse;
    }


    @Override
    public String downloadJson(Long id, IAuthContextViewModel auth) {
        try {
            return iMailRoomReposiitory.findById(id)
                    .map(Mailroom::getJsonData)
                    .orElseThrow(() -> {
                        logger.warn("No Mailroom record found with ID: {}", id);
                        return new DomainInvariantException("No data found for the given ID.");
                    });
        } catch (Exception exception) {
            logger.error("Failed to download invoice json for ID: {}. Error: {}", id, exception.getMessage(), exception);
            throw new DomainInvariantException("Something went wrong, please try again later...(Failed to download invoice json.)");
        }
    }

    @Override
    public boolean createOrUpdateInvoiceSyncHistory(
            InvoiceSyncReportRequestViewModel requestViewModel,
            IAuthContextViewModel auth) {

        Long companyCode = auth.getCompanyCode();
        String requestId = requestViewModel.getRequestId();

        logger.info("Starting createOrUpdateInvoiceSyncHistory for company: {} with requestId: {}", companyCode, requestId);

        try {
            InvoiceSyncReport invoiceSyncReport = iInvoiceSyncReportReposiitory
                    .findByCompanyCodeAndRequestId(companyCode, requestId)
                    .map(report -> {
                        logger.info("Updating existing report with ID: {}", report.getId());
                        report.update(requestViewModel, auth);
                        return report;
                    })
                    .orElseGet(() -> {
                        logger.info("Creating new report for company: {} with requestId: {}", companyCode, requestId);
                        InvoiceSyncReport newReport = new InvoiceSyncReport();
                        newReport.create(requestViewModel, auth);
                        return newReport;
                    });

            iInvoiceSyncReportReposiitory.save(invoiceSyncReport);

            logger.info("Successfully saved report with ID: {}", invoiceSyncReport.getId());
            return true;

        } catch (Exception exception) {
            logger.error("Failed to create or update report for company: {} with requestId: {}. Error: {}",
                    companyCode, requestId, exception.getMessage(), exception);
            return false;
        }
    }

    public String decodeJwt(String jwt) {
        try {
            String[] parts = jwt.split("\\.");
            if (parts.length < 2) {
                throw new IllegalArgumentException("Invalid JWT token");
            }
            byte[] decodedBytes = Base64.getUrlDecoder().decode(parts[1]);
            return new String(decodedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("Failed to decode JWT token. Error: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean buyerDataUpload(SignedDataDTO requestViewModel, String requestId, IAuthContextViewModel auth) {

        Long companyCode = auth.getCompanyCode();
        String irn = requestViewModel.getIrn();

        logger.info("Starting buyer data upload for company: {} with requestId: {}", companyCode, requestId);

        try {
            // Check if Mailroom entry already exists
            if (iMailRoomReposiitory.existsByCompanyCodeAndIrn(companyCode, irn)) {
                logger.info("Mailroom entry already exists for IRN: {} in company: {}", irn, companyCode);
                return true;
            }

            // Decode JWT
            String signedInvoice = decodeJwt(requestViewModel.getSignedInvoice());
            if (signedInvoice == null) {
                logger.error("Failed to decode JWT for IRN: {} in company: {}", irn, companyCode);
                return false;
            }

            // Map JSON to DTO
            InvoiceDataDTO invoiceDataDTO = objectMapper.readValue(signedInvoice, InvoiceDataDTO.class);
            InvoiceDTO invoiceDTO = objectMapper.readValue(invoiceDataDTO.getData(), InvoiceDTO.class);

            // Fetch company details by GSTIN
            String gstin = invoiceDTO.getSellerDtls().getGst();
            List<Company> companies = companyRepository.findByCamCompanyIdAndGst(companyCode, gstin);

            // Create and populate Mailroom entity
            Mailroom mailroom = new Mailroom();
            mailroom.buildMailroomEntity(objectMapper, requestViewModel, invoiceDTO, companies, auth);

            // Save Mailroom entity
            iMailRoomReposiitory.save(mailroom);

            logger.info("Mailroom entry saved successfully for IRN: {} and company: {}", irn, companyCode);
            return true;

        } catch (JsonProcessingException e) {
            logger.error("JSON processing failed for company: {} with requestId: {}. Error: {}",
                    companyCode, requestId, e.getMessage(), e);
            return false;
        } catch (Exception e) {
            logger.error("Failed to create entry of invoice JSON for company: {} with requestId: {}. Error: {}",
                    companyCode, requestId, e.getMessage(), e);
            return false;
        }
    }

}
