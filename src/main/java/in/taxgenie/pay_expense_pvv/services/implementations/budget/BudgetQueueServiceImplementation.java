package in.taxgenie.pay_expense_pvv.services.implementations.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IUsersRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetFrequencyMappingRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetStructureMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomBudgetRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.rbac.IOrgHierarchyRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetQueueService;
import in.taxgenie.pay_expense_pvv.services.interfaces.rbac.IOrgHierarchyService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.ExcelUtil;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericPageableViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.ConsumptionDetailsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.queue.BudgetLookupViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.queue.BudgetRowViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.DocumentWidgetStatsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.rmi.RemoteException;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class BudgetQueueServiceImplementation implements IBudgetQueueService {
    IBudgetStructureMasterRepository structureMasterRepository;
    IBudgetRepository budgetRepository;
    IBudgetFrequencyMappingRepository frequencyMappingRepository;
    IUsersRepository usersRepository;
    CustomBudgetRepository customBudgetRepository;
    private final IOrgHierarchyService orgHierarchyService;
    private final IOrgHierarchyRepository orgHierarchyRepository;
    private final CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository;
    private ExcelUtil excelUtil;
    private final Logger logger;

    public BudgetQueueServiceImplementation(IBudgetStructureMasterRepository structureMasterRepository,
                                            IBudgetRepository budgetRepository,
                                            IBudgetFrequencyMappingRepository frequencyMappingRepository,
                                            IUsersRepository usersRepository,
                                            CustomBudgetRepository customBudgetRepository, IOrgHierarchyService orgHierarchyService, IOrgHierarchyRepository orgHierarchyRepository, CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository,
                                            ExcelUtil excelUtil) {
        this.structureMasterRepository = structureMasterRepository;
        this.budgetRepository = budgetRepository;
        this.frequencyMappingRepository = frequencyMappingRepository;
        this.usersRepository = usersRepository;
        this.customBudgetRepository = customBudgetRepository;
        this.orgHierarchyService = orgHierarchyService;
        this.orgHierarchyRepository = orgHierarchyRepository;
        this.customDocumentApprovalContainerRepository = customDocumentApprovalContainerRepository;
        this.excelUtil = excelUtil;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public GenericPageableViewModel<BudgetRowViewModel> getQueue(QueueFilterViewModel filterValues, IAuthContextViewModel auth) {
        logger.info(String.format("BudgetQueueServiceImplementation-getQueue: Called for company id: %s", auth.getCompanyCode()));
        GenericPageableViewModel<BudgetRowViewModel> pagedQueue = new GenericPageableViewModel<>();

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize());
        Page<BudgetRowViewModel> budgetRowsPaged = customBudgetRepository.getBudgetRowModelsForCompany(auth.getCompanyCode(), filterValues, pageable);

        // Set response
        pagedQueue.setData(budgetRowsPaged.getContent());
        pagedQueue.setPages(budgetRowsPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(budgetRowsPaged.getTotalElements()));

        return pagedQueue;
    }

    @Override
    public List<BudgetLookupViewModel> getQueueLookup(IAuthContextViewModel auth) {

        return structureMasterRepository.findByCompanyCodeAndStatus(
                        auth.getCompanyCode(), ReportStatus.ACCEPTED)
                .stream()
                .map(this::getLookupViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public DocumentWidgetStatsResponse<GenericPageableViewModel<ConsumptionDetailsViewModel>> getBudgetConsumptionDetailsForPurchaseOrder(Long budgetId, QueueFilterViewModel filterValues, IAuthContextViewModel auth) {
        List<ConsumptionDetailsViewModel> consumptionDetailsList = new ArrayList<>();
        logger.info(String.format("getBudgetConsumptionDetailsForPurchaseOrder: Called for company id: %s", auth.getCompanyCode()));
        DocumentWidgetStatsResponse response = new DocumentWidgetStatsResponse();
        GenericPageableViewModel<ConsumptionDetailsViewModel> pagedQueue = new GenericPageableViewModel<>();

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize());

        String rbacKey = orgHierarchyRepository.findDistinctKeyByCompanyCode(auth.getCompanyCode());
        List<String> rbacValues = orgHierarchyService.getValuesForRbac(auth.getUserId(), auth.getCompanyCode()); // List of values for the key

        Page<ConsumptionDetailsViewModel> viewModelPaged = customDocumentApprovalContainerRepository
                .getBudgetConsumptionDetailsForPurchaseOrder(budgetId, auth.getCompanyCode(), auth.getUserId(), filterValues, pageable, rbacKey, rbacValues);

        pagedQueue.setData(viewModelPaged.getContent().isEmpty() ? consumptionDetailsList : viewModelPaged.getContent());
        pagedQueue.setTotalElements(Math.toIntExact(viewModelPaged.getTotalElements()));
        pagedQueue.setPages(viewModelPaged.getTotalPages());

        String lastUpdatedAt = getLatestLastActionedAt(viewModelPaged.getContent());
        response.setLastUpdatedAt(lastUpdatedAt);
        response.setData(pagedQueue);

        return response;
    }
    public String getLatestLastActionedAt(List<ConsumptionDetailsViewModel> consumptionDetailsList) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        return consumptionDetailsList.stream()
                .map(ConsumptionDetailsViewModel::getLastActionedAt)
                .max(Comparator.comparing(date -> LocalDate.parse(date, formatter)))
                .orElse(null);
    }
    @Override
    public DocumentWidgetStatsResponse<GenericPageableViewModel<ConsumptionDetailsViewModel>> getBudgetConsumptionDetailsForInvoice(Long budgetId, QueueFilterViewModel filterValues, IAuthContextViewModel auth) {
        List<ConsumptionDetailsViewModel> consumptionDetailsList = new ArrayList<>();
        logger.info(String.format("getBudgetConsumptionDetailsForInvoice: Called for company id: %s", auth.getCompanyCode()));
        DocumentWidgetStatsResponse response = new DocumentWidgetStatsResponse();
        GenericPageableViewModel<ConsumptionDetailsViewModel> pagedQueue = new GenericPageableViewModel<>();

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize());

        String rbacKey = orgHierarchyRepository.findDistinctKeyByCompanyCode(auth.getCompanyCode());
        List<String> rbacValues = orgHierarchyService.getValuesForRbac(auth.getUserId(), auth.getCompanyCode()); // List of values for the key

        Page<ConsumptionDetailsViewModel> viewModelPaged = customDocumentApprovalContainerRepository
                .getBudgetConsumptionDetailsForInvoice(budgetId, auth.getCompanyCode(), auth.getUserId(), filterValues, pageable, rbacKey, rbacValues);

        pagedQueue.setData(viewModelPaged.getContent().isEmpty() ? consumptionDetailsList : viewModelPaged.getContent());
        pagedQueue.setTotalElements(Math.toIntExact(viewModelPaged.getTotalElements()));
        pagedQueue.setPages(viewModelPaged.getTotalPages());

        String lastUpdatedAt = getLatestLastActionedAt(viewModelPaged.getContent());
        response.setLastUpdatedAt(lastUpdatedAt);
        response.setData(pagedQueue);
        return response;
    }

    @Override
    public ResponseEntity<Resource> getBudgetConsumptionDetailsExcelForInvoice(Long budgetId, QueueFilterViewModel filterValues, IAuthContextViewModel auth) {

        try {
            List<ConsumptionDetailsViewModel> consumptionDetailsList = getBudgetConsumptionDetailsForInvoice(budgetId, filterValues, auth).getData().getData();
            String filePath = generateConsumptionDetailsExcel(consumptionDetailsList, "Invoice");
            Path file = Paths.get(filePath);
            Resource resource = new FileSystemResource(file.toFile());
            ResponseEntity<Resource> response = ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + file.getFileName().toString() + "\"")
                    .body(resource);

            return response;
        } catch (Exception e) {
            logger.error("Error occurred : " + e.getLocalizedMessage());
//            Files.deleteIfExists(file);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public List<BudgetLookupViewModel> getBudgetStructureByExpenseType(String expenseType, IAuthContextViewModel auth) {
        return structureMasterRepository.findByCompanyCodeAndStatusAndApplicableExpenseType(
                        auth.getCompanyCode(), ReportStatus.ACCEPTED, ExpenseType.fromString(expenseType));
    }

    public String generateConsumptionDetailsExcel(List<ConsumptionDetailsViewModel> details, String entity) throws IOException {
        // Create a temporary file in the system's temp directory

        File tempFile = null;
        try {
            tempFile = File.createTempFile(entity+"-consumption-details", ".xlsx");
            tempFile.deleteOnExit();

            return excelUtil.generateConsumptionDetailsExcel(details, tempFile.getAbsolutePath());

        } catch (IOException e) {
            logger.error("Error generating the Excel report", e);
            if (tempFile != null && tempFile.exists()) {
                boolean fileDeleted = tempFile.delete();
                if (fileDeleted) {
                    logger.info("Temporary file deleted successfully");
                } else {
                    logger.info("There was an error deleting the tempoary file");
                }
            }
            throw new RemoteException(e.getMessage());
        }
    }

    public BudgetLookupViewModel getLookupViewModel(Object entity) {
        BudgetLookupViewModel viewModel = new BudgetLookupViewModel();
        BeanUtils.copyProperties(entity, viewModel);
        return viewModel;
    }
}
