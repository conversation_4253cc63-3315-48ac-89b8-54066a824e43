package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.exceptions.RestConsumptionFailedException;
import in.taxgenie.pay_expense_pvv.services.interfaces.IRestConsumer;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class RestConsumerImplementation<TRead>
        implements
        IRestConsumer<TRead> {
    private final RestTemplate restTemplate;

    public RestConsumerImplementation(RestTemplateBuilder restTemplateBuilder) {
        this.restTemplate = restTemplateBuilder.build();
    }


    @Override
    public TRead read(HttpMethod method, HttpHeaders headers, String url, Class<TRead> classInfoModel) {
        HttpEntity<TRead> httpEntity = new HttpEntity<>(headers);

        ResponseEntity<TRead> response =
                this.restTemplate.exchange(url, method, httpEntity, classInfoModel);

        if (response.getStatusCode() == HttpStatus.OK) {
            return response.getBody();
        } else {
            throw new RestConsumptionFailedException("Failed to fetch data from: " + url);
        }
    }
}
