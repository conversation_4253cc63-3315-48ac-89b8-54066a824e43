package in.taxgenie.pay_expense_pvv.services.implementations.users;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Users;
import in.taxgenie.pay_expense_pvv.repositories.IUsersRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IUsersService;
import in.taxgenie.pay_expense_pvv.services.interfaces.rbac.IKeyValueMappingService;
import in.taxgenie.pay_expense_pvv.utils.CommonUtility;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.users.UsersViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UsersServiceImplementation implements IUsersService{
    private final IUsersRepository usersRepository;
    private final Logger logger;
    private final IKeyValueMappingService keyValueMappingService;

    public UsersServiceImplementation(IUsersRepository usersRepository, IKeyValueMappingService keyValueMappingService) {
        this.usersRepository = usersRepository;
        this.keyValueMappingService = keyValueMappingService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Transactional
    @Override
    public void updateUserInfoFromAuth(IAuthContextViewModel auth) {
        logger.info("updateUserInfoFromAuth: Entered function for userId: {}", auth.getUserId());
        UsersViewModel viewModel = new UsersViewModel(auth.getUserId(), auth.getUserEmail());
        viewModel.setCompanyCode(auth.getCompanyCode());
        Users entity;
        // Check if user is created and if user not created then create new entry
        Optional<Users> entityOptional = usersRepository.findById(viewModel.getUserId());
        logger.info("updateUserInfoFromAuth: User with Id: {} present in system: {}", auth.getUserId(), entityOptional.isPresent());
        // If user exists then updated with the fields that have been provided.
        entity = entityOptional.map(users -> Users.update(users, viewModel))
                .orElseGet(() -> Users.create(viewModel));

        usersRepository.saveAndFlush(entity);

        if (entityOptional.isPresent()) {
            logger.info("updateUserInfoFromAuth: User with Id: {} was updated", auth.getUserId());
        } else {
            logger.info("updateUserInfoFromAuth: User with Id: {} was created", auth.getUserId());
        }
        logger.info("updateUserInfoFromAuth: Returning for userId: {}", auth.getUserId());
    }

    @Override
    @Transactional
    public void saveUserAndKeyValues(List<UsersViewModel> usersViewModelList, IAuthContextViewModel auth) {

        logger.info("saveUserAndKeyValues: Entered function for companyId: {}", auth.getCompanyCode());
        HashMap<String, Users> usersToUpdate = getUsersToUpdate(usersViewModelList, auth.getCompanyCode());

        List<Users> usersList = new ArrayList<Users>();

        usersViewModelList.forEach(vm -> {
            Users entity = null;
            String email = vm.getEmailId();
            if(usersToUpdate.containsKey(email)){
                entity = updateUser(vm, usersToUpdate.get(email), auth.getCompanyCode());
            }else {
                entity = createUser(vm, auth.getCompanyCode());
            }
            usersList.add(entity);
        });

        usersRepository.saveAllAndFlush(usersList);
        logger.info("Employee sync successful.");

        //find out unique key-value pairs from usersViewModelList and insert/update key-value pairs table records
        List<Map.Entry<String, String>> distinctKeyValuePairs = usersViewModelList.stream()
                .flatMap(user -> user.getKeyValueMap().entrySet().stream())
                .distinct()
                .filter(entry -> StaticDataRegistry.isNotNullOrEmptyOrWhitespace(entry.getKey()) && StaticDataRegistry.isNotNullOrEmptyOrWhitespace(entry.getValue()))
                .collect(Collectors.toList());
        keyValueMappingService.updateKeyValues(distinctKeyValuePairs, auth.getCompanyCode());
    }

    private Users createUser(UsersViewModel viewModel, long companyCode) {
        Users entity = new Users();
        CommonUtility.copyNonNullProperties(viewModel, entity);
        entity.setId(viewModel.getId().intValue());
        entity.setUserId(viewModel.getUserId().intValue());
        entity.setCompanyCode(companyCode);
        entity.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        entity.setUserName(viewModel.getEmailId());
        return entity;
    }

    private Users updateUser(UsersViewModel viewModel, Users entity, long companyCode) {
        CommonUtility.copyNonNullProperties(viewModel, entity);
        entity.setUserName(viewModel.getEmailId());
        entity.setId(viewModel.getId().intValue());
        entity.setUserId(viewModel.getUserId().intValue());
        entity.setCompanyCode(companyCode);
        return entity;
    }

    private HashMap<String, Users> getUsersToUpdate(List<UsersViewModel> usersViewModelList, Long companyCode) {

        List<Integer> idList = usersViewModelList.stream()
                .map(e -> e.getId().intValue())
                .collect(Collectors.toList());

        List<Users> updateUsersList = usersRepository.findByCompanyCodeAndIdIn(companyCode, idList);

        HashMap<String, Users> usersToUpdate = new HashMap<>();
        updateUsersList.forEach(e -> {
            usersToUpdate.put(e.getEmailId(), e);
        });

        return usersToUpdate;
    }

}
