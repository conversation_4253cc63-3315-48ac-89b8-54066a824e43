package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.EntitlementDetails;
import in.taxgenie.pay_expense_pvv.repositories.IEntitlementDetailsRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEntitlementDetailsService;
import in.taxgenie.pay_expense_pvv.viewmodels.EntitlementDetailsViewModel;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class EntitlementDetailsServiceImplementation implements IEntitlementDetailsService  {

    private IEntitlementDetailsRepository entitlementDetailsRepository;

    public EntitlementDetailsServiceImplementation(
            IEntitlementDetailsRepository entitlementDetailsRepository) {
                this.entitlementDetailsRepository = entitlementDetailsRepository;
    }

    @Override
    public EntitlementDetailsViewModel create(List<EntitlementDetailsViewModel> viewModel, IAuthContextViewModel auth) {


            viewModel.stream().map(details -> {
                EntitlementDetails entitlementDetails = new EntitlementDetails();
                entitlementDetails.setCompanyCode(auth.getCompanyCode());
                entitlementDetails.setEntitlementHeader(details.getEntitlementHeader());
                entitlementDetails.setJobBand(details.getJobBand());
                entitlementDetails.setEntitlementInformation(details.getEntitlementInformation());
                entitlementDetailsRepository.saveAndFlush(entitlementDetails);
                return null;
            }).collect(Collectors.toList());
            return null;
    }

    @Override
    public List<EntitlementDetailsViewModel> findByCompanyCodeAndJobBand(String jobBand, IAuthContextViewModel auth) {

            return entitlementDetailsRepository.findByCompanyCodeAndJobBand(auth.getCompanyCode(), jobBand)
                    .stream()
                    .map(r-> {
                        EntitlementDetailsViewModel entitlementDetailsViewModel = new EntitlementDetailsViewModel();
                        entitlementDetailsViewModel.setCompanyCode(r.getCompanyCode());
                        entitlementDetailsViewModel.setEntitlementHeader(r.getEntitlementHeader());
                        entitlementDetailsViewModel.setJobBand(r.getJobBand());
                        entitlementDetailsViewModel.setEntitlementInformation(r.getEntitlementInformation());
                        return entitlementDetailsViewModel;
                    }).collect(Collectors.toList());

    }

    @Override
    public void save(List<EntitlementDetailsViewModel> viewModel, IAuthContextViewModel auth) {


        viewModel.stream().map(details -> {
            EntitlementDetails entitlementDetails = entitlementDetailsRepository.findByCompanyCodeAndJobBandAndEntitlementHeader
                            (auth.getCompanyCode(),details.getJobBand(),details.getEntitlementHeader())
                    .orElse(new EntitlementDetails());
            entitlementDetails.setCompanyCode(auth.getCompanyCode());
            entitlementDetails.setEntitlementHeader(details.getEntitlementHeader());
            entitlementDetails.setJobBand(details.getJobBand());
            entitlementDetails.setEntitlementInformation(details.getEntitlementInformation());
            entitlementDetailsRepository.saveAndFlush(entitlementDetails);
            return null;
        }).collect(Collectors.toList());

    }

    private EntitlementDetailsViewModel getViewModel(EntitlementDetails entitlementDetails) {
        EntitlementDetailsViewModel viewModel = new EntitlementDetailsViewModel();
        BeanUtils.copyProperties(entitlementDetails, viewModel);
        return viewModel;
    }
}
