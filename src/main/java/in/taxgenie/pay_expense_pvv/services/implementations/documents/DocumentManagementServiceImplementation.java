package in.taxgenie.pay_expense_pvv.services.implementations.documents;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cloud.interfaces.IGcpCSFileIOProvider;
import in.taxgenie.pay_expense_pvv.entities.DocumentStorage;
import in.taxgenie.pay_expense_pvv.entities.InvoiceHeader;
import in.taxgenie.pay_expense_pvv.entities.LookupData;
import in.taxgenie.pay_expense_pvv.entities.Users;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestHeader;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.invoice.repository.IInvoiceHeaderRepository;
import in.taxgenie.pay_expense_pvv.repositories.ILookupRepository;
import in.taxgenie.pay_expense_pvv.repositories.IUsersRepository;
import in.taxgenie.pay_expense_pvv.repositories.documents.IDocumentStorageRepository;
import in.taxgenie.pay_expense_pvv.repositories.po.IPurchaseOrderHeaderRepository;
import in.taxgenie.pay_expense_pvv.repositories.pr.IPurchaseRequestHeaderRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IFileIOService;
import in.taxgenie.pay_expense_pvv.services.interfaces.documents.IDocumentManagementService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.FileUtils;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericListRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.files.DocumentStorageQueueViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.files.DocumentUploadResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URL;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DocumentManagementServiceImplementation implements IDocumentManagementService {

    private final IUsersRepository usersRepository;

    private final IDocumentStorageRepository documentStorageRepository;
    private final IInvoiceHeaderRepository invoiceHeaderRepository;
    private final ILookupRepository lookupRepository;

    private final IPurchaseRequestHeaderRepository purchaseRequestHeaderRepository;
    private final IPurchaseOrderHeaderRepository purchaseOrderHeaderRepository;


    private final IFileIOService fileIOService;
    private final IGcpCSFileIOProvider gcpCSFileIOProvider;
    private final Logger logger;

    public DocumentManagementServiceImplementation(IUsersRepository usersRepository, IDocumentStorageRepository documentStorageRepository, IInvoiceHeaderRepository invoiceHeaderRepository, ILookupRepository lookupRepository, IPurchaseRequestHeaderRepository purchaseRequestHeaderRepository, IPurchaseOrderHeaderRepository purchaseOrderHeaderRepository, IFileIOService fileIOService, IGcpCSFileIOProvider gcpCSFileIOProvider) {
        this.usersRepository = usersRepository;
        this.documentStorageRepository = documentStorageRepository;
        this.invoiceHeaderRepository = invoiceHeaderRepository;
        this.lookupRepository = lookupRepository;
        this.purchaseRequestHeaderRepository = purchaseRequestHeaderRepository;
        this.purchaseOrderHeaderRepository = purchaseOrderHeaderRepository;
        this.fileIOService = fileIOService;
        this.gcpCSFileIOProvider = gcpCSFileIOProvider;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }


    @Override
    public DocumentUploadResponse uploadDocuments(List<MultipartFile> supportingDocuments, Long entityId, Integer categoryId, IAuthContextViewModel auth) {

        DocumentUploadResponse response = new DocumentUploadResponse();
        List<Long> uploadedDocIds = new ArrayList<>();
        StringBuilder message = new StringBuilder();
        StringBuilder failedDocs = new StringBuilder();

        FileUtils.validateSupportingDocumentFilenames(supportingDocuments);

        // get category
        LookupData documentType = lookupRepository.findById(categoryId)
                .orElseThrow(() -> {
                    logger.error(String.format("create: Lookup table error. Could not find document type with id : %d ", categoryId));
                    return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
                });

        // check category present
        if (documentType.getValue() == null || documentType.getValue().isEmpty()) {
            logger.error("getDocumentCategory: Lookup Value not present for categoryId {}", categoryId);
            throw new DomainInvariantException("getDocumentCategory: Lookup Value not present");
        }

        // validate category against entity
        if (!validateCategory(categoryId, entityId)) {
            logger.error("validateCategory in upload document: category {} mismatch with entity {}", categoryId, entityId);
            throw new DomainInvariantException("category mismatch with entity");
        }

        // get created user
        Users createdBy = usersRepository.findById(auth.getUserId())
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find User with id: %d", auth.getUserId())));

        // upload documents
        logger.info("started doc upload");
        int cnt = 1;
        for (MultipartFile doc : supportingDocuments) {

            try {
//                logger.info("doc "+cnt+" upload started : ");
//                long startTime = System.nanoTime();
                String fileHash = FileUtils.computeFileHash(doc);
                String filename = fileIOService.handleDocumentUpload(doc, auth.getCompanyCode(), documentType.getValue(), fileHash, categoryId, entityId);

//                long endTime = System.nanoTime();
//                long duration = endTime - startTime;
//                logger.info("uploadDocuments: execution time for doc: "+cnt+"in nanosecond is "+duration+"and in milliseconds is "+(duration/1000000));

//                startTime = System.nanoTime();
                DocumentStorage documentStorage = prepareDocumentStorage(doc, createdBy, filename, categoryId, entityId, auth.getUserId(), fileHash);
                logger.info("uploadDocuments: Saving DocumentStorage");
                documentStorageRepository.save(documentStorage);

//                endTime = System.nanoTime();
//                duration = endTime - startTime;
//                logger.info("DocumentStorage: execution time for doc: "+cnt+"in nanosecond is "+duration+"and in milliseconds is "+(duration/1000000));



                uploadedDocIds.add(documentStorage.getId());
                cnt++;
            } catch (IOException e) {
                failedDocs.append(doc.getOriginalFilename()).append(", ");
            }
        }
        if (!failedDocs.isEmpty()) {
            failedDocs.replace(failedDocs.length() - 1, failedDocs.length(), "");
            failedDocs.append("could not be uploaded for pr id : ").append(entityId).append("please try again");
            response.setMessage(message.append(failedDocs).toString());
        }
        response.setUploadedDocIds(uploadedDocIds);
        return response;
    }

    private DocumentStorage prepareDocumentStorage(MultipartFile doc, Users createdBy, String filename, Integer categoryId, Long entityId, Long userId, String fileHash) {
        DocumentStorage documentStorage = new DocumentStorage();
        documentStorage.setName(doc.getOriginalFilename());
        documentStorage.setPath(filename);
        documentStorage.setFileSize(doc.getSize());
        documentStorage.setEntityId(entityId);
        documentStorage.setCategoryId(categoryId);
        documentStorage.setCreatedBy(createdBy);
        documentStorage.setCreatingUserId(userId);
        documentStorage.setCreatedAt(ZonedDateTime.now());
        documentStorage.setContentType(doc.getContentType());
        documentStorage.setSource("UI");
        documentStorage.setFileHash(fileHash);
        return documentStorage;
    }

    private boolean validateCategory(Integer categoryId, Long entityId) {

        switch (categoryId) {
            case 1:
                InvoiceHeader invoiceHeader = getInvoiceHeader(entityId);
                return categoryId.equals(invoiceHeader.getDocument().getDocumentApprovalContainer().getDocumentMetadata().getDocumentCategoryId());
            case 2:
                PurchaseRequestHeader prHeader = getPR(entityId);
                return categoryId.equals(prHeader.getDocument().getDocumentApprovalContainer().getDocumentMetadata().getDocumentCategoryId());
            case 3:
                PurchaseOrderHeader poHeader = getPO(entityId);
                return categoryId.equals(poHeader.getDocument().getDocumentApprovalContainer().getDocumentMetadata().getDocumentCategoryId());
        }
        return false;
    }

    private InvoiceHeader getInvoiceHeader(Long entityId) {
        logger.info(String.format("uploadDocuments: Getting Invoice header by id: {%d}", entityId));
        return invoiceHeaderRepository.findById(entityId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find Invoice header with id: %d", entityId)));
    }

    private PurchaseRequestHeader getPR(Long entityId) {
        logger.info(String.format("uploadDocuments: Getting PR header by id: {%d}", entityId));
        return purchaseRequestHeaderRepository.findById(entityId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find PR with id: %d", entityId)));
    }

    PurchaseOrderHeader getPO(Long entityId) {
        logger.info(String.format("documents: Getting PO header by id: {%d}", entityId));
        return purchaseOrderHeaderRepository.findById(entityId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find PO with id: %d", entityId)));
    }

    public List<DocumentStorageQueueViewModel> getSupportingDocuments(Long documentApprovalContainerId, Integer categoryId) {

        Long entityId = getEntityId(categoryId, documentApprovalContainerId);
        return documentStorageRepository.findByEntityIdAndCategoryId(entityId,
                        categoryId)
                .stream()
                .map(doc -> {
                    DocumentStorageQueueViewModel viewModel = new DocumentStorageQueueViewModel();
                    BeanUtils.copyProperties(doc, viewModel);
                    viewModel.setCreatedBy(doc.getCreatedBy().getFirstName() + " " + doc.getCreatedBy().getLastName());
                    viewModel.setCreatedAt(DateTimeUtils.formatTimestamp(doc.getCreatedAt()));
                    URL downloadUrl = gcpCSFileIOProvider.getSignedUrl(doc.getPath());

                    if (null != downloadUrl)
                        viewModel.setUrl(downloadUrl.toString());

                    return viewModel;
                })
                .collect(Collectors.toList());

    }

    private Long getEntityId(Integer categoryId, Long documentApprovalContainerId) {

        switch (categoryId) {
            case 1:
                logger.info(String.format("uploadDocuments: Getting Invoice header by documentApprovalContainerId: {%d}", documentApprovalContainerId));
                InvoiceHeader invoiceHeader =
                        invoiceHeaderRepository.findByDocumentApprovalContainerId(documentApprovalContainerId)
                                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find Invoice with docContainerId: %d", documentApprovalContainerId)));
                return invoiceHeader.getInvoiceHeaderId();
            case 2:
                logger.info(String.format("uploadDocuments: Getting PR header by documentApprovalContainerId: {%d}", documentApprovalContainerId));
                PurchaseRequestHeader prHeader =
                        purchaseRequestHeaderRepository.findByDocumentApprovalContainerId(documentApprovalContainerId)
                                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find PR with docContainerId: %d", documentApprovalContainerId)));
                return prHeader.getId();
            case 3:
                logger.info(String.format("uploadDocuments: Getting PO header by documentApprovalContainerId: {%d}", documentApprovalContainerId));
                PurchaseOrderHeader poHeader =
                        purchaseOrderHeaderRepository.findByDocumentApprovalContainerId(documentApprovalContainerId)
                                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find PO with docContainer with id: %d", documentApprovalContainerId)));
                return poHeader.getId();
            default: throw new DomainInvariantException(String.format("Unknown category with id %d, Not supported ", categoryId));
        }
    }

    public void performMappingDocumentToSupportingDocs(List<Long> uploadedDocIds, long documentId) {
        if (uploadedDocIds != null && !uploadedDocIds.isEmpty()) {
            documentStorageRepository.updateDocumentIds(documentId, uploadedDocIds);
        }
    }
    @Override
    public void deleteSupportingDocument(Integer categoryId, Long entityId, GenericListRequestViewModel<Long> docIds, IAuthContextViewModel auth) {
        if (null == docIds.getIds()) {
            logger.info("deleteSupportingDocument: No Doc ids in the request");
            throw new DomainInvariantException("deleteSupportingDocument: No Doc ids in the request");
        }
        Optional.of(docIds.getIds()).ifPresent(ids -> {
            ids.forEach(docId -> {
                DocumentStorage doc = documentStorageRepository.findByCategoryIdAndEntityIdAndId(categoryId, entityId, docId).orElseThrow(() -> new RecordNotFoundException(String.format("Could not find document with id: %d", docId)));
                documentStorageRepository.delete(doc);
            });
        });
    }
}
