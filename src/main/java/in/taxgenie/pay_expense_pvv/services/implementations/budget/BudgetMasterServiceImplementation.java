package in.taxgenie.pay_expense_pvv.services.implementations.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Users;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMaster;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetMappingMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetMasterRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetMasterService;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.*;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.MasterResponseViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.dao.DataIntegrityViolationException;
import org.hibernate.exception.ConstraintViolationException;

import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;



import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class BudgetMasterServiceImplementation implements IBudgetMasterService {

    private final IBudgetMasterRepository budgetMasterRepository;
    private final IBudgetMappingMasterRepository budgetMappingMasterRepository;
    private final Logger logger;

    public BudgetMasterServiceImplementation(IBudgetMasterRepository budgetMasterRepository, IBudgetMappingMasterRepository budgetMappingMasterRepository
    ) {
        this.budgetMasterRepository = budgetMasterRepository;
        this.budgetMappingMasterRepository = budgetMappingMasterRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }



    @Override
    public MultiBudgetMasterViewModel getAllBudgetMaster(IAuthContextViewModel auth, Boolean isActive) {

        MultiBudgetMasterViewModel multiBudgetMasterViewModel = new MultiBudgetMasterViewModel();
        List<BudgetMasterViewModel> budgetMasterViewModelList = new ArrayList<>();

        //Get budget master by company code
        List<BudgetMaster> budgetMasterList;
        if(isActive){
            budgetMasterList = budgetMasterRepository.findByCompanyCodeAndIsActive(auth.getCompanyCode(), true, Sort.by(Sort.Direction.ASC, "name"));
        }else{
            budgetMasterList = budgetMasterRepository.findByCompanyCode(auth.getCompanyCode(), Sort.by(Sort.Direction.ASC, "name"));
        }

        budgetMasterList.forEach((budgetMaster -> {
            BudgetMasterViewModel budgetMasterViewModel = budgetMaster.getViewModel();
            Users c = budgetMaster.getCreatingUser();
            budgetMasterViewModelList.add(budgetMasterViewModel);
        }));
        multiBudgetMasterViewModel.setData(budgetMasterViewModelList);

        return multiBudgetMasterViewModel;
    }

    @Override
    public List<MasterResponseViewModel> getAllBudgetMasterV2(IAuthContextViewModel auth, Boolean isActive) {

        List<MasterResponseViewModel> budgetMasterViewModelList = new ArrayList<>();

        //Get budget master by company code
        List<BudgetMaster> budgetMasterList = budgetMasterRepository.findByCompanyCodeAndIsActive(auth.getCompanyCode(), true, Sort.by(Sort.Direction.ASC, "name"));

        budgetMasterList.forEach((budgetMaster -> {
            MasterResponseViewModel budgetMasterViewModel = new MasterResponseViewModel();
            budgetMasterViewModel.setMasterId(budgetMaster.getId());
            budgetMasterViewModel.setName(budgetMaster.getName());
            budgetMasterViewModel.setDisplayName(budgetMaster.getDisplayName() == null ? budgetMaster.getName():budgetMaster.getDisplayName());
            budgetMasterViewModelList.add(budgetMasterViewModel);
        }));

        return budgetMasterViewModelList;
    }

    @Override
    public BudgetMasterViewModel updateBudgetMaster(BudgetMasterUpdateViewModel budgetMasterUpdateViewModel, IAuthContextViewModel auth) {
        BudgetMasterViewModel budgetMasterViewModel = new BudgetMasterViewModel();

        Optional<BudgetMaster> dbBudgetMaster = Optional.ofNullable(budgetMasterRepository.findById(budgetMasterUpdateViewModel.getId())
                .orElseThrow(() -> new RecordNotFoundException(
                        String.format("Could not find Budget Master with id: %d", budgetMasterUpdateViewModel.getId()))));

        if (!budgetMasterUpdateViewModel.isActive()) { // if marking budget master - isActive as false
            boolean exists = budgetMappingMasterRepository.existsByBudgetMasterIdAndCompanyCodeAndIsActive(budgetMasterUpdateViewModel.getId(), auth.getCompanyCode(), true);
            if (exists)
                throw new DomainInvariantException("Master is being used in one or more budgets");
        }

        BudgetMaster budgetMaster = budgetMasterUpdateViewModel.getUpdatedBudgetMaster(dbBudgetMaster.get(),auth);
        BudgetMaster budgetMasterUpdated = budgetMasterRepository.saveAndFlush(budgetMaster);

        budgetMasterViewModel.setBudgetMaster(budgetMasterUpdated);
        return budgetMasterViewModel;
    }

    @Override
    public BudgetMasterViewModel createBudgetMaster(BudgetMasterCreateViewModel budgetMasterCreateViewModel, IAuthContextViewModel auth) {
    	BudgetMasterViewModel budgetMasterViewModel = new BudgetMasterViewModel();

        BudgetMaster budgetMaster = budgetMasterCreateViewModel.getBudgetMaster(auth);

        try{
            BudgetMaster budgetMasterCreated = budgetMasterRepository.saveAndFlush(budgetMaster);
            budgetMasterViewModel.setBudgetMaster(budgetMasterCreated);
        }catch (DataIntegrityViolationException e) {
            // Handle the case where the unique constraint on name and companyCode is violated
            ConstraintViolationException constraintViolationException = (ConstraintViolationException) e.getCause();

            if (constraintViolationException.getMessage().contains("foreign key constraint")) {
                throw new RuntimeException("The Creating User ID is invalid or missing.", e);
            } else if (constraintViolationException.getMessage().contains("ERROR: duplicate key value violates unique constraint")) {
                throw new RuntimeException("A BudgetMaster with the same name already exists for the current company.", e);
            } else {
                throw new RuntimeException("An error occurred while saving the BudgetMaster.", e);
            }
        }


        
        return budgetMasterViewModel;
    }

}
