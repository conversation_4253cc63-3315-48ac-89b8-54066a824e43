package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cem.employee.implementations.CemEmployeeListResponse;
import in.taxgenie.pay_expense_pvv.cem.employee.implementations.CemEmployeeSingularResponse;
import in.taxgenie.pay_expense_pvv.cem.employee.implementations.EmployeeMasterImplementation;
import in.taxgenie.pay_expense_pvv.cem.employee.interfaces.IEmployeeViewModel;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmployeeMasterDataService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IRestConsumer;
import in.taxgenie.pay_expense_pvv.services.interfaces.IRestUpdater;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.EmployeeGlDetailUpdateContainerViewModel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class EmployeeMasterDataServiceImplementation
        implements
        IEmployeeMasterDataService {
    private final IRestConsumer<CemEmployeeSingularResponse> cemEmployeeRestConsumer;
    private final IRestConsumer<CemEmployeeListResponse> cemEmployeeListRestConsumer;
    private final IRestUpdater<EmployeeGlDetailUpdateContainerViewModel> glDetailUpdater;

    public EmployeeMasterDataServiceImplementation(
            IRestConsumer<CemEmployeeSingularResponse> cemEmployeeRestConsumer,
            IRestConsumer<CemEmployeeListResponse> cemEmployeeListRestConsumer,
            IRestUpdater<EmployeeGlDetailUpdateContainerViewModel> glDetailUpdater
    ) {
        this.cemEmployeeRestConsumer = cemEmployeeRestConsumer;
        this.cemEmployeeListRestConsumer = cemEmployeeListRestConsumer;
        this.glDetailUpdater = glDetailUpdater;
    }

    @Value("${CEM_URL}")
    private String cemUrl;

    @Override
    public IEmployeeViewModel getEmployeeMasterData(String email, IAuthContextViewModel auth) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(auth.getToken());

        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                StaticDataRegistry.CEM_SERVICE_EMPLOYEE_FRAGMENT,
                "by-email",
                email
        );

        return cemEmployeeRestConsumer
                .read(
                        HttpMethod.GET,
                        headers,
                        url,
                        CemEmployeeSingularResponse.class
                )
                .getBody();
    }

    @Override
    public IEmployeeViewModel getEmployeeMasterDataByCode(String employeeCode, IAuthContextViewModel auth) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(auth.getToken());

        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                StaticDataRegistry.CEM_SERVICE_EMPLOYEE_FRAGMENT,
                "by-code",
                employeeCode
        );

        return cemEmployeeRestConsumer
                .read(
                        HttpMethod.GET,
                        headers,
                        url,
                        CemEmployeeSingularResponse.class
                )
                .getBody();
    }

    @Override
    public List<IEmployeeViewModel> getAllEmployeeMasterData(IAuthContextViewModel auth) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(auth.getToken());

        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                StaticDataRegistry.CEM_SERVICE_EMPLOYEE_FRAGMENT
        );

        return cemEmployeeListRestConsumer
                .read(
                        HttpMethod.GET,
                        headers,
                        url,
                        CemEmployeeListResponse.class
                )
                .getBody()
                .stream()
                .map(i -> {
                    IEmployeeViewModel viewModel = new EmployeeMasterImplementation();
                    BeanUtils.copyProperties(i, viewModel);
                    return viewModel;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<IEmployeeViewModel> getAllApproversMasterData(IAuthContextViewModel auth) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(auth.getToken());

        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                StaticDataRegistry.CEM_SERVICE_EMPLOYEE_FRAGMENT,
                "approvers"
        );

        return cemEmployeeListRestConsumer
                .read(
                        HttpMethod.GET,
                        headers,
                        url,
                        CemEmployeeListResponse.class
                )
                .getBody()
                .stream()
                .map(i -> {
                    IEmployeeViewModel viewModel = new EmployeeMasterImplementation();
                    BeanUtils.copyProperties(i, viewModel);
                    return viewModel;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void updateEmployeeGlDetails(EmployeeGlDetailUpdateContainerViewModel viewModel, IAuthContextViewModel auth) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(auth.getToken());

        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                StaticDataRegistry.CEM_SERVICE_EMPLOYEE_FRAGMENT,
                "update",
                "gl-details"
        );

        glDetailUpdater.update(headers, url, EmployeeGlDetailUpdateContainerViewModel.class, viewModel);
    }
}
