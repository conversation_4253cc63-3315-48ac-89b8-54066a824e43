package in.taxgenie.pay_expense_pvv.services.implementations.company;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.company.PanDetail;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.company.IPanDetailRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.company.IPanDetailService;
import in.taxgenie.pay_expense_pvv.utils.CommonUtility;
import in.taxgenie.pay_expense_pvv.utils.StringUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.company.PanDetailViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.Optional;

@Service
@Transactional
public class PanDetailServiceImplementation implements IPanDetailService {
    private final IPanDetailRepository repository;
    private final Logger logger;

    public PanDetailServiceImplementation(IPanDetailRepository repository) {
        this.repository = repository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public PanDetailViewModel getPanDetails(IAuthContextViewModel auth) {
        if (auth == null) {
            logger.error("getAllGstinDetails: Auth context");
            throw new IllegalArgumentException("Auth context cannot be null");
        }

        logger.info("getPanDetail: Entered for company ID {}", auth.getCompanyCode());

        try {
            PanDetail entity = repository.findByCompanyCode(auth.getCompanyCode())
                    .orElseThrow(() -> new RecordNotFoundException(
                            String.format("PAN details could not be found for company ID %s", auth.getCompanyCode())
                    ));

            logger.info("getPanDetail: Returning PAN details for company ID {}", auth.getCompanyCode());

            return PanDetail.getViewModel(entity); // Assuming getViewModel is an instance method
        } catch (Exception e) {
            logger.error("getPanDetail: An error occurred while retrieving PAN details for company ID {}", auth.getCompanyCode(), e);
            throw new DomainInvariantException("Failed to retrieve PAN details"); // Use a custom exception
        }
    }

    @Override
    public PanDetailViewModel createOrUpdatePanDetails(PanDetailViewModel panDetailViewModel, IAuthContextViewModel auth) {

        PanDetail entity = null;
        String panNumber = null;
        Optional<PanDetail> panDetailOptional = repository.findByCompanyCode(auth.getCompanyCode());
        if(panDetailOptional.isPresent()){
            entity = panDetailOptional.get();
            panNumber = entity.getPanNumber();
            entity.setUpdatingUserId(auth.getUserId());
            entity.setUpdatedTimestamp(ZonedDateTime.now());
        }else {
            entity = new PanDetail();
            if(StringUtils.isNullOrEmpty(panDetailViewModel.getPanNumber())){
                throw new IllegalArgumentException("PAN Number cannot be empty!");
            }
            if(StringUtils.isNullOrEmpty(panDetailViewModel.getCompanyName())){
                throw new IllegalArgumentException("Company Name cannot be empty!");
            }
            panNumber = panDetailViewModel.getPanNumber();
            entity.setCreatingUserId(auth.getUserId());
            entity.setCreatedTimestamp(ZonedDateTime.now());
        }
        CommonUtility.copyNonNullProperties(panDetailViewModel, entity);
        //pan Number cannot be updated
        entity.setPanNumber(panNumber);
        entity.setCompanyCode(auth.getCompanyCode());

        PanDetail savedPanDetail = repository.saveAndFlush(entity);
        return PanDetail.getViewModel(savedPanDetail);
    }
}
