package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.services.interfaces.IEnvironmentDataProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class EnvironmentDataProviderImplementation implements IEnvironmentDataProvider {
    @Value("${PAY_EXPENSE_CUSTOMER_ID}")
    private String customerId;

    @Value("${COMMUNICATION_ENGINE_URL}")
    private String communicationEngineUrl;

    @Value("${PAY_EXPENSE_PRODUCT_ID}")
    private String productId;

    @Value("${SENDBACK_EMAIL_TEMPLATE_ID}")
    private String sendBackTemplateId;

    @Value("${SENDBACK_REMINDER_EMAIL_TEMPLATE_ID}")
    private String sendBackReminderTemplateId;

    @Value("${APPROVAL_REMINDER_EMAIL_TEMPLATE_ID}")
    private String approvalReminderTemplateId;

    @Value("${SUBMIT_TO_CREATOR_EMAIL_TEMPLATE_ID}")
    private String submitToCreatorTemplateId;

    @Value("${SUBMIT_TO_APPROVER_EMAIL_TEMPLATE_ID}")
    private String submitToApproverTemplateId;

    @Value("${APPROVED_TO_CREATOR_EMAIL_TEMPLATE_ID}")
    private String approvedToCreatorTemplateId;

    @Value("${APPROVED_TO_NEXT_APPROVER_EMAIL_TEMPLATE_ID}")
    private String approvedToNextApproverTemplateId;

    @Value("${POSTED_EMAIL_TEMPLATE_ID}")
    private String postedTemplateId;

    @Value("${PAID_EMAIL_TEMPLATE_ID}")
    private String paidTemplateId;

    @Value("${NEW_USER_WELCOME_EMAIL_TEMPLATE_ID}")
    private String newUserWelcomeTemplateId;

    @Value("${PAY_EXPENSE_SENDER_EMAIL}")
    private String senderEmail;

    @Override
    public String getCustomerId() {
        return customerId;
    }

    @Override
    public String getCommunicationEngineUrl() {
        return communicationEngineUrl;
    }

    @Override
    public String getProductId() {
        return productId;
    }

    @Override
    public String getSendBackTemplateId() {
        return sendBackTemplateId;
    }

    @Override
    public String getSubmitToCreatorTemplateId() {
        return submitToCreatorTemplateId;
    }

    @Override
    public String getSubmitToApproverTemplateId() {
        return submitToApproverTemplateId;
    }

    @Override
    public String getApprovedToCreatorTemplateId() {
        return approvedToCreatorTemplateId;
    }

    @Override
    public String getApprovedToNextApproverTemplateId() {
        return approvedToNextApproverTemplateId;
    }

    @Override
    public String getPostedTemplateId() {
        return postedTemplateId;
    }

    @Override
    public String getPaidTemplateId() {
        return paidTemplateId;
    }

    @Override
    public String getSenderEmail() {
        return senderEmail;
    }

    @Override
    public String getSendBackReminderTemplateId() {
        return sendBackReminderTemplateId;
    }

    @Override
    public String getApprovalReminderTemplateId() {
        return approvalReminderTemplateId;
    }

    @Override
    public String getNewUserWelcomeTemplateId() {
        return newUserWelcomeTemplateId;
    }
}
