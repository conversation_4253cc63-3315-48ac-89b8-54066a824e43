package in.taxgenie.pay_expense_pvv.services.implementations.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.budget.*;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.*;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetFrequencyMappingService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetMappingMasterService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetStepperService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.budget.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetMasterTreeDataViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetMasterTreeViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.mappingmaster.BudgetMappingCreateViewModelWrapper;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.mappingmaster.BudgetMappingMasterCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.mappingmaster.BudgetMappingMasterViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.*;

@Service
public class BudgetMappingMasterServiceImplementation implements IBudgetMappingMasterService {
    private final IBudgetMappingMasterRepository repository;
    private final IBudgetMasterRepository budgetMasterRepository;
    private final IBudgetStructureMasterRepository budgetStructureMasterRepository;
    private final IBudgetRepository budgetRepository;
    private final IDocumentApprovalContainerRepository documentApprovalContainerRepository;
    private final IBudgetFrequencyMappingRepository budgetFrequencyMappingRepository;



    // Services
    private final IBudgetFrequencyMappingService budgetFrequencyMappingService;
    private final IBudgetService budgetService;
    private final IBudgetStepperService budgetStepperService;
    private final Logger logger;

    public BudgetMappingMasterServiceImplementation(IBudgetMappingMasterRepository repository,
                                                    IBudgetMasterRepository budgetMasterRepository,
                                                    IBudgetStructureMasterRepository budgetStructureMasterRepository,
                                                    IBudgetRepository budgetRepository, IDocumentApprovalContainerRepository documentApprovalContainerRepository,
                                                    IBudgetFrequencyMappingRepository budgetFrequencyMappingRepository,
                                                    IBudgetFrequencyMappingService budgetFrequencyMappingService,
                                                    IBudgetService budgetService,
                                                    IBudgetStepperService budgetStepperService) {
        this.repository = repository;
        this.budgetMasterRepository = budgetMasterRepository;
        this.budgetStructureMasterRepository = budgetStructureMasterRepository;
        this.budgetRepository = budgetRepository;
        this.documentApprovalContainerRepository = documentApprovalContainerRepository;
        this.budgetService = budgetService;
        this.budgetFrequencyMappingRepository = budgetFrequencyMappingRepository;
        this.budgetFrequencyMappingService = budgetFrequencyMappingService;
        this.budgetStepperService = budgetStepperService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public BudgetMasterTreeViewModel getTree(long structureId, IAuthContextViewModel auth) {
        List<BudgetMappingMaster> availableRoots =  repository.findRootNodeForStructure(auth.getCompanyCode(), structureId);

        // Sanity Checks
        if (availableRoots.size() > 1) {
            throw new DomainInvariantException(String.format("Multiple org trees found for structure id: %d", structureId));
        }

        // If no root found, then not yet created so return empty object.
        if (availableRoots.isEmpty()) {
            logger.info(String.format("getBudgetMappingMaster: Cannot find the root for the budget mapping master with id: %d for company: %s. This may be a new mapping init", structureId, auth.getCompanyCode()));
            return null;
        }

        BudgetMappingMaster root = availableRoots.get(0);
        return getViewModel(root);
    }

    @Override
    @Transactional
    public BudgetMasterTreeViewModel create(long structureId,
                                            BudgetMappingCreateViewModelWrapper budgetMappingCreateViewModelWrapper,
                                            Boolean resetStages, IAuthContextViewModel auth) {

        if (Boolean.TRUE.equals(resetStages)) {
            budgetStepperService.initiateReset(StaticDataRegistry.SCREEN_TWO, structureId, auth);
        }
        budgetStepperService.setLockScrren(resetStages, StaticDataRegistry.SCREEN_TWO, structureId, auth);

        // Initialise the values
        Map<Long, BudgetMaster> budgetMasterMap = generateBudgetMappingMasterMap(auth.getCompanyCode(), structureId);


        BudgetStructureMaster budgetStructureMaster = budgetStructureMasterRepository
                .findByCompanyCodeAndIdAndIsActive(auth.getCompanyCode(), structureId, true)
                .orElseThrow(() -> {
                    logger.info("createBudgetMappingMaster: The provided budget structure id: {} does not exist for company: {}", structureId, auth.getCompanyCode());
                    return new DomainInvariantException(StaticDataRegistry.BUDGET_MAPPING_CREATION_ERROR);
                });

        BudgetMappingMaster budgetMappingMaster = createBudgetMappingMasterTree(budgetMappingCreateViewModelWrapper, null, structureId, budgetStructureMaster, auth, budgetMasterMap, StaticDataRegistry.BUDGET_MAPPING_MASTER_STARTING_LEVEL, new HashSet<>());

        budgetStructureMaster.setUpdatedTimestamp(DateTimeUtils.getCurrentZonedDateTime());
        updateDocumentApprovalContainerTimestampByStructureMaster(budgetStructureMaster.getId(), budgetMappingMaster.getUpdatedTimestamp());

        repository.saveAndFlush(budgetMappingMaster);
        return getViewModel(budgetMappingMaster);
    }

    @Transactional
    public void updateDocumentApprovalContainerTimestampByStructureMaster(Long structureMasterId, ZonedDateTime updatedTimestamp) {
        documentApprovalContainerRepository.updateUpdatedTimestampByStructureMasterId(updatedTimestamp, structureMasterId);
    }

    @Override
    public List<BudgetMappingMasterViewModel> getChildren(long structureId, Long parentId, IAuthContextViewModel auth) {
        List<BudgetMappingMaster> nodes;
        if (parentId != null) {
            nodes = repository.findActiveBudgetMappingChildNodesForStructure(
                    auth.getCompanyCode(), structureId, parentId);
        } else {
            // If your looking for a node with no parent then it is the root structure.
            nodes = repository.findRootNodeForStructure(auth.getCompanyCode(), structureId);
        }
        return nodes
                .stream()
                .map(entity -> new BudgetMappingMasterViewModel(entity.getBudgetStructureMasterId(), entity.getBudgetMasterId(), entity.getDisplayName(),
                        entity.getId(), entity.getCompanyCode(), entity.getLevel()))
                .toList();
    }

    @Override
    public BudgetMappingMasterViewModel getRoot(long structureId, IAuthContextViewModel auth) {
        List<BudgetMappingMaster> availableRoots = repository.findRootNodeForStructure(auth.getCompanyCode(), structureId);
        if (availableRoots.size() > 1) {
            throw new DomainInvariantException(String.format("Multiple org trees found for structure id: %d", structureId));
        }
        // If no root found, then not yet created so return empty object.
        if (availableRoots.isEmpty()) {
            logger.info(String.format("getBudgetMappingMaster: Cannot find the root for the budget mapping master with id: %d for company: %s. This may be a new mapping init", structureId, auth.getCompanyCode()));
            return null;
        }
        BudgetMappingMaster entity = availableRoots.get(0);
        return new BudgetMappingMasterViewModel(entity.getBudgetStructureMasterId(), entity.getBudgetMasterId(), entity.getDisplayName(),
                entity.getId(), entity.getCompanyCode(), entity.getLevel());
    }

    // HELPER FUNCTIONS --- START ---

    private Map<Long, BudgetMaster> generateBudgetMappingMasterMap(long companyCode, long structureId) {
        Map<Long, BudgetMaster> budgetMasterMap = new HashMap<>();
        budgetMasterRepository.findByCompanyCode(companyCode)
                .forEach(i -> budgetMasterMap.put(i.getId(), i));
        return budgetMasterMap;
    }

    private BudgetMappingMaster createBudgetMappingMasterTree(BudgetMappingCreateViewModelWrapper node,
                                                              BudgetMappingMaster parent,
                                                              long structureId, BudgetStructureMaster structureMaster,
                                                              IAuthContextViewModel auth, Map<Long, BudgetMaster> budgetMasterMap,
                                                              int level, Set<Long> parentMasters) {
        if (node == null) {
            return null;
        }

        BudgetMappingMaster mappingMaster = createNewMappingMaster(node.getData(), parent, budgetMasterMap, auth, level, structureId, structureMaster);
        Long budgetMasterId = mappingMaster.getBudgetMasterId();

        if (parentMasters.contains(budgetMasterId)) {
            logger.info("createBudgetMappingMasters: The child node {} is already in a parent of the current node", node);
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_MAPPING_CREATION_ERROR);
        }

        parentMasters.add(budgetMasterId);

        // Increment the level for the all children below
        level++;
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (BudgetMappingCreateViewModelWrapper child : node.getChildren()) {
                BudgetMappingMaster childNode = createBudgetMappingMasterTree(child, mappingMaster, structureId, structureMaster, auth, budgetMasterMap, level, parentMasters);
                mappingMaster.addChildren(childNode);
            }
        }

        parentMasters.remove(budgetMasterId);
        return mappingMaster;
    }

    private BudgetMappingMaster createNewMappingMaster(BudgetMappingMasterCreateViewModel viewModel,
                                                       BudgetMappingMaster parent,
                                                       Map<Long, BudgetMaster> budgetMasterMap, IAuthContextViewModel auth,
                                                       int level, long structureId, BudgetStructureMaster structureMaster) {
        BudgetMappingMaster mappingMaster = new BudgetMappingMaster();

        // PreWork
        BudgetMaster budgetMaster = budgetMasterMap.get(viewModel.getBudgetMasterId());
        if (budgetMaster == null || !budgetMasterMap.containsKey(viewModel.getBudgetMasterId())) {
            logger.info("populateBudgetMappingMasters: The provided budget master mapping id: {} does not exists for company: {}", viewModel.getBudgetMasterId(), auth.getCompanyCode());
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_MAPPING_CREATION_ERROR);
        }

        // Set base attributes
        mappingMaster.setId(viewModel.getId());
        mappingMaster.setCompanyCode(auth.getCompanyCode());
        mappingMaster.setCreatingUserId(auth.getUserId());
        mappingMaster.setActive(true);
        mappingMaster.setUuid(UUID.randomUUID());

        // Set Budget Mapping details
        mappingMaster.setDisplayName(viewModel.getDisplayName() == null ? budgetMaster.getName() : viewModel.getDisplayName());
        mappingMaster.setLevel(level);

        // Set Budget Structure mapping
        mappingMaster.setBudgetStructureMaster(structureMaster);
        mappingMaster.setBudgetStructureMasterId(structureId);

        // Set Budget Master mapping
        mappingMaster.setBudgetMaster(budgetMaster);
        mappingMaster.setBudgetMasterId(viewModel.getBudgetMasterId());

        // Set parent
        mappingMaster.setParent(parent);

        return mappingMaster;
    }



    private BudgetMasterTreeViewModel getViewModel(BudgetMappingMaster node) {
        if (node == null) {
            return null;
        }
        BudgetMasterTreeViewModel viewModel = setBudgetMappingMasterDataViewModel(node);

        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (BudgetMappingMaster child : node.getChildren()) {
                viewModel.addChildren(getViewModel(child));
            }
        }

        return viewModel;
    }

    private BudgetMasterTreeViewModel setBudgetMappingMasterDataViewModel(BudgetMappingMaster node) {
        BudgetMasterTreeViewModel viewModel = new BudgetMasterTreeViewModel();
        BudgetMasterTreeDataViewModel dataViewModel = new BudgetMasterTreeDataViewModel();

        // Set Data model with currentId and parentId if present;
        dataViewModel.setDisplayName(node.getDisplayName());
        dataViewModel.setBudgetMasterId(node.getBudgetMasterId());
        dataViewModel.setId(node.getId());

        BudgetMappingMaster parent = node.getParent();
        dataViewModel.setParentId(parent == null ? null : parent.getId());


        viewModel.setExpanded(true);
        viewModel.setData(dataViewModel);


        return viewModel;

    }

    // HELPER FUNCTIONS --- END ---

}
