package in.taxgenie.pay_expense_pvv.services.implementations.po;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.erp.POGrnMapping;
import in.taxgenie.pay_expense_pvv.entities.gl_master.GlMaster;
import in.taxgenie.pay_expense_pvv.entities.masters.SegmentMaster;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderItem;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestItem;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.invoice.mapper.InvoiceHeaderMapper;
import in.taxgenie.pay_expense_pvv.invoice.message.CompanyDetails;
import in.taxgenie.pay_expense_pvv.invoice.message.ShipToDetails;
import in.taxgenie.pay_expense_pvv.invoice.repository.IStateCodeRepo;
import in.taxgenie.pay_expense_pvv.repositories.*;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetRepository;
import in.taxgenie.pay_expense_pvv.repositories.erp.IPOGrnMappingRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.masters.ISegmentMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.po.IPurchaseOrderHeaderRepository;
import in.taxgenie.pay_expense_pvv.repositories.po.IPurchaseOrderItemRepository;
import in.taxgenie.pay_expense_pvv.repositories.pr.IPurchaseRequestItemRepository;
import in.taxgenie.pay_expense_pvv.services.implementations.budget.BudgetServiceImplementation;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentApprovalContainerUserService;
import in.taxgenie.pay_expense_pvv.services.interfaces.documents.IDocumentManagementService;
import in.taxgenie.pay_expense_pvv.services.interfaces.dynamic.form.IDynamicFormService;
import in.taxgenie.pay_expense_pvv.services.interfaces.masters.IRatioCategoryMasterService;
import in.taxgenie.pay_expense_pvv.services.interfaces.po.IPurchaseOrderService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentCreateResponse;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericListRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetDetails;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetSelectionViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.erp.ERPGrnViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SegmentRatioToEntityRequest;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderDetailsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderItemDetailsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.SegmentsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Transactional
public class PurchaseOrderServiceImplementation implements IPurchaseOrderService {

    private final IDocumentApprovalContainerRepository reportRepository;
    private final IDocumentSubgroupRepository subgroupRepository;
    private final ILookupRepository lookupRepository;
    private final IStateCodeRepo stateCodeRepository;
    private final IDocumentRepository documentRepository;
    private final ICompanyRepository companyRepository;
    private final IGlMasterRepository glMasterRepository;
    private final ISegmentMasterRepository segmentMasterRepository;
    private final IDocumentApprovalContainerUserService documentApprovalContainerUserService;
    private final BudgetServiceImplementation budgetServiceImplementation;
    private final IDocumentManagementService documentManagementService;
    private final Logger logger;
    private final IPurchaseOrderHeaderRepository purchaseOrderHeaderRepository;
    private final IPurchaseOrderItemRepository purchaseOrderItemRepository;
    private final IPurchaseRequestItemRepository purchaseRequestItemRepository;
    private final IItemMasterRepository itemMasterRepository;
    private final IBudgetRepository budgetRepository;
    private final CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository;
    private final IPOGrnMappingRepository grnMappingRepository;
    private final IDynamicFormService dynamicFormService;
    private final IRatioCategoryMasterService ratioCategoryMasterService;

    public PurchaseOrderServiceImplementation(IDocumentApprovalContainerRepository reportRepository, IDocumentRuleRepository ruleRepository, IDocumentSubgroupRepository subgroupRepository,
                                              ILookupRepository lookupRepository, IStateCodeRepo stateCodeRepository, IDocumentRepository expenseRepository, ICompanyRepository companyRepository, IGlMasterRepository glMasterRepository, ISegmentMasterRepository segmentMasterRepository,
                                              @Lazy IDocumentApprovalContainerUserService documentApprovalContainerUserService, BudgetServiceImplementation budgetServiceImplementation,
                                              IDocumentManagementService documentManagementService, IPurchaseOrderHeaderRepository purchaseOrderHeaderRepository, IPurchaseOrderItemRepository purchaseOrderItemRepository, IPurchaseRequestItemRepository purchaseRequestItemRepository, IItemMasterRepository itemMasterRepository, IBudgetRepository budgetRepository,
                                              CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository, IPOGrnMappingRepository grnMappingRepository, IDynamicFormService dynamicFormService, IRatioCategoryMasterService ratioCategoryMasterService) {
        this.reportRepository = reportRepository;
        this.subgroupRepository = subgroupRepository;
        this.lookupRepository = lookupRepository;
        this.stateCodeRepository = stateCodeRepository;
        this.documentRepository = expenseRepository;
        this.companyRepository = companyRepository;
        this.glMasterRepository = glMasterRepository;
        this.segmentMasterRepository = segmentMasterRepository;
        this.documentApprovalContainerUserService = documentApprovalContainerUserService;
        this.budgetServiceImplementation = budgetServiceImplementation;
        this.documentManagementService = documentManagementService;
        this.purchaseOrderHeaderRepository = purchaseOrderHeaderRepository;
        this.purchaseOrderItemRepository = purchaseOrderItemRepository;
        this.purchaseRequestItemRepository = purchaseRequestItemRepository;
        this.itemMasterRepository = itemMasterRepository;
        this.budgetRepository = budgetRepository;
        this.customDocumentApprovalContainerRepository = customDocumentApprovalContainerRepository;
        this.grnMappingRepository = grnMappingRepository;
        this.dynamicFormService = dynamicFormService;
        this.ratioCategoryMasterService = ratioCategoryMasterService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Transactional
    public DocumentCreateResponse create(Long metadataId, Long subgroupId, Integer poTypeId, IAuthContextViewModel auth) {
        DocumentCreateResponse response = new DocumentCreateResponse();

        long documentApprovalContainerId = documentApprovalContainerUserService.create(metadataId, auth).getId();
        DocumentApprovalContainer documentApprovalContainer = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), documentApprovalContainerId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find documentApprovalContainer with id: %d", metadataId)));

        if (!isParentExpenseReportSubmittable(documentApprovalContainer)) {
            throw new DomainInvariantException("Parent expense documentApprovalContainer is neither in draft or sent-back state; hence not submittable");
        }
        DocumentSubgroup subgroup = subgroupRepository.findByCompanyCodeAndId(auth.getCompanyCode(), subgroupId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find subgroup with id: %d", subgroupId)));

        logger.info("create: Creating a new Purchase Request entity");

        Document document = new Document();

        createDocument(document, documentApprovalContainer, subgroup, auth);

        logger.info("create: Saving both Document and Document Approval Container");
        documentRepository.saveAndFlush(document);
        reportRepository.saveAndFlush(documentApprovalContainer);

        PurchaseOrderHeader poHeader = createPOHeader(poTypeId, document, auth);
        poHeader.setHasPrecedingDocument(subgroup.getHasPrecedingDocument());
        poHeader.setOrderType(subgroup.getHasPrecedingDocument() ? "PR based" : "Non PR based");

        subgroupRepository.saveAndFlush(subgroup);

        logger.info("create: Save successful");
        purchaseOrderHeaderRepository.saveAndFlush(poHeader);

        response.setContainerId(documentApprovalContainer.getId());
        return response;
    }

    @Override
    @Transactional
    public PurchaseOrderItemDetailsViewModel addItem(Long poId, AddNewLineItemViewModel viewModel, IAuthContextViewModel auth) {
        PurchaseOrderHeader purchaseOrderHeader = getPurchaseOrderHeader(poId, auth);
        PurchaseOrderItem purchaseOrderItem = new PurchaseOrderItem();

        // Sanity Check. Make sure the item that is being added belongs to the same vendor as the PO
        ItemMaster item = getItemMaster(viewModel.getItemMasterId(), (int) auth.getCompanyCode(), purchaseOrderHeader.getVendorId());
        purchaseOrderItem.setType(item.getType());
        purchaseOrderItem.setQuantity(viewModel.getQuantity());

        if (null != viewModel.getPurchaseRequestItemId()) {
            PurchaseRequestItem purchaseRequestItem = getPurchaseRequestItem(viewModel.getPurchaseRequestItemId(), auth.getCompanyCode());

            addPurchaseOrderItemFromPurchaseRequestItem(purchaseOrderItem, purchaseRequestItem, viewModel.getQuantity(), auth.getCompanyCode());
        } else {
            purchaseOrderItem.setTotal(item.getRate().multiply(BigDecimal.valueOf(purchaseOrderItem.getQuantity())));
        }

        // Set the bidirectional mapping
        purchaseOrderHeader.addItem(purchaseOrderItem);
        purchaseOrderHeaderRepository.saveAndFlush(purchaseOrderHeader);

        purchaseOrderItem.setPurchaseOrderHeader(purchaseOrderHeader);
        purchaseOrderItem.setPurchaseOrderHeaderId(purchaseOrderHeader.getId());
        purchaseOrderItem = purchaseOrderItemRepository.saveAndFlush(purchaseOrderItem);

        return PurchaseOrderItem.getViewModel(purchaseOrderItem);
    }

    private PurchaseRequestItem getPurchaseRequestItem(Long purchaseRequestItemId, long companyCode) {
        return purchaseRequestItemRepository.findById(purchaseRequestItemId)
                .orElseThrow(() -> {
                    logger.info("addItem: Could not find PurchaseRequestItem with id: {} for company id: {}", purchaseRequestItemId, companyCode);
                    return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
                });
    }

    private ItemMaster getItemMaster(Long itemMasterId, int companyCode, Integer vendorId) {
        return itemMasterRepository.findByIdAndCompanyCode(itemMasterId, companyCode) // Todo: Convert CompanyId in itemMaster table to long
                .orElseThrow(() -> {
                    logger.info("addItem: Could not find item with id: {} with vendor id: {} for company id: {}", itemMasterId, vendorId, companyCode);
                    return new DomainInvariantException(StaticDataRegistry.PO_ITEM_CANNOT_BE_ADDED);
                });
    }

    private PurchaseOrderHeader getPurchaseOrderHeader(Long poId, IAuthContextViewModel auth) {
        return purchaseOrderHeaderRepository.findByIdAndCompanyCode(poId, auth.getCompanyCode())
                .orElseThrow(() -> {
                    logger.info("Could not find purchase order header with id: {} for company id: {}", poId, auth.getCompanyCode());
                    return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_PO_NOT_FOUND);
                });
    }

    private void addPurchaseOrderItemFromPurchaseRequestItem(PurchaseOrderItem purchaseOrderItem, PurchaseRequestItem purchaseRequestItem, Double quantity, long companyCode) {
        purchaseOrderItem.setPurchaseRequestItemId(purchaseRequestItem.getId());
        purchaseOrderItem.setPurchaseRequestItem(purchaseRequestItem);
        purchaseOrderItem.setTotal(purchaseRequestItem.getUnitRate().multiply(checkQuantityAndAdd(purchaseRequestItem, quantity, companyCode)));
    }

    private BigDecimal checkQuantityAndAdd(PurchaseRequestItem purchaseRequestItem, Double quantity, long companyCode) {
        double availableItemCount = purchaseRequestItem.getQuantity() -
                customDocumentApprovalContainerRepository.getConsumedPRItemCount(purchaseRequestItem.getId(), companyCode);

        if (availableItemCount <= quantity) {
            logger.info("checkQuantityAndAdd: The available count for PRItem with id:{} is less then the requested quantity: {}", purchaseRequestItem.getId(), quantity);
            throw new DomainInvariantException(StaticDataRegistry.PR_ITEM_AVAILABLE_LESS_THEN_REQUESTED);
        }

        return BigDecimal.valueOf(quantity);
    }

    private void createDocument(Document document, DocumentApprovalContainer documentApprovalContainer, DocumentSubgroup subgroup, IAuthContextViewModel auth) {

        document.setDocumentApprovalContainer(documentApprovalContainer);
        // Bi-directional mapping
        documentApprovalContainer.getDocuments().add(document);
        subgroup.getDocuments().add(document);

        document.setClaimAmount(new BigDecimal("0.0"));
        document.setDocumentApprovalContainerId(documentApprovalContainer.getId());
        document.setDocumentSubgroup(subgroup);
        document.setDocumentSubgroupId(subgroup.getId());
        document.setFrequency(subgroup.getFrequency());

        logger.info("create: Finding the applicable Expense Rule");
        DocumentRule applicableRule = getApplicableRule(document);

        logger.info("create: Attaching the applicable Expense Rule as id");
        document.setDocumentRuleId(applicableRule.getId());

        document.setCreatingUserId(auth.getUserId());
        document.setCompanyCode(auth.getCompanyCode());
        document.setEmployeeEmail(auth.getUserEmail());
        document.setStartDate(LocalDate.now());
        document.setEndDate(LocalDate.now());
        document.setCreatedTimestamp(ZonedDateTime.now());
    }

    private DocumentRule getApplicableRule(Document document) {
        logger.trace("getApplicableRule: Finding the Expense with id: {}", document.getId());

        DocumentSubgroup subgroup = document.getDocumentSubgroup();

        Optional<DocumentRule> rule = subgroup.getRules().stream().filter(r -> !r.isFrozen()).filter(r -> isRuleMatch(r, document)).findFirst();

        return rule.orElseThrow(() -> new RecordNotFoundException("Could not find applicable rule for expense"));
    }

    private boolean isRuleMatch(DocumentRule r, Document document) {
        return true;
    }

    public PurchaseOrderHeader createPOHeader(Integer poTyeId, Document document, IAuthContextViewModel auth) {
        PurchaseOrderHeader poHeader = new PurchaseOrderHeader();
        poHeader.setCreatingUserId(auth.getUserId());
        poHeader.setCreatedAt(LocalDateTime.now());
        poHeader.setCurrency("INR"); // TODO : change this later when set from UI

        LookupData docType = getLookupData(StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_PO);
        poHeader.setDocType(docType);

        // set po status - open default
        LookupData poStatus = getLookupData(StaticDataRegistry.LOOKUP_PO_STATUS, StaticDataRegistry.LOOKUP_PO_STATUS_OPEN);
        poHeader.setPoStatus(poStatus);

        // create order no
        /* -- commenting this code as since PO sync - po number will come from ERP in reverse feed
        Long lasRecordId = purchaseOrderHeaderRepository.findByLastPOId();
        document.setDocNo(StringConstants.createOrderNo(lasRecordId));
         */
        document.setDocumentDate(LocalDate.now());
        poHeader.setDocument(document);
        return poHeader;
    }

    private LookupData getLookupData(String lookupType, String lookupValue) {
        return lookupRepository.findByTypeAndValue(lookupType, lookupValue)
                .orElseThrow(() -> {
                            logger.error(String.format("createPOHeader: Could not find lookup type: %s and value: %s", lookupType, lookupValue));
                            return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
                        }
                );
    }

    private boolean isParentExpenseReportSubmittable(DocumentApprovalContainer report) {
        return report.getReportStatus() == ReportStatus.DRAFT || report.getReportStatus() == ReportStatus.SENT_BACK;

    }

    @Transactional
    @Override
    public DocumentCreateResponse save(PurchaseOrderCreateViewModel poCreateViewModel, IAuthContextViewModel auth) {
        DocumentCreateResponse response = new DocumentCreateResponse();

        PurchaseOrderHeader poHeader = getPurchaseOrderHeader(poCreateViewModel.getPoDetails().getId(), auth);

        logger.info(String.format("save: Getting document and document header for PO header id: {%d}", poCreateViewModel.getPoDetails().getId()));
        Document document = poHeader.getDocument();
        saveDocument(document, poCreateViewModel, auth);

        logger.info(String.format("save: Preparing the documentApprovalContainer with id: {%d} from the invoice header id: {%d}", document.getDocumentApprovalContainerId(), poCreateViewModel.getPoDetails().getId()));
        DocumentApprovalContainer documentApprovalContainer = document.getDocumentApprovalContainer();
        saveDocumentApprovalContainer(documentApprovalContainer, poCreateViewModel);

        logger.info(String.format("preparePOHeader: saving purchaser order and items PO header id: {%d}", poCreateViewModel.getPoDetails().getId()));
        preparePOHeader(poHeader, document, poCreateViewModel, auth);
        poHeader.setDynamicFieldsJson(poCreateViewModel.getDynamicFieldsJson());
        logger.info("Mapping supporting docs to Document if any");
        documentManagementService.performMappingDocumentToSupportingDocs(poCreateViewModel.getUploadedDocIds(), document.getId());
        documentApprovalContainer.setApprovalContainerClaimAmount(document.getClaimAmount());
        logger.info("create: Saving both Document and Document Approval Container");
        documentRepository.saveAndFlush(document);
        reportRepository.saveAndFlush(documentApprovalContainer);

        logger.info("create: Save successful");
        purchaseOrderHeaderRepository.saveAndFlush(poHeader);

        response.setContainerId(documentApprovalContainer.getId());
        return response;
    }

    @Override
    public PurchaseOrderCreateViewModel getByDocumentContainerId(Long documentContainerId, IAuthContextViewModel auth) {

        DocumentApprovalContainer documentApprovalContainer = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), documentContainerId)
                .orElseThrow(() -> {
                    logger.error(String.format("getByDocumentContainerId: DocumentApprovalContainer with id: {%s} could not be found.", documentContainerId));
                    return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_PO_NOT_FOUND);
                });

        // Sanity check in case of any data inconsistency (if unrequired can be removed later)
        performApprovalContainerAndDocumentContainerSantityCheck(documentApprovalContainer, documentContainerId);

        Document document = getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks(auth.getCompanyCode(), documentContainerId);
        PurchaseOrderHeader poHeader = getPOHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks(auth.getCompanyCode(), document);
        BudgetSelectionViewModel budgetDetails = getBudgetNodeForDocument(document, auth);

        return preparePODetailsResponseData(documentApprovalContainer, poHeader, document, budgetDetails, auth);
    }

    @Override
    public List<ReadyToConvertEntityViewModel> getPurchaseOrdersReadyToConvert(IAuthContextViewModel auth, Integer vendorId) {

        logger.info("getPurchaseOrdersReadyToConvert: Getting available PO's for company {} and vendor {}", auth.getCompanyCode(), vendorId);
        List<ReadyToConvertEntityViewModel> poList = new ArrayList<>();
        List<Long> poIds = new ArrayList<>();
        purchaseOrderHeaderRepository.findByCompanyCodeAndStatusAndVendorId(auth.getCompanyCode(), ReportStatus.RELEASED, vendorId)
                .forEach(i -> {
                    i.setStatus(ReportStatus.ACCEPTED); // required on front end to manage view on 'eye' symbol
                    poList.add(i);
                    poIds.add(i.getId());
                });

        logger.info("getPurchaseOrdersReadyToConvert: Getting PO consumptions by header and item");
        HashMap<Long, PurchaseRequestItemDetailsAggViewModel> poHeaderItemCounts = new HashMap<>();
        // The below function get count of the remaining quantity of PoItems that have been either partially or fully converted into invoice items
        List<PRHeaderWithConsumptionCount> availablePOHeaderWithCounts = customDocumentApprovalContainerRepository.getAvailablePOHeaderWithCounts(auth.getCompanyCode(), Long.valueOf(vendorId));
        availablePOHeaderWithCounts.forEach(i -> {
            // PO can contain multiple items and if the PO id is already found, we add the remaining count of the current item to the total of the previous items belonging to the PO header
            PurchaseRequestItemDetailsAggViewModel aggViewModel;
            if (poHeaderItemCounts.containsKey(i.getId())) {
                aggViewModel = poHeaderItemCounts.get(i.getId());
                aggViewModel.addQuantity(i.getCount());
                aggViewModel.incrementCount();
            } else {
                aggViewModel = new PurchaseRequestItemDetailsAggViewModel(i.getId(), StaticDataRegistry.PR_ITEM_COUNT_INIT, i.getCount());
            }
            poHeaderItemCounts.put(i.getId(), aggViewModel);
        });


        logger.info("getPurchaseOrdersReadyToConvert: Updating and filtering the following PO's according to available item quantity:\t{}", poIds);
        // The below function filters out PR that have no items with available quantities left and updates the total counts of the items that have partially been converted into PO's
        List<ReadyToConvertEntityViewModel> result = new ArrayList<>();
        List<Long> poIdsWithRemainingQty = new ArrayList<>();
        PurchaseRequestItemDetailsAggViewModel poAggDetails;
        // get po headers
        List<PurchaseOrderHeader> poHeaders = purchaseOrderHeaderRepository.findAllById(poIds);
        Map<Long, PurchaseOrderHeader> poHeaderMap = poHeaders.stream()
                .collect(Collectors.toMap(PurchaseOrderHeader::getId, Function.identity()));


        for (ReadyToConvertEntityViewModel poViewModel : poList) {
            PurchaseOrderHeader poHeader = poHeaderMap.get(poViewModel.getId());
            try {
                // set business details for PO
                if (poHeader != null) {
                    poViewModel.setBusinessDetails(dynamicFormService.getBusinessDetailsFromJson(poHeader.getDynamicFieldsJson()));
                }
            } catch (Exception e) {
                logger.error("getPurchaseOrdersReadyToConvert: Error while setting business details for PO: {}", poViewModel.getId(), e);
            }
            List<POGrnMapping> poGrnMappings = grnMappingRepository.findBypurchaseOrderHeaderId(poViewModel.getId());

            List<ERPGrnViewModel> grnViewModels = new ArrayList<>();

            if (!poGrnMappings.isEmpty()) {
                grnViewModels = poGrnMappings.stream()
                        .map(POGrnMapping::getViewModel) // call getViewModel() on each POGrnMapping
                        .collect(Collectors.toList());
            }
            poViewModel.setGrnViewModels(grnViewModels);
            if (poHeaderItemCounts.containsKey(poViewModel.getId())) {
                poAggDetails = poHeaderItemCounts.get(poViewModel.getId());
                if (poAggDetails.getQuantity() > 0) {
                    poViewModel.setItemCount(poAggDetails.getItemCount());
                    result.add(poViewModel);
                    poIdsWithRemainingQty.add(poViewModel.getId());
                }
            } else {
                result.add(poViewModel);
            }
        }
        logger.info("getPurchaseOrdersReadyToConvert: Result after filtering:\t{}", poIdsWithRemainingQty);
        return result;

    }

    @Override
    public List<PurchaseOrderItemDetailsViewModel> getLineItemsReadyToConvertByPO(IAuthContextViewModel auth, Integer vendorId, GenericListRequestViewModel<Long> poIds) {
        return customDocumentApprovalContainerRepository.getReadyToConvertPoItems(auth.getCompanyCode(), vendorId, poIds.getIds());
    }

    @Override
    public Document getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks(long companyCode, Long documentContainerId) {
        List<Document> attachedDocumentsArray = documentRepository.findAllByCompanyCodeAndDocumentApprovalContainerId(companyCode, documentContainerId);

        if (attachedDocumentsArray.size() != 1) {
            logger.error(String.format("getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks: DocumentApprovalContainer with id: {%s} has multiple documents attached to it.", documentContainerId));
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_PO_NOT_FOUND);
        }

        return attachedDocumentsArray.get(0);
    }

    @Override
    public PurchaseOrderHeader getPOHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks(long companyCode, Document document) {
        return purchaseOrderHeaderRepository.findByDocumentId(document.getId()).orElseThrow(() -> {
            logger.error(String.format("getPOHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks: Document with id: {%s} has not got an invoice attached to it.", document.getId()));
            return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_PO_NOT_FOUND);
        });
    }

    @Override
    public PurchaseOrderItemDetailsViewModel poItemToDto(PurchaseOrderItem poItem, IAuthContextViewModel auth) {

        PurchaseOrderItemDetailsViewModel dto = PurchaseOrderItem.getViewModel(poItem);

        // check if PR based PO - No PR items in case of NON-PR based PO
        boolean isPrBasedPo = null != poItem.getPurchaseRequestItemId();
        if (isPrBasedPo) {
            // quantity in case of PO is PR available quantity
            Double quantity = customDocumentApprovalContainerRepository.getConsumedPRItemCount(poItem.getPurchaseRequestItemId(), auth.getCompanyCode());

            dto.setQuantity(quantity);
            // set PR doc number as per UX
            dto.setDocNo(poItem.getPurchaseRequestItem().getPurchaseRequestHeader().getDocument().getDocNo());
            dto.setRequesterRemark(poItem.getPurchaseRequestItem().getPurchaseRequestHeader().getDocument().getRequesterRemark());
        }
        return dto;

    }

    @Override
    public void performApprovalContainerAndDocumentContainerSantityCheck(DocumentApprovalContainer documentApprovalContainer, Long documentContainerId) {
        // Sanity check in case of any data inconsitency (if unrequired can be removed later)
        if (!documentApprovalContainer.getId().equals(documentContainerId)) {
            logger.error(String.format("getByDocumentContainerId: DocumentApprovalContainer sanity check failed. The provided id in the query : {%s} and the resulting id in the object:  {%s} are mismatched.", documentContainerId, documentApprovalContainer.getId()));
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_PO_NOT_FOUND);
        }
    }

    @Override
    public PurchaseOrderCreateViewModel preparePODetailsResponseData(DocumentApprovalContainer documentApprovalContainer, PurchaseOrderHeader poHeader, Document document, BudgetSelectionViewModel budgetDetails, IAuthContextViewModel auth) {
        PurchaseOrderCreateViewModel response = new PurchaseOrderCreateViewModel();

        PurchaseOrderDetailsViewModel poDetails = poToDto(poHeader, document);
        poDetails.setCreatedBy(documentApprovalContainer.getFirstName() + " " + documentApprovalContainer.getLastName());
        poDetails.setDocumentIdentifier(documentApprovalContainer.getDocumentIdentifier());

        if (poHeader.getUpdatingBy() != null)
            poDetails.setUpdatedBy(StringConstants.validateAndGetName(poHeader.getUpdatingBy().getFirstName()) + " " + StringConstants.validateAndGetName(poHeader.getUpdatingBy().getLastName()));

        List<PurchaseOrderItemDetailsViewModel> poItems = poHeader.getPurchaseOrderItems().stream()
                .sorted(Comparator.comparing(PurchaseOrderItem::getId)).map(poItem -> poItemToDto(poItem, auth))
                .collect(Collectors.toList());

        if (documentApprovalContainer.getReportStatus() == ReportStatus.SUBMITTED || documentApprovalContainer.getReportStatus() == ReportStatus.ACCEPTED || documentApprovalContainer.getReportStatus() == ReportStatus.REVOKED) {
            response.setBusinessDetails(dynamicFormService.getBusinessDetailsFromJsonForStaticView(poHeader.getDynamicFieldsJson(), auth));
        } else {
            response.setBusinessDetails(dynamicFormService.getBusinessDetailsFromJson(poHeader.getDynamicFieldsJson()));
        }

        // set Purchasing DocumentType
        poDetails.setPurchasingDocType(determinePurchasingDocumentType(poItems));

        response.setPoDetails(poDetails);
        response.setItemDetails(poItems);
        response.setBudgetDetails(prepareBudgetDetailsData(budgetDetails));
        response.setVendorDetails(InvoiceHeaderMapper.prepareCompanyDetailsData(poHeader.getVendor()));
        response.setBuyerDetails(InvoiceHeaderMapper.prepareCompanyDetailsDataFromPO(poHeader));
        response.setShipToDetails(InvoiceHeaderMapper.prepareShipToDetailsDataForPO(poHeader));
        response.setAdditionalDetails(createAdditionalDetails(document)); //TODO: pending
        response.setContainerId(documentApprovalContainer.getId());
        return response;
    }

    public String determinePurchasingDocumentType(List<PurchaseOrderItemDetailsViewModel> items) {
        if (items.isEmpty()) {
            return null;
        }

        String currency = items.get(0).getCurrency();
        boolean allGoods = items.stream().allMatch(item -> ItemType.GOODS.name().equalsIgnoreCase(item.getType()));
        boolean allServices = items.stream().allMatch(item -> ItemType.SERVICE.name().equalsIgnoreCase(item.getType()));

        if (allGoods) {
            return "INR".equalsIgnoreCase(currency) ? StringConstants.PurchasingDocumentType.DOMESTIC_GOODS : StringConstants.PurchasingDocumentType.DOMESTIC_GOODS;
        } else if (allServices) {
            return "INR".equalsIgnoreCase(currency) ? StringConstants.PurchasingDocumentType.DOMESTIC_SERVICE : StringConstants.PurchasingDocumentType.DOMESTIC_SERVICE;
        } else {
            return StringConstants.PurchasingDocumentType.PROJECT_PO;
        }
    }

    private PurchaseOrderDetailsViewModel poToDto(PurchaseOrderHeader poHeader, Document poDocument) {
        PurchaseOrderDetailsViewModel poDetails = new PurchaseOrderDetailsViewModel();
        BeanUtils.copyProperties(poHeader, poDetails);
        // TODO : set documentTypeId - this should come from UI on PO save and then fetch from PO
        poDetails.setDocTypeId(DocumentType.PURCHASE_ORDER.ordinal() + 1); // ordinal starts with 0 + 1 = documentCategoryId
        if (null != poHeader.getPaymentTerms()) {
            poDetails.setPaymentTermsId(poHeader.getPaymentTerms().getId());
        }
        if (null != poHeader.getFinancialYear()) {
            poDetails.setFinancialYearId(poHeader.getFinancialYear().getId());
        }
        if (null != poHeader.getPoStatus()) {
            poDetails.setPoStatus(poHeader.getPoStatus().getId());
        }
        if (null != poHeader.getStateCode()) {
            poDetails.setStateId(poHeader.getStateCode().getId());
        }
        if (null != poHeader.getStateCode()) {
            poDetails.setStateId(poHeader.getStateCode().getId());
        }
        // ---- data from SAP will be set for below
        poDetails.setOrderValidTo(DateTimeUtils.formatDate(poHeader.getOrderValidTo())); // startdate
        poDetails.setOrderValidFrom(DateTimeUtils.formatDate(poHeader.getOrderValidFrom())); // enddate
        poDetails.setStartDate(poDetails.getOrderValidFrom());
        poDetails.setEndDate(poDetails.getOrderValidTo());
        poDetails.setPoDate(DateTimeUtils.formatDate(poHeader.getPoDate())); // podate
        poDetails.setDocumentDate(DateTimeUtils.formatDateJPQL(poDocument.getDocumentDate()));
        poDetails.setOrderNo(poDocument.getDocNo());
        // -------

        poDetails.setTotalPOValue(poDocument.getTotalAmountGSTInclusive());
        poDetails.setTaxableValue(poDocument.getTaxableAmount());

        poDetails.setCreatedAt(DateTimeUtils.formatDateJPQL(poDocument.getCreatedDate()));
        poDetails.setUpdatedAt(DateTimeUtils.formatDateJPQL(poHeader.getUpdatedAt()));
        poDetails.setDeliveryDate(DateTimeUtils.formatDateJPQL(poHeader.getDeliveryDate()));
        poDetails.setDescription(poHeader.getPurchaseReason());
        poDetails.setCurrency(poHeader.getCurrency() == null ? "INR" : poHeader.getCurrency());
        poDetails.setExpenditureType(poHeader.getDocument().getDocumentSubgroup().getDocumentMetadata().getApplicableExpenseType().name());


        //TODO: dummy
        poDetails.setIsRatioBasedPO(false);
        poDetails.setSegmentMasterId(1);

        return poDetails;
    }

    public static BudgetDetails prepareBudgetDetailsData(BudgetSelectionViewModel budgetDetails) {
        if (null == budgetDetails) return null;
        return BudgetDetails.builder()
                .id(budgetDetails.getId())
                .code(budgetDetails.getBudgetCode() == null ? StringConstants.EMPTY : budgetDetails.getBudgetCode())
                .description(budgetDetails.getDescription() == null ? StringConstants.EMPTY : budgetDetails.getDescription())
                .build();
    }

    private AdditionalDetailsView createAdditionalDetails(Document document) {
        return AdditionalDetailsView.builder().referenceDetails(document.getReferenceDetails()).termsAndCondition(document.getTermsAndCondition()).build();
    }

    @Override
    public BudgetSelectionViewModel getBudgetNodeForDocument(Document document, IAuthContextViewModel auth) {
        // Todo This needs to be converted to a api call.
        Long budgetId = document.getDocumentSubgroup().getBudgetId();
        if (budgetId != null) {
            return budgetServiceImplementation.getBudgetNode(budgetId, auth);
        }
        return null;
    }

    private void saveDocument(Document document, PurchaseOrderCreateViewModel poRequest, IAuthContextViewModel auth) {
        if (null != poRequest) {
            // Nominal Amount
            document.setTaxableAmount(Optional.ofNullable(poRequest.getPoDetails()).map(PurchaseOrderDetailsViewModel::getTaxableValue).orElse(new BigDecimal("0.0")));
            document.setTotalAmountGSTInclusive(Optional.ofNullable(poRequest.getPoDetails()).map(PurchaseOrderDetailsViewModel::getTotalPOValue).orElse(new BigDecimal("0.0")));
            // Document Dates (Document Date + Start Date + End Date)
            document.setDocumentDate(Optional.ofNullable(poRequest.getPoDetails()).map(value -> Optional.ofNullable(value.getDocumentDate()).map(DateTimeUtils::parseDatetoLocalDate).orElse(null)).orElse(null));
            // Here as invoice doesn't have a start date and end date, we manually set it to the day of entry into the system.
            document.setUpdatedTimestamp(ZonedDateTime.now());
        }
    }

    public void preparePOHeader(PurchaseOrderHeader poHeader, Document poDocument, PurchaseOrderCreateViewModel poCreateViewModel, IAuthContextViewModel auth) {

        dtoToPo(poHeader, poDocument, poCreateViewModel);
        poHeader.setUpdatingUserId(auth.getUserId());

        addUpdateLineItemDetails(poCreateViewModel.getItemDetails(), poHeader, poDocument, poCreateViewModel.getVendorDetails(), auth);

        // add segment details to segment master
        addUpdateSegmentDetails(poCreateViewModel.getSegmentDetails(), auth);

        // update doc amounts
//        poDocument.setTotalAmountGSTInclusive(poHeader.getTotalAmount());
//        poDocument.setClaimAmount(poHeader.getTotalAmount());
        poDocument.setTotalAmountGSTInclusive(poDocument.getClaimAmount()); // Todo Vishal: Check changes

        // delete if any
        if (!poHeader.getHasPrecedingDocument()) // if true - in previous steps all items are already reset
            deleteLineItems(poHeader, poCreateViewModel.getLineItemIds());

        documentRepository.saveAndFlush(poDocument);
    }

    // old implementation
    private void deleteSegments(List<Integer> segmentIds, IAuthContextViewModel auth) {
        Optional.ofNullable(segmentIds).ifPresent(ids -> ids.forEach(id -> {
            SegmentMaster segmentMaster = segmentMasterRepository.findById(id).orElseThrow(() -> new RecordNotFoundException(String.format("Could not find Segment master with id: %d", id)));
            segmentMasterRepository.delete(segmentMaster);
        }));
        segmentMasterRepository.flush();
    }

    private void addUpdateSegmentDetails(SegmentRatioToEntityRequest segmentDetails, IAuthContextViewModel auth) {
        if (null == segmentDetails)
            return;
        logger.info("Saving segment details to Purchase order.");
        ratioCategoryMasterService.saveSegmentRatio(auth.getCompanyCode(), segmentDetails);
    }

    private SegmentMaster dtoToSegmentMaster(SegmentsViewModel viewModel) {
        SegmentMaster segment = new SegmentMaster(); // handle edit case
        BeanUtils.copyProperties(viewModel, segment);
        return segment;
    }

    private void dtoToPo(PurchaseOrderHeader poHeader, Document poDocument, PurchaseOrderCreateViewModel poCreateViewModel) {
        PurchaseOrderDetailsViewModel poDetails = poCreateViewModel.getPoDetails();
        CompanyDetails vendorDetails = poCreateViewModel.getVendorDetails();
        CompanyDetails buyerDetails = poCreateViewModel.getBuyerDetails();
        ShipToDetails shipToDetails = poCreateViewModel.getShipToDetails();

        BeanUtils.copyProperties(poDetails, poHeader);
        // TODO : check if hasPreceding Document is coming from UI on save - if not then fetch an set again explicitly
        poHeader.setHasPrecedingDocument(poDocument.getDocumentSubgroup().getHasPrecedingDocument());


        if (null != poDetails.getPaymentTermsId()) {
            LookupData paymentTerms = lookupRepository.findByIdAndType(poDetails.getPaymentTermsId(), StaticDataRegistry.PAYMENT_TERMS)
                    .orElseThrow(() -> {
                                logger.error(String.format("preparePOHeader: Could not find payment terms : %s and value: %s", StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_PO));
                                return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
                            }
                    );
            poHeader.setPaymentTerms(paymentTerms);
        }
        if (null != poDetails.getStateId()) {
            StateCodes stateCode = stateCodeRepository.findById(poDetails.getStateId())
                    .orElseThrow(() -> {
                                logger.error(String.format("preparePOHeader: Could not find state : %s and value: %s", StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_PO));
                                return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
                            }
                    );
            poHeader.setStateCode(stateCode);
        }

        logger.info("Set vendor to po");
        Optional<Company> vendor = Optional.ofNullable(vendorDetails)
                .map(CompanyDetails::getId)
                .flatMap(companyRepository::findById);
        if (vendor.isPresent()) {
            poHeader.setVendor(vendor.get());
            poHeader.setVendorId(vendor.get().getCompanyId());
        }

        logger.info("Set buyer to po");
        Optional<Company> buyer = Optional.ofNullable(buyerDetails)
                .map(CompanyDetails::getId)
                .flatMap(companyRepository::findById);
        if (null != buyerDetails) {
            poHeader.setBranch(buyerDetails.getBuyerBranch());
            poHeader.setPurchasingGroup(buyerDetails.getOrganisationType());
            poHeader.setPurchasingOrganization(buyerDetails.getLegalName());
            poHeader.setGstin(buyerDetails.getGstin());
            poHeader.setEmail(buyerDetails.getEmail());
            poHeader.setAddress1(buyerDetails.getAddress1());
            poHeader.setAddress2(buyerDetails.getAddress1());
            poHeader.setPhoneNo(buyerDetails.getPhoneNumber());
            poHeader.setLoc(buyerDetails.getLocation());
            poHeader.setPin(buyerDetails.getPincode());
            poHeader.setDistrict(buyerDetails.getDistrict());
            poHeader.setBuyerContactPerson(buyerDetails.getBuyerContactPerson());
            poHeader.setStreetName(buyerDetails.getStreetName());

        }
        if (null != shipToDetails) {
            poHeader.setShipGstin(shipToDetails.getGstin());
            poHeader.setShipEmail(shipToDetails.getEmail());
            poHeader.setShipAddress1(shipToDetails.getAddress1());
            poHeader.setShipAddress2(shipToDetails.getAddress1());
            poHeader.setShipPhoneNumber(shipToDetails.getPhoneNumber());
            poHeader.setShipLocation(shipToDetails.getLocation());
            poHeader.setShipPin(shipToDetails.getPin());
            if (shipToDetails.getStateCodeId() != null)
                poHeader.setShipStateCode1(StateCodes.builder().id(shipToDetails.getStateCodeId()).build());
            poHeader.setIsDeliveryAddressSame(shipToDetails.getIsDeliveryAddressSame());
        }

        if (buyer.isPresent()) {
            poHeader.setBuyer(buyer.get());
            poHeader.setBuyerId(buyer.get().getCompanyId());
        }

        /* TODO:check - for now, calculated while adding items to PO -
        poHeader.setTotalAmount(poDetails.getTotalPOValue());
        // amount to document
        poDocument.setTotalAmountGSTInclusive(poDetails.getTotalPOValue());
        poDocument.setClaimAmount(poDetails.getTotalPOValue()); //TODO: confirm ClaimAmount is same as TotalAmountGSTInclusive
         */

        // set start date and end date
        poHeader.setOrderValidFrom(DateTimeUtils.parseDatetoLocalDate(poDetails.getStartDate()));
        poHeader.setOrderValidTo(DateTimeUtils.parseDatetoLocalDate(poDetails.getEndDate()));
        // poDate comes from ERP

        // TODO: date of document - where to set date of document, poDetails.getDocumentDate();
        poHeader.setOrderCreationTime(DateTimeUtils.getCurrentTimestamp());

        poHeader.setUpdatedAt(LocalDateTime.now());
        poHeader.setDeliveryDate(DateTimeUtils.parseDatetoLocalDate(poDetails.getDeliveryDate()));
        poHeader.setPurchaseReason(poDetails.getDescription());

        if (vendorDetails != null)
            poHeader.setIsPanBasedPo(null != vendorDetails.getIsPanBasedPo() && vendorDetails.getIsPanBasedPo());

        if (poCreateViewModel.getAdditionalDetails() != null) {
            poDocument.setReferenceDetails(poCreateViewModel.getAdditionalDetails().getReferenceDetails());
            poDocument.setTermsAndCondition(poCreateViewModel.getAdditionalDetails().getTermsAndCondition());
        }
    }

    private void addUpdateLineItemDetails(List<PurchaseOrderItemDetailsViewModel> itemDetailsViewModel, PurchaseOrderHeader poHeader, Document document, CompanyDetails vendorDetails, IAuthContextViewModel auth) {
        if (null == itemDetailsViewModel) {
            return;
        }

        // validation to check requestedAmount > budget amount
        validateRequestedAmountExceedsBudgetAmount(itemDetailsViewModel, auth);

        if (poHeader.getHasPrecedingDocument()) { // PR based
            // override - delete old items belong to PO
            deleteAllLineItems(poHeader.getId());
            poHeader.getPurchaseOrderItems().clear();
            // intialize total PO value
//                poHeader.setTotalAmount(new BigDecimal(0.0));
            document.setInitialAmounts();
//                document.setClaimAmount(new BigDecimal(0.0)); // Todo Vishal: Check changes
            purchaseOrderHeaderRepository.saveAndFlush(poHeader);

            List<PurchaseOrderItem> lineItems = new ArrayList<>();
            itemDetailsViewModel.forEach(viewModel -> {

                lineItems.add(addItemFromPurchaseRequestItem(viewModel, poHeader, document, vendorDetails.getId(), auth));
            });

            poHeader.getPurchaseOrderItems().addAll(lineItems);
            purchaseOrderItemRepository.saveAllAndFlush(lineItems);
            documentRepository.saveAndFlush(document);
        } else { // Non PR based
//                poHeader.setTotalAmount(BigDecimal.ZERO);
            // poHeader.setTotalAmount(new BigDecimal(0.0));
            document.setInitialAmounts();
//                document.setClaimAmount(new BigDecimal(0.0)); // Todo Vishal: Check changes
            itemDetailsViewModel.forEach(viewModel -> {
                if (viewModel.getId() != null) {
                    // If LineItemId is not null, it's an existing LineItem, Find the existing LineItem in the PO
                    poHeader.getPurchaseOrderItems()
                            .stream()
                            .filter(lineItem -> lineItem.getId().equals(viewModel.getId()))
                            .findFirst()
                            .ifPresent(lineItem -> addUpdatePoItem(viewModel, lineItem, poHeader, document, auth, false));
                } else {
                    poHeader.getPurchaseOrderItems()
                            .add(addUpdatePoItem(viewModel, new PurchaseOrderItem(), poHeader, document, auth, true));
                }
            });
            documentRepository.saveAndFlush(document);
        }
    }

    private void validateRequestedAmountExceedsBudgetAmount(List<PurchaseOrderItemDetailsViewModel> itemDetailsViewModel, IAuthContextViewModel auth) {

        Set<Long> uniqueBudgetNodeIds = itemDetailsViewModel.stream()
                .map(PurchaseOrderItemDetailsViewModel::getBudgetNodeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (uniqueBudgetNodeIds.isEmpty()) {
            return;
        }
        // TODO: revisit for performance cost - can be done only on UI
        BigDecimal budgetNodeAmount = calculateTotalBudgetNodeAmount(uniqueBudgetNodeIds, auth, DocumentType.PURCHASE_ORDER);
        BigDecimal requestedAmount = calculateRequestedAmount(itemDetailsViewModel);

    }

    public BigDecimal calculateTotalBudgetNodeAmount(Set<Long> uniqueBudgetNodeIds, IAuthContextViewModel auth, DocumentType type) {

        return uniqueBudgetNodeIds.stream()
                .map(budgetNodeId -> budgetServiceImplementation.getBudgetFrequencyTotalAmount(auth, budgetNodeId, DocumentType.PURCHASE_ORDER))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal calculateRequestedAmount(List<PurchaseOrderItemDetailsViewModel> itemDetailsViewModel) {
        return itemDetailsViewModel.stream()
                .map(PurchaseOrderItemDetailsViewModel::getAmountWithoutGst)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public PurchaseOrderItem addItemFromPurchaseRequestItem(PurchaseOrderItemDetailsViewModel viewModel, PurchaseOrderHeader purchaseOrderHeader, Document document, Integer vendorId, IAuthContextViewModel auth) {
        // set vendor id from vendor details
        viewModel.setVendorId(vendorId);
        PurchaseOrderItem purchaseOrderItem = new PurchaseOrderItem();
        BeanUtils.copyProperties(viewModel, purchaseOrderItem, "id");

        purchaseOrderItem.setPurchaseOrderHeader(purchaseOrderHeader);
        purchaseOrderItem.setPurchaseOrderHeaderId(purchaseOrderHeader.getId());

        if (viewModel.getItemMasterId() != null) {
            ItemMaster item = getItemMaster(viewModel.getItemMasterId(), (int) auth.getCompanyCode(), purchaseOrderHeader.getVendorId());
            if (item != null) {
                purchaseOrderItem.setType(item.getType());
                purchaseOrderItem.setVendor(item.getVendor());
                purchaseOrderItem.setVendorId(item.getVendorId());
            }
        } else {
            // new case -- if item is not present in item master and considered as new item then set info and fetch vendor and set to item
            Company vendor = companyRepository.findByCamCompanyIdAndCompanyId(auth.getCompanyCode(), viewModel.getVendorId()).orElse(null);
            purchaseOrderItem.setVendor(vendor);
            purchaseOrderItem.setVendorId(viewModel.getVendorId().longValue());
            if (viewModel.getType() != null) {
                purchaseOrderItem.setType(ItemType.fromString(viewModel.getType()));
            }
        }


        // set Quantity = selectedQuantity - as for PO it will be same
        purchaseOrderItem.setQuantity(viewModel.getSelectedQuantity());

        PurchaseRequestItem purchaseRequestItem = getPurchaseRequestItem(viewModel.getPrItemId(), auth.getCompanyCode());
        purchaseOrderItem.setPurchaseRequestItem(purchaseRequestItem);
        purchaseOrderItem.setPurchaseRequestItemId(purchaseRequestItem.getId());

        // rate * quantity
        purchaseOrderItem.setAmountWithoutGst(viewModel.getUnitRate().multiply(BigDecimal.valueOf(purchaseOrderItem.getQuantity())));
        // amount after discount
        BigDecimal assessableAmount = (purchaseOrderItem.getAmountWithoutGst()).subtract(
                null == viewModel.getDiscount() ? BigDecimal.ZERO : viewModel.getDiscount());
        purchaseOrderItem.setAssessableAmount(assessableAmount);
        purchaseOrderItem.setAmountWithoutGst(assessableAmount);

        // calculate gst on assessable amount and add
        BigDecimal totalWithTax = BigDecimal.ZERO;
        BigDecimal totalGstAmount = BigDecimal.ZERO;
        if (null != viewModel.getTaxPercentage()) {
            totalGstAmount = purchaseOrderItem.getAssessableAmount().multiply(BigDecimal.valueOf(viewModel.getTaxPercentage() * 0.01));
            totalWithTax = purchaseOrderItem.getAssessableAmount().add(totalGstAmount).add(calculateTotalWithTax(viewModel));
        } else {
            totalWithTax = assessableAmount.add(calculateTotalWithTax(viewModel));
        }

        purchaseOrderItem.setTotal(totalWithTax);
        purchaseOrderItem.setTotalGstAmount(totalGstAmount);

//        purchaseOrderItem.setTotal(viewModel.getTotal());

        BigDecimal totalAmount = null == document.getClaimAmount() ? BigDecimal.ZERO : document.getClaimAmount();
        document.setClaimAmount(totalAmount.add(null == purchaseOrderItem.getTotal() ? BigDecimal.ZERO : purchaseOrderItem.getTotal())); // Todo Vishal: Check changes

        if (viewModel.getBudgetNodeId() != null) {
            Budget budgetNode = getBudget(auth.getCompanyCode(), viewModel.getBudgetNodeId());
            purchaseOrderItem.setBudgetNode(budgetNode);
            purchaseOrderItem.setBudgetNodeId(viewModel.getBudgetNodeId());
        }
        purchaseOrderItem.setGlMasterId(viewModel.getGlId());
        purchaseOrderItem.setCreatedAt(LocalDateTime.now());
        return purchaseOrderItem;
    }

    private BigDecimal calculateTotalWithTax(PurchaseOrderItemDetailsViewModel viewModel) {
        BigDecimal cgst = null == viewModel.getCgst() ? BigDecimal.ZERO : viewModel.getCgst();
        BigDecimal igst = null == viewModel.getIgst() ? BigDecimal.ZERO : viewModel.getIgst();
        BigDecimal sgst = null == viewModel.getSgst() ? BigDecimal.ZERO : viewModel.getSgst();
        BigDecimal cessAmt = null == viewModel.getCessAmt() ? BigDecimal.ZERO : (viewModel.getCessAmt().multiply(BigDecimal.valueOf(null != viewModel.getSelectedQuantity() ? viewModel.getSelectedQuantity() : viewModel.getQuantity())));
        BigDecimal stateCessAmt = null == viewModel.getStateCessAmt() ? BigDecimal.ZERO : viewModel.getStateCessAmt().multiply(BigDecimal.valueOf(null != viewModel.getSelectedQuantity() ? viewModel.getSelectedQuantity() : viewModel.getQuantity()));
        BigDecimal cessNonAdvolAmt = null == viewModel.getCessNonAdvolAmount() ? BigDecimal.ZERO : viewModel.getCessNonAdvolAmount();
        BigDecimal cessStateNonAdvolAmt = null == viewModel.getStateCessNonAdvolAmount() ? BigDecimal.ZERO : viewModel.getStateCessNonAdvolAmount();
        BigDecimal otherCharges = null == viewModel.getOtherCharges() ? BigDecimal.ZERO : viewModel.getOtherCharges();
        return cgst.add(igst).add(sgst).add(cessAmt).add(stateCessAmt).add(cessNonAdvolAmt).add(cessStateNonAdvolAmt).add(otherCharges);
    }

    public void deleteAllLineItems(long purchaseOrderId) {
        List<PurchaseOrderItem> lineItems = purchaseOrderItemRepository.findByPurchaseOrderHeaderId(purchaseOrderId);
        purchaseOrderItemRepository.deleteAll(lineItems);
        purchaseOrderItemRepository.flush();
    }

    public void deleteLineItem(List<Long> lineItemIds, IAuthContextViewModel auth) {
        Optional.ofNullable(lineItemIds).ifPresent(ids -> ids.forEach(lineItemId -> {
            PurchaseOrderItem item = purchaseOrderItemRepository.findById(lineItemId).orElseThrow(() -> new RecordNotFoundException(String.format("Could not find PO line-item with id: %d", lineItemId)));
            purchaseOrderItemRepository.delete(item);
        }));
        purchaseOrderItemRepository.flush();
    }

    public void deleteLineItems(PurchaseOrderHeader purchaseOrderHeader, List<Long> lineItemIds) {
        Optional.ofNullable(lineItemIds).ifPresent(ids ->
                ids.forEach(lineItemId -> {
                    PurchaseOrderItem item = purchaseOrderItemRepository.findById(lineItemId).orElseThrow(() -> new RecordNotFoundException(String.format("Could not find line-item with id: %d for PO with id: %d", lineItemId, purchaseOrderHeader.getId())));
                    purchaseOrderItemRepository.delete(item);
                    purchaseOrderHeader.getPurchaseOrderItems().remove(item);
                })
        );
        purchaseOrderItemRepository.flush();
        purchaseOrderHeaderRepository.saveAndFlush(purchaseOrderHeader);
    }

    public PurchaseOrderItem addUpdatePoItem(PurchaseOrderItemDetailsViewModel viewModel, PurchaseOrderItem purchaseOrderItem, PurchaseOrderHeader purchaseOrderHeader, Document document, IAuthContextViewModel auth, boolean isNew) {

        if (isNew) {
            BeanUtils.copyProperties(viewModel, purchaseOrderItem);
            if (viewModel.getItemMasterId() != null) {
                ItemMaster item = getItemMaster(viewModel.getItemMasterId(), (int) auth.getCompanyCode(), purchaseOrderHeader.getVendorId());
                purchaseOrderItem.setType(item.getType());
                purchaseOrderItem.setVendorId(item.getVendorId());
                purchaseOrderItem.setVendor(item.getVendor());
                purchaseOrderItem.setIsCatalog(true);
                if (null != item.getSupplierRate())
                    purchaseOrderItem.setUnitRate(item.getSupplierRate());
            } else {
                // new case -- if item is not present in item master and considered as new item then set info and fetch vendor and set to item
                if (viewModel.getVendorId() != null) {
                    Company vendor = companyRepository.findByCamCompanyIdAndCompanyId(auth.getCompanyCode(), viewModel.getVendorId()).orElse(null);
                    purchaseOrderItem.setVendor(vendor);
                    purchaseOrderItem.setVendorId(viewModel.getVendorId().longValue());
                }
                purchaseOrderItem.setType(ItemType.fromString(viewModel.getType()));
            }
        } else {
            // TODO: No provision yet in UI to edit item rate - hence not setting explicitly from vendor in else case
            BeanUtils.copyProperties(viewModel, purchaseOrderItem, "id");
        }
        purchaseOrderItem.setPurchaseOrderHeader(purchaseOrderHeader);
        purchaseOrderItem.setPurchaseOrderHeaderId(purchaseOrderHeader.getId());

        purchaseOrderItem.setQuantity(viewModel.getQuantity());

        // rate * quantity
        purchaseOrderItem.setAmountWithoutGst(purchaseOrderItem.getUnitRate().multiply(BigDecimal.valueOf(purchaseOrderItem.getQuantity())));
        // amount after discount
        BigDecimal assessableAmount = (purchaseOrderItem.getAmountWithoutGst()).subtract(
                null == viewModel.getDiscount() ? BigDecimal.ZERO : viewModel.getDiscount());
        purchaseOrderItem.setAssessableAmount(assessableAmount);
        purchaseOrderItem.setAmountWithoutGst(assessableAmount);

        // calculate gst on assessable amount and add
        BigDecimal totalWithTax;
        BigDecimal totalGstAmount = BigDecimal.ZERO;
        if (null != viewModel.getTaxPercentage()) {
            totalGstAmount = purchaseOrderItem.getAssessableAmount().multiply(BigDecimal.valueOf(viewModel.getTaxPercentage() * 0.01));
            totalWithTax = purchaseOrderItem.getAssessableAmount().add(totalGstAmount).add(calculateTotalWithTax(viewModel));
        } else {
            totalWithTax = assessableAmount.add(calculateTotalWithTax(viewModel));
        }

        purchaseOrderItem.setTotal(totalWithTax);
        purchaseOrderItem.setTotalGstAmount(totalGstAmount);

//        purchaseOrderItem.setTotal(viewModel.getTotal());

        BigDecimal totalAmount = null == document.getClaimAmount() ? BigDecimal.ZERO : document.getClaimAmount();
        document.setClaimAmount(totalAmount.add(null == purchaseOrderItem.getTotal() ? BigDecimal.ZERO : purchaseOrderItem.getTotal())); // Todo Vishal: Check changes

        if (viewModel.getBudgetNodeId() != null) {
            Budget budgetNode = getBudget(auth.getCompanyCode(), viewModel.getBudgetNodeId());
            purchaseOrderItem.setBudgetNode(budgetNode);
            purchaseOrderItem.setBudgetNodeId(viewModel.getBudgetNodeId());
        }
        if (viewModel.getGlId() != null) {
            GlMaster glMaster = getGLMaster(viewModel.getGlId(), auth.getCompanyCode());
            purchaseOrderItem.setGlMaster(glMaster);
            purchaseOrderItem.setGlMasterId(glMaster.getId());
        }

        if (isNew) {
            purchaseOrderItem.setCreatedAt(LocalDateTime.now());
        } else {
            purchaseOrderItem.setUpdatedAt(LocalDateTime.now());
        }
        purchaseOrderItemRepository.saveAndFlush(purchaseOrderItem);
        return purchaseOrderItem;
    }

    private Budget getBudget(long companyCode, Long budgetNodeId) {
        return budgetRepository.findByCompanyCodeAndIdAndIsActiveTrue(companyCode, budgetNodeId).orElseThrow(() -> {
            logger.info("add budget to po item: Could not find with id: {} ", budgetNodeId);
            return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
        });
    }
    private GlMaster getGLMaster(Long id, long companyCode) {
        return glMasterRepository.findByIdAndCompanyCode(id, companyCode).orElseThrow(() -> {
            logger.info("Could not find gl master with id: {} ", id);
            return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
        });
    }
    private void saveDocumentApprovalContainer(DocumentApprovalContainer entity, PurchaseOrderCreateViewModel saveDocumentApprovalContainer) {
        // Here as invoice doesn't have a start date and end date, we manually set it to the day of entry into the system.
        entity.setApprovalContainerClaimAmount(saveDocumentApprovalContainer.getPoDetails().getTotalPOValue());
        // TODO : commented because - if startdate and enddate keeps updating on every save then expenses/docs created may fall before this date
        //        entity.setStartDate(LocalDate.now());
        entity.setEndDate(LocalDate.now());
        entity.setUpdatedTimestamp(DateTimeUtils.getCurrentZonedDateTime());
    }

}
