package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Document;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IFileIOService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

@Service
@Profile("LocalFS")
public class FileIOServiceImplementation implements IFileIOService {
    private final Logger logger;
    private final IDocumentRepository expenseRepository;

    public FileIOServiceImplementation(IDocumentRepository expenseRepository) {
        this.expenseRepository = expenseRepository;
        this.logger = LoggerFactory.getLogger(FileIOServiceImplementation.class);
    }

    @Override
    public void upload(MultipartFile file, String identifier, int index, Document document) {
        logger.trace("upload: Checking if the file is empty");
        if (file.isEmpty()) {
            logger.info("upload: File is empty");
            throw new DomainInvariantException("File is empty");
        }

        try {
            logger.trace("upload: Checking if the expense already has the document uploaded");

            if (index == 1) {
                if (document.getDocument1UploadUrl() != null) {
                    Files.delete(Paths.get(document.getDocument1UploadUrl()));
                }

                logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
                if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
                    throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
                }

                UUID uuid = UUID.randomUUID();

                logger.trace("upload: Writing file to file system on the server");
                byte[] fileBytes = file.getBytes();
                Path filePath = Paths.get("uploads", uuid.toString());
                Files.write(filePath, fileBytes);

                logger.trace("upload: Setting relevant fields on the expense");
                document.setDocument1UploadMarker(identifier);
                document.setDocument1UploadUuid(uuid.toString());
                document.setDocument1UploadContentType(file.getContentType());
                document.setDocument1UploadUrl(filePath.toString());

                logger.trace("upload: Saving the expense");
                expenseRepository.saveAndFlush(document);

                logger.trace("upload: Save successful");
            }

            if (index == 2) {
                if (document.getDocument2UploadUrl() != null) {
                    Files.delete(Paths.get(document.getDocument2UploadUrl()));
                }

                logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
                if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
                    throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
                }

                UUID uuid = UUID.randomUUID();

                logger.trace("upload: Writing file to file system on the server");
                byte[] fileBytes = file.getBytes();
                Path filePath = Paths.get("uploads", uuid.toString());
                Files.write(filePath, fileBytes);

                logger.trace("upload: Setting relevant fields on the expense");
                document.setDocument2UploadMarker(identifier);
                document.setDocument2UploadUuid(uuid.toString());
                document.setDocument2UploadContentType(file.getContentType());
                document.setDocument2UploadUrl(filePath.toString());

                logger.trace("upload: Saving the expense");
                expenseRepository.saveAndFlush(document);

                logger.trace("upload: Save successful");
            }

            if (index == 3) {
                if (document.getDocument3UploadUrl() != null) {
                    Files.delete(Paths.get(document.getDocument3UploadUrl()));
                }

                logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
                if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
                    throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
                }

                UUID uuid = UUID.randomUUID();

                logger.trace("upload: Writing file to file system on the server");
                byte[] fileBytes = file.getBytes();
                Path filePath = Paths.get("uploads", uuid.toString());
                Files.write(filePath, fileBytes);

                logger.trace("upload: Setting relevant fields on the expense");
                document.setDocument3UploadMarker(identifier);
                document.setDocument3UploadUuid(uuid.toString());
                document.setDocument3UploadContentType(file.getContentType());
                document.setDocument3UploadUrl(filePath.toString());

                logger.trace("upload: Saving the expense");
                expenseRepository.saveAndFlush(document);

                logger.trace("upload: Save successful");
            }


        } catch (IOException ioException) {
            logger.error("upload: Exception caught: cannot write: {}", ioException.getMessage());
            throw new RuntimeException("Failed to upload the file");
        }
    }

    @Override
    public ResponseEntity<Resource> getAttachment1(String identifier, long expenseId, IAuthContextViewModel auth) {
        try {
            logger.trace("getFile: Checking if the expense exists");
            Document document = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                    .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

            logger.trace("getFile: Checking if the document upload url exists");
            if (document.getDocument1UploadUrl() == null) {
                logger.info("getFile: Document upload path is missing (no upload)");
                throw new DomainInvariantException("Expense doesn't have a document uploaded");
            }

            logger.trace("getFile: Reading the file");
            InputStreamResource resource = new InputStreamResource(new FileInputStream(document.getDocument1UploadUrl()));

            logger.trace("getFile: Preparing the response");
            return ResponseEntity.ok()
                    .contentType(MediaType.valueOf(document.getDocument1UploadContentType()))
                    .body(resource);
        } catch (FileNotFoundException fileNotFoundException) {
            logger.error("getFile: Caught exception: {}", fileNotFoundException.getMessage());
            throw new DomainInvariantException("Could not find the file");
        }
    }

    @Override
    public ResponseEntity<Resource> getAttachment2(String identifier, long expenseId, IAuthContextViewModel auth) {
        try {
            logger.trace("getFile: Checking if the expense exists");
            Document document = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                    .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

            logger.trace("getFile: Checking if the document upload url exists");
            if (document.getDocument2UploadUrl() == null) {
                logger.info("getFile: Document upload path is missing (no upload)");
                throw new DomainInvariantException("Expense doesn't have a document uploaded");
            }

            logger.trace("getFile: Reading the file");
            InputStreamResource resource = new InputStreamResource(new FileInputStream(document.getDocument2UploadUrl()));

            logger.trace("getFile: Preparing the response");
            return ResponseEntity.ok()
                    .contentType(MediaType.valueOf(document.getDocument2UploadContentType()))
                    .body(resource);
        } catch (FileNotFoundException fileNotFoundException) {
            logger.error("getFile: Caught exception: {}", fileNotFoundException.getMessage());
            throw new DomainInvariantException("Could not find the file");
        }
    }

    @Override
    public ResponseEntity<Resource> getAttachment3(String identifier, long expenseId, IAuthContextViewModel auth) {
        try {
            logger.trace("getFile: Checking if the expense exists");
            Document document = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                    .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

            logger.trace("getFile: Checking if the document upload url exists");
            if (document.getDocument3UploadUrl() == null) {
                logger.info("getFile: Document upload path is missing (no upload)");
                throw new DomainInvariantException("Expense doesn't have a document uploaded");
            }

            logger.trace("getFile: Reading the file");
            InputStreamResource resource = new InputStreamResource(new FileInputStream(document.getDocument3UploadUrl()));

            logger.trace("getFile: Preparing the response");
            return ResponseEntity.ok()
                    .contentType(MediaType.valueOf(document.getDocument3UploadContentType()))
                    .body(resource);
        } catch (FileNotFoundException fileNotFoundException) {
            logger.error("getFile: Caught exception: {}", fileNotFoundException.getMessage());
            throw new DomainInvariantException("Could not find the file");
        }
    }

    @Override
    public void delete(Document document) {
        try {
            logger.trace("delete: Checking existence of document 1 for deletion");
            if (document.getDocument1UploadUrl() != null) {
                Files.delete(Paths.get(document.getDocument1UploadUrl()));
            }

            logger.trace("delete: Checking existence of document 2 for deletion");
            if (document.getDocument2UploadUrl() != null) {
                Files.delete(Paths.get(document.getDocument2UploadUrl()));
            }

            logger.trace("delete: Checking existence of document 3 for deletion");
            if (document.getDocument3UploadUrl() != null) {
                Files.delete(Paths.get(document.getDocument3UploadUrl()));
            }
        } catch (IOException ioException) {
            logger.trace("delete: IOException thrown while deleting: {}", ioException.getMessage());
        }
    }

    @Override
    public void delete(int index, Document document) {
        try {
            logger.trace("delete: Checking existence of document 1 for deletion");
            if (index == 1 && document.getDocument1UploadUrl() != null) {
                Files.delete(Paths.get(document.getDocument1UploadUrl()));
            }

            logger.trace("delete: Checking existence of document 2 for deletion");
            if (index == 2 && document.getDocument2UploadUrl() != null) {
                Files.delete(Paths.get(document.getDocument2UploadUrl()));
            }

            logger.trace("delete: Checking existence of document 3 for deletion");
            if (index == 3 && document.getDocument3UploadUrl() != null) {
                Files.delete(Paths.get(document.getDocument3UploadUrl()));
            }
        } catch (IOException ioException) {
            logger.trace("delete: IOException thrown while deleting: {}", ioException.getMessage());
        }
    }

    @Override
    public String handleInvoiceUpload(MultipartFile file, long companyCode) throws IOException {
        logger.trace("Dummy Implementation of invoice upload");
        return null;
    }

    @Override
    public String handleMoveInvoice(String tempFilePath, String name, long companyCode) throws IOException {
        return "";
    }

    @Override
    public String handleDocumentUpload(MultipartFile doc, long companyCode, String category, String fileHash, Integer categoryId, Long entityId) throws IOException {
        logger.trace("Dummy Implementation of document upload");
        return null;
    }

    @Override
    public URL getSignedUrl(String filename) {
        return null;
    }

}
