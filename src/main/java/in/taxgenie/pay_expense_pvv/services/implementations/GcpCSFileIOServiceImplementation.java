package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cloud.GcpCSFileIOProvider;
import in.taxgenie.pay_expense_pvv.cloud.GcpCSProvider;
import in.taxgenie.pay_expense_pvv.entities.Document;
import in.taxgenie.pay_expense_pvv.entities.DocumentStorage;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentRepository;
import in.taxgenie.pay_expense_pvv.repositories.documents.IDocumentStorageRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IFileIOService;
import in.taxgenie.pay_expense_pvv.utils.FileUtils;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URL;
import java.util.Optional;
import java.util.UUID;

@Service
@Profile({"tg-internal-gcp", "production-gcp"})
public class GcpCSFileIOServiceImplementation implements IFileIOService {
    @Value("${cloud.uploadEnv}")
    private String uploadEnv;
    private final GcpCSFileIOProvider gsFileIOProvider;
    private final GcpCSProvider gsProvider;

    private final IDocumentRepository expenseRepository;
    private final IDocumentStorageRepository documentStorageRepository;
    private final Logger logger;

    public GcpCSFileIOServiceImplementation(GcpCSFileIOProvider gsFileIOProvider, GcpCSProvider gsProvider, IDocumentRepository expenseRepository, IDocumentStorageRepository documentStorageRepository) {
        this.gsFileIOProvider = gsFileIOProvider;
        this.gsProvider = gsProvider;
        this.expenseRepository = expenseRepository;
        this.documentStorageRepository = documentStorageRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public void upload(MultipartFile file, String identifier, int index, Document document) {
        logger.trace("upload: Checking if the file is empty");
        if (file.isEmpty()) {
            logger.info("upload: File is empty");
            throw new DomainInvariantException("File is empty");
        }

        try {
            logger.trace("upload: Checking if the expense already has the document uploaded");

            if (index == 1) {
                handleDocument1Updload(file, identifier, document);
            }

            if (index == 2) {
                handleDocument2Upload(file, identifier, document);
            }

            if (index == 3) {
                handleDocument3Upload(file, identifier, document);
            }


        } catch (IOException ioException) {
            logger.error("upload: Exception caught: cannot write: {}", ioException.getMessage());
            throw new RuntimeException("Failed to upload the file");
        }
    }

    @Override
    public ResponseEntity<Resource> getAttachment1(String identifier, long expenseId, IAuthContextViewModel auth) {
        logger.trace("getFile: Checking if the expense exists");
        Document document = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

        logger.trace("getFile: Checking if the document upload url exists");
        if (document.getDocument1UploadUrl() == null) {
            logger.info("getFile: Document upload path is missing (no upload)");
            throw new DomainInvariantException("Expense doesn't have a document uploaded");
        }

        logger.trace("getFile: Reading the file");
        ByteArrayResource resource = gsFileIOProvider.downloadFile(document.getDocument1UploadUrl());

        logger.trace("getFile: Preparing the response");
        return ResponseEntity.ok()
                .contentType(MediaType.valueOf(document.getDocument1UploadContentType()))
                .body(resource);
    }

    @Override
    public ResponseEntity<Resource> getAttachment2(String identifier, long expenseId, IAuthContextViewModel auth) {
        logger.trace("getFile: Checking if the expense exists");
        Document document = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

        logger.trace("getFile: Checking if the document upload url exists");
        if (document.getDocument2UploadUrl() == null) {
            logger.info("getFile: Document upload path is missing (no upload)");
            throw new DomainInvariantException("Expense doesn't have a document uploaded");
        }

        logger.trace("getFile: Reading the file");
        ByteArrayResource resource = gsFileIOProvider.downloadFile(document.getDocument2UploadUrl());

        logger.trace("getFile: Preparing the response");
        return ResponseEntity.ok()
                .contentType(MediaType.valueOf(document.getDocument2UploadContentType()))
                .body(resource);
    }

    @Override
    public ResponseEntity<Resource> getAttachment3(String identifier, long expenseId, IAuthContextViewModel auth) {
        logger.trace("getFile: Checking if the expense exists");
        Document document = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

        logger.trace("getFile: Checking if the document upload url exists");
        if (document.getDocument3UploadUrl() == null) {
            logger.info("getFile: Document upload path is missing (no upload)");
            throw new DomainInvariantException("Expense doesn't have a document uploaded");
        }

        logger.trace("getFile: Reading the file");
        ByteArrayResource resource = gsFileIOProvider.downloadFile(document.getDocument3UploadUrl());

        logger.trace("getFile: Preparing the response");
        return ResponseEntity.ok()
                .contentType(MediaType.valueOf(document.getDocument3UploadContentType()))
                .body(resource);
    }

    @Override
    public void delete(Document document) {
        if (document.getDocument1UploadUrl() != null) {
            gsFileIOProvider.deleteFile(document.getDocument1UploadUrl());
        }

        if (document.getDocument2UploadUrl() != null) {
            gsFileIOProvider.deleteFile(document.getDocument2UploadUrl());
        }

        if (document.getDocument3UploadUrl() != null) {
            gsFileIOProvider.deleteFile(document.getDocument3UploadUrl());
        }
    }

    @Override
    public void delete(int index, Document document) {
        if (index == 1 && document.getDocument1UploadUrl() != null) {
            gsFileIOProvider.deleteFile(document.getDocument1UploadUrl());
        }

        if (index == 2 && document.getDocument2UploadUrl() != null) {
            gsFileIOProvider.deleteFile(document.getDocument2UploadUrl());
        }

        if (index == 3 && document.getDocument3UploadUrl() != null) {
            gsFileIOProvider.deleteFile(document.getDocument3UploadUrl());
        }
    }

    private void handleDocument1Updload(MultipartFile file, String identifier, Document document) throws IOException {
        if (document.getDocument1UploadUrl() != null) {
            gsFileIOProvider.deleteFile(document.getDocument1UploadUrl());
        }

        logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
        if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
            throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
        }

        UUID uuid = UUID.randomUUID();
        String fileName = String.format("%s/%d/%d/%d/%s", gsProvider.getBucketName(), document.getCompanyCode(), document.getDocumentApprovalContainerId(), document.getId(), uuid);

        logger.trace("upload: Uploading {} to GCP Cloud Storage", fileName);
        gsFileIOProvider.uploadFile(fileName, file);
        logger.trace("upload: Upload to GCP Cloud Storage succeeded");

        logger.trace("upload: Setting relevant fields on the expense");
        document.setDocument1UploadMarker(identifier);
        document.setDocument1UploadUuid(uuid.toString());
        document.setDocument1UploadContentType(file.getContentType());
        document.setDocument1UploadUrl(fileName);

        logger.trace("upload: Saving the expense");
        expenseRepository.saveAndFlush(document);

        logger.trace("upload: Save successful");
    }

    private void handleDocument2Upload(MultipartFile file, String identifier, Document document) throws IOException {
        if (document.getDocument2UploadUrl() != null) {
            gsFileIOProvider.deleteFile(document.getDocument2UploadUrl());
        }

        logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
        if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
            throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
        }

        UUID uuid = UUID.randomUUID();
        String fileName = String.format("%s/%d/%d/%d/%s", gsProvider.getBucketName(), document.getCompanyCode(), document.getDocumentApprovalContainerId(), document.getId(), uuid);

        logger.trace("upload: Uploading {} to GCP Cloud Storage", fileName);
        gsFileIOProvider.uploadFile(fileName, file);
        logger.trace("upload: Upload to GCP Cloud Storage succeeded");

        logger.trace("upload: Setting relevant fields on the expense");
        document.setDocument2UploadMarker(identifier);
        document.setDocument2UploadUuid(uuid.toString());
        document.setDocument2UploadContentType(file.getContentType());
        document.setDocument2UploadUrl(fileName);

        logger.trace("upload: Saving the expense");
        expenseRepository.saveAndFlush(document);

        logger.trace("upload: Save successful");
    }

    private void handleDocument3Upload(MultipartFile file, String identifier, Document document) throws IOException {
        if (document.getDocument3UploadUrl() != null) {
            gsFileIOProvider.deleteFile(document.getDocument3UploadUrl());
        }

        logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
        if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
            throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
        }

        UUID uuid = UUID.randomUUID();
        String fileName = String.format("%s/%d/%d/%d/%s", gsProvider.getBucketName(), document.getCompanyCode(), document.getDocumentApprovalContainerId(), document.getId(), uuid);

        logger.trace("upload: Uploading {} to GCP Cloud Storage", fileName);
        gsFileIOProvider.uploadFile(fileName, file);
        logger.trace("upload: Upload to GCP Cloud Storage succeeded");

        logger.trace("upload: Setting relevant fields on the expense");
        document.setDocument3UploadMarker(identifier);
        document.setDocument3UploadUuid(uuid.toString());
        document.setDocument3UploadContentType(file.getContentType());
        document.setDocument3UploadUrl(fileName);

        logger.trace("upload: Saving the expense");
        expenseRepository.saveAndFlush(document);

        logger.trace("upload: Save successful");
    }


    @Override
    public String handleInvoiceUpload(MultipartFile file, long companyCode) throws IOException {
        logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
        String fileName = FileUtils.processFile(file, uploadEnv, companyCode, StaticDataRegistry.CloudStorageConstans.CATEGORY_INVOICE);

        logger.trace("upload: Uploading {} to GCP Cloud Storage", fileName);
        gsFileIOProvider.uploadFile(fileName, file);
        logger.trace("upload: Upload to GCP Cloud Storage succeeded");
        return fileName;
    }

    @Override
    public String handleMoveInvoice(String tempFilePath, String name, long companyCode) throws IOException {
        logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
        try {
            String fileName = FileUtils.processFilePath(name, uploadEnv, companyCode, StaticDataRegistry.CloudStorageConstans.CATEGORY_INVOICE);

            logger.trace("upload: Uploading {} to GCP Cloud Storage", fileName);
            gsFileIOProvider.moveFile(tempFilePath, fileName);
            logger.trace("upload: Upload to GCP Cloud Storage succeeded");
            return fileName;
        } catch (Exception e) {
            logger.error("Error occurred while moving invoice file: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to move file", e);
        }
    }

    @Override
    public String handleDocumentUpload(MultipartFile file, long companyCode, String category, String fileHash, Integer categoryId, Long entityId) throws IOException {
        // created new method - may use additoinal parameters

        // Check if the file already exists
        Optional<DocumentStorage> existingFile = documentStorageRepository.findByFileHashAndCategoryIdAndEntityId(fileHash, categoryId, entityId);
        if (existingFile.isPresent()) {
            logger.info("File already exists: {}", existingFile.get().getName());
            throw new DomainInvariantException("Already exist");
        }

        logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
        String fileName = FileUtils.processFile(file, uploadEnv, companyCode, category);

        logger.trace("upload: Uploading {} to GCP Cloud Storage", fileName);
        gsFileIOProvider.uploadFile(fileName, file);
        logger.trace("upload: Upload to GCP Cloud Storage succeeded");
        return fileName;
    }

    @Override
    public URL getSignedUrl(String filename) {
        return gsFileIOProvider.getSignedUrl(filename);
    }

}
