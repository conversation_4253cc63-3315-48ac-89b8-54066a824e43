package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.DocumentApprovalContainer;
import in.taxgenie.pay_expense_pvv.entities.DocumentIdentifier;
import in.taxgenie.pay_expense_pvv.entities.DocumentMetadata;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentIdentifierRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentIdentifierService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import jakarta.persistence.OptimisticLockException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;

import jakarta.transaction.Transactional;

@Service
public class DocumentIdentifierServiceImplementation implements IDocumentIdentifierService {
    private final IDocumentIdentifierRepository repository;
    private final Logger logger;
    private final IDocumentApprovalContainerRepository documentApprovalContainerRepository;

    public DocumentIdentifierServiceImplementation(
            IDocumentIdentifierRepository repository, IDocumentApprovalContainerRepository documentApprovalContainerRepository
    ) {
        this.repository = repository;
        this.documentApprovalContainerRepository = documentApprovalContainerRepository;
        this.logger = LoggerFactory.getLogger(DocumentIdentifierServiceImplementation.class);
    }

    @Transactional
    public String processAndGetIdentifier(DocumentMetadata metadata, int year, int month, IAuthContextViewModel auth) {
        logger.info("processAndGetIdentifier: Document identifier creation");

        return repository.findFirstByCompanyCodeAndDocumentTypeAndYearAndMonth(
                        auth.getCompanyCode(), metadata.getDocumentType(), year, month)
                .map(this::handleFoundEntry)
                .orElseGet(() -> handleFreshEntry(metadata, auth, year, month));
    }

    @Transactional
    private String handleFreshEntry(DocumentMetadata metadata, IAuthContextViewModel auth, int year, int month) {
        logger.info("processAndGetIdentifier: Creating new identifier for Company: {}, Document Type: {}, Document Group: {}, Year: {}, Month: {}",
                auth.getCompanyCode(), metadata.getDocumentType(), metadata.getDocumentGroup(), year, month);

        try {
            DocumentIdentifier entity = DocumentIdentifier.builder()
                    .companyCode(auth.getCompanyCode())
                    .documentType(metadata.getDocumentType())
                    .documentTypePrefix(metadata.getDocumentTypePrefix())
                    .documentGroup(metadata.getDocumentGroup())
                    .documentGroupPrefix(metadata.getDocumentGroupPrefix())
                    .year(year)
                    .month(month)
                    .currentIndex(StaticDataRegistry.START_CURRENT_NUMBER)
                    .createdTimestamp(ZonedDateTime.now())
                    .build();

            DocumentIdentifier savedEntity = repository.saveAndFlush(entity);
            logger.info("processAndGetIdentifier: Saved new identifier with ID: {}", savedEntity.getId());

            String documentIdentifier = StaticDataRegistry.getDocumentIdentifier(savedEntity);
            logger.info("processAndGetIdentifier: Newly generated document identifier: " + documentIdentifier + ".");
            List<DocumentApprovalContainer> documentApprovalContainers= documentApprovalContainerRepository.findByCompanyCodeAndDocumentIdentifier(auth.getCompanyCode(), documentIdentifier);
            if(!documentApprovalContainers.isEmpty()){
                logger.info("***processAndGetIdentifier: Document identifier is already present. Creating new by calling processAndGetIdentifier method.");
                return processAndGetIdentifier(metadata, year, month, auth);
            }
            return documentIdentifier;
        } catch (OptimisticLockException e) {
            logger.info("***processAndGetIdentifier: Race condition detected, retrying fetch.");
            return processAndGetIdentifier(metadata, year, month, auth);
        }
    }

    @Transactional
    private String handleFoundEntry(DocumentIdentifier entity) {
        logger.info("processAndGetIdentifier: Found identifier, incrementing index");

        entity.setCurrentIndex(entity.getCurrentIndex() + StaticDataRegistry.CURRENT_NUMBER_INCREMENT_FACTOR);
        entity.setUpdatedTimestamp(ZonedDateTime.now());

        DocumentIdentifier savedEntity = repository.saveAndFlush(entity);
        logger.info("processAndGetIdentifier: Updated entity with new index: {}", savedEntity.getCurrentIndex());

        return StaticDataRegistry.getDocumentIdentifier(savedEntity);
    }
}
