package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.DocumentMetadata;
import in.taxgenie.pay_expense_pvv.entities.DocumentSubgroup;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.MasterRecordNotFoundException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentMetadataRepository;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentSubgroupRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentSubgroupService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentSubgroupCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentSubgroupUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentSubgroupViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.KeyValuePairViewModel;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DocumentSubgroupServiceImplementation implements IDocumentSubgroupService {
    private final IBudgetRepository budgetRepository;
    private final IDocumentSubgroupRepository repository;
    private final IDocumentMetadataRepository metadataRepository;
    private final Logger logger;

    public DocumentSubgroupServiceImplementation(
            IBudgetRepository budgetRepository, IDocumentSubgroupRepository repository,
            IDocumentMetadataRepository metadataRepository
    ) {
        this.budgetRepository = budgetRepository;
        this.repository = repository;
        this.metadataRepository = metadataRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Transactional
    @Override
    public DocumentSubgroupViewModel create(DocumentSubgroupCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("create: Ensuring expense code uniqueness");

        if (repository.getCountByCode(auth.getCompanyCode(), viewModel.getDocumentCode(), viewModel.getDocumentMetadataId(), StaticDataRegistry.NEW_ENTITY_ID) > 0) {
            throw new DomainInvariantException(String.format("Subgroup exists by code: %s", viewModel.getDocumentCode()));
        }

        logger.trace("create: Ensuring expense subgroup uniqueness");
        if (repository.getCountBySubgroup(auth.getCompanyCode(), viewModel.getDocumentSubgroup(), viewModel.getDocumentMetadataId(), StaticDataRegistry.NEW_ENTITY_ID) > 0) {
            throw new DomainInvariantException(String.format("Subgroup exists by name: %s", viewModel.getDocumentSubgroup()));
        }

        logger.trace("create: Ensuring expense subgroup prefix uniqueness");
        if (repository.getCountByPrefix(auth.getCompanyCode(), viewModel.getDocumentSubgroupPrefix(), viewModel.getDocumentMetadataId(), StaticDataRegistry.NEW_ENTITY_ID) > 0) {
            throw new DomainInvariantException(String.format("Subgroup exists by prefix: %s", viewModel.getDocumentSubgroupPrefix()));
        }

        logger.trace("create: Ensuring expense metadata exists");
        DocumentMetadata metadata = metadataRepository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getDocumentMetadataId())
                .orElseThrow(() -> new MasterRecordNotFoundException(String.format("Expense metadata with id: %d is not found", viewModel.getDocumentMetadataId())));

        logger.trace("create: Creating new entity and copying fields");
        DocumentSubgroup entity = new DocumentSubgroup();
        entity.setApplicableExpenseType(null == viewModel.getApplicableExpenseType() ? null : ExpenseType.valueOf(viewModel.getApplicableExpenseType()));
        BeanUtils.copyProperties(viewModel, entity, "applicableExpenseType");

        logger.trace("create: Setting audit fields");
        entity.setCreatingUserId(auth.getUserId());
        entity.setCreatedTimestamp(ZonedDateTime.now());
        entity.setCompanyCode(auth.getCompanyCode());
        if (entity.getBudgetId() != null) {
            entity.setBudget(budgetRepository.findById(entity.getBudgetId()).get());
        }
        metadata.setSubgroupsCount(metadata.getSubgroupsCount() + StaticDataRegistry.COUNT_INCREMENT_FACTOR);

        logger.trace("create: Setting bi-directional mapping");
        entity.setDocumentMetadata(metadata);
        metadata.getDocumentSubgroups().add(entity);

        logger.trace("create: Start saving");
        repository.saveAndFlush(entity);
        metadataRepository.saveAndFlush(metadata);
        logger.trace("create: Save complete");

        return getViewModel(entity);
    }

    @Override
    public DocumentSubgroupViewModel update(long id, DocumentSubgroupUpdateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("update: Ensuring the id matches with viewmodel");
        if (id != viewModel.getId()) {
            throw new DomainInvariantException("ExpenseSubgroupViewModel update: id mismatch");
        }

        logger.trace("update: Ensuring the entity exists");
        DocumentSubgroup entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getId())
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find ExpenseSubgroup with id: %d", id)));


        logger.trace("update: Ensuring the expense code uniqueness");
        if (repository.getCountByCode(auth.getCompanyCode(), viewModel.getDocumentCode(), viewModel.getDocumentMetadataId(), viewModel.getId()) > 0) {
            throw new DomainInvariantException(String.format("Subgroup exists by code: %s", viewModel.getDocumentCode()));
        }

        logger.trace("update: Ensuring subgroup uniqueness");
        if (repository.getCountBySubgroup(auth.getCompanyCode(), viewModel.getDocumentSubgroup(), viewModel.getDocumentMetadataId(), viewModel.getId()) > 0) {
            throw new DomainInvariantException(String.format("Subgroup exists by name: %s", viewModel.getDocumentSubgroup()));
        }

        logger.trace("update: Ensuring prefix uniquensess");
        if (repository.getCountByPrefix(auth.getCompanyCode(), viewModel.getDocumentSubgroupPrefix(), viewModel.getDocumentMetadataId(), viewModel.getId()) > 0) {
            throw new DomainInvariantException(String.format("Subgroup exists by prefix: %s", viewModel.getDocumentSubgroupPrefix()));
        }

        logger.trace("update: Copying fields");
        entity.setApplicableExpenseType(null == viewModel.getApplicableExpenseType() ? null : ExpenseType.valueOf(viewModel.getApplicableExpenseType()));
        BeanUtils.copyProperties(viewModel, entity, "expenseMetadataId", "applicableExpenseType");

        logger.trace("update: Setting the audit fields");
        entity.setUpdatingUserId(auth.getUserId());
        entity.setUpdatedTimestamp(ZonedDateTime.now());

        logger.trace("update: Start saving");
        repository.saveAndFlush(entity);
        logger.trace("update: Save complete");

        return getViewModel(entity);
    }

    @Override
    public List<DocumentSubgroupViewModel> getAll(IAuthContextViewModel auth) {
        return repository
                .findAllByCompanyCode(auth.getCompanyCode())
                .stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public List<DocumentSubgroupViewModel> getAllByMetadata(long metadataId, IAuthContextViewModel auth) {
        return repository
                .findAllByCompanyCodeAndDocumentMetadataId(auth.getCompanyCode(), metadataId)
                .stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    public List<DocumentSubgroupViewModel> getDocumentSubroupFilteredBasedOnRulesByMetadata(long metadataId, IAuthContextViewModel auth) {
        return repository
                .findDocumentSubgroupForInvoiceCreation(auth.getCompanyCode(), metadataId)
                .stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public DocumentSubgroupViewModel getById(long id, IAuthContextViewModel auth) {
        DocumentSubgroup entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find ExpenseSubgroup with id: %d", id)));

        return getViewModel(entity);
    }

    @Override
    public DocumentSubgroupViewModel getByDocumentContainerId(long id, IAuthContextViewModel auth) {
        DocumentSubgroup entity = repository.findSubgroupByDocumentApprovalContainerIdAndCompanyId(id, auth.getCompanyCode())
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find ExpenseSubgroup with document container id: %d", id)));

        return getViewModel(entity);
    }

    @Override
    public List<KeyValuePairViewModel<Long, String>> getKeyValueCollection(IAuthContextViewModel auth) {
        return repository
                .findAllByCompanyCode(auth.getCompanyCode())
                .stream()
                .filter(e -> !e.isFrozen())
                .map(e -> new KeyValuePairViewModel<>(
                        e.getId(),
                        String.format("%s > %s", e.getDocumentSubgroup(), e.getDocumentCode())
                ))
                .collect(Collectors.toList());
    }

    @Override
    public List<KeyValuePairViewModel<Long, String>> getKeyValueCollection(long metadataId, IAuthContextViewModel auth) {
        return repository
                .findAllByCompanyCodeAndDocumentMetadataId(auth.getCompanyCode(), metadataId)
                .stream()
                .filter(e -> !e.isFrozen())
                .map(e -> new KeyValuePairViewModel<>(
                        e.getId(),
                        String.format("%s > %s", e.getDocumentSubgroup(), e.getDocumentCode())
                ))
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsByExpenseCode(String expenseCode, long metadataId, IAuthContextViewModel auth) {
        return repository.getCountByCode(auth.getCompanyCode(), expenseCode, metadataId, StaticDataRegistry.DEFAULT_ENTITY_ID) > 0;
    }

    @Override
    public boolean existsBySubgroup(String subgroup, long metadataId, IAuthContextViewModel auth) {
        return repository.getCountBySubgroup(auth.getCompanyCode(), subgroup, metadataId, StaticDataRegistry.DEFAULT_ENTITY_ID) > 0;
    }

    @Override
    public boolean existsByPrefix(String prefix, long metadataId, IAuthContextViewModel auth) {
        return repository.getCountByPrefix(auth.getCompanyCode(), prefix, metadataId, StaticDataRegistry.DEFAULT_ENTITY_ID) > 0;
    }

    private DocumentSubgroupViewModel getViewModel(DocumentSubgroup entity) {
        DocumentSubgroupViewModel viewModel = new DocumentSubgroupViewModel();
        DocumentMetadata documentMetadata = entity.getDocumentMetadata();
        viewModel.setApplicableExpenseType(null == documentMetadata.getApplicableExpenseType() ? null : documentMetadata.getApplicableExpenseType().name());
        BeanUtils.copyProperties(entity, viewModel, "applicableExpenseType");

        if (entity.getBudget() != null) {
            viewModel.setBudgetStructureMasterId(entity.getBudget().getBudgetStructureMasterId());
        }
        viewModel.setDocumentSubgroupMarker(StaticDataRegistry.getExpenseSubgroupMarker(entity));
//        viewModel.setRulesCount(entity.getRules().size());

        return viewModel;
    }
}
