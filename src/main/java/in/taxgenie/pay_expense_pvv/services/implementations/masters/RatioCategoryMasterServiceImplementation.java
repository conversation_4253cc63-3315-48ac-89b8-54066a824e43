package in.taxgenie.pay_expense_pvv.services.implementations.masters;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.taxgenie.pay_expense_pvv.entities.DocumentType;
import in.taxgenie.pay_expense_pvv.entities.masters.MappingSegmentRatioToEntity;
import in.taxgenie.pay_expense_pvv.entities.masters.RatioCategoryConfig;
import in.taxgenie.pay_expense_pvv.entities.masters.RatioCategoryMaster;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.repositories.masters.IMappingSegmentRatioToEntityRepository;
import in.taxgenie.pay_expense_pvv.repositories.masters.IRatioCategoryConfigRepository;
import in.taxgenie.pay_expense_pvv.repositories.masters.IRatioCategoryMasterRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.masters.IRatioCategoryMasterService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.MappersUtil;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericListRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.*;
import in.taxgenie.pay_expense_pvv.viewmodels.po.SegmentsViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RatioCategoryMasterServiceImplementation implements IRatioCategoryMasterService {
    private final Logger logger;
    private final IRatioCategoryMasterRepository ratioCategoryMasterRepository;
    private final IRatioCategoryConfigRepository ratioCategoryConfigRepository;
    private final IMappingSegmentRatioToEntityRepository mappingSegmentRatioToEntityRepository;

    public RatioCategoryMasterServiceImplementation(IRatioCategoryMasterRepository ratioCategoryMasterRepository, IRatioCategoryConfigRepository ratioCategoryConfigRepository, IMappingSegmentRatioToEntityRepository mappingSegmentRatioToEntityRepository) {
        this.ratioCategoryMasterRepository = ratioCategoryMasterRepository;
        this.ratioCategoryConfigRepository = ratioCategoryConfigRepository;
        this.mappingSegmentRatioToEntityRepository = mappingSegmentRatioToEntityRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public List<SegmentsViewModel> getSegmentMasters(long companyCode) {
        logger.info("getSegmentMasters: for companyCode :" + companyCode);
//        return ratioCategoryMasterRepository.findAllByCompanyCode(companyCode).stream().map(segmentMaster -> {
//            SegmentsViewModel viewModel = new SegmentsViewModel();
//            BeanUtils.copyProperties(segmentMaster, viewModel);
//            return viewModel;
//        }).collect(Collectors.toList());
        return List.of();
    }

    @Override
    public void saveRatioCategoryConfig(long companyCode, RatioCategoryConfigRequest request) {
        List<RatioCategoryConfig> configs = request.getMasterIds().stream()
                .map(masterId -> {
                    RatioCategoryConfig config = new RatioCategoryConfig();
                    config.setMasterId(masterId);
                    config.setCompanyCode(companyCode);
                    config.setCreatedAt(LocalDateTime.now());
                    return config;
                }).collect(Collectors.toList());
        ratioCategoryConfigRepository.saveAll(configs);
    }

    @Override
    public List<MasterResponseViewModel> getConfig(long companyCode) {
        List<MasterResponseViewModel> viewModel = ratioCategoryConfigRepository.findByCompanyCode(companyCode);
        return viewModel;
    }

    @Override
    public List<RatioCategoryViewModel> getAllRatioCategories(long companyCode) {
        List<RatioCategoryMaster> ratioCategoryMasters = ratioCategoryMasterRepository.findAllByCompanyCode(companyCode);

        return ratioCategoryMasters.stream().map(this::mapToResponse).collect(Collectors.toList());
    }

    @Override
    public List<LookupDataModel> getAllCategoriesLookup(long companyCode) {
        return ratioCategoryMasterRepository.findDistinctByNameAndCompanyCode(companyCode).stream().map(ratioCategoryMaster -> {
            LookupDataModel viewModel = new LookupDataModel();
            viewModel.setId(ratioCategoryMaster.getId());
            viewModel.setValue(ratioCategoryMaster.getName());
            return viewModel;
        }).collect(Collectors.toList());
    }

    @Transactional
    @Override
    public void saveOrUpdateRatioCategory(long companyCode, RatioCategoryViewModel request) {
        RatioCategoryMaster ratioCategoryMaster = request.getId() != null
                ? ratioCategoryMasterRepository.findByCompanyCodeAndId(companyCode, request.getId())
                .orElseThrow(() -> new IllegalArgumentException("RatioCategoryMaster not found for ID: " + request.getId()))
                : new RatioCategoryMaster();

        // check
        if (request.getMasterJson() == null || request.getMasterJson().isEmpty()) {
            throw new DomainInvariantException("Invalid data - No masters are selected");
        }

        LocalDateTime now = LocalDateTime.now();
        if (ratioCategoryMaster.getId() == null) {
            logger.info("Creating ratio category master");
            String masterJson = MappersUtil.convertToJsonString(request.getMasterJson());
            boolean exists = ratioCategoryMasterRepository.existsByNameAndMasterJsonAndCompanyCode(
                    request.getName(),
                    masterJson,
                    companyCode
            );

            if (exists) {
                logger.info("Ratio category master for given combination value is already present");
                throw new DomainInvariantException("Mapping already exists, please select another criteria for mapping");
            }

            Optional<RatioCategoryMaster> existingMasterOptional =
                    ratioCategoryMasterRepository.findFirstByNameIgnoreCaseAndCompanyCode(request.getName(), companyCode);

            if (existingMasterOptional.isPresent()) {
                ratioCategoryMaster.setUuid(existingMasterOptional.get().getUuid());
            } else {
                ratioCategoryMaster.setUuid(UUID.randomUUID().toString());
            }

            ratioCategoryMaster.setName(request.getName());
            ratioCategoryMaster.setRatio(request.getRatio());

            ratioCategoryMaster.setMasterJson(masterJson);
            ratioCategoryMaster.setCompanyCode(companyCode);
            ratioCategoryMaster.setCreatedAt(now);
        } else {
            logger.info("Updating ratio category master");
            ratioCategoryMaster.setRatio(request.getRatio());
            ratioCategoryMaster.setUpdatedAt(now);
        }

        logger.info("Saving Ratio Category");
        ratioCategoryMasterRepository.saveAndFlush(ratioCategoryMaster);

    }

    @Override
    @Transactional
    public void deleteRatioCategory(long companyCode, Long id) {
        try {
            ratioCategoryMasterRepository.deleteByCompanyCodeAndId(companyCode, id);
        } catch (Exception ex) {
            logger.error("Exception occurred on deleteRatioCategory for companyCode {} and id {}", companyCode, id);
        }
    }

    @Override
    @Transactional
    public void deleteRatioCategoryConfig(long companyCode) {
        try {
            logger.info("deleting all ratio category masters");
            ratioCategoryMasterRepository.deleteAllByCompanyCode(companyCode);

            logger.info("deleting ratio category configurations");
            ratioCategoryConfigRepository.deleteAllByCompanyCode(companyCode);

        } catch (Exception ex) {
            logger.error("Exception occurred on deleteRatioCategoryConfig for companyCode {}", companyCode);
        }

    }

    @Override
    public void saveSegmentRatio(long companyCode, SegmentRatioToEntityRequest request) {
        logger.info("get segment ratio for document type {} and entity id {}", DocumentType.getType(request.getDocumentTypeId()), request.getEntityId());
        MappingSegmentRatioToEntity segmentRatio = mappingSegmentRatioToEntityRepository.findByDocumentTypeIdAndEntityId(request.getDocumentTypeId(), request.getEntityId()).orElse(new MappingSegmentRatioToEntity());

        segmentRatio.setEntityId(request.getEntityId());
        segmentRatio.setDocumentTypeId(request.getDocumentTypeId());
        segmentRatio.setRatioDistributionJson(MappersUtil.convertToJsonString(request.getData()));
        if (segmentRatio.getId() == null) segmentRatio.setCreatedAt(LocalDateTime.now());
        segmentRatio.setUpdatedAt(LocalDateTime.now());

        logger.info("saving segmentRatio");
        mappingSegmentRatioToEntityRepository.saveAndFlush(segmentRatio);
    }

    @Override
    public SegmentRatioViewModel getSegmentRatio(long companyCode, Integer documentTypeId, Long entityId, GenericListRequestViewModel<Long> entityIds) {
        SegmentRatioViewModel response = new SegmentRatioViewModel();
        ObjectMapper objectMapper = new ObjectMapper();
        boolean isPoBased = entityIds != null && !entityIds.getIds().isEmpty();
        if (!isPoBased) {
            return getSegmentRatioViewModel(documentTypeId, entityId, response, objectMapper);
        }
        // else part -- if previous document is present
        // fetch segment ratio_distribution_json for each entity ID
        // use PO doctype id
        List<MappingSegmentRatioToEntity> mappings = mappingSegmentRatioToEntityRepository.findByDocumentTypeIdAndEntityIds(DocumentType.PURCHASE_ORDER.ordinal() + 1, entityIds.getIds());
        if (mappings == null || mappings.isEmpty()) {
            logger.info("No segment ratio found for documentTypeId {} and entityIds {} ", documentTypeId, entityIds);
            // TODO: if no mappings found for given POids - return document segment ratio if present ?
            return getSegmentRatioViewModel(documentTypeId, entityId, response, objectMapper);
        }

        List<SegmentRatioViewModel> segmentRatioViewModels = mappings.stream()
                .map(mapping -> {
                    return getSegmentRatioViewModelFromJson(mapping, objectMapper);
                })
                .toList();

        // Validate all entities have the same ratio category
        String categoryName = segmentRatioViewModels.get(0).getRatioCategoryName();
        Integer categoryId = segmentRatioViewModels.get(0).getRatioCategoryId();
        for (SegmentRatioViewModel viewModel : segmentRatioViewModels) {
            if (!Objects.equals(viewModel.getRatioCategoryId(), categoryId) ||
                    !Objects.equals(viewModel.getRatioCategoryName(), categoryName)) {
                // unmap i.e. set categoryId as null
                categoryName = null;
                categoryId = null;
                break;
            }
        }

        // Aggregate segment details by masters
        Map<String, SegmentDetail> aggregatedDetails = new HashMap<>();
        BigDecimal totalAmountAcrossAllPOs = BigDecimal.ZERO;

        for (SegmentRatioViewModel viewModel : segmentRatioViewModels) {
            for (SegmentDetail detail : viewModel.getSegmentDetails()) {
                String masterJson = MappersUtil.convertToJsonString(detail.getMasters());

                SegmentDetail existingDetail = aggregatedDetails.get(masterJson);

                if (existingDetail == null) {
                    // Add a new entry for unique masters combination
                    SegmentDetail newDetail = new SegmentDetail();
                    newDetail.setMasters(detail.getMasters());
                    newDetail.setAmount(detail.getAmount());
                    newDetail.setRatio(detail.getRatio());
                    aggregatedDetails.put(masterJson, newDetail);
                } else {
                    // Aggregate amounts for matching masters
                    existingDetail.setAmount(existingDetail.getAmount().add(detail.getAmount()));

                    // If ratios differ, reset and recalculate later
                    if (!Objects.equals(existingDetail.getRatio(), detail.getRatio())) {
                        existingDetail.setRatio(null); // Mark as needing recalculation
                    }
                }

                // Sum total amounts for recalculating ratios
                totalAmountAcrossAllPOs = totalAmountAcrossAllPOs.add(detail.getAmount());
            }
        }

        // Recalculate ratios where needed
        // when ratio is different for similar masters combination, (in such case ratio is set as null in above steps) (sum of segments amount is divided by total of POs amount) * 100 then new ratio is calculated
        for (SegmentDetail detail : aggregatedDetails.values()) {
            if (detail.getRatio() == null) {
                BigDecimal newRatio = detail.getAmount()
                        .divide(totalAmountAcrossAllPOs, MathContext.DECIMAL128)
                        .multiply(BigDecimal.valueOf(100))
                        .setScale(2, RoundingMode.HALF_UP);
                ;
                detail.setRatio(newRatio.doubleValue());
            }
        }
        // Adjust ratio to 100% in case not - TODO : uncomment after test
        adjustRatiosTo100Percent(totalAmountAcrossAllPOs, aggregatedDetails);
        // Build the final consolidated response
        SegmentRatioViewModel consolidatedResponse = new SegmentRatioViewModel();
        consolidatedResponse.setRatioCategoryId(categoryId);
        consolidatedResponse.setRatioCategoryName(categoryName);
        consolidatedResponse.setSegmentDetails(new ArrayList<>(aggregatedDetails.values()));

        return consolidatedResponse;
    }

    private SegmentRatioViewModel getSegmentRatioViewModel(Integer documentTypeId, Long entityId, SegmentRatioViewModel response, ObjectMapper objectMapper) {
        MappingSegmentRatioToEntity mapping = mappingSegmentRatioToEntityRepository.findByDocumentTypeIdAndEntityId(documentTypeId, entityId).orElse(null);
        if (mapping == null) {
            logger.info("No segment ratio found for documentTypeId {} and entity {} ", documentTypeId, entityId);
            return response;
        }
        return getSegmentRatioViewModelFromJson(mapping, objectMapper);
    }

    private static SegmentRatioViewModel getSegmentRatioViewModelFromJson(MappingSegmentRatioToEntity mapping, ObjectMapper objectMapper) {
        try {
            return objectMapper.readValue(mapping.getRatioDistributionJson(), SegmentRatioViewModel.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error deserializing ratio_distribution_json", e);
        }
    }

    @Override
    public SegmentRatioViewModel getRatioCategoryDetails(long companyCode, Integer id) {
        logger.info("Get ratio category data by id {}", id);
        RatioCategoryMaster ratioCategoryMaster = ratioCategoryMasterRepository.findByCompanyCodeAndId(companyCode, id).orElseThrow(() -> new IllegalArgumentException("RatioCategoryMaster not found for ID: " + id));

        List<RatioCategoryMaster> ratioCategoryMasterList = ratioCategoryMasterRepository.findAllByCompanyCodeAndNameIgnoreCase(companyCode, ratioCategoryMaster.getName());

        return prepareRatioCategoryMasterDetails(id, ratioCategoryMaster.getName(), ratioCategoryMasterList);
    }

    private SegmentRatioViewModel prepareRatioCategoryMasterDetails(Integer id, String name, List<RatioCategoryMaster> ratioCategoryMasters) {
        SegmentRatioViewModel ratioViewModel = new SegmentRatioViewModel();
        List<SegmentDetail> segmentDetails = new ArrayList<>();
        ratioViewModel.setRatioCategoryId(id);
        ratioViewModel.setRatioCategoryName(name);

        BigDecimal totalRatio = BigDecimal.ZERO;

        for (RatioCategoryMaster ratioCategoryMaster : ratioCategoryMasters) {
            totalRatio = totalRatio.add(BigDecimal.valueOf(ratioCategoryMaster.getRatio()));
            segmentDetails.add(
                    new SegmentDetail(
                            ratioCategoryMaster.getRatio(),
                            parseMasters(ratioCategoryMaster.getMasterJson())
                    )
            );
        }
        // If the total ratio exceeds 100%, adjust the ratios proportionally
        if (totalRatio.compareTo(BigDecimal.valueOf(100)) > 0) {
            BigDecimal adjustmentFactor = BigDecimal.valueOf(100).divide(totalRatio, MathContext.DECIMAL128);

            for (SegmentDetail detail : segmentDetails) {
                BigDecimal adjustedRatio = BigDecimal.valueOf(detail.getRatio())
                        .multiply(adjustmentFactor)
                        .setScale(2, RoundingMode.HALF_UP);
                detail.setRatio(adjustedRatio.doubleValue());
            }
        }
        ratioViewModel.setSegmentDetails(segmentDetails);
        return ratioViewModel;
    }

    private RatioCategoryViewModel mapToResponse(RatioCategoryMaster ratioCategoryMasters) {
        RatioCategoryViewModel response = new RatioCategoryViewModel();
        response.setId(ratioCategoryMasters.getId());
        response.setName(ratioCategoryMasters.getName());
        response.setRatio(ratioCategoryMasters.getRatio());
        response.setMasterJson(parseMasters(ratioCategoryMasters.getMasterJson()));
        response.setCreatedAt(DateTimeUtils.formatDateJPQL(ratioCategoryMasters.getCreatedAt()));
        response.setUpdatedAt(DateTimeUtils.formatDateJPQL(ratioCategoryMasters.getUpdatedAt()));
        return response;
    }

    private List<SegmentRatioData> parseMasters(String masterJson) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode node = objectMapper.readTree(masterJson);
            return objectMapper.convertValue(node, new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to parse masterJson", e);
        }
    }

    public void adjustRatiosTo100Percent(BigDecimal totalAmountAcrossAllPOs, Map<String, SegmentDetail> aggregatedDetails) {
        // Calculate total ratio
        BigDecimal totalRatio = aggregatedDetails.values()
                .stream()
                .map(SegmentDetail::getRatio)
                .filter(Objects::nonNull)
                .map(BigDecimal::valueOf)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalRatio.compareTo(BigDecimal.valueOf(100)) > 0) {
            // Calculate excess ratio
            BigDecimal excessRatio = totalRatio.subtract(BigDecimal.valueOf(100));

            // Distribute the excess ratio proportionally
            BigDecimal finalTotalRatio = BigDecimal.ZERO;
            for (SegmentDetail detail : aggregatedDetails.values()) {
                BigDecimal currentRatio = BigDecimal.valueOf(detail.getRatio());
                BigDecimal adjustment = currentRatio.divide(totalRatio, MathContext.DECIMAL128)
                        .multiply(excessRatio)
                        .setScale(2, RoundingMode.HALF_UP);
                BigDecimal newRatio = currentRatio.subtract(adjustment).setScale(2, RoundingMode.HALF_UP);
                detail.setRatio(newRatio.doubleValue());
                finalTotalRatio = finalTotalRatio.add(newRatio);
            }
            // Handle potential rounding issues to ensure exact 100%
            if (finalTotalRatio.compareTo(BigDecimal.valueOf(100)) != 0) {
                BigDecimal roundingDifference = BigDecimal.valueOf(100).subtract(finalTotalRatio);
                for (SegmentDetail detail : aggregatedDetails.values()) {
                    BigDecimal currentRatio = BigDecimal.valueOf(detail.getRatio());
                    // Skip to the next segment if the current segment's ratio is less than the rounding difference
                    if (currentRatio.compareTo(roundingDifference.abs()) < 0) {
                        continue;
                    }
                    // Apply the adjustment to the current segment
                    detail.setRatio(currentRatio.add(roundingDifference).setScale(2, RoundingMode.HALF_UP).doubleValue());
                    break;
                }
            }
        }
    }

    @Override
    public boolean isTotalRatioEqualsTo100(MappingSegmentRatioToEntity entity) {
        if (entity == null)
            return true; // in case of no segment ratio present // eg. for PO it is not mandatory.
        try {
            // Parse the ratioDistributionJson field
            ObjectMapper objectMapper = new ObjectMapper();
            SegmentRatioViewModel ratioViewModel = objectMapper.readValue(
                    entity.getRatioDistributionJson(),
                    SegmentRatioViewModel.class
            );

            BigDecimal totalRatio = ratioViewModel.getSegmentDetails()
                    .stream()
                    .map(SegmentDetail::getRatio)
                    .map(BigDecimal::valueOf)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);

            // Compare total ratio with 100 // if the two values are equal then compareTo returns 0
            return totalRatio.compareTo(BigDecimal.valueOf(100)) == 0;
        } catch (Exception e) {
            logger.error("Exception occurred in validate segment ratio");
            throw new RuntimeException("Error calculating total ratio", e.getCause());
        }
    }
}
