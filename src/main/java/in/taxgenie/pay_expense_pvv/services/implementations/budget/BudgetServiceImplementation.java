package in.taxgenie.pay_expense_pvv.services.implementations.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.budget.*;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.factory.budgetaction.Action;
import in.taxgenie.pay_expense_pvv.factory.budgetaction.CommandFactory;
import in.taxgenie.pay_expense_pvv.factory.budgetaction.DocumentData;
import in.taxgenie.pay_expense_pvv.factory.budgetaction.FactoryResponse;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentMetadataRepository;
import in.taxgenie.pay_expense_pvv.repositories.ILookupRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.*;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomBudgetRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.ILookupService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetStepperService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.budget.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ValidationStatusViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.*;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetAddLessViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetTreeViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels.BudgetSyncToBudgetMappingMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels.BudgetToBudgetMappingMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels.BudgetTotalsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels.MonthFinancialYearModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.mappingmaster.BudgetMappingCreateViewModelWrapper;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.mappingmaster.BudgetMappingMasterCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.structuremaster.BudgetStructureMasterCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.structuremaster.BudgetStructureMasterViewModel;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BudgetServiceImplementation implements IBudgetService {

    private final IBudgetRepository repository;
    private final IDocumentApprovalContainerRepository reportRepository;
    private final ILookupRepository lookupRepository;
    private final IDocumentMetadataRepository metadataRepository;
    private final CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository;
    private final IBudgetSyncMasterRepository budgetSyncMasterRepository;
    private final IBudgetFrequencyMappingRepository budgetFrequencyMappingRepository;
    private final IBudgetStructureMasterRepository budgetStructureMasterRepository;
    private final IBudgetMasterRepository budgetMasterRepository;
    private final CustomBudgetRepository customBudgetRepository;
    private final IDocumentApprovalContainerRepository documentApprovalContainerRepository;
    private final ILookupService lookupService;
    private final IBudgetStepperService budgetStepperService;
    private final BudgetFrequencyMappingServiceImplementation budgetFrequencyMappingService;
    private final Logger logger;

    public BudgetServiceImplementation(IBudgetRepository repository, IDocumentApprovalContainerRepository reportRepository, ILookupRepository lookupRepository, IDocumentMetadataRepository metadataRepository, CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository,
                                       IBudgetSyncMasterRepository budgetSyncMasterRepository, IBudgetFrequencyMappingRepository budgetFrequencyMappingRepository, IBudgetStructureMasterRepository budgetStructureMasterRepository,
                                       IBudgetMasterRepository budgetMasterRepository,
                                       CustomBudgetRepository customBudgetRepository, IDocumentApprovalContainerRepository documentApprovalContainerRepository,
                                       ILookupService lookupService,
                                       IBudgetStepperService budgetStepperService, BudgetFrequencyMappingServiceImplementation budgetFrequencyMappingService) {

        this.repository = repository;
        this.reportRepository = reportRepository;
        this.lookupRepository = lookupRepository;
        this.metadataRepository = metadataRepository;
        this.customDocumentApprovalContainerRepository = customDocumentApprovalContainerRepository;
        this.budgetSyncMasterRepository = budgetSyncMasterRepository;
        this.budgetFrequencyMappingRepository = budgetFrequencyMappingRepository;
        this.budgetStructureMasterRepository = budgetStructureMasterRepository;
        this.budgetMasterRepository = budgetMasterRepository;
        this.customBudgetRepository = customBudgetRepository;
        this.documentApprovalContainerRepository = documentApprovalContainerRepository;
        this.lookupService = lookupService;
        this.budgetStepperService = budgetStepperService;
        this.budgetFrequencyMappingService = budgetFrequencyMappingService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }


    private BudgetMappingMaster createBudgetMappingMasterTree(BudgetMappingCreateViewModelWrapper node,
                                                              BudgetMappingMaster parent,
                                                              long structureId, BudgetStructureMaster structureMaster,
                                                              IAuthContextViewModel auth, Map<Long, BudgetMaster> budgetMasterMap,
                                                              int level, Set<Long> parentMasters) {
        if (node == null) {
            return null;
        }

        BudgetMappingMaster mappingMaster = createNewMappingMaster(node.getData(), parent, budgetMasterMap, auth, level, structureId, structureMaster);
        Long budgetMasterId = mappingMaster.getBudgetMasterId();

        if (parentMasters.contains(budgetMasterId)) {
            logger.info("createBudgetMappingMasters: The child node {} is already in a parent of the current node", node);
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_MAPPING_CREATION_ERROR);
        }

        parentMasters.add(budgetMasterId);

        // Increment the level for the all children below
        level++;
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (BudgetMappingCreateViewModelWrapper child : node.getChildren()) {
                BudgetMappingMaster childNode = createBudgetMappingMasterTree(child, mappingMaster, structureId, structureMaster, auth, budgetMasterMap, level, parentMasters);
                mappingMaster.addChildren(childNode);
            }
        }

        parentMasters.remove(budgetMasterId);
        return mappingMaster;
    }

    private BudgetMappingMaster createNewMappingMaster(BudgetMappingMasterCreateViewModel viewModel,
                                                       BudgetMappingMaster parent,
                                                       Map<Long, BudgetMaster> budgetMasterMap, IAuthContextViewModel auth,
                                                       int level, long structureId, BudgetStructureMaster structureMaster) {
        BudgetMappingMaster mappingMaster = new BudgetMappingMaster();

        // PreWork
        BudgetMaster budgetMaster = budgetMasterMap.get(viewModel.getBudgetMasterId());
        if (budgetMaster == null || !budgetMasterMap.containsKey(viewModel.getBudgetMasterId())) {
            logger.info("populateBudgetMappingMasters: The provided budget master mapping id: {} does not exists for company: {}", viewModel.getBudgetMasterId(), auth.getCompanyCode());
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_MAPPING_CREATION_ERROR);
        }

        // Set base attributes
        mappingMaster.setCompanyCode(auth.getCompanyCode());
        mappingMaster.setCreatingUserId(auth.getUserId());
        mappingMaster.setActive(true);
        mappingMaster.setUuid(UUID.randomUUID());

        // Set Budget Mapping details
        mappingMaster.setDisplayName(viewModel.getDisplayName() == null ? budgetMaster.getName() : viewModel.getDisplayName());
        mappingMaster.setLevel(level);

        // Set Budget Structure mapping
        mappingMaster.setBudgetStructureMaster(structureMaster);
        mappingMaster.setBudgetStructureMasterId(structureId);

        // Set Budget Master mapping
        mappingMaster.setBudgetMaster(budgetMaster);
        mappingMaster.setBudgetMasterId(viewModel.getBudgetMasterId());

        // Set parent
        mappingMaster.setParent(parent);

        return mappingMaster;
    }

    @Override
    @Transactional
    public BudgetViewModel create(BudgetViewModel viewModel, long structureId, IAuthContextViewModel auth) {

        Map<Long, BudgetSyncMaster> budgetSyncMasterMap = generateBudgetSyncMasterMap(auth.getCompanyCode());
        Budget masterBudget = createBudgetTree(viewModel, null, auth.getCompanyCode(), auth.getUserId(), budgetSyncMasterMap, new HashSet<>());

        repository.saveAndFlush(masterBudget);

        return getViewModel(masterBudget);
    }

    @Override
    public BudgetNodeUpdateViewModel updateBudgetNode(BudgetNodeUpdateViewModel viewModel, IAuthContextViewModel authContextViewModel) {
        Budget budget = repository.findByCompanyCodeAndIdAndIsActiveTrue(authContextViewModel.getCompanyCode(), viewModel.getId())
                .orElseThrow(() -> new DomainInvariantException("This budget node could not be found."));

        // Handle input validation here
        setEntityData(budget, viewModel.getData());
        handleBudgetNodeValidation(budget);

        repository.saveAndFlush(budget);

        return null;
    }

    @Transactional
    @Override
    public BudgetViewModel updateBudgetNodes(MultiBudgetNodeUpdateViewModel multiBudgetNodeUpdateViewModel, long structureId, IAuthContextViewModel auth) {
        List<BudgetNodeUpdateViewModel> viewModels = multiBudgetNodeUpdateViewModel.getNodes();
        Map<Long, Budget> budgetMap = validateAndGetBudgetMap(viewModels, auth.getCompanyCode());

        for (BudgetNodeUpdateViewModel viewModel : viewModels) {
            Budget budget = budgetMap.get(viewModel.getId());
            setEntityData(budget, viewModel.getData());
            handleBudgetNodeValidation(budget);
        }

        repository.saveAllAndFlush(budgetMap.values());

        List<Budget> availableRoots = repository.findByCompanyCodeAndBudgetStructureMasterIdAndParentIsNullAndIsActiveTrue(auth.getCompanyCode(), structureId);

        if (availableRoots.isEmpty()) {
            throw new DomainInvariantException(String.format("Cannot find the org tree for the structure with id: %d", structureId));
        } else if (availableRoots.size() > 1) {
            throw new DomainInvariantException(String.format("Multiple org trees found for structure id: %d", structureId));
        }

        Budget root = availableRoots.get(0);

        return getViewModel(root);
    }

    @Override
    @Transactional
    public BudgetTreeViewModel createTree(BudgetCreateViewModel viewModel, long structureId, Boolean resetStages, IAuthContextViewModel auth) {

        if (Boolean.TRUE.equals(resetStages)) {
            budgetStepperService.initiateReset(StaticDataRegistry.SCREEN_THREE, structureId, auth);
        }
        budgetStepperService.setLockScrren(resetStages, StaticDataRegistry.SCREEN_THREE, structureId, auth);

        // Todo: Abstract this to common function.
        Map<Long, BudgetSyncMaster> budgetSyncMasterMap = generateBudgetSyncMasterMap(auth.getCompanyCode());
        Map<Long, BudgetSyncToBudgetMappingMasterViewModel> map = generateBudgetSyncToMasterMap(auth.getCompanyCode(), budgetSyncMasterMap.keySet());


        BudgetStructureMaster structureMaster = budgetStructureMasterRepository.findByCompanyCodeAndIdAndIsActive(auth.getCompanyCode(), structureId, true)
                .orElseThrow(() -> {
                    logger.info(String.format("createTree: An active structureId: %d could not be found for company: %d ", structureId, auth.getCompanyCode()));
                    return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_CREATION_ERROR);
                });
        Budget root = createBudgetTreeNoAmounts(viewModel, null, structureMaster, auth, budgetSyncMasterMap, new HashSet<>(), resetStages);
        repository.saveAndFlush(root);
        Map<Long, BudgetToBudgetMappingMasterViewModel> budgetToMappingMaster = generateBudgetToBudgetMasterMap(auth.getCompanyCode(), structureId);

        // Stepper 3 - Budget tree created and updated time set to structureMaster - to show updated at on budget queue
        structureMaster.setUpdatedTimestamp(DateTimeUtils.getCurrentZonedDateTime());
        documentApprovalContainerRepository.updateUpdatedTimestampByStructureMasterId(structureMaster.getUpdatedTimestamp(), structureMaster.getId());

        return root.createTreeViewModel(root, map, budgetToMappingMaster);
    }


    @Override
    public BudgetTreeViewModel getTree(long structureId, IAuthContextViewModel auth) {
        List<Budget> availableRoots = repository.findByCompanyCodeAndBudgetStructureMasterIdAndParentIsNullAndIsActiveTrue(auth.getCompanyCode(), structureId);
        Map<Long, BudgetSyncMaster> budgetSyncMasterMap = generateBudgetSyncMasterMap(auth.getCompanyCode());
        Map<Long, BudgetSyncToBudgetMappingMasterViewModel> map = generateBudgetSyncToMasterMap(auth.getCompanyCode(), budgetSyncMasterMap.keySet());
        Map<Long, BudgetToBudgetMappingMasterViewModel> budgetToMappingMaster = generateBudgetToBudgetMasterMap(auth.getCompanyCode(), structureId); //
        // Sanity Checks
        if (availableRoots.size() > 1) {
            throw new DomainInvariantException(String.format("Multiple org trees found for structure id: %d", structureId));
        }
        // If no root found, then not yet created so return empty object.
        if (availableRoots.isEmpty()) {
            logger.info(String.format("getTree: Cannot find the root for the budget tree with id: %d for company: %s. This may be a new mapping init", structureId, auth.getCompanyCode()));
            return null;
        }

        Budget root = availableRoots.get(0);
        return root.createTreeViewModel(root, map, budgetToMappingMaster);
    }

    public BudgetContainerViewModel getBudgetDetailsByDocumentContainerId(IAuthContextViewModel auth, long id) {

        // Business logic start
        LookupData budgetLookup = lookupRepository.findByTypeAndValue(in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry.LOOKUP_VALUE_BUDGET)
                .orElseThrow(() -> {
                            logger.error(String.format("getBudgetQueue: Lookup table error. Could not find document of type: %s and value: %s", in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry.LOOKUP_VALUE_PR));
                            return new RecordNotFoundException(in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry.ERROR_MESSAGE_PR_NOT_FOUND);
                        }
                );
        ;
        // Get all metadata that lookup type
        List<Long> documentMetadataArray = metadataRepository
                .findByCompanyCodeAndDocumentCategoryIdOrderByIdDesc(
                        auth.getCompanyCode(),
                        budgetLookup.getId()
                ).stream()
                .map(DocumentMetadata::getId)
                .distinct()
                .toList();

        BudgetContainerViewModel budgetContainerViewModel = customDocumentApprovalContainerRepository
                .getBudgetQueue(auth.getCompanyCode(), auth.getUserId(), id);

        Budget root = budgetFrequencyMappingService.getRootNodeForStructure(budgetContainerViewModel.getStructureId(), auth.getCompanyCode());
        int numberOfIntervals = root.getBudgetFrequencyMappings().size();
        LookupData lookupData = budgetFrequencyMappingService.getIntervalName(numberOfIntervals);
        budgetContainerViewModel.setBudgetFrequency(lookupData.getValue());

        return budgetContainerViewModel;

    }

    @Override
    public void checkBudgetCodeUnique(Long structureId, String budgetCode, IAuthContextViewModel auth) {

        Optional<Budget> budgetOptional = repository.findByCompanyCodeAndBudgetCodeAndBudgetStructureMasterIdAndIsActiveTrue(auth.getCompanyCode(),
                budgetCode, structureId);

        if (budgetOptional.isPresent()) {
            throw new DomainInvariantException("Budget code is not unique");
        }
    }

    @Override
    public BudgetAddLessViewModel getAddLessBudgetData(Long structureId, IAuthContextViewModel auth) {
        BudgetAddLessViewModel budgetAddLessViewModel = new BudgetAddLessViewModel();

        Optional<BudgetStructureMaster> budgetStructureMasterOptional = budgetStructureMasterRepository.findById(structureId);

        if (budgetStructureMasterOptional.isPresent()) {
            BudgetStructureMaster budgetStructureMaster = budgetStructureMasterOptional.get();
            budgetAddLessViewModel = budgetStructureMaster.getAddLessViewModel();
            budgetAddLessViewModel.setExpenditureType(budgetStructureMaster.getDocumentMetadata().getApplicableExpenseType().name());
            return budgetAddLessViewModel;
        } else {
            logger.info(String.format("getAddLessBudgetData: The StructureMaster with id: %d could not found.", structureId));
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        }
    }

    public BudgetAddLessViewModel getAddLessBudgetDataByDocumentContainerId(Long documentContainerId, IAuthContextViewModel auth) {
        BudgetAddLessViewModel budgetAddLessViewModel = new BudgetAddLessViewModel();

        DocumentApprovalContainer documentApprovalContainer = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), documentContainerId)
                .orElseThrow(() -> {
                    logger.error(String.format("getByDocumentContainerId: DocumentApprovalContainer with id: {%s} could not be found.", documentContainerId));
                    return new DomainInvariantException(in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry.ERROR_MESSAGE_PO_NOT_FOUND);
                });

        Document document = documentApprovalContainer.getDocuments().get(0);
        BudgetDocumentAction budgetDocumentAction = document.getBudgetDocumentAction();
        Budget budget = budgetDocumentAction.getBudget();
        Long structureId = budget.getBudgetStructureMasterId();
        Optional<BudgetStructureMaster> budgetStructureMasterOptional = budgetStructureMasterRepository.findById(structureId);

        if (budgetStructureMasterOptional.isPresent()) {
            BudgetStructureMaster budgetStructureMaster = budgetStructureMasterOptional.get();

            budgetAddLessViewModel = budgetStructureMaster.getAddLessViewModel();
            budgetAddLessViewModel.setId(documentApprovalContainer.getId());
            budgetAddLessViewModel.setExpenditureType(budgetStructureMaster.getDocumentMetadata().getApplicableExpenseType().name());

            budgetAddLessViewModel.setBudgetId(budget.getId());
            budgetAddLessViewModel.setDocumentMetadataId(documentApprovalContainer.getDocumentMetadataId());
            budgetAddLessViewModel.setReason(document.getDescription());
            budgetAddLessViewModel.setAddLessAmount(document.getClaimAmount());
            budgetAddLessViewModel.setAmountAvailable(budgetDocumentAction.getTotalAmount());
            return budgetAddLessViewModel;
        } else {
            logger.info(String.format("getAddLessBudgetData: The StructureMaster with id: %d could not found.", structureId));
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        }
    }

    private Map<Long, BudgetToBudgetMappingMasterViewModel> generateBudgetToBudgetMasterMap(long companyCode, long structureId) {
        HashMap<Long, BudgetToBudgetMappingMasterViewModel> linkMap = new HashMap<>();
        customBudgetRepository.findBudgetMappingMastersForBudget(companyCode, structureId).forEach(i -> {
            linkMap.put(i.getBudgetId(), i);
        });
        return linkMap;
    }

    private Budget createBudgetTreeNoAmounts(BudgetCreateViewModel node, Budget parent, BudgetStructureMaster structureMaster, IAuthContextViewModel auth,
                                             Map<Long, BudgetSyncMaster> budgetSyncMasterMap, Set<Long> parentDepartments, Boolean resetStages) {
        if (node == null) {
            return null;
        }

        //If resetStages is true then set budget id to null
        if (resetStages) {
            node.getData().setBudgetId(null);
        }

        // Data sanity checks
        BudgetSyncMaster budgetSyncMaster = budgetSyncMasterMap.get(node.getData().getBudgetSyncMasterId());
        if (budgetSyncMaster == null) {
            throw new DomainInvariantException("createBudgetEntry: The entry " + node.toString() + " did not have a valid budgetSyncMasterId: " + node.getData().getBudgetSyncMasterId());
        }
        Long budgetSyncMasterId = budgetSyncMaster.getId();
        if (parentDepartments.contains(budgetSyncMasterId)) {
            logger.info(String.format("createBudgetTree: The child node %s department is already in a parent of the current node", node.toString()));
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_CREATION_ERROR);
        } else {
            parentDepartments.add(budgetSyncMasterId);
        }

        // Data population
        Budget budget;
        if (null != node.getData().getBudgetId()) {
            budget = repository.findById(node.getData().getBudgetId()).orElseGet(Budget::new);
        } else {
            budget = new Budget();
        }
        boolean createNewZeroedBudgetFrequencyRow = node.getData().getBudgetId() == null;
        budget.setBudgetDetailsFromCreateTree(node.getData().getBudgetId(),
                budgetSyncMaster, budgetSyncMaster.getId(),
                parent, auth.getCompanyCode(), structureMaster);
        Budget budgetDB = repository.saveAndFlush(budget);

        if (structureMaster.getIndexFromBudgetStep() > StaticDataRegistry.SCREEN_THREE && createNewZeroedBudgetFrequencyRow) {
            createNewZeroedBudgetFrequencyRow(budgetDB, auth.getUserId(), structureMaster.getId());
        }

        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (BudgetCreateViewModel child : node.getChildren()) {
                budget.addChildren(createBudgetTreeNoAmounts(child, budget, structureMaster, auth, budgetSyncMasterMap, parentDepartments, resetStages));
            }
        }

        parentDepartments.remove(budgetSyncMasterId);
        return budget;
    }

    @Override
    public BudgetSelectionViewModel getBudgetNode(Long budgetId, IAuthContextViewModel auth) {
            if(budgetId == null) return null;
        BudgetSelectionViewModel budgetSelectionViewModel = repository.getBudgetNodeByStructure(auth.getCompanyCode(), budgetId).orElseThrow(() -> {
            logger.info(String.format("getBudgetNode: The node with id: %d could be found for company code: %d", budgetId, auth.getCompanyCode()));
            return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        });
        // TODO : re-visit after demo - check method usage and usecase
        Budget root = getRootNodeForStructure(budgetSelectionViewModel.getBudgetStructureMasterId(), auth.getCompanyCode());
        BudgetTotalsViewModel totals = customBudgetRepository.getBudgetTotals(auth.getCompanyCode(), budgetSelectionViewModel.getBudgetStructureMasterId(), root.getId());
        budgetSelectionViewModel.setTotal(totals.getTreasury());
        return budgetSelectionViewModel;
    }

    @Override
    public BudgetSelectionViewModel getBudgetNodeByStructureAndBudgetId(long structureId, long budgetId, IAuthContextViewModel auth) {
        BudgetSelectionViewModel budgetSelectionViewModel = repository.getBudgetNodeByStructure(auth.getCompanyCode(), budgetId).orElseThrow(() -> {
            logger.info(String.format("getBudgetNode: The node with id: %d could be found for company code: %d", budgetId, auth.getCompanyCode()));
            return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        });
        BudgetTotalsViewModel totals = customBudgetRepository.getBudgetTotals(auth.getCompanyCode(), structureId, budgetId);
        budgetSelectionViewModel.setTotal(totals.getTreasury());
        return budgetSelectionViewModel;
    }

    @Override
    public List<BudgetSelectionViewModel> getBudgetNodes(IAuthContextViewModel auth, Long structureId) {

        if (null != structureId) {
            return repository.getBudgetNodesByCompanyCodeAndStructureId(auth.getCompanyCode(), structureId);
        } else {
            return repository.getBudgetNodesByCompanyCode(auth.getCompanyCode());
        }
    }

    @Override
    public Long getIntervalValue(int startMonth, int endMonth, int intervalSize) {
        return (long) getInterval(startMonth, endMonth, intervalSize);
    }

    @Override
    public BudgetDetails getBudgetDetails(Long budgetId, IAuthContextViewModel auth) {
        logger.trace("getBudgetDetails: Returning details");
        Budget entity = repository
                .findByCompanyCodeAndIdAndIsActiveTrue(auth.getCompanyCode(), budgetId).orElseThrow(() -> {
                    logger.info(String.format("getBudgetDetails: The following budget id: %d could not be found", budgetId));
                    return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
                });
        return getBudgetDetails(entity);
    }

    @Transactional
    @Override
    public void createNewZeroedBudgetFrequencyRow(Budget node, long userId, long budgetStructureMasterId) {
        List<BudgetFrequencyMapping> budgetSheetRow = new ArrayList<>();
        List<Budget> budgetRoot = repository.findByCompanyCodeAndBudgetStructureMasterIdAndParentIsNullAndIsActiveTrue(node.getCompanyCode(), budgetStructureMasterId);
        Integer intervalCount = budgetFrequencyMappingRepository.getIntervalCount(budgetRoot.get(0).getCompanyCode(), node.getBudgetStructureMasterId(), budgetRoot.get(0).getId());
        for (int i = 0; i < intervalCount; i++) {
            budgetSheetRow.add(new BudgetFrequencyMapping(i + 1, node.getCompanyCode(), userId, budgetStructureMasterId, node.getId()));
        }
        budgetFrequencyMappingRepository.saveAllAndFlush(budgetSheetRow);
    }

    @Override
    public List<BudgetSelectionViewModel> getBudgetNodesInTree(IAuthContextViewModel auth, long budgetId, String documentType) {


        logger.info(String.format("getBudgetNodesInTree: Getting Budget budgetId: {%d}", budgetId));
        Budget budget = repository
                .findByCompanyCodeAndIdAndIsActiveTrue(auth.getCompanyCode(), budgetId).orElseThrow(() -> {
                    logger.info(String.format("getBudgetDetails: The following budget id: %d could not be found", budgetId));
                    return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
                });

        if (budget == null) {
            return Collections.emptyList();
        }
        List<Budget> budgetTree = getBudgetTree(budget);

        return budgetTree.stream().map(bg -> getBudgetDetailsWithAmountBasedOnDocumentType(bg, documentType)).collect(Collectors.toList());
    }

    public BigDecimal getBudgetFrequencyTotalAmount(IAuthContextViewModel auth, long budgetId, DocumentType documentType) {

        logger.info(String.format("getBudgetFrequencyTotalAmount: Getting Budget budgetId: {%d}", budgetId));
        Budget budget = repository
                .findByCompanyCodeAndIdAndIsActiveTrue(auth.getCompanyCode(), budgetId).orElseThrow(() -> {
                    logger.info(String.format("getBudgetDetails: The following budget id: %d could not be found", budgetId));
                    return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
                });

        BudgetFrequencyMapping bfm = budgetFrequencyMappingService.getCurrentFrequency(budget);
        if (bfm == null) {
            logger.info("getBudgetDetailsWithAmount: Current frequncy not found for budget");
            throw new DomainInvariantException("No frequency mapping is present for budget");
        }

        return getBudgetAmount(documentType, bfm);
    }

    private List<Budget> getBudgetTree(Budget budget) {
        List<Budget> budgets = new ArrayList<>();
        budgets.add(budget);
        if (budget.getChildren() != null) {
            for (Budget child : budget.getChildren()) {
                budgets.addAll(getBudgetTree(child));
            }
        }
        return budgets;
    }

    public Boolean handleBudgetConsumption(List<DocumentData> documentDataList) {

        List<FactoryResponse> factoryResponseList = new ArrayList<>();

        try {
            documentDataList.forEach(documentData -> {
                Action action = CommandFactory.createCommand(documentData);
                factoryResponseList.add(action.execute(documentData));
            });

        }catch (DomainInvariantException e) {
            throw new DomainInvariantException(e.getMessage());
        }
        catch (Exception e) {
            FactoryResponse factoryResponse = new FactoryResponse();
            factoryResponse.setSuccess(false);
            factoryResponse.setMessage("Exception while handle budget consumption. Exception Message : " + e.getMessage());
            factoryResponseList.add(factoryResponse);
        }


        if (factoryResponseList.stream().allMatch(FactoryResponse::getSuccess)) {
            factoryResponseList.forEach(factoryResponse -> {

                if(factoryResponse.getBudget() != null) {
                    repository.saveAndFlush(factoryResponse.getBudget());
                }

                if(factoryResponse.getBudgetFrequencyMapping() != null) {
                    budgetFrequencyMappingRepository.saveAndFlush(factoryResponse.getBudgetFrequencyMapping());
                }

                if(null != factoryResponse.getBaseBudget()) {
                    repository.saveAndFlush(factoryResponse.getBaseBudget());
                    budgetFrequencyMappingRepository.saveAndFlush(factoryResponse.getBaseBudgetFrequencyMapping());
                }
            });
            return true;
        } else {
            throw new DomainInvariantException("Failed to consume budget.");
        }

    }

    @Override
    public void addLessBudget(Long budgetId, BigDecimal amount, ActionType actionType) {

        Optional<Budget> budgetOptional = repository.findById(budgetId);

        if (budgetOptional.isPresent()) {
            Budget budget = budgetOptional.get();
            BudgetFrequencyMapping budgetFrequencyMapping = budgetFrequencyMappingService.getCurrentFrequency(budget);

            if (actionType.equals(ActionType.ADD)) {
                budget.setTreasury(budget.getTreasury().add(amount));
                budget.setTotal(budget.getTotal().add(amount));
                budgetFrequencyMapping.setTreasury(budgetFrequencyMapping.getTreasury().add(amount));
                budgetFrequencyMapping.setTotal(budgetFrequencyMapping.getTotal().add(amount));
            } else {
                //for less
                budget.setTreasury(budget.getTreasury().subtract(amount));
                budget.setTotal(budget.getTotal().subtract(amount));
                budgetFrequencyMapping.setTreasury(budgetFrequencyMapping.getTreasury().subtract(amount));
                budgetFrequencyMapping.setTotal(budgetFrequencyMapping.getTotal().subtract(amount));
            }

            budget.setBudgetLocked(false);
            repository.saveAndFlush(budget);
            budgetFrequencyMappingRepository.saveAndFlush(budgetFrequencyMapping);

            if (budget.getParent() != null) {
                handleAmount(budget.getParent(), amount, actionType); // Corrected typo
            }
        } else {
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR_NOT_FOUND);
        }
    }

    @Override
    public BudgetSelectionViewModel getBudgetNodeByBudgetNodeId(Long budgetId, IAuthContextViewModel auth) {

        BudgetSelectionViewModel budgetSelectionViewModel = repository.getBudgetNodeByStructure(auth.getCompanyCode(), budgetId).orElseThrow(() -> {
            logger.info(String.format("getBudgetNode: The node with id: %d could be found for company code: %d", budgetId, auth.getCompanyCode()));
            return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        });
        BudgetTotalsViewModel totals = customBudgetRepository.getBudgetTotalsForBudgetNode(auth.getCompanyCode(), budgetId);
        budgetSelectionViewModel.setTotal(totals.getTreasury());
        return budgetSelectionViewModel;
    }

    @Override
    public BudgetSelectionViewModel getBudgetNodeForCurrentFrequency(Long budgetId, IAuthContextViewModel auth) {
        Budget budget = repository.findById(budgetId).orElseThrow(() -> {
            logger.info(String.format("getBudgetNode: The node with id: %d could be found for company code: %d", budgetId, auth.getCompanyCode()));
            return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR_NOT_FOUND);
        });

//        Budget budget = repository.findByCompanyCodeAndIdAndIsActive(budgetId, auth.getCompanyCode(), true).orElseThrow(() -> {
//            logger.info(String.format("getBudgetNode: The node with id: %d could be found for company code: %d", budgetId, auth.getCompanyCode()));
//            return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR_NOT_FOUND);
//        });
//        Budget budget = repository.findByCompanyCodeAndIdAndIsActiveTrue(budgetId, auth.getCompanyCode()).orElseThrow(() -> {
//            logger.info(String.format("getBudgetNode: The node with id: %d could be found for company code: %d", budgetId, auth.getCompanyCode()));
//            return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR_NOT_FOUND);
//        });
        // TODO : recheck - if amount needs to be fetched document type
        return getBudgetDetailsWithAmount(budget);
    }

    void handleAmount(Budget budget, BigDecimal amount, ActionType actionType) {

        BudgetFrequencyMapping budgetFrequencyMapping = budgetFrequencyMappingService.getCurrentFrequency(budget);

        if (actionType.equals(ActionType.ADD)) {
            budget.setTotal(budget.getTotal().add(amount));
            budgetFrequencyMapping.setTotal(budgetFrequencyMapping.getTotal().add(amount));
        } else {
            budget.setTotal(budget.getTotal().subtract(amount));
            budgetFrequencyMapping.setTotal(budgetFrequencyMapping.getTotal().subtract(amount));
        }

        repository.saveAndFlush(budget);
        budgetFrequencyMappingRepository.saveAndFlush(budgetFrequencyMapping);

        if (budget.getParent() != null) {
            handleAmount(budget.getParent(), amount, actionType);
        }

    }

    private BudgetDetails getBudgetDetails(Budget entity) {
        BudgetDetails viewModel = new BudgetDetails();
        viewModel.setId(entity.getId());
        viewModel.setCode(entity.getBudgetCode());
        viewModel.setDescription(entity.getDescription());
        return viewModel;
    }

    private BudgetSelectionViewModel getBudgetDetailsWithAmountBasedOnDocumentType(Budget budget, String documentType) {
        BudgetSelectionViewModel viewModel = prepareBudgetDetails(budget);
        // get amount from current frequency of budget
        BudgetFrequencyMapping bfm = getCurrentFrequencyOfBudget(budget);

        //TODO: change next line when doc type comes from UI
        DocumentType docType = documentType.isEmpty() ? DocumentType.INVOICE : DocumentType.valueOf(documentType.toUpperCase());
        viewModel.setTotal(getBudgetAmount(docType, bfm));
        return viewModel;
    }

    private BudgetSelectionViewModel getBudgetDetailsWithAmount(Budget budget) {
        BudgetSelectionViewModel viewModel = prepareBudgetDetails(budget);
        BudgetFrequencyMapping bfm = getCurrentFrequencyOfBudget(budget);
        viewModel.setTotal(bfm.getAvailableAmount());
        return viewModel;
    }

    private BudgetSelectionViewModel prepareBudgetDetails(Budget budget) {
        BudgetSelectionViewModel viewModel = new BudgetSelectionViewModel();
        viewModel.setId(budget.getId());
        viewModel.setBudgetCode(budget.getBudgetCode());
        viewModel.setDescription(budget.getBudgetSyncMaster().getDescription());
        viewModel.setDisplayName(viewModel.getBudgetCode() + " - " + (null == viewModel.getDescription() ? "NA" : viewModel.getDescription()));
        viewModel.setBudgetLocked(budget.getBudgetLocked());
        return viewModel;
    }

    private BigDecimal getBudgetAmount(DocumentType documentType, BudgetFrequencyMapping bfm) {
        return bfm.getTreasury().subtract(bfm.getConsumed().add(bfm.getLocked()));
    }

    private BudgetFrequencyMapping getCurrentFrequencyOfBudget(Budget budget) {
        BudgetFrequencyMapping bfm = budgetFrequencyMappingService.getCurrentFrequency(budget);
        if (bfm == null) {
            logger.info("getBudgetDetailsWithAmount: Current frequncy not found for budget");
            throw new DomainInvariantException("No frequency mapping is present for budget");
        }
        return bfm;
    }

    private List<BudgetColumnsViewModel> generateBudgetSheetColumns(long structureId, long companyCode, int numberOfIntervals, int monthIntervalIncrementFactor) {
        // Initialise variables
        BudgetStructureMaster budgetStructureMaster = budgetStructureMasterRepository.findByCompanyCodeAndIdAndIsActive(companyCode, structureId, true)
                .orElseThrow(() -> {
                    logger.info(String.format("generateBudgetSheetColumns: The following structure id: %d could not be found", structureId));
                    return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
                });
        // Struct : 2024-2025 -> [2024, 2025]
        int[] finYearArray;
        try {
            finYearArray = DateTimeUtils.parseFinancialYearForBudget(budgetStructureMaster.getFinancialYear());
        } catch (IllegalArgumentException e) {
            logger.info(String.format("generateBudgetSheetColumns: There was an issue parsing the date\t%s", e.getMessage()));
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        } catch (Exception e) {
            logger.info(String.format("generateBudgetSheetColumns: There was an unhandled exception\t%s", e.getMessage()));
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        }

        return getBudgetColumnsViewModels(numberOfIntervals, monthIntervalIncrementFactor, budgetStructureMaster, finYearArray);
    }

    private static List<BudgetColumnsViewModel> getBudgetColumnsViewModels(int numberOfIntervals, int monthIntervalIncrementFactor, BudgetStructureMaster budgetStructureMaster, int[] finYearArray) {
        // Note: This is because for monthly intervals the col names are singular months (e.g. jan2024, feb2024) else in all other cases col names will display with
        // hypen in between e.g (nov2024-jan2025).
        if (numberOfIntervals == StaticDataRegistry.YEAR_LENGTH) {
            return createMonthlyList(numberOfIntervals, budgetStructureMaster, finYearArray);
        } else {
            return createRangedIntervalList(numberOfIntervals, monthIntervalIncrementFactor, budgetStructureMaster, finYearArray);
        }
    }

    private static List<BudgetColumnsViewModel> createRangedIntervalList(int numberOfIntervals, int monthInterval, BudgetStructureMaster budgetStructureMaster, int[] finYearArray) {
        List<BudgetColumnsViewModel> budgetColumnsViewModels = new ArrayList<>();

        // Population logic : Initalise looping index to provided start month or default to 0.
        int monthIndex = budgetStructureMaster.getStartMonthIndex() == null ? 0 : budgetStructureMaster.getStartMonthIndex();
        MonthFinancialYearModel monthStart = new MonthFinancialYearModel(monthIndex, finYearArray[0]);
        MonthFinancialYearModel monthEnd = monthStart.add(monthInterval);

        String headerName = null;
        for (int i = 0; i < numberOfIntervals; i++) {
            BudgetColumnsViewModel budgetColumnsViewModel = new BudgetColumnsViewModel();
            headerName = String.format("%s%d - %s%d", monthStart.getMonthInclusive().getMonthName(), monthStart.getMonthInclusive().getYear(), monthEnd.getMonthExclusive().getMonthName(), monthEnd.getMonthExclusive().getYear());

            // For month get from the starting index
            budgetColumnsViewModel.setHeader(headerName);
            // For key Values pairs
            budgetColumnsViewModel.setKey(StaticDataRegistry.KEY_VALUE_BASE + (i + StaticDataRegistry.KEY_VALUE_INCREMENT_FACTOR));
            budgetColumnsViewModels.add(budgetColumnsViewModel);

            // Increment both
            monthStart = monthStart.add(monthInterval);
            monthEnd = monthEnd.add(monthInterval);
        }
        return budgetColumnsViewModels;
    }

    private static List<BudgetColumnsViewModel> createMonthlyList(int numberOfIntervals, BudgetStructureMaster budgetStructureMaster, int[] finYearArray) {
        List<BudgetColumnsViewModel> budgetColumnsViewModels = new ArrayList<>();

        // Population logic : Initalise looping index to provided start month or default to 0.
        int monthIndex = budgetStructureMaster.getStartMonthIndex() == null ? 0 : budgetStructureMaster.getStartMonthIndex();
        MonthFinancialYearModel monthStart = new MonthFinancialYearModel(monthIndex, finYearArray[0]);

        String headerName = null;
        for (int i = 0; i < numberOfIntervals; i++) {
            BudgetColumnsViewModel budgetColumnsViewModel = new BudgetColumnsViewModel();
            headerName = String.format("%s%d", monthStart.getMonthInclusive().getMonthName(), monthStart.getMonthInclusive().getYear());

            // For month get from the starting index
            budgetColumnsViewModel.setHeader(headerName);
            // For key Values pairs
            budgetColumnsViewModel.setKey(StaticDataRegistry.KEY_VALUE_BASE + (i + StaticDataRegistry.KEY_VALUE_INCREMENT_FACTOR));
            budgetColumnsViewModels.add(budgetColumnsViewModel);
            monthStart = monthStart.add(StaticDataRegistry.MONTH_INCREMENT_FACTOR);
        }
        return budgetColumnsViewModels;
    }

    private Budget getRootNodeForStructure(long structureId, long companyCode) {
        List<Budget> availableRoots = repository.findByCompanyCodeAndBudgetStructureMasterIdAndParentIsNullAndIsActiveTrue(companyCode, structureId);
        if (availableRoots.isEmpty()) {
            logger.error(String.format("initaliseFrequencyMapping: Budget for structure id: %d could not be found", structureId));
            throw new DomainInvariantException("The requested org budget tree could not be found.");
        } else if (availableRoots.size() > 1) {
            logger.error(String.format("Multiple org trees found for structure id: %d", structureId));
            throw new DomainInvariantException("The requested org budget tree could not be found.");
        }

        return availableRoots.get(0);
    }

    private void performCheckAndWipePreviousFrequencyMap(long structureId, long companyId) {
        if (!isStructureIsEditable(structureId, companyId)) {
            throw new DomainInvariantException("This structures budget is in use, cannot re-initialise.");
        }
        List<BudgetFrequencyMapping> budgetFrequencyMappingsToClean = budgetFrequencyMappingRepository.findByCompanyCodeAndBudgetStructureMasterIdAndIsActiveTrue(companyId, structureId);
        budgetFrequencyMappingRepository.deleteAll(budgetFrequencyMappingsToClean);
    }

    // Todo : Add logic to check published option when complete
    private boolean isStructureIsEditable(long structureId, long companyId) {
        return true;
    }

    private Map<Long, BudgetSyncMaster> generateBudgetSyncMasterMap(long companyCode) {
        Map<Long, BudgetSyncMaster> budgetSyncMasterMap = new HashMap<>();
        budgetSyncMasterRepository.findByCompanyCode(companyCode)
                .forEach(i -> budgetSyncMasterMap.put(i.getId(), i));
        return budgetSyncMasterMap;
    }

    private Map<Long, BudgetSyncToBudgetMappingMasterViewModel> generateBudgetSyncToMasterMap(long companyCode, Set<Long> budgetSyncMasterIds) {
        Map<Long, BudgetSyncToBudgetMappingMasterViewModel> map = new HashMap<>();
        customBudgetRepository.getBudgetSyncToMappingMasterMap(companyCode, new ArrayList<>(budgetSyncMasterIds))
                .forEach(i -> map.put(i.getSyncId(), i));
        return map;
    }

    private Integer getIntervalCount(String interval) {
        Integer intervalCount = null;
        List<LookupViewModel> availableValues = lookupService.lookupByType(interval);
        if (availableValues.isEmpty()) {
            throw new DomainInvariantException(String.format("create: The requested period %s could not be found", interval));
        } else if (availableValues.size() > 1) {
            throw new DomainInvariantException(String.format("create: More then one value for the given period %s was found", interval));
        }

        try {
            intervalCount = Integer.parseInt(availableValues.get(0).getValue());
        } catch (NumberFormatException numberFormatException) {
            logger.error(String.format("create: Cannot convert %s into a number", availableValues.get(0).getValue()));
            throw new DomainInvariantException("create: Error...please try again later");
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw new DomainInvariantException("create: Error...please try again later");
        }
        return intervalCount;
    }

    private void addBudgetFrequencyLists(Budget node, long companyCode, long userId, List<BudgetFrequencyMapping> budgetFrequencyMappings,
                                         Integer intervalCode, BigDecimal budgetTotal) {
        if (node == null) {
            return;
        }

        // When initialising separate the provided amount into equal segments for each period
        BigDecimal budgetAllocationAmount = budgetTotal.divide(BigDecimal.valueOf(intervalCode), 2, RoundingMode.HALF_EVEN);

        List<BudgetFrequencyMapping> budgetFrequencyMappingCollector = new ArrayList<>();
        for (int i = 0; i < intervalCode; i++) {
            BudgetFrequencyMapping budgetFrequencyMapping = createBudgetFrequencyMapping(i + 1, companyCode, userId);
            // Data mapping
            budgetFrequencyMapping.setTotal(budgetAllocationAmount);
            budgetFrequencyMapping.setTreasury(budgetAllocationAmount);

            // bidirectional mapping
            budgetFrequencyMapping.setBudget(node);
            budgetFrequencyMapping.setBudgetId(node.getId());
            budgetFrequencyMapping.setBudgetStructureMasterId(node.getBudgetStructureMasterId());
            budgetFrequencyMapping.setBudgetStructureMaster(node.getBudgetStructureMaster());

            node.getBudgetFrequencyMappings().add(budgetFrequencyMapping);
            budgetFrequencyMappingCollector.add(budgetFrequencyMapping);
        }
        budgetFrequencyMappings.addAll(budgetFrequencyMappingCollector);

        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            BigDecimal numberOfChildrenToAllocateBudgetTo = BigDecimal.valueOf(node.getChildren().size());
            for (Budget child : node.getChildren()) {
                addBudgetFrequencyLists(child, companyCode, userId, budgetFrequencyMappings, intervalCode, budgetTotal.divide(numberOfChildrenToAllocateBudgetTo, 2, RoundingMode.HALF_EVEN));
            }
        }
    }

    private Map<Long, Budget> validateAndGetBudgetMap(List<BudgetNodeUpdateViewModel> viewModels, long companyCode) {
        Map<Long, Budget> budgets = new HashMap<>();
        Set<Long> entityIds = new HashSet<>();
        Set<Long> viewModelIds = viewModels.stream().map(BudgetNodeUpdateViewModel::getId).collect(Collectors.toSet());

        repository.findByCompanyCodeAndIdInAndIsActiveTrue(companyCode, new ArrayList<>(viewModelIds))
                .forEach(i -> {
                    budgets.put(i.getId(), i);
                    entityIds.add(i.getId());
                });

        List<Long> notFoundElements = viewModelIds.stream().filter(i -> !entityIds.contains(i)).toList();

        if (!notFoundElements.isEmpty()) {
            throw new DomainInvariantException(String.format("The following node ids could not be found in the tree: %s ", notFoundElements));
        }

        return budgets;
    }


    private Budget createBudgetTree(BudgetViewModel node, Budget parent, long companyCode, long creatingUserId,
                                    Map<Long, BudgetSyncMaster> budgetSyncMasterMap, Set<Long> parentDepartments) {

        if (node == null) {
            return null;
        }

        Budget budget = createNewBudgetNode(node.getData(), budgetSyncMasterMap, parent, companyCode, creatingUserId);
        BigDecimal childrenTotal = BigDecimal.ZERO;
        Long budgetSyncMasterId = budget.getBudgetSyncMasterId();

        if (parentDepartments.contains(budgetSyncMasterId)) {
            logger.info(String.format("createBudgetTree: The child node %s department is already in a parent of the current node", node.toString()));
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_CREATION_ERROR);
        }

        parentDepartments.add(budgetSyncMasterId);

        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (BudgetViewModel child : node.getChildren()) {
                Budget childNode = createBudgetTree(child, budget, companyCode, creatingUserId, budgetSyncMasterMap, new HashSet<>(parentDepartments));
                budget.addChildren(childNode);
                childrenTotal = childrenTotal.add(childNode.getTotal());
            }
            if (budget.getTotal().compareTo(childrenTotal) < 0) {
                throw new DomainInvariantException(String.format("The sum of the children total budget is greater then the parent. Parent total : %s, Children total : %s ", budget.getTotal(), childrenTotal));
            }
        }

        parentDepartments.remove(budgetSyncMasterId);
        return budget;
    }

    private Budget createNewBudgetNode(BudgetDataViewModel node, Map<Long, BudgetSyncMaster> budgetSyncMasterMap,
                                       Budget parent, long companyCode, long creatingUserId) {

        Budget budget = new Budget();
        BudgetSyncMaster budgetSyncMaster = budgetSyncMasterMap.get(node.getBudgetSyncMasterId());

        if (budgetSyncMaster == null) {
            throw new DomainInvariantException("createBudgetEntry: The following entry with id: " + node.getId() + " did not have a valid budgetSyncMasterId: " + node.getBudgetSyncMasterId());
        }

        // Set budget bidirectional mapping
        budget.setBudgetSyncMaster(budgetSyncMaster);
        budget.setBudgetSyncMasterId(budgetSyncMaster.getId());

        // Set data fields
        setEntityData(budget, node);

        // Set parent for relationship here
        budget.setParent(parent);

        // Required base fields
        budget.setCompanyCode(companyCode);
        budget.setCreatingUserId(creatingUserId);
        budget.setUuid(UUID.randomUUID());

        handleBudgetNodeValidation(budget);

        return budget;
    }

    private ValidationStatusViewModel validateBudgetNodeData(Budget node) {
        //TODO : Hrushi
//        BigDecimal available = node.getUnallocated().add(node.getTreasury());
//        BigDecimal totalCalculatedAmount = available.add(node.getLocked()); // Soft lock doesn't form a part of the calculation
        BigDecimal available = node.getTreasury();
        BigDecimal totalCalculatedAmount = available.add(node.getLocked()); // Soft lock doesn't form a part of the calculation
        logger.trace("getValidationStatus: Running validations on the budget node");

        boolean status = true;
        StringBuilder stringBuilder = new StringBuilder();

        try {
            // Sanity checks
            logger.trace("validateBudgetNodeAmount: Validating soft-locked amount against locked");
            if (node.getLocked().compareTo(node.getSoftLocked()) < 0) {
                stringBuilder.append(String.format("The soft locked amount: %s must be lesser than equal to the locked amount: %s ", node.getSoftLocked(), node.getLocked()));
                status = false;
            }

            logger.trace("validateBudgetNodeAmount: Validating available sanity");
            if (node.getTotal().compareTo(available) < 0) {
                stringBuilder.append(String.format("The available amount: %s must be lesser than equal to the total amount: %s ", available, node.getTotal()));
                status = false;
            }

            logger.trace("validateBudgetNodeAmount: Validating total amount against available");
            if (node.getTotal().compareTo(totalCalculatedAmount) != 0) {
                stringBuilder.append(String.format("The total calculated amount: %s should be equal to the total amount: %s ", totalCalculatedAmount, node.getTotal()));
                status = false;
            }
        } catch (NullPointerException nullPointerException) {
            logger.error("validateBudgetNodeAmount: One of the required fields set to null; exception raised");
            throw new DomainInvariantException(
                    String.format("Error accessing necessary objects: %s", nullPointerException.getMessage()));
        }

        if (!status) {
            String parentDesc = node.getParent() == null ? null : node.getParent().getBudgetSyncMaster().getDescription();

            stringBuilder.insert(0, String.format("The parent-child node: %s-%s failed because of the following reasons:\t", parentDesc, node.getBudgetSyncMaster().getDescription()));
        }

        return new ValidationStatusViewModel(status, stringBuilder.toString());
    }

    private void handleBudgetNodeValidation(Budget budget) {
        ValidationStatusViewModel validationStatus = validateBudgetNodeData(budget);

        logger.info("handleBudgetValidation: Checking if all validations are passing");
        if (!validationStatus.isValid()) {
            logger.info("handleBudgetValidation: Validation errors present: {}", validationStatus.getValidationErrors());
            throw new DomainInvariantException("Invalid node, cannot save");
        }
    }

    private BudgetViewModel getViewModel(Budget node) {
        if (node == null) {
            return null;
        }

        BudgetViewModel budgetViewModel = setBudgetDataViewModel(node);

        // Perform validation while getting the view model
        BigDecimal childrenTotal = BigDecimal.ZERO;
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (Budget child : node.getChildren()) {
                childrenTotal = childrenTotal.add(child.getTotal());
                budgetViewModel.addChildren(getViewModel(child));
            }
            if (budgetViewModel.getData().getTotal().compareTo(childrenTotal) < 0) {
                throw new DomainInvariantException(String.format("The sum of the children total budget is greater then the parent. Parent total : %s, Children total : %s ", budgetViewModel.getData().getTotal(), childrenTotal));
            }
        }

        return budgetViewModel;
    }

    private BudgetStructureMasterViewModel getViewModel(BudgetStructureMaster entity) {
        BudgetStructureMasterViewModel viewModel = new BudgetStructureMasterViewModel();

        viewModel.setId(entity.getId());
        viewModel.setCompanyCode(entity.getCompanyCode());
        viewModel.setStructureName(entity.getStructureName());
        viewModel.setStartMonthIndex(entity.getStartMonthIndex());
        viewModel.setFinancialYear(entity.getFinancialYear());

        return viewModel;
    }

    private BudgetMasterTreeViewModel getViewModel(BudgetMappingMaster node) {
        if (node == null) {
            return null;
        }
        BudgetMasterTreeViewModel viewModel = setBudgetMappingMasterDataViewModel(node);

        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (BudgetMappingMaster child : node.getChildren()) {
                viewModel.addChildren(getViewModel(child));
            }
        }

        return viewModel;
    }

    private BudgetMasterTreeViewModel setBudgetMappingMasterDataViewModel(BudgetMappingMaster node) {
        BudgetMasterTreeViewModel viewModel = new BudgetMasterTreeViewModel();
        BudgetMasterTreeDataViewModel dataViewModel = new BudgetMasterTreeDataViewModel();

        // Set Data model with currentId and parentId if present;
        dataViewModel.setDisplayName(node.getDisplayName());
        dataViewModel.setBudgetMasterId(node.getBudgetMasterId());
        dataViewModel.setId(node.getId());

        BudgetMappingMaster parent = node.getParent();
        dataViewModel.setParentId(parent == null ? null : parent.getId());


        viewModel.setExpanded(true);
        viewModel.setData(dataViewModel);


        return viewModel;

    }


    private BudgetNodeViewModel getViewModelBudget(Budget node, List<String> keys) {
        // Initial main objects
        BudgetNodeViewModel budgetNode = new BudgetNodeViewModel();
        BudgetNodeDataViewModel budgetNodeData = new BudgetNodeDataViewModel();

        // Initial required objects for calculations objects
        List<BudgetFrequencyMappingNodeViewModel> frequencyData = new ArrayList<>();

        for (int i = 0; i < node.getBudgetFrequencyMappings().size(); i++) {
            // Todo : See if can convert -> node.getBudgetFrequencyMappings().get(i) to use single instance hashmap to convert back to lazy loading
            BudgetFrequencyMappingNodeViewModel frequencyMappingNodeViewModel = setBudgetFrequencyMappingNodeViewModel(node.getBudgetFrequencyMappings().get(i), keys.get(i), node.getId());
            frequencyData.add(frequencyMappingNodeViewModel);
        }

        // Populate main node
        budgetNodeData.setFrequencyData(frequencyData);
        budgetNodeData.setLabel(node.getBudgetSyncMaster().getDescription());

        // Set Main node
        budgetNode.setData(budgetNodeData);
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (Budget child : node.getChildren()) {
                budgetNode.getChildren().add(getViewModelBudget(child, keys));
            }
        }

        return budgetNode;
    }

    private BudgetFrequencyMappingNodeViewModel setBudgetFrequencyMappingNodeViewModel(BudgetFrequencyMapping entity, String key, long budgetId) {
        BudgetFrequencyMappingNodeViewModel frequencyMappingNodeViewModel = new BudgetFrequencyMappingNodeViewModel();
        frequencyMappingNodeViewModel.setKey(key);
        frequencyMappingNodeViewModel.setData(setBudgetFrequencyMappingDataNodeViewModel(entity, budgetId));

        return frequencyMappingNodeViewModel;
    }

    private BudgetFrequencyMappingDataNodeViewModel setBudgetFrequencyMappingDataNodeViewModel(BudgetFrequencyMapping entity, long budgetId) {
        BudgetFrequencyMappingDataNodeViewModel frequencyMappingDataNodeViewModel = new BudgetFrequencyMappingDataNodeViewModel();
        // Self node id
        frequencyMappingDataNodeViewModel.setId(entity.getId());
        // Set parent budgetId
        frequencyMappingDataNodeViewModel.setBudgetId(budgetId);

        // Set data fields
        frequencyMappingDataNodeViewModel.setInterval(entity.getInterval());

        frequencyMappingDataNodeViewModel.setTotal(entity.getTotal());
        frequencyMappingDataNodeViewModel.setTreasury(entity.getTreasury());


        frequencyMappingDataNodeViewModel.setSoftLocked(entity.getSoftLocked());
        frequencyMappingDataNodeViewModel.setLocked(entity.getLocked());
        frequencyMappingDataNodeViewModel.setConsumed(entity.getConsumed());

        return frequencyMappingDataNodeViewModel;
    }

    private BudgetFrequencyMappingViewModel getViewModel(BudgetFrequencyMapping entity) {
        BudgetFrequencyMappingViewModel viewModel = new BudgetFrequencyMappingViewModel();
        viewModel.setId(entity.getId());
        viewModel.setBudgetId(entity.getBudget().getId());
        viewModel.setInterval(entity.getInterval());

        viewModel.setTotal(entity.getTotal());
        viewModel.setTreasury(entity.getTreasury());

        viewModel.setLocked(entity.getLocked());
        viewModel.setSoftLocked((entity.getSoftLocked()));
        viewModel.setConsumed(entity.getConsumed());

        return viewModel;
    }

    private BudgetFrequencyMapping createBudgetFrequencyMapping(int intervalCount, long companyCode, long userId) {
        BudgetFrequencyMapping budgetFrequencyMapping = new BudgetFrequencyMapping();
        budgetFrequencyMapping.setInterval(intervalCount);

        budgetFrequencyMapping.setTotal(BigDecimal.ZERO);
        budgetFrequencyMapping.setTreasury(BigDecimal.ZERO);

        budgetFrequencyMapping.setSoftLocked(BigDecimal.ZERO);
        budgetFrequencyMapping.setLocked(BigDecimal.ZERO);
        budgetFrequencyMapping.setTreasury(BigDecimal.ZERO);

        budgetFrequencyMapping.setCompanyCode(companyCode);
        budgetFrequencyMapping.setCreatingUserId(userId);
        return budgetFrequencyMapping;
    }

    private BudgetViewModel setBudgetDataViewModel(Budget node) {
        BudgetViewModel budgetViewModel = new BudgetViewModel();
        BudgetDataViewModel budgetDataViewModel = new BudgetDataViewModel();

        // Set additional values for the UI
        budgetViewModel.setLabel(node.getBudgetSyncMaster().getDescription());
        budgetViewModel.setExpanded(true);
        budgetDataViewModel.setId(node.getId());

        // Set Data
        budgetDataViewModel.setTotal(node.getTotal());
        budgetDataViewModel.setTreasury(node.getTreasury());

        budgetDataViewModel.setSoftLocked(node.getSoftLocked());
        budgetDataViewModel.setLocked(node.getLocked());


        budgetViewModel.setData(budgetDataViewModel);

        budgetViewModel.setBudgetByFrequencyMapList(node.getBudgetFrequencyMappings().stream().map(this::getViewModel).toList());
        return budgetViewModel;
    }

    private void setEntityData(Budget budget, BudgetDataViewModel node) {
        budget.setTotal(node.getTotal());
        budget.setTreasury(node.getTreasury());
        budget.setSoftLocked(node.getSoftLocked());
        budget.setLocked(node.getLocked());

    }

    public static int getInterval(int startMonth, int currentMonth, int intervalLength) {
        if (startMonth == currentMonth) return StaticDataRegistry.SAME_MONTH_INTERVAL;
        int travelDistance = currentMonth - startMonth;
        if (travelDistance < 0) {
            travelDistance = 12 + travelDistance;
        }
        int interval = (travelDistance / intervalLength);
        return travelDistance % intervalLength == 0 ? interval : interval + 1;
    }

    private boolean performBudgetStructureSanityChecks(BudgetStructureMasterCreateViewModel budgetStructureMasterCreateViewModel, long companyCode) {
        boolean sanityCheckPassFlag = true;
        Optional<LookupViewModel> validFinancialYear = lookupService.lookUpByTypeAndValue(StaticDataRegistry.LOOKUP_TYPE_FINANCIAL_YEAR, budgetStructureMasterCreateViewModel.getFinancialYear());
        if (validFinancialYear.isEmpty()) {
            logger.info("createBudgetStructureMaster: The financial year {} that was provided does not exist in the lookup table", budgetStructureMasterCreateViewModel.getFinancialYear());
            sanityCheckPassFlag = false;
        }

        if (budgetStructureMasterCreateViewModel.getStartMonthIndex() == null ||
                (budgetStructureMasterCreateViewModel.getStartMonthIndex() > StaticDataRegistry.START_MONTH_MAXIMUM
                        || budgetStructureMasterCreateViewModel.getStartMonthIndex() < StaticDataRegistry.START_MONTH_MINIMUM)) {
            logger.info("createBudgetStructureMaster: The provided start month index: {} was not valid ", budgetStructureMasterCreateViewModel.getStartMonthIndex());
            sanityCheckPassFlag = false;
        }

        if (budgetStructureMasterRepository.findByCompanyCodeAndStructureNameAndFinancialYearAndIsActive(companyCode,
                budgetStructureMasterCreateViewModel.getStructureName(), budgetStructureMasterCreateViewModel.getFinancialYear(), true).isPresent()) {
            logger.info("createBudgetStructureMaster: The provided structure name is already in use: {} for company: {} and financial year: {}",
                    budgetStructureMasterCreateViewModel.getStructureName(), companyCode, budgetStructureMasterCreateViewModel.getFinancialYear());
            sanityCheckPassFlag = false;
        }

        return sanityCheckPassFlag;
    }

}
