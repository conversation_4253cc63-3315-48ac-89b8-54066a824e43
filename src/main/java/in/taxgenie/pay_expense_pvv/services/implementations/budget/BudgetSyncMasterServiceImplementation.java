package in.taxgenie.pay_expense_pvv.services.implementations.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetSyncMaster;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetSyncMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetSyncMasterService;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericPageableViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.*;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.syncmaster.BudgetSyncMasterCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.syncmaster.BudgetSyncMasterOptionsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.syncmaster.BudgetSyncMasterUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.syncmaster.BudgetSyncMasterViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class BudgetSyncMasterServiceImplementation implements IBudgetSyncMasterService {

    private final IBudgetSyncMasterRepository repository;
    private final CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository;
    private final Logger logger;

    public BudgetSyncMasterServiceImplementation(IBudgetSyncMasterRepository repository, CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository) {
        this.repository = repository;
        this.customDocumentApprovalContainerRepository = customDocumentApprovalContainerRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public GenericPageableViewModel<BudgetSyncMasterViewModel> getAllBudgetSyncMaster(IAuthContextViewModel auth,
                                                                                      long budgetMasterId,
                                                                                      Boolean isActive,
                                                                                      QueueFilterViewModel filterValues) {

        logger.info(String.format("BudgetQueueServiceImplementation-getQueue: Called for company id: %s", auth.getCompanyCode()));
        GenericPageableViewModel<BudgetSyncMasterViewModel> pagedQueue = new GenericPageableViewModel<>();

        Pageable pageable = PageRequest.of(Math.max(0, filterValues.getPage() - 1), filterValues.getPageSize(), Sort.by(Sort.Direction.ASC, "description"));
        Page<BudgetSyncMaster> budgetSyncMasters;

        boolean activeFlag = Boolean.TRUE.equals(isActive);
        if (activeFlag) {
            budgetSyncMasters = repository.findByCompanyCodeAndBudgetMasterIdAndIsActive(auth.getCompanyCode(), budgetMasterId, true,
                    pageable);

        } else {
            budgetSyncMasters = repository.findByCompanyCodeAndBudgetMasterId(auth.getCompanyCode(), budgetMasterId,
                    pageable);
        }

        List<BudgetSyncMasterViewModel> budgetSyncMasterViewModelList = budgetSyncMasters.getContent()
                .stream()
                .map(BudgetSyncMaster::getViewModel)
                .collect(Collectors.toList());

        // Set response
        pagedQueue.setData(budgetSyncMasterViewModelList);
        pagedQueue.setPages(budgetSyncMasters.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(budgetSyncMasters.getTotalElements()));


        return pagedQueue;
    }


    @Override
    public MultiBudgetSyncMasterViewModel getAllBudgetSyncMaster(IAuthContextViewModel auth,
                                                                 long budgetMasterId,
                                                                 Boolean isActive) {

        MultiBudgetSyncMasterViewModel multiBudgetSyncMasterViewModel = new MultiBudgetSyncMasterViewModel();
        List<BudgetSyncMasterViewModel> budgetSyncMasterViewModelList = new ArrayList<>();

        List<BudgetSyncMaster> budgetSyncMasterList;
        if (isActive) {
            budgetSyncMasterList = repository
                    .findByCompanyCodeAndBudgetMasterIdAndIsActive(auth.getCompanyCode(), budgetMasterId, true,
                            Sort.by(Sort.Direction.ASC, "description"));
        } else {
            budgetSyncMasterList = repository
                    .findByCompanyCodeAndBudgetMasterId(auth.getCompanyCode(), budgetMasterId,
                            Sort.by(Sort.Direction.ASC, "description"));
        }
        budgetSyncMasterList.forEach((budgetSyncMaster -> {
            BudgetSyncMasterViewModel budgetSyncMasterViewModel = budgetSyncMaster.getViewModel();
            budgetSyncMasterViewModelList.add(budgetSyncMasterViewModel);
        }));

        multiBudgetSyncMasterViewModel.setData(budgetSyncMasterViewModelList);
        return multiBudgetSyncMasterViewModel;
    }

    @Override
    public BudgetSyncMasterViewModel updateBudgetSyncMaster(
            BudgetSyncMasterUpdateViewModel budgetSyncMasterUpdateViewModel,
            IAuthContextViewModel auth) {

        BudgetSyncMasterViewModel budgetSyncMasterViewModel = new BudgetSyncMasterViewModel();

        //Get db entity by its pk
        Optional<BudgetSyncMaster> dbBudgetSyncMaster = Optional
                .ofNullable(repository.findById(budgetSyncMasterUpdateViewModel.getId()).orElseThrow(
                        () -> new RecordNotFoundException(String.format("Could not find BudgetSyncMaster with id: %d",
                                budgetSyncMasterUpdateViewModel.getId()))));

        //call of setUpdatedData will set updated data to the entity.
        dbBudgetSyncMaster.get().setDataForUpdate(budgetSyncMasterUpdateViewModel, auth);

        //Update db call
        BudgetSyncMaster budgetSyncMasterUpdated = repository.saveAndFlush(dbBudgetSyncMaster.get());

        //Set view model data by using db entity
        budgetSyncMasterViewModel.setBudgetSyncMaster(budgetSyncMasterUpdated);

        return budgetSyncMasterViewModel;
    }

    @Override
    public BudgetSyncMasterViewModel createBudgetSyncMaster(
            BudgetSyncMasterCreateViewModel budgetSyncMasterCreateViewModel, IAuthContextViewModel auth) {
        BudgetSyncMasterViewModel budgetSyncMasterViewModel = new BudgetSyncMasterViewModel();

        BudgetSyncMaster budgetSyncMaster = new BudgetSyncMaster();
        budgetSyncMaster.setDataForCreate(budgetSyncMasterCreateViewModel, auth);

        BudgetSyncMaster budgetSyncMasterCreated = repository.saveAndFlush(budgetSyncMaster);
        budgetSyncMasterViewModel.setBudgetSyncMaster(budgetSyncMasterCreated);


        return budgetSyncMasterViewModel;
    }

    @Override
    public List<BudgetSyncMasterOptionsViewModel> getOptions(long budgetMasterId, long structureId, IAuthContextViewModel auth) {
        List<BudgetSyncMaster> budgetSyncMasterOptions = repository.findByCompanyCodeAndBudgetMasterIdAndIsActiveTrue(auth.getCompanyCode(), budgetMasterId);
        return budgetSyncMasterOptions.stream()
                .map(entity -> {
                    return new BudgetSyncMasterOptionsViewModel(entity.getId(), entity.getCode(), entity.getDescription());
                })
                .toList();
    }

    @Override
    public List<LookupDataModel> getBudgetSyncMasterLookupData(IAuthContextViewModel auth, Long masterId, Boolean isActive) {
        return repository
                .findByCompanyCodeAndBudgetMasterIdAndIsActive(auth.getCompanyCode(), masterId, true,
                        Sort.by(Sort.Direction.ASC, "code")).stream().map(budgetSyncMaster -> {
                    LookupDataModel viewModel = new LookupDataModel();
                    viewModel.setId(budgetSyncMaster.getId().intValue());
                    viewModel.setValue(budgetSyncMaster.getCode());
                    return viewModel;
                }).collect(Collectors.toList());
    }

    @Override
    public GenericPageableViewModel<BudgetSyncMasterViewModel> getCustomAllBudgetSyncMaster(IAuthContextViewModel auth, Long budgetMasterId, Boolean isActive, QueueFilterViewModel filterValues) {

        logger.info(String.format("BudgetQueueServiceImplementation-getQueue: Called for company id: %s", auth.getCompanyCode()));
        GenericPageableViewModel<BudgetSyncMasterViewModel> pagedQueue = new GenericPageableViewModel<>();

        Pageable pageable = PageRequest.of(Math.max(0, filterValues.getPage() - 1), filterValues.getPageSize(), Sort.by(Sort.Direction.ASC, "description"));

        // here call the query dsl method
        Page<BudgetSyncMasterViewModel> budgetSyncMastersPaged = customDocumentApprovalContainerRepository.getCustomAllBudgetSyncMaster(auth.getCompanyCode(), budgetMasterId, isActive, filterValues, pageable);

        // Set response
        pagedQueue.setData(budgetSyncMastersPaged.getContent());
        pagedQueue.setPages(budgetSyncMastersPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(budgetSyncMastersPaged.getTotalElements()));

        return pagedQueue;
    }


}
