package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.DocumentRule;
import in.taxgenie.pay_expense_pvv.entities.DocumentSubgroup;
import in.taxgenie.pay_expense_pvv.entities.Location;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.MasterRecordNotFoundException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentRuleRepository;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentSubgroupRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentRuleService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentRuleCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentRuleUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentRuleViewModel;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DocumentRuleServiceImplementation implements IDocumentRuleService {
    private final IDocumentRuleRepository repository;
    private final IDocumentSubgroupRepository headerRepository;
    private final Logger logger;
    private Location location;

    public DocumentRuleServiceImplementation(
            IDocumentRuleRepository repository,
            IDocumentSubgroupRepository headerRepository
    ) {
        this.repository = repository;
        this.headerRepository = headerRepository;
        this.logger = LoggerFactory.getLogger(DocumentRuleServiceImplementation.class);
    }

    @Transactional
    @Override
    public DocumentRuleViewModel create(DocumentRuleCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("create: Ensuring the existence of Expense Subgroup record for this request");
        DocumentSubgroup headerEntity =
                headerRepository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getDocumentSubgroupId())
                        .orElseThrow(() -> new MasterRecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", DocumentSubgroup.class, viewModel.getDocumentSubgroupId(), auth.getCompanyCode())));

        logger.trace("create: Conducting preliminary screening");
        processPreliminaryScreeningForCreate(viewModel, auth, headerEntity);

        logger.trace("create: Validating the rule");
        validateRule(viewModel);

        logger.trace("create: Creating a new entity");
        DocumentRule entity = new DocumentRule();
        BeanUtils.copyProperties(viewModel, entity);


        logger.trace("create: Setting audit fields");
        entity.setCreatingUserId(auth.getUserId());
        entity.setCreatedTimestamp(ZonedDateTime.now());
        entity.setCompanyCode(headerEntity.getCompanyCode());

        headerEntity.setRulesCount(headerEntity.getRulesCount() + StaticDataRegistry.COUNT_INCREMENT_FACTOR);

        logger.trace("create: Mapping both Rule and Subgroup");
        entity.setDocumentSubgroup(headerEntity);
        headerEntity.getRules().add(entity);


        logger.trace("create: Saving entity and metadata");
        DocumentRule savedEntity = repository.saveAndFlush(entity);
        headerRepository.saveAndFlush(headerEntity);
        logger.trace("create: Save successful");

        return getViewModel(savedEntity);
    }

    private void processPreliminaryScreeningForCreate(DocumentRuleCreateViewModel viewModel, IAuthContextViewModel auth, DocumentSubgroup headerEntity) {
        logger.trace("processPreliminaryScreeningForCreate: Checking if the location is required");
        if (headerEntity.isLocationRequired() && (viewModel.getLocationCategory() == null || !StaticDataRegistry.locationCategories.contains(viewModel.getLocationCategory()))) {
            logger.trace("processPreliminaryScreeningForCreate: This rule doesn't define a valid location");
            throw new DomainInvariantException("Valid location category is required for this rule");
        }

        logger.trace("processPreliminaryScreeningForCreate: Checking if standard deduction is applicable");
        if (headerEntity.isStandardDeductionApplicable() && viewModel.getStandardDeductionRate() == null) {
            logger.info("processPreliminaryScreeningForCreate: This rule doesn't define standard deduction rate");
            throw new DomainInvariantException("Standard deduction rate is required for this rule");
        }

        logger.trace("processPreliminaryScreeningForCreate: Setting the location field from location id");
        if (viewModel.getLocationCategory() != null && !StaticDataRegistry.locationCategories.contains(viewModel.getLocationCategory())) {
            throw new MasterRecordNotFoundException(String.format("Could not find location category %s", viewModel.getLocationCategory()));
        }

        logger.trace("processPreliminaryScreeningForCreate: Checking if rule already exists");
        if (isDuplicateRule(viewModel, headerEntity.getId(), auth.getCompanyCode())) {
            throw new DomainInvariantException("Duplicate rule; already exists");
        }


        logger.trace("processPreliminaryScreeningForCreate: Found valid");
    }

    private void processPreliminaryScreeningForUpdate(DocumentRuleUpdateViewModel viewModel, IAuthContextViewModel auth, DocumentSubgroup headerEntity) {
        logger.trace("processPreliminaryScreeningForUpdate: Checking if the location is required and defined");
        if (headerEntity.isLocationRequired() && (viewModel.getLocationCategory() == null || !StaticDataRegistry.locationCategories.contains(viewModel.getLocationCategory()))) {
            logger.trace("processPreliminaryScreeningForUpdate: This rule doesn't define a valid location");
            throw new DomainInvariantException("Valid location category is required for this rule");
        }

        logger.trace("processPreliminaryScreeningForUpdate: Checking if standard deduction rate is applicable");
        if (headerEntity.isStandardDeductionApplicable() && viewModel.getStandardDeductionRate() == null) {
            logger.trace("processPreliminaryScreeningForUpdate: This rule doesn't define standard deduction rate");
            throw new DomainInvariantException("Standard deduction rate is required for this rule");
        }

        logger.trace("processPreliminaryScreeningForUpdate: Checking if rule already exists");
        if (isDuplicateRule(viewModel, headerEntity.getId(), auth.getCompanyCode())) {
            throw new DomainInvariantException("Duplicate rule; already exists");
        }

        logger.trace("processPreliminaryScreeningForUpdate: Found valid");
    }

    @Override
    public DocumentRuleViewModel update(long id, DocumentRuleUpdateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("update: Matching id with that of the view model");
        if (id != viewModel.getId()) {
            throw new DomainInvariantException("Passed id doesn't match with that of the view model");
        }

        logger.trace("update: Finding the source record");
        DocumentRule entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getId())
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", DocumentRule.class, viewModel.getId(), auth.getCompanyCode())));

        processPreliminaryScreeningForUpdate(viewModel, auth, entity.getDocumentSubgroup());

        logger.trace("update: Validating the rule");
        validateRule(viewModel);

        logger.trace("update: Updating the source record");
        BeanUtils.copyProperties(viewModel, entity, "expenseMetadataId");

        logger.trace("update: Setting the audit fields");
        entity.setUpdatingUserId(auth.getUserId());
        entity.setUpdatedTimestamp(ZonedDateTime.now());

        logger.trace("update: Saving the update");
        DocumentRule savedEntity = repository.saveAndFlush(entity);
        logger.trace("update: Save successful");

        return getViewModel(savedEntity);
    }

    @Override
    public List<DocumentRuleViewModel> getRulesBySubgroup(long subgroupId, IAuthContextViewModel auth) {
        logger.trace("getRulesBySubgroup: Returning list of view models");
        return repository
                .findByCompanyCodeAndDocumentSubgroupId(auth.getCompanyCode(), subgroupId)
                .stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public DocumentRuleViewModel getRuleById(long id, IAuthContextViewModel auth) {
        logger.trace("getRuleById: Finding the source record");
        DocumentRule entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", DocumentRule.class, id, auth.getCompanyCode())));

        return getViewModel(entity);
    }

    private boolean isDuplicateRule(DocumentRuleUpdateViewModel viewModel, long expenseSubgroupId, long companyId) {
        return repository
                .findByCompanyCodeAndDocumentSubgroupId(companyId, expenseSubgroupId)
                .stream()
                .anyMatch(r -> doesRuleMatch(viewModel, r));
    }

    private boolean isDuplicateRule(DocumentRuleCreateViewModel viewModel, long expenseSubgroupId, long companyId) {
        return repository
                .findByCompanyCodeAndDocumentSubgroupId(companyId, expenseSubgroupId)
                .stream()
                .anyMatch(r -> doesRuleMatch(viewModel, r));
    }

    private boolean doesRuleMatch(DocumentRuleCreateViewModel viewModel, DocumentRule rule) {
        boolean result = true;

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
            result = rule.getBranchCode().equals(viewModel.getBranchCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
            result = rule.getDepartmentCode().equals(viewModel.getDepartmentCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
            result = rule.getCostCenterCode().equals(viewModel.getCostCenterCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
            result = rule.getEmployeeType().equals(viewModel.getEmployeeType());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
            result = rule.getEmployeeGrade().equals(viewModel.getEmployeeGrade());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
            result = rule.getLocationCategory().equals(viewModel.getLocationCategory());
            if (!result) return false;
        }

        return result;
    }

    private boolean doesRuleMatch(DocumentRuleUpdateViewModel viewModel, DocumentRule rule) {
        boolean result = true;

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
            result = rule.getBranchCode().equals(viewModel.getBranchCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
            result = rule.getDepartmentCode().equals(viewModel.getDepartmentCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
            result = rule.getCostCenterCode().equals(viewModel.getCostCenterCode());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
            result = rule.getEmployeeType().equals(viewModel.getEmployeeType());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
            result = rule.getEmployeeGrade().equals(viewModel.getEmployeeGrade());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
            result = rule.getLocationCategory().equals(viewModel.getLocationCategory());
            if (!result) return false;
        }

        return result ? viewModel.getId() != rule.getId() : result;
    }

    private DocumentRuleViewModel getViewModel(DocumentRule entity) {
        logger.trace("getViewModel: Preparing view model");
        DocumentRuleViewModel returnViewModel = new DocumentRuleViewModel();

        logger.trace("getViewModel: Copying fields to view model");
        BeanUtils.copyProperties(entity, returnViewModel);

        returnViewModel.setDocumentMetadataMarker(StaticDataRegistry.getExpenseMetadataMarkerForRule(entity));

        logger.trace("getViewModel: Returning the view model");
        return returnViewModel;
    }

    private void validateRule(DocumentRuleCreateViewModel viewModel) {
        if (viewModel.isInvoiceRequired() && viewModel.getInvoiceRequiredThreshold() == null) {
            throw new DomainInvariantException("Rule doesn't provide invoice required threshold");
        }

        if (viewModel.getStartDate().isAfter(viewModel.getEndDate())) {
            throw new DomainInvariantException("Rule's start and end dates are invalid");
        }

//        viewModel.getMaximumAmount() < viewModel.getLimitAmount()
        if (viewModel.getMaximumAmount().compareTo(viewModel.getLimitAmount()) < 0) {
            throw new DomainInvariantException("Rule's maximum amount should be greater or equal to limit amount");
        }

        if (viewModel.isPerDiemAllowed() && viewModel.getPerDiemAmount() == null) {
            throw new DomainInvariantException("Rule doesn't provide per-diem amount");
        }

//        if (viewModel.isUnitRateApplicable() && viewModel.getUnitRate() == null) {
//            throw new DomainInvariantException("Rule doesn't provide unit rate");
//        }
//
//        if (viewModel.isUnitRateApplicable() && StaticDataRegistry.isNullOrEmptyOrWhitespace(viewModel.getUnitOfMeasure())) {
//            throw new DomainInvariantException("Rule doesn't provide unit of measure");
//        }
//
//        if (viewModel.isUnitRateApplicable() && StaticDataRegistry.isNullOrEmptyOrWhitespace(viewModel.getUnitRateType())) {
//            throw new DomainInvariantException("Rule doesn't provide unit rate type");
//        }
    }

    private void validateRule(DocumentRuleUpdateViewModel viewModel) {
        if (viewModel.isInvoiceRequired() && viewModel.getInvoiceRequiredThreshold() == null) {
            throw new DomainInvariantException("Rule doesn't provide invoice required threshold");
        }

        if (viewModel.getStartDate().isAfter(viewModel.getEndDate())) {
            throw new DomainInvariantException("Rule's start and end dates are invalid");
        }

//        viewModel.getMaximumAmount() < viewModel.getLimitAmount()
        if (viewModel.getMaximumAmount().compareTo(viewModel.getLimitAmount()) < 0) {
            throw new DomainInvariantException("Rule's maximum amount should be greater or equal to limit amount");
        }

        if (viewModel.isPerDiemAllowed() && viewModel.getPerDiemAmount() == null) {
            throw new DomainInvariantException("Rule doesn't provide per-diem amount");
        }

        if (viewModel.isUnitRateApplicable() && viewModel.getUnitRate() == null) {
            throw new DomainInvariantException("Rule doesn't provide unit rate");
        }

        if (viewModel.isUnitRateApplicable() && StaticDataRegistry.isNullOrEmptyOrWhitespace(viewModel.getUnitOfMeasure())) {
            throw new DomainInvariantException("Rule doesn't provide unit of measure");
        }

        if (viewModel.isUnitRateApplicable() && StaticDataRegistry.isNullOrEmptyOrWhitespace(viewModel.getUnitRateType())) {
            throw new DomainInvariantException("Rule doesn't provide unit rate type");
        }
    }
}
