package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.entities.AuditLog;
import in.taxgenie.pay_expense_pvv.repositories.IAuditLogRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEntityUpdateLogService;
import org.springframework.stereotype.Service;

@Service
public class EntityUpdateLogServiceImplementation implements IEntityUpdateLogService {

    private IAuditLogRepository repository;

    public EntityUpdateLogServiceImplementation(
            IAuditLogRepository repository) {
        this.repository = repository;
    }

    @Override
    public void saveLog(AuditLog entityUpdateLog){

        AuditLog x = repository.saveAndFlush(entityUpdateLog);

    }
}
