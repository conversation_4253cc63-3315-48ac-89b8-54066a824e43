package in.taxgenie.pay_expense_pvv.services.implementations.dashboard;

import com.fasterxml.jackson.databind.ObjectMapper;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ApproverType;
import in.taxgenie.pay_expense_pvv.entities.DocumentMetadata;
import in.taxgenie.pay_expense_pvv.entities.DocumentType;
import in.taxgenie.pay_expense_pvv.entities.dashboard.DashboardConfiguration;
import in.taxgenie.pay_expense_pvv.entities.dashboard.Widget;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentMetadataRepository;
import in.taxgenie.pay_expense_pvv.repositories.dashboard.IDashboardConfigRepository;
import in.taxgenie.pay_expense_pvv.repositories.dashboard.IWidgetRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomInvoiceTatReportRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomWidgetDashbosrdRepository;
import in.taxgenie.pay_expense_pvv.repositories.rbac.IOrgHierarchyRepository;
import in.taxgenie.pay_expense_pvv.repositories.tat_report.IInvoiceTatReportRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.dashboard.IWidgetDashboardService;
import in.taxgenie.pay_expense_pvv.services.interfaces.rbac.IOrgHierarchyService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.QueueStatisticsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.*;
import jakarta.persistence.EntityNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.DateTimeException;
import java.time.YearMonth;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class WidgetDashboardServiceImplementation implements IWidgetDashboardService {
    private final CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository;
    private final CustomWidgetDashbosrdRepository customWidgetDashbosrdRepository;
    private final CustomInvoiceTatReportRepository customInvoiceTatReportRepository;
    private final IWidgetRepository widgetRepository;
    private final IDocumentMetadataRepository metadataRepository;
    private final IDashboardConfigRepository dashboardConfigurationRepository;
    private final IInvoiceTatReportRepository iInvoiceTatReportRepository;
    private final IOrgHierarchyService orgHierarchyService;
    private final IOrgHierarchyRepository orgHierarchyRepository;
    private final Logger logger;


    public WidgetDashboardServiceImplementation(CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository, CustomWidgetDashbosrdRepository customWidgetDashbosrdRepository, CustomInvoiceTatReportRepository customInvoiceTatReportRepository, IWidgetRepository widgetRepository, IDocumentMetadataRepository metadataRepository, IDashboardConfigRepository dashboardConfigurationRepository,
                                                IInvoiceTatReportRepository iInvoiceTatReportRepository, IOrgHierarchyService orgHierarchyService, IOrgHierarchyRepository orgHierarchyRepository) {

        this.customDocumentApprovalContainerRepository = customDocumentApprovalContainerRepository;
        this.customWidgetDashbosrdRepository = customWidgetDashbosrdRepository;
        this.customInvoiceTatReportRepository = customInvoiceTatReportRepository;
        this.widgetRepository = widgetRepository;
        this.metadataRepository = metadataRepository;
        this.dashboardConfigurationRepository = dashboardConfigurationRepository;
        this.iInvoiceTatReportRepository = iInvoiceTatReportRepository;
        this.orgHierarchyService = orgHierarchyService;
        this.orgHierarchyRepository = orgHierarchyRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Autowired
    private ObjectMapper objectMapper;
    private static final int DEFAULT_MIN_TAT = 0;
    private static final int DEFAULT_MAX_TAT = 10000;
    @Override
    public DocumentWidgetStatsResponse<QueueStatisticsViewModel> getDocumentWidgetStats(Integer widgetId, DashboardWidgetFilterViewModel filterValues, IAuthContextViewModel auth) {
        String requestId = UUID.randomUUID().toString();
        logger.info("START getDocumentWidgetStats | RequestId: {} | WidgetId: {} | CompanyCode: {}", requestId, widgetId, auth.getCompanyCode());
        Integer documentCategoryId = getDocumentCategory(widgetId);
        DocumentWidgetStatsResponse<QueueStatisticsViewModel> response = new DocumentWidgetStatsResponse();
        // Get all metadata for a category
        List<Long> documentMetadataArray = metadataRepository
                .findByCompanyCodeAndDocumentCategoryIdOrderByIdDesc(
                        auth.getCompanyCode(),
                        documentCategoryId
                ).stream()
                .map(DocumentMetadata::getId)
                .distinct()
                .toList();

        String rbacKey = orgHierarchyRepository.findDistinctKeyByCompanyCode(auth.getCompanyCode());
        logger.info("Fetched RBAC Key: {} | RequestId: {}", rbacKey, requestId);

        List<String> rbacValues = orgHierarchyService.getValuesForRbac(auth.getUserId(), auth.getCompanyCode()); // List of values for the key
        logger.info("Fetched RBAC Values: {} | RequestId: {}", rbacValues, requestId);

        String financialYear = filterValues.getFinancialYear();
        Integer month = filterValues.getMonth();

        YearMonth targetMonthYear = null;
        Integer year = null;
        boolean validFinancialYearAndMonth = StaticDataRegistry.isNotNullOrEmptyOrWhitespace(financialYear) && null != month;

        try {
            if (validFinancialYearAndMonth) {
                int startYear = Integer.parseInt(financialYear.split("-")[0]);
                year = (month >= 4) ? startYear : startYear + 1;
                targetMonthYear = YearMonth.of(year, month);
                logger.info("Parsed TargetMonthYear: {} | RequestId: {}", targetMonthYear, requestId);
            }
        } catch (DateTimeException | NumberFormatException e) {
            logger.error("Error parsing financial year/month | RequestId: {} | Error: {}", requestId, e.getMessage(), e);
            throw new RuntimeException("Error parsing financial year or month for Dashboard getInvoiceStatistics", e);
        }

        QueueStatisticsViewModel statisticsData;
        try {
            statisticsData = customDocumentApprovalContainerRepository.getStatisticsForWidgetDashboard(
                    auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, rbacKey, rbacValues, targetMonthYear, month
            );
            logger.info("Fetched Statistics Data successfully | RequestId: {}", requestId);
        } catch (Exception e) {
            logger.error("Failed to fetch statistics data | RequestId: {} | Error: {}", requestId, e.getMessage(), e);
            throw e;
        }

        response.setName(filterValues.getName());
        response.setLastUpdatedAt(DateTimeUtils.getFormattedDateWithOrdinal(ZonedDateTime.now()));
        response.setData(statisticsData);

        logger.info("END getDocumentWidgetStats | RequestId: {}", requestId);
        return response;
    }

    private static Integer getDocumentCategory(Integer widgetId) {
        Integer documentCategoryId;
        switch (widgetId) {
            case StaticDataRegistry.Widget.W_INVOICE_ID -> documentCategoryId = DocumentType.INVOICE.ordinal() + 1;
            case StaticDataRegistry.Widget.W_PURCHASE_REQUEST_ID ->
                    documentCategoryId = DocumentType.PURCHASE_REQUEST.ordinal() + 1;
            case StaticDataRegistry.Widget.W_PURCHASE_ORDER_ID ->
                    documentCategoryId = DocumentType.PURCHASE_ORDER.ordinal() + 1;

            default -> throw new DomainInvariantException("No stats available");
        }
        return documentCategoryId;
    }

    @Override
    public List<DashboardConfigurationViewModel> getDashboardConfigurations(IAuthContextViewModel auth) {
        logger.info("getDashboardConfigurations: for company with id: " + auth.getCompanyCode());
        List<DashboardConfigurationViewModel> configurations = dashboardConfigurationRepository.findByCompanyCodeOrderById(auth.getCompanyCode());
        if (configurations == null || configurations.isEmpty()) {
            throw new EntityNotFoundException("No dashboard configurations found for company with id: " + auth.getCompanyCode());
        }
        return configurations;
    }

    @Override
    @Transactional
    public void initializeDashboardConfigurations(IAuthContextViewModel auth) {
        logger.info("initializeDashboardConfigurations: Started");
        // If dashboard already created exit without any changes
        List<DashboardConfigurationViewModel> currentConfigurations = dashboardConfigurationRepository.findByCompanyCodeOrderById(auth.getCompanyCode());
        if (!currentConfigurations.isEmpty()) {
            logger.info("initializeDashboardConfigurations: There already exists a dashboard configuration for company with id: {}", auth.getCompanyCode());
            return;
        }

        // Get list of widgets.
        List<Widget> availableWidgets = widgetRepository.findByIsActiveTrueOrderById();
        List<DashboardConfiguration> configurationList = new ArrayList<>();

        logger.info("initializeDashboardConfigurations: Found {} standard widgets for initialization", availableWidgets.size());

        for (int i = 0; i < availableWidgets.size(); i++) {
            DashboardConfiguration configuration = DashboardConfiguration.generateDashboardConfig(availableWidgets.get(i),
                    i + StaticDataRegistry.DASHBOARD_SEQUENCE_INCREMENT, auth.getCompanyCode(), auth.getUserId());
            configurationList.add(configuration);
        }

        logger.info("initializeDashboardConfigurations: Saving and returning");
        dashboardConfigurationRepository.saveAllAndFlush(configurationList);
    }

    @Override
    public void saveDashboardConfigurations(DashboardConfigurationRequest request, IAuthContextViewModel auth) {

        List<DashboardConfigurationViewModel> configsViewModel = request.getConfigs();
        List<Long> ids = configsViewModel.stream()
                .map(DashboardConfigurationViewModel::getId)
                .collect(Collectors.toList());

        logger.info(String.format("fetching all configs for companyCode : {%d}", auth.getCompanyCode()));
        List<DashboardConfiguration> configurations = dashboardConfigurationRepository.findByCompanyCodeAndIdIn(auth.getCompanyCode(), ids);

        Map<Long, DashboardConfiguration> existingConfigsMap = configurations.stream()
                .collect(Collectors.toMap(DashboardConfiguration::getId, Function.identity()));

        // Update existing configurations
        for (DashboardConfigurationViewModel viewModel : configsViewModel) {
            DashboardConfiguration dashboardConfig = existingConfigsMap.get(viewModel.getId());
            if (dashboardConfig != null) {
                dashboardConfig.setSequenceId(viewModel.getSequenceId());
                dashboardConfig.setIsActive(viewModel.getIsActive());
                dashboardConfig.setIsMandatory(viewModel.getIsMandatory());
//                dashboardConfig.setUpdatedAt(ZonedDateTime.now());
            }
        }
        dashboardConfigurationRepository.saveAll(configurations);

    }

    @Override
    public GenericPageableWidgetViewModel<BudgetDetailsWidgetViewModel> getBudgetDetails(QueueFilterViewModel filterValues, IAuthContextViewModel auth) {
        GenericPageableWidgetViewModel<BudgetDetailsWidgetViewModel> pagedQueue = new GenericPageableWidgetViewModel<>();
        pagedQueue.setName("Budget Details");
        pagedQueue.setLastUpdatedAt(DateTimeUtils.getFormattedDateWithOrdinal(ZonedDateTime.now()));

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize());

        String rbacKey = orgHierarchyRepository.findDistinctKeyByCompanyCode(auth.getCompanyCode());
        List<String> rbacValues = orgHierarchyService.getValuesForRbac(auth.getUserId(), auth.getCompanyCode()); // List of values for the key

        Page<BudgetDetailsWidgetViewModel> viewModelPaged = customWidgetDashbosrdRepository
                .fetchBudgetDetails(auth.getCompanyCode(), auth.getUserId(), filterValues, pageable, rbacKey, rbacValues);

        pagedQueue.setData(viewModelPaged.getContent());
        pagedQueue.setPages(viewModelPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(viewModelPaged.getTotalElements()));

        return pagedQueue;
    }

    @Override
    public DocumentWidgetStatsResponse<InvoicePayableStatisticsViewModel> getInvoicePayableStats(
            DashboardWidgetFilterViewModel filterValues,
            IAuthContextViewModel auth
    ) {
        String requestId = UUID.randomUUID().toString();
        logger.info("START getInvoicePayableStats | RequestId: {} | CompanyCode: {}", requestId, auth.getCompanyCode());

        DocumentWidgetStatsResponse<InvoicePayableStatisticsViewModel> response = new DocumentWidgetStatsResponse<>();
        int documentCategoryId = DocumentType.INVOICE.ordinal() + 1;

        List<Long> documentMetadataArray = metadataRepository
                .findByCompanyCodeAndDocumentCategoryIdOrderByIdDesc(auth.getCompanyCode(), documentCategoryId)
                .stream()
                .map(DocumentMetadata::getId)
                .distinct()
                .toList();

        logger.info("Fetched Metadata IDs: {} | RequestId: {}", documentMetadataArray, requestId);

        InvoicePayableStatisticsViewModel viewModel = customWidgetDashbosrdRepository
                .getInvoiceStatistics(auth, documentMetadataArray, filterValues.getFinancialYear(), filterValues.getMonth(), requestId);

        response.setData(viewModel);

        logger.info("END getInvoicePayableStats | RequestId: {}", requestId);
        return response;
    }

    @Override
    public TATResponseViewModel getInvoiceTatReport(Map<String, String> metadataFilters, IAuthContextViewModel auth) {
        Logger logger = LoggerFactory.getLogger(getClass());

        TATResponseViewModel response = new TATResponseViewModel();

        try {
            // Get approverType from metadataFilters, default to null if not available or empty
            ApproverType approverType = ApproverType.valueOf(metadataFilters.get("widgetName"));

            // Remove widgetName from metadataFilters
            metadataFilters.remove("widgetName");

            // Convert metadataFilters map to JSON string, handle null or empty map gracefully
            String metadataFiltersJson = null;
            if (metadataFilters != null && !metadataFilters.isEmpty()) {
                metadataFiltersJson = mapToJson(metadataFilters);
            }

            // Log the values being used for fetching the report
            logger.info("Fetching invoice TAT report with approverType: {} and metadataFiltersJson: {}", approverType, metadataFiltersJson);

            // Fetch data from the repository
            List<Object[]> results = null;

            if (approverType != ApproverType.OVERALL) {
                results = iInvoiceTatReportRepository.findInvoiceAgeingData(approverType.name(), metadataFiltersJson, auth.getCompanyCode());
            } else {
                results = iInvoiceTatReportRepository.findInvoiceAgeingDataForAll(metadataFiltersJson, auth.getCompanyCode());
            }

            // Map the results to the response model
            List<TATDataViewModel> data = results.stream()
                    .map(row -> new TATDataViewModel(
                            (String) row[0], // ageing
                            (String) row[1], // indicator
                            (Long) row[2], // noOfInvoices
                            row[3] != null ? BigDecimal.valueOf(((Number) row[3]).doubleValue()) : BigDecimal.ZERO, // totalInvoiceAmount
                            row[4] != null ? BigDecimal.valueOf(((Number) row[4]).doubleValue()) : BigDecimal.ZERO // mix

                    ))
                    .collect(Collectors.toList());

            // Set the data in the response view model and return
            response.setData(data);

            logger.info("Successfully fetched {} invoice records", data.size());
        } catch (Exception e) {
            // Log the exception with details
            logger.error("Error occurred while fetching invoice TAT report", e);

            // Set a failure response or return a default response
            throw new DomainInvariantException("Failed to fetch invoice TAT report. Please try again later.");

        }

        return response;
    }

    @Override
    public IInvoiceTATIdentifiersViewModel getInvoiceTatReportItems(IInvoiceTATIdentifiersRequestViewModel tatFilters, IAuthContextViewModel auth) {
        try {
            logger.info("getInvoiceTatReportItems: Entered function and executing the query to get the document identifiers for the following parameters {}", tatFilters.toString());
            if (tatFilters.isValid()) {
                logger.error("Invalid Request");
                throw new DomainInvariantException("Invalid Request");
            }
            String metadataFiltersJson = null;
            List<Object[]> results = null;
//
            // total tat time till pay - UI is sending overall as approver type
            List<String> documentIdentifiers = new ArrayList<>();
            if (tatFilters.getApproverType().equalsIgnoreCase(ApproverType.OVERALL.name())) {
                tatFilters.setApproverType(ApproverType.PAYER.name());
                // Convert metadataFilters map to JSON string, handle null or empty map gracefully
                if (tatFilters.getMetadataFilters() != null && !tatFilters.getMetadataFilters().isEmpty()) {
                    metadataFiltersJson = mapToJson(tatFilters.getMetadataFilters());
                }
                if (tatFilters.getMinTat() == null) {
                    logger.warn("minTat is null, setting to default: {}", DEFAULT_MIN_TAT);
                    tatFilters.setMinTat(DEFAULT_MIN_TAT);
                }

                if (tatFilters.getMaxTat() == null) {
                    logger.warn("maxTat is null, setting to default: {}", DEFAULT_MAX_TAT);
                    tatFilters.setMaxTat(DEFAULT_MAX_TAT);
                }

                results = iInvoiceTatReportRepository
                        .getDocumentIdentifiersForIntervalAndApprovalTypeForAll(tatFilters.getMinTat(), tatFilters.getMaxTat(), tatFilters.getApproverType(), metadataFiltersJson, auth.getCompanyCode());
                documentIdentifiers = results.stream()
                        .map(row -> row != null && row.length > 0 && row[0] != null ? String.valueOf(row[0]) : null)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            } else {
                documentIdentifiers = customInvoiceTatReportRepository
                        .getDocumentIdentifiersForIntervalAndApprovalType(tatFilters.getMinTat(), tatFilters.getMaxTat(), ApproverType.valueOf(tatFilters.getApproverType()), tatFilters.getMetadataFilters(), auth.getCompanyCode());
            }
            logger.info("getInvoiceTatReportItems: Returning {} document identifiers were found", documentIdentifiers.size());
            return new IInvoiceTATIdentifiersViewModel(tatFilters.getMinTat(), tatFilters.getMaxTat(), tatFilters.getApproverType(), documentIdentifiers);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid value for ApproverType: " + tatFilters.getApproverType(), e.getMessage());
            throw new DomainInvariantException("There was an error for this filter");
        }
    }


    private String mapToJson(Map<String, String> metadataFilters) {
        // Convert the Map to JSON string (you can use libraries like Jackson or Gson for this)
        // Here's a simple example using Jackson:
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(metadataFilters);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}
