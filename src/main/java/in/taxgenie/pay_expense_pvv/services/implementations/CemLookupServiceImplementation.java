package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cem.stereotype.*;
import in.taxgenie.pay_expense_pvv.services.interfaces.ICemLookupService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IRestConsumer;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CemLookupServiceImplementation implements ICemLookupService {
    private final IRestConsumer<CemBranchListResponse> branchListRestConsumer;
    private final IRestConsumer<CemCostCenterListResponse> costCenterListRestConsumer;
    private final IRestConsumer<CemDepartmentListResponse> departmentListRestConsumer;
    private final IRestConsumer<CemEmployeeGradeListResponse> employeeGradeListRestConsumer;
    private final IRestConsumer<CemEmployeeTypeListResponse> employeeTypeListRestConsumer;

    public CemLookupServiceImplementation(
            IRestConsumer<CemBranchListResponse> branchListRestConsumer,
            IRestConsumer<CemCostCenterListResponse> costCenterListRestConsumer,
            IRestConsumer<CemDepartmentListResponse> departmentListRestConsumer,
            IRestConsumer<CemEmployeeGradeListResponse> employeeGradeListRestConsumer,
            IRestConsumer<CemEmployeeTypeListResponse> employeeTypeListRestConsumer
    ) {
        this.branchListRestConsumer = branchListRestConsumer;
        this.costCenterListRestConsumer = costCenterListRestConsumer;
        this.departmentListRestConsumer = departmentListRestConsumer;
        this.employeeGradeListRestConsumer = employeeGradeListRestConsumer;
        this.employeeTypeListRestConsumer = employeeTypeListRestConsumer;
    }

    @Value("${CEM_URL}")
    private String cemUrl;

    @Override
    public List<BranchViewModel> getBranches(IAuthContextViewModel auth) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(auth.getToken());

        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                "branch"
        );

        return branchListRestConsumer
                .read(
                        HttpMethod.GET,
                        headers,
                        url,
                        CemBranchListResponse.class
                )
                .getBody();
    }

    @Override
    public List<CostCenterViewModel> getCostCenters(IAuthContextViewModel auth) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(auth.getToken());

        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                "cost-center"
        );

        return costCenterListRestConsumer
                .read(
                        HttpMethod.GET,
                        headers,
                        url,
                        CemCostCenterListResponse.class
                )
                .getBody();
    }

    @Override
    public List<DepartmentViewModel> getDepartments(IAuthContextViewModel auth) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(auth.getToken());

        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                "department"
        );

        return departmentListRestConsumer
                .read(
                        HttpMethod.GET,
                        headers,
                        url,
                        CemDepartmentListResponse.class
                )
                .getBody();
    }

    @Override
    public List<EmployeeGradeViewModel> getEmployeeGrades(IAuthContextViewModel auth) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(auth.getToken());

        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                "employee-grade"
        );

        return employeeGradeListRestConsumer
                .read(
                        HttpMethod.GET,
                        headers,
                        url,
                        CemEmployeeGradeListResponse.class
                )
                .getBody();
    }

    @Override
    public List<EmployeeTypeViewModel> getEmployeeTypes(IAuthContextViewModel auth) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(auth.getToken());

        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                "employee-type"
        );

        return employeeTypeListRestConsumer
                .read(
                        HttpMethod.GET,
                        headers,
                        url,
                        CemEmployeeTypeListResponse.class
                )
                .getBody();
    }
}
