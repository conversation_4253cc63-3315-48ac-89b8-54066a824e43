package in.taxgenie.pay_expense_pvv.services.implementations.rbac;

import in.taxgenie.pay_expense_pvv.api.rbac.UnmappedValuesViewModel;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestHeader;
import in.taxgenie.pay_expense_pvv.entities.rbac.KeyValueMapping;
import in.taxgenie.pay_expense_pvv.entities.rbac.OrgHierarchyStructure;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IApprovalDefinitionRepository;
import in.taxgenie.pay_expense_pvv.repositories.rbac.CustomRbacRepository;
import in.taxgenie.pay_expense_pvv.repositories.rbac.IKeyValueMappingRepository;
import in.taxgenie.pay_expense_pvv.repositories.rbac.IOrgHierarchyRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.rbac.IKeyValueMappingService;
import in.taxgenie.pay_expense_pvv.services.interfaces.rbac.IOrgHierarchyService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.utils.StringUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SupplierMastersViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.KeyValueMappingRequest;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.KeyValueMappingViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.KeyWithValuesResponseViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.ResponseViewWithWarning;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static in.taxgenie.pay_expense_pvv.utils.MappersUtil.convertSortOrderToJPASort;

@Service
public class KeyValueMappingServiceImplementation implements IKeyValueMappingService {
    private final Logger logger;
    private final CustomRbacRepository customRbacRepository;
    private final IKeyValueMappingRepository keyValueMappingRepository;
    private final IOrgHierarchyRepository orgHierarchyRepository;
    private final IApprovalDefinitionRepository approvalDefinitionRepository;
    private final IOrgHierarchyService orgHierarchyService;

    public KeyValueMappingServiceImplementation(CustomRbacRepository customRbacRepository, IKeyValueMappingRepository keyValueMappingRepository, IOrgHierarchyRepository orgHierarchyRepository, IApprovalDefinitionRepository approvalDefinitionRepository, IOrgHierarchyService orgHierarchyService) {
        this.customRbacRepository = customRbacRepository;
        this.keyValueMappingRepository = keyValueMappingRepository;
        this.orgHierarchyRepository = orgHierarchyRepository;
        this.approvalDefinitionRepository = approvalDefinitionRepository;
        this.orgHierarchyService = orgHierarchyService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public GenericPageableViewModel<KeyValueMappingViewModel> getKeyValueMappingQueue(IAuthContextViewModel auth, QueueFilterViewModel filterValues) {
        GenericPageableViewModel<KeyValueMappingViewModel> pagedQueue = new GenericPageableViewModel<>();
        Sort sort = null;

        String sortColumn = filterValues.getSortByColumnName() == null ? "default" : filterValues.getSortByColumnName();
        sort = switch (sortColumn) {
            case "key" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "key").and(Sort.by(Sort.Direction.DESC, "id"));
            case "value" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "value").and(Sort.by(Sort.Direction.DESC, "id"));
            case "label" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "label").and(Sort.by(Sort.Direction.DESC, "id"));
            default -> Sort.by(
                    Sort.Order.desc("key")
            );
        };

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize(), sort);

        Page<KeyValueMappingViewModel> viewModelPaged = customRbacRepository.getKeyValueMappingQueue(auth.getCompanyCode(), filterValues, pageable);
        pagedQueue.setData(viewModelPaged.getContent());
        pagedQueue.setPages(viewModelPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(viewModelPaged.getTotalElements()));


//        String existingLabel = keyValueMapping.getLabel();
//        String newLabel = keyValueMappingRequest.getLabel();

//        if (!existingLabel.equals(newLabel)) {
//            // Check if the label is the last one for the combination
//            long labelCount = keyValueMappingRepository.countByKeyAndValueAndLabel(
//                    keyValueMapping.getKey(),
//                    keyValueMapping.getValue(),
//                    existingLabel
//            );
//
//            if (labelCount == 1) {
//                // Check if the label is used in OrgHierarchyStructure
//                boolean isLabelUsedInHierarchy = orgHierarchyRepository.existsByLabel(existingLabel);
//
//                if (isLabelUsedInHierarchy) {
//                    response.setWarning(true);
//                    throw new DomainInvariantException("Last instance. If change is made, hierarchy will get deleted");
//                }
//            }
//        }

        return pagedQueue;
    }

    @Override
    @Transactional
    public ResponseViewWithWarning save(KeyValueMappingRequest keyValueMappingRequest, IAuthContextViewModel auth) throws RuntimeException {
        ResponseViewWithWarning response = new ResponseViewWithWarning();
        try {
            logger.info("Getting KeyValueMapping for id : {}", keyValueMappingRequest.getId());
            KeyValueMapping keyValueMapping =
                    keyValueMappingRepository.findById(keyValueMappingRequest.getId())
                            .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find keyValueMapping with id: %d", keyValueMappingRequest.getId())));


            keyValueMapping.setUpdatedTimestamp(ZonedDateTime.now());
            keyValueMapping.setUpdatingUserId(keyValueMapping.getUpdatingUserId());
            keyValueMapping.setLabel(keyValueMappingRequest.getLabel());
            logger.info("Saving KeyValueMapping");
            keyValueMappingRepository.saveAndFlush(keyValueMapping);

            // it may happen that - either new value get assigned to label or label is un-assigned from value (label can be empty)

            Optional<OrgHierarchyStructure> rootNodeForStructure = orgHierarchyRepository.findRootNodeForStructureByKey(auth.getCompanyCode(), keyValueMappingRequest.getKey());
            logger.info("Updating Mapping values to label hierarchy from save key value mappings");
            // rootNodeForStructure will never be empty - sanity check
            if (rootNodeForStructure.isEmpty())
                throw new RecordNotFoundException(String.format("Could not find root node for key : %s", keyValueMappingRequest.getKey()));
            orgHierarchyService.createMappingOfValuesAndLabels(keyValueMappingRequest.getKey(), auth, rootNodeForStructure.get());


        } catch (Exception ex) {
            response.setMessage(ex.getMessage());
            logger.error("{},{}", response.getMessage(), keyValueMappingRequest.getId());
        }
        return response;
    }

    @Override
    public List<String> getLabelsByKey(String key, IAuthContextViewModel auth) {
        logger.info("Getting Labels for company code : {} and for key {}", auth.getCompanyCode(), key);
        return keyValueMappingRepository.findDistinctLabelsByCompanyCodeAndKey(auth.getCompanyCode(), key);
    }

    @Override
    public List<LookupDataModel> getValuesByKey(String key, IAuthContextViewModel auth) {
        logger.info("Getting Values for company code : {} and for key {}", auth.getCompanyCode(), key);
        List<String> values = keyValueMappingRepository.findDistinctValuesByCompanyCodeAndKey(auth.getCompanyCode(), key);

        return values.stream().map(value -> {
            LookupDataModel viewModel = new LookupDataModel();
            viewModel.setValue(value);
            return viewModel;
        }).collect(Collectors.toList());
    }

    @Override
    public List<LookupDataModel> getKeys(IAuthContextViewModel auth) {
        logger.info("Getting Keys for company code : {}", auth.getCompanyCode());
        List<String> keys = keyValueMappingRepository.findDistinctKeysByCompanyCode(auth.getCompanyCode());
        return keys.stream().map(key -> {
            LookupDataModel viewModel = new LookupDataModel();
            viewModel.setValue(key);
            return viewModel;
        }).collect(Collectors.toList());
    }

    @Override
    public List<UnmappedValuesViewModel> getUnmappedLabelsByKey(String key, boolean isEdit, IAuthContextViewModel auth) {
        logger.info("Getting unmapped values for company code and key : {} and for key {}", auth.getCompanyCode(), key);
        List<KeyValueMapping> keyValueMappings = keyValueMappingRepository.findByCompanyCodeAndKey(auth.getCompanyCode(), key);
        return keyValueMappings.stream().map(mapping -> {
            UnmappedValuesViewModel viewModel = new UnmappedValuesViewModel();
            viewModel.setId(mapping.getId());
            viewModel.setValue(mapping.getValue());
            viewModel.setLabel(mapping.getLabel() == null ? "unassigned" : mapping.getLabel());
            return viewModel;
        }).collect(Collectors.toList());
    }

    @Override
    public List<LookupDataModel> getHierarchyLabelsByKey(String key, IAuthContextViewModel auth) {
        logger.info("Getting labels on hierarchy view for company code : {}", auth.getCompanyCode());
        List<String> labels = keyValueMappingRepository.findDistinctLabelsByCompanyCodeAndKey(auth.getCompanyCode(), key);
        return labels.stream().filter(StringUtils::isNotNullOrEmpty).map(label -> {
            LookupDataModel viewModel = new LookupDataModel();
            viewModel.setValue(label);
            return viewModel;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateKeyValues(List<Map.Entry<String, String>> keyValuePairs, Long companyCode) {

        List<KeyValueMapping> keyValueMappingsToBeCreated = new ArrayList<KeyValueMapping>();
        keyValuePairs.forEach(keyValuePair -> {

            Optional<KeyValueMapping> optionalKeyValueMapping = keyValueMappingRepository.findByKeyAndValueAndCompanyCode(keyValuePair.getKey(), keyValuePair.getValue(), companyCode);
            if (optionalKeyValueMapping.isEmpty()) {
                KeyValueMapping keyValueMapping = new KeyValueMapping();
                keyValueMapping.setKey(keyValuePair.getKey());
                keyValueMapping.setValue(keyValuePair.getValue());
                keyValueMapping.setSource("CEM");
                keyValueMapping.setCompanyCode(companyCode);
                keyValueMappingsToBeCreated.add(keyValueMapping);
            }
        });
        if (!keyValueMappingsToBeCreated.isEmpty()) {
            logger.info("Inside updateKeyValues method: Creating key-value mappings.");
            keyValueMappingRepository.saveAllAndFlush(keyValueMappingsToBeCreated);
        }
    }

    @Override
    public List<KeyWithValuesResponseViewModel> getAllKeysAndValues(long documentMetadataId, IAuthContextViewModel auth) {

        try {
            List<String> approvalMatchers = approvalDefinitionRepository.findDistinctApprovalMatchersByDocumentMetadataId(documentMetadataId);
            logger.debug("Fetched {} distinct approval matchers for documentMetadataId: {}", approvalMatchers.size(), documentMetadataId);

            if (approvalMatchers.isEmpty()) {
                throw new DomainInvariantException("No Approval Matrix is found for the policy");
            }

            logger.info("Fetching keys and values for companyCode: {}", auth.getCompanyCode());
            List<Object[]> results = keyValueMappingRepository.findValuesByCompanyCodeAndKeys(auth.getCompanyCode(), approvalMatchers);

            // Grouping values by key
            Map<String, List<String>> groupedData = results.stream()
                    .collect(Collectors.groupingBy(
                            row -> (String) row[0],  // get key
                            Collectors.mapping(row -> (String) row[1], Collectors.toList())  // get values
                    ));

            List<KeyWithValuesResponseViewModel> response = groupedData.entrySet().stream()
                    .map(entry -> new KeyWithValuesResponseViewModel(entry.getKey(), entry.getValue()))
                    .collect(Collectors.toList());

            return response;

        } catch (Exception e) {
            logger.error("Unexpected error while fetching keys and values for companyCode: {}", auth.getCompanyCode());
            throw new RuntimeException("An unexpected error occurred. Please try again later. -> "+e.getMessage());
        }
    }
}
