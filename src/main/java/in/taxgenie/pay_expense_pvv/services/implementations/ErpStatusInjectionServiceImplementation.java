package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.DocumentApprovalContainer;
import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.entities.ReportState;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.IReportStateRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentApprovalContainerUserService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmailNotificationService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmployeeMasterDataService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IErpStatusInjectionService;
import in.taxgenie.pay_expense_pvv.services.interfaces.consumption.IConsumptionService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentApprovalContainerSendBackResultViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Optional;

/**
 * Service implementation for handling ERP validation error injection.
 * This service provides modular sub-routines for managing the complete validation error workflow.
 */
@Service
public class ErpStatusInjectionServiceImplementation implements IErpStatusInjectionService {

    private final IDocumentApprovalContainerRepository documentApprovalContainerRepository;
    private final IReportStateRepository stateRepository;
    private final CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository;
    private final IEmployeeMasterDataService employeeService;
    private final IDocumentApprovalContainerUserService documentApprovalContainerUserService;
    private final IConsumptionService consumptionService;
    private final IEmailNotificationService emailNotificationService;
    private final Logger logger;

    public ErpStatusInjectionServiceImplementation(
            IDocumentApprovalContainerRepository documentApprovalContainerRepository,
            IReportStateRepository stateRepository, CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository,
            IEmployeeMasterDataService employeeService,
            IDocumentApprovalContainerUserService documentApprovalContainerUserService,
            IConsumptionService consumptionService,
            IEmailNotificationService emailNotificationService) {
        this.documentApprovalContainerRepository = documentApprovalContainerRepository;
        this.stateRepository = stateRepository;
        this.customDocumentApprovalContainerRepository = customDocumentApprovalContainerRepository;
        this.employeeService = employeeService;
        this.documentApprovalContainerUserService = documentApprovalContainerUserService;
        this.consumptionService = consumptionService;
        this.emailNotificationService = emailNotificationService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DocumentApprovalContainerSendBackResultViewModel injectERPStatus(
            long reportId, 
            String remarks, 
            ExpenseActionStatus expenseActionStatus, 
            IAuthContextViewModel auth) {
        
        logger.info("injectERPStatus: Starting {} injection for report ID: {}", expenseActionStatus.name(), reportId);

        try {
            // Step 1: Validate and retrieve document
            DocumentApprovalContainer report = validateAndRetrieveDocument(reportId, expenseActionStatus, auth);
            
            // Step 2: Validate approval workflow state
            ReportState maxLevelApprovedState = validateApprovalWorkflowState(reportId, auth);
            
            // Step 3: Create validation error ReportState
            ReportState validationErrorState = createValidationErrorState(report, maxLevelApprovedState, remarks, expenseActionStatus);
            
            // Step 4: Update document status (similar to sendBack)
            updateDocumentStatusForSendBack(report, remarks, expenseActionStatus);
            
            // Step 6: Save all changes
            saveChanges(validationErrorState, report);
            
            // Step 7: Handle consumption
            handleConsumption(report);
            
            // Step 8: Send notification
            sendNotification(report);
            
            logger.info("injectERPStatus: Successfully completed {} injection for document: {}", expenseActionStatus.name(), reportId);
            
            // Step 9: Return result
            return createResultViewModel(report);
            
        } catch (Exception e) {
            logger.error("injectERPStatus: Error during {} injection for document: {}. Error: {}", 
                        expenseActionStatus.name(), reportId, e.getMessage(), e);
            
            // Re-throw appropriate exceptions
            if (e instanceof DomainInvariantException || e instanceof RecordNotFoundException) {
                throw e;
            } else {
                throw new RuntimeException("Failed to inject " + expenseActionStatus.name() + " for document " + reportId + ": " + e.getMessage(), e);
            }
        }
    }

    /**
     * Validates document state and retrieves the document
     */
    private DocumentApprovalContainer validateAndRetrieveDocument(long reportId, ExpenseActionStatus expenseActionStatus, IAuthContextViewModel auth) {
        logger.info("validateAndRetrieveDocument: Validating document {} for {} injection", reportId, expenseActionStatus.name());
        
        DocumentApprovalContainer report = documentApprovalContainerRepository
                .findByCompanyCodeAndId(auth.getCompanyCode(), reportId)
                .orElseThrow(() -> new RecordNotFoundException("Could not find DocumentApprovalContainer with id: " + reportId));

        // Validate document is in VALIDATION_FAILED state (set by ERP integration flow)
        if (report.getReportStatus() != ReportStatus.VALIDATION_FAILED) {
            throw new DomainInvariantException("Document must be in VALIDATION_FAILED status to inject " + expenseActionStatus.name() + 
                                             ". Current status: " + report.getReportStatus());
        }
        
        logger.info("validateAndRetrieveDocument: Document {} validation passed", reportId);
        return report;
    }

    /**
     * Validates the approval workflow state and finds max level approved state
     */
    private ReportState validateApprovalWorkflowState(long reportId, IAuthContextViewModel auth) {
        logger.info("validateApprovalWorkflowState: Validating approval workflow for document {}", reportId);

        Optional<ReportState> maxLevelApprovedStateOptional =
                customDocumentApprovalContainerRepository.getMaxLevelApprovedState(auth.getCompanyCode(), reportId, ExpenseActionStatus.APPROVED);


        if (maxLevelApprovedStateOptional.isEmpty()) {
            throw new DomainInvariantException("No APPROVED states found for DocumentApprovalContainer: " + reportId);
        }

        ReportState maxLevelApprovedState = maxLevelApprovedStateOptional.get();
        logger.info("validateApprovalWorkflowState: Found max level APPROVED state at level {} for document: {}", 
                   maxLevelApprovedState.getLevel(), reportId);
        
        return maxLevelApprovedState;
    }

    /**
     * Creates the validation error ReportState
     */
    private ReportState createValidationErrorState(DocumentApprovalContainer report, ReportState maxLevelApprovedState, 
                                                  String remarks, ExpenseActionStatus expenseActionStatus) {
        logger.info("createValidationErrorState: Creating ReportState to record {} action for document: {}", 
                   expenseActionStatus.name(), report.getId());
        
        ReportState validationErrorState = new ReportState();
        validationErrorState.setCompanyCode(report.getCompanyCode());
        validationErrorState.setDocumentApprovalContainerId(report.getId());
        validationErrorState.setLevel(maxLevelApprovedState.getLevel() + StaticDataRegistry.DEFINITION_LEVEL_INCREMENT_FACTOR);
        validationErrorState.setStatus(expenseActionStatus);
        validationErrorState.setRemarks(remarks);
        validationErrorState.setActionDate(LocalDate.now());
        validationErrorState.setCreatedTimestamp(ZonedDateTime.now());
        validationErrorState.setUpdatedTimestamp(ZonedDateTime.now());
        validationErrorState.setApproverFirstName(StaticDataRegistry.ERPConstants.ERP_SYSTEM);
        validationErrorState.setApproverLastName("");
        validationErrorState.setApproverEmployeeCode("");
        validationErrorState.setDocumentType(report.getDocumentType());
        validationErrorState.setDocumentApprovalContainer(report);

        report.getReportStates().add(validationErrorState);
        
        logger.info("createValidationErrorState: Created validation error state at level {} for document: {}", 
                   validationErrorState.getLevel(), report.getId());
        
        return validationErrorState;
    }

    /**
     * Updates document status exactly like sendBack operation
     */
    private void updateDocumentStatusForSendBack(DocumentApprovalContainer report, String remarks, ExpenseActionStatus expenseActionStatus) {
        logger.info("updateDocumentStatusForSendBack: Updating document status to SENT_BACK for document: {}", report.getId());
        
        report.setReportStatus(ReportStatus.VALIDATION_FAILED);
        report.setActionStatus(expenseActionStatus);
        report.setActionLevel(StaticDataRegistry.FIRST_LEVEL);
        report.setCurrentApproverFirstName(null);
        report.setCurrentApproverLastName(null);
        report.setCurrentApproverEmployeeCode(null);
        report.setSendBackRemarks(remarks);
        report.setContainsSentBack(true);
        
        logger.info("updateDocumentStatusForSendBack: Successfully updated document status for document: {}", report.getId());
    }

    /**
     * Saves the validation error state and updated report
     */
    private void saveChanges(ReportState validationErrorState, DocumentApprovalContainer report) {
        try {
            logger.info("saveChanges: Saving ReportState and DocumentApprovalContainer for document: {}", report.getId());

            stateRepository.saveAndFlush(validationErrorState);
            documentApprovalContainerRepository.saveAndFlush(report);

            logger.info("saveChanges: Successfully saved changes for document: {}", report.getId());
        } catch (Exception e) {
            logger.error("saveChanges: Failed to save changes for document: {}. Error: {}",
                        report.getId(), e.getMessage(), e);
            throw new RuntimeException("Failed to save ReportState and DocumentApprovalContainer: " + e.getMessage(), e);
        }
    }

    /**
     * Handles consumption similar to sendBack
     */
    private void handleConsumption(DocumentApprovalContainer report) {
        try {
            logger.info("handleConsumption: Handling consumption for document: {}", report.getId());

            consumptionService.handleConsumption(report);

            logger.info("handleConsumption: Successfully handled consumption for document: {}", report.getId());
        } catch (Exception e) {
            logger.error("handleConsumption: Failed to handle consumption for document: {}. Error: {}",
                        report.getId(), e.getMessage(), e);
            throw new RuntimeException("Failed to handle consumption: " + e.getMessage(), e);
        }
    }

    /**
     * Sends notification similar to sendBack
     */
    private void sendNotification(DocumentApprovalContainer report) {
        try {
            logger.info("sendNotification: Sending notification for document: {}", report.getId());

            emailNotificationService.transmitSendBackNotification(report);

            logger.info("sendNotification: Successfully sent notification for document: {}", report.getId());
        } catch (Exception e) {
            logger.warn("sendNotification: Failed to send notification for document: {}. Error: {} (continuing anyway)",
                       report.getId(), e.getMessage());
            // Don't throw exception for notification failure - it's not critical
        }
    }

    /**
     * Creates the result view model
     */
    private DocumentApprovalContainerSendBackResultViewModel createResultViewModel(DocumentApprovalContainer report) {
        logger.info("createResultViewModel: Creating result view model for document: {}", report.getId());

        DocumentApprovalContainerSendBackResultViewModel resultViewModel = new DocumentApprovalContainerSendBackResultViewModel();
        resultViewModel.setDocumentNumber(report.getDocumentIdentifier());
        resultViewModel.setCreatorFirstName(report.getFirstName());
        resultViewModel.setCreatorLastName(report.getLastName());

        return resultViewModel;
    }
}
