package in.taxgenie.pay_expense_pvv.services.implementations;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.core.QueryResults;
import com.taxgenie.cem.viewmodels.employee.keyvaluepair.EmployeeAllValuesForKeyViewModel;
import com.taxgenie.cem.viewmodels.employee.keyvaluepair.EmployeeKeyRequestViewModel;
import com.taxgenie.utils.api.MultiServiceClient;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cem.employee.interfaces.IEmployeeViewModel;
import in.taxgenie.pay_expense_pvv.cloud.interfaces.IGcpCSFileIOProvider;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.invoice.InvoiceItem;
import in.taxgenie.pay_expense_pvv.entities.masters.MappingSegmentRatioToEntity;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.entities.pr.ProcurementStatus;
import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestItem;
import in.taxgenie.pay_expense_pvv.exceptions.ConflictException;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.factory.budgetaction.DocumentData;
import in.taxgenie.pay_expense_pvv.invoice.repository.IInvoiceHeaderRepository;
import in.taxgenie.pay_expense_pvv.repositories.*;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.masters.IMappingSegmentRatioToEntityRepository;
import in.taxgenie.pay_expense_pvv.repositories.pr.IPurchaseRequestHeaderRepository;
import in.taxgenie.pay_expense_pvv.repositories.pr.IPurchaseRequestItemRepository;
import in.taxgenie.pay_expense_pvv.repositories.rbac.IOrgHierarchyRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.*;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetService;
import in.taxgenie.pay_expense_pvv.services.interfaces.consumption.IConsumptionService;
import in.taxgenie.pay_expense_pvv.services.interfaces.masters.IRatioCategoryMasterService;
import in.taxgenie.pay_expense_pvv.services.interfaces.po.IPurchaseOrderService;
import in.taxgenie.pay_expense_pvv.services.interfaces.rbac.IOrgHierarchyService;
import in.taxgenie.pay_expense_pvv.utils.*;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.ConsumedItemAvailableCountViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.ExpenseValidationAggregates;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.IExpenseValidationAggregates;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.QueueStatisticsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.ServerResponseWithBodyEmployeeKeyPairValues;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice.InvoiceItemDetailsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice.InvoicePreviewViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.POPreviewViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderItemDetailsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.ProcurementStatisticsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.ProcurementViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.PurchaseRequestContainerViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.sql.Date;
import java.time.LocalDate;
import java.time.Month;
import java.time.Period;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static in.taxgenie.pay_expense_pvv.utils.MappersUtil.convertSortOrderToJPASort;
import static in.taxgenie.pay_expense_pvv.utils.MappersUtil.enumHandler;

@Service
public class DocumentApprovalContainerUserServiceImplementation implements IDocumentApprovalContainerUserService {
    private final IDocumentApprovalContainerRepository documentApprovalContainerRepository;
    private final IMetadataLimitRuleRepository ruleRepository;
    private final IDocumentRepository documentRepository;
    private final IDocumentMetadataRepository metadataRepository;
    private final IApprovalDefinitionRepository definitionRepository;
    private final IApprovalDelegationRepository delegationRepository;
    private final ILookupRepository lookupRepository;
    private final IReportStateRepository stateRepository;
    private final IApproverRepository approverRepository;
    private final IInvoiceHeaderRepository invoiceHeaderRepository;
    private final IInvoiceReceivedRepository invoiceReceivedRepository;
    private final IPurchaseRequestHeaderRepository purchaseRequestHeaderRepository;
    private final IPurchaseRequestItemRepository purchaseRequestItemRepository;
    private final ICompanyRepository companyRepository;
    private final IBudgetRepository budgetRepository;
    private final CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository;
    private final IMappingSegmentRatioToEntityRepository mappingSegmentRatioToEntityRepository;
    private final IEmployeeMasterDataService employeeService;
    private final IDocumentIdentifierService documentIdentifierService;
    private final IBudgetService budgetService;
    private final IConsumptionService consumptionService;
    private final IPurchaseOrderService purchaseOrderService;
    private final IOrgHierarchyService orgHierarchyService;
    private final IOrgHierarchyRepository orgHierarchyRepository;
    private final InvoiceServiceImplementation invoiceServiceImplementation;
    private final IRatioCategoryMasterService ratioCategoryMasterService;
    private final IGcpCSFileIOProvider gcpCSFileIOProvider;
    private final IInvoiceTatReportService iInvoiceTatReportService;

    private final IDocumentApprovalContainerApproverService documentApprovalContainerApproverService;
    //	private final IApprovalService approvalService;
    private final IPropertyCopier<DocumentApprovalContainer, IEmployeeViewModel> employeeMasterDataCopier;
    private final IEmailNotificationService emailNotificationService;

    private final MultiServiceClient webClientHelper;
    private final MultiServiceClientFactory multiServiceClientFactory;
    private final ObjectMapper objectMapper;


    private final Logger logger;


    @Autowired
    QueueV2Repository queueRepo;

    public DocumentApprovalContainerUserServiceImplementation(IDocumentApprovalContainerRepository documentApprovalContainerRepository,
                                                              IMetadataLimitRuleRepository ruleRepository, IDocumentRepository documentRepository,
                                                              IDocumentMetadataRepository metadataRepository, IApprovalDefinitionRepository definitionRepository,
                                                              IApprovalDelegationRepository delegationRepository, IReportStateRepository stateRepository,
                                                              IApproverRepository approverRepository,
                                                              IInvoiceHeaderRepository invoiceHeaderRepository, IInvoiceReceivedRepository invoiceReceivedRepository, IPurchaseRequestHeaderRepository purchaseRequestHeaderRepository, IPurchaseRequestItemRepository purchaseRequestItemRepository,
                                                              ICompanyRepository companyRepository, IBudgetRepository budgetRepository,
                                                              CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository,
                                                              IEmployeeMasterDataService employeeService,
                                                              IDocumentIdentifierService documentIdentifierService,
                                                              ILookupRepository lookupRepository,
                                                              IBudgetService budgetService,
                                                              IConsumptionService consumptionService,
                                                              @Lazy IPurchaseOrderService purchaseOrderService, IOrgHierarchyService orgHierarchyService, IOrgHierarchyRepository orgHierarchyRepository,
                                                              @Lazy InvoiceServiceImplementation invoiceServiceImplementation, IMappingSegmentRatioToEntityRepository mappingSegmentRatioToEntityRepository, IRatioCategoryMasterService ratioCategoryMasterService, IGcpCSFileIOProvider gcpCSFileIOProvider, IInvoiceTatReportService iInvoiceTatReportService,
                                                              IDocumentApprovalContainerApproverService documentApprovalContainerApproverService,

//			IApprovalService approvalService,
                                                              IPropertyCopier<DocumentApprovalContainer, IEmployeeViewModel> employeeMasterDataCopier,
                                                              IEmailNotificationService emailNotificationService,
                                                              MultiServiceClientFactory multiServiceClientFactory,
                                                              ObjectMapper objectMapper) {
        this.documentApprovalContainerRepository = documentApprovalContainerRepository;
        this.ruleRepository = ruleRepository;
        this.documentRepository = documentRepository;
        this.metadataRepository = metadataRepository;
        this.definitionRepository = definitionRepository;
        this.delegationRepository = delegationRepository;
        this.stateRepository = stateRepository;
        this.approverRepository = approverRepository;
        this.invoiceHeaderRepository = invoiceHeaderRepository;
        this.invoiceReceivedRepository = invoiceReceivedRepository;
        this.purchaseRequestHeaderRepository = purchaseRequestHeaderRepository;
        this.purchaseRequestItemRepository = purchaseRequestItemRepository;
        this.companyRepository = companyRepository;
        this.budgetRepository = budgetRepository;
        this.customDocumentApprovalContainerRepository = customDocumentApprovalContainerRepository;
        this.lookupRepository = lookupRepository;
//		this.approvalService = approvalService;
        this.documentIdentifierService = documentIdentifierService;
        this.employeeService = employeeService;
        this.orgHierarchyService = orgHierarchyService;
        this.orgHierarchyRepository = orgHierarchyRepository;
        this.budgetService = budgetService;
        this.consumptionService = consumptionService;
        this.purchaseOrderService = purchaseOrderService;
        this.invoiceServiceImplementation = invoiceServiceImplementation;
        this.mappingSegmentRatioToEntityRepository = mappingSegmentRatioToEntityRepository;
        this.ratioCategoryMasterService = ratioCategoryMasterService;
        this.gcpCSFileIOProvider = gcpCSFileIOProvider;
        this.iInvoiceTatReportService = iInvoiceTatReportService;
        this.documentApprovalContainerApproverService = documentApprovalContainerApproverService;
        this.employeeMasterDataCopier = employeeMasterDataCopier;
        this.emailNotificationService = emailNotificationService;
        this.multiServiceClientFactory = multiServiceClientFactory;
        this.webClientHelper = multiServiceClientFactory.createMultiServiceClient(WebClient.builder());
        this.objectMapper = objectMapper;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    @Transactional
    public DocumentApprovalContainerViewModel create(long metadataId, IAuthContextViewModel auth) {
        DocumentMetadata metadata = metadataRepository.findByCompanyCodeAndId(auth.getCompanyCode(), metadataId)
                .orElseThrow(() -> new RecordNotFoundException("Could not find the expense metadata: " + metadataId));

        if (metadata.isFrozen()) {
            throw new DomainInvariantException("Metadata is frozen");
        }

        if (documentApprovalContainerRepository.countByCompanyCodeAndReportStatusAndCreatingUserId(auth.getCompanyCode(),
                ReportStatus.DRAFT, auth.getUserId()) > StaticDataRegistry.MAX_DRAFT_REPORTS) {
            throw new DomainInvariantException("Maximum allowed draft report limit exceeded");
        }

        DocumentApprovalContainer report = new DocumentApprovalContainer();

        logger.info("create: Fetching the employee details");
        IEmployeeViewModel employeeViewModel = employeeService.getEmployeeMasterData(auth.getUserEmail(), auth);

//      TODO: check later, failing gender mismatch, employee gender male

//       if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(metadata.getApplicableGender())
//                && !metadata.getApplicableGender().equalsIgnoreCase(employeeViewModel.getGender())) {
//            throw new DomainInvariantException("This type/group is gender restricted");
//        }

        logger.info("create: Copying employee details");
        employeeMasterDataCopier.copyProperties(report, employeeViewModel);
        report.setEmployeeEmail(auth.getUserEmail().trim());

        report.setCompanyCode(auth.getCompanyCode());
        report.setCreatedDate(LocalDate.now());
        report.setCreatingUserId(auth.getUserId());
        report.setCreatedTimestamp(ZonedDateTime.now());
        report.setCompanyCode(metadata.getCompanyCode());
        report.setActionLevel(StaticDataRegistry.FIRST_LEVEL);
        report.setApprovalContainerTitle(String.format("%s/%s Expense", metadata.getDocumentType(), metadata.getDocumentGroup()));

        report.setStartDate(LocalDate.now());
        report.setEndDate(LocalDate.now()); // TODO : check end date

        report.setDocumentMetadata(metadata);
        report.setDocumentMetadataId(metadataId);
        report.setDocumentType(DocumentType.values()[metadata.getDocumentCategoryId() - 1]);
        metadata.getDocumentApprovalContainers().add(report);

        documentApprovalContainerRepository.saveAndFlush(report);
        metadataRepository.saveAndFlush(metadata);

        report.setDocumentIdentifier(documentIdentifierService.processAndGetIdentifier(metadata,
                LocalDate.now().getYear(), LocalDate.now().getMonthValue(), auth));

        documentApprovalContainerRepository.saveAndFlush(report);

        return getViewModel(report);
    }

    @Override
    public DocumentApprovalContainerViewModel getById(long id, IAuthContextViewModel auth) {
        if (auth.getAuthorities().contains(StaticDataRegistry.ADMIN_ROLE_MARKER)) {
            return getViewModel(documentApprovalContainerRepository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
                    .orElseThrow(() -> new RecordNotFoundException("Could not find report with id: " + id)));
        }

        if (auth.getAuthorities().contains(StaticDataRegistry.APPROVER_ROLE_MARKER)
                || auth.getAuthorities().contains(StaticDataRegistry.CHECKER_ROLE_MARKER)) {
            Optional<DocumentApprovalContainer> reportOptional = documentApprovalContainerRepository.findByCompanyCodeAndId(auth.getCompanyCode(), id);
            if (reportOptional.isPresent()) {
//				boolean isApprover = reportOptional.stream().anyMatch(r ->
//						stateRepository
//								.findByCompanyCodeAndExpenseReportId(r.getCompanyCode(), r.getId())
//								.stream()
//						.anyMatch(s -> s.getApprover().equalsIgnoreCase(auth.getUserEmail())));
                boolean isApprover = reportOptional.stream().anyMatch(r -> r.getReportStates().stream()
                        .anyMatch(s -> s.getApprover().equalsIgnoreCase(auth.getUserEmail())));
                if (isApprover) {
                    return getViewModel(reportOptional.get());
                }
            }
        }
        return getViewModel(documentApprovalContainerRepository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new RecordNotFoundException("Could not find report with id: " + id)));

//        return getViewModel(reportRepository.findByCompanyCodeAndIdAndCreatingUserId(auth.getCompanyCode(), id, auth.getUserId())
//                .orElseThrow(() -> new RecordNotFoundException("Could not find report with id: " + id)));
    }

    @Override
    public void save(DocumentApprovalContainerUpdateViewModel viewModel, IAuthContextViewModel auth) {
        DocumentApprovalContainer report = documentApprovalContainerRepository.findByCompanyCodeAndIdAndEmployeeEmail(auth.getCompanyCode(), viewModel.getId(), auth.getUserEmail())
                .orElseThrow(() -> new RecordNotFoundException("Could not find Expense Report with id: " + viewModel.getId()));
//		if (!this.approvalService.isSubmittable(report)) {
        if (!isSubmittable(report)) {
            throw new DomainInvariantException("Report is neither in draft or sent-back state; hence not saveable");
        }

        BeanUtils.copyProperties(viewModel, report, "expenseMetadataId", "documentIdentifier", "createdDate");
        long t1 = System.currentTimeMillis();
        logger.info(String.format("Save: Performing validation for Expense Report id: %s created by user: %s", report.getId(), auth.getUserEmail()));
        validateReport(report, viewModel);
        long t2 = System.currentTimeMillis();
        logger.info(String.format("Validating Expense report id %s created by user : %s took %s seconds", report.getId(), auth.getUserEmail(), (t2 - t1) * 0.001));
        logger.info(String.format("Save : Saving Expense report id: %s created by user : %s", report.getId(), auth.getUserEmail()));
        documentApprovalContainerRepository.saveAndFlush(report);
        long t3 = System.currentTimeMillis();
        logger.info(String.format("Saving Expense report id: %s created by user : %s took %s seconds", report.getId(), auth.getUserEmail(), (t3 - t1) * 0.001));
    }

    @Transactional
    @Override
    public DocumentApprovalContainerSubmitResultViewModel submit(long id, IAuthContextViewModel auth, long saveTime, OtherDepartmentKeyValuePairsViewModel otherDepartmentKeyValuePairsViewModel) {

        DocumentApprovalContainer report = documentApprovalContainerRepository
                .findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new RecordNotFoundException("Could not find Report with id: " + id));

        if (!DocumentType.INVOICE.equals(report.getDocumentType()) &&
                !auth.getUserEmail().equals(report.getEmployeeEmail())) {
            throw new DomainInvariantException("Not authorized to submit the Report with id: " + id);
        }

        Document document = report.getDocuments().iterator().next();

        Integer documentCategoryId = document.getDocumentApprovalContainer().getDocumentMetadata().getDocumentCategoryId();
        performDocumentSpecificAction(documentCategoryId, document);

        if (report.getDocuments().isEmpty() || (report.getApprovalContainerClaimAmount().compareTo(BigDecimal.ZERO) == 0) && (!documentCategoryId.equals(StaticDataRegistry.PR_CATEGORY_ID))) {
            throw new DomainInvariantException("Report has no expenses or total claim is zero");
        }
//		if (!approvalService.isSubmittable(report)) {
        if (!isSubmittable(report)) {
            throw new DomainInvariantException("Report is neither in draft or sent-back state; hence not submittable");
        }

        if (report.getEndDate().isAfter(LocalDate.now())) {
            throw new DomainInvariantException("Report end date cannot be after the current date: " + LocalDate.now());
        }

        validateMetadataLimits(report);

        validateExistingExpenseDateSanity(report);
        validateReportSubmission(report, documentCategoryId);

        logger.info("submit: Checking if the report has any expense that is deviated");
        if (getDeviationStatus(report)) {
            report.setContainsDeviation(true);
            report.setDeviationRemarks("Report consists of one or more deviated expenses");
        } else {
            // May be deviated before, but not now
            report.setContainsDeviation(false);
            report.setDeviationRemarks(null);
        }

        IEmployeeViewModel employeeViewModel = employeeService.getEmployeeMasterData(auth.getUserEmail(), auth);

        // IMPORTANT: This means that the approval is now dictated by the submitter not the creator
        // -- opposite to PayExpense
        logger.info("submit: Adding key values from submitter");
        employeeMasterDataCopier.copyProperties(report, employeeViewModel);
        report.setEmployeeEmail(auth.getUserEmail().trim());

        if (otherDepartmentKeyValuePairsViewModel != null && !otherDepartmentKeyValuePairsViewModel.isEmpty()) {

            // Override all the values in the report.
            // set on behalf true
            report.setIsOnBehalf(true);
            // Keys
            report.setKey01(otherDepartmentKeyValuePairsViewModel.getKey01());
            report.setKey02(otherDepartmentKeyValuePairsViewModel.getKey02());
            report.setKey03(otherDepartmentKeyValuePairsViewModel.getKey03());
            report.setKey04(otherDepartmentKeyValuePairsViewModel.getKey04());
            report.setKey05(otherDepartmentKeyValuePairsViewModel.getKey05());
            report.setKey06(otherDepartmentKeyValuePairsViewModel.getKey06());
            report.setKey07(otherDepartmentKeyValuePairsViewModel.getKey07());
            report.setKey08(otherDepartmentKeyValuePairsViewModel.getKey08());
            report.setKey09(otherDepartmentKeyValuePairsViewModel.getKey09());
            report.setKey10(otherDepartmentKeyValuePairsViewModel.getKey10());

            // Values
            report.setValue01(otherDepartmentKeyValuePairsViewModel.getValue01());
            report.setValue02(otherDepartmentKeyValuePairsViewModel.getValue02());
            report.setValue03(otherDepartmentKeyValuePairsViewModel.getValue03());
            report.setValue04(otherDepartmentKeyValuePairsViewModel.getValue04());
            report.setValue05(otherDepartmentKeyValuePairsViewModel.getValue05());
            report.setValue06(otherDepartmentKeyValuePairsViewModel.getValue06());
            report.setValue07(otherDepartmentKeyValuePairsViewModel.getValue07());
            report.setValue08(otherDepartmentKeyValuePairsViewModel.getValue08());
            report.setValue09(otherDepartmentKeyValuePairsViewModel.getValue09());
            report.setValue10(otherDepartmentKeyValuePairsViewModel.getValue10());

        }

        if (report.getReportStatus() == ReportStatus.SENT_BACK || report.getReportStatus() == ReportStatus.VALIDATION_FAILED) {
//            handleResubmission(report, employeeViewModel, auth, saveTime);
//			approvalService.attachInitialApprovers(report, report.getExpenseMetadata(), auth, employeeViewModel, true, report.getId(), report.getExpenseMetadataId(), report.getReportClaimAmount(), saveTime);
            attachInitialApprovers(report, report.getDocumentMetadata(), auth, employeeViewModel, true);

        }

        if (report.getReportStatus() == ReportStatus.DRAFT) {
            logger.info("submit: Attaching approvers to the report");
//			approvalService.attachInitialApprovers(report, report.getExpenseMetadata(), auth, employeeViewModel, false, report.getId(), report.getExpenseMetadataId(), report.getReportClaimAmount(), saveTime);
            attachInitialApprovers(report, report.getDocumentMetadata(), auth, employeeViewModel, true);
            report.setSubmitDate(LocalDate.now());
        }

        // segment details validation if present
        // imp : document category id sequence in database and document type sequence in enum should always match (docCategory starts with 1 whereas document type starts with 0)
        if (documentCategoryId.equals(DocumentType.PURCHASE_ORDER.ordinal() + 1) || documentCategoryId.equals(DocumentType.INVOICE.ordinal() + 1)) {
            Long entityId = documentCategoryId.equals(DocumentType.PURCHASE_ORDER.ordinal() + 1) ? document.getPoHeader().getId() : document.getInvoiceHeader().getInvoiceHeaderId();
            logger.info("validate if Segment ratio is 100%, get segment ratio for document type {} and entity id {}", DocumentType.getType(documentCategoryId), entityId);
            MappingSegmentRatioToEntity segmentRatio = mappingSegmentRatioToEntityRepository.findByDocumentTypeIdAndEntityId((documentCategoryId), entityId).orElse(null);
        }
        logger.info("submit: Setting the report status as submitted");
        report.setReportStatus(ReportStatus.SUBMITTED);

//		ReportState firstState = stateRepository.findByCompanyCodeAndExpenseReportId(report.getCompanyCode(), report.getId()).get(0);
//        ReportState firstState = report.getReportStates().get(0);

        ReportState firstState = report.getReportStates()
                .stream()
                .filter(i -> i.getStatus().equals(ExpenseActionStatus.UNACTIONED))
                .findFirst()
                .orElseThrow(() -> {
                    logger.info("submit: No unactioned report state found {}", report.getId());
                    return new DomainInvariantException("No unactioned report state found");
                });

        report.setCurrentApproverEmployeeCode(firstState.getApproverEmployeeCode());
        report.setCurrentApproverFirstName(firstState.getApproverFirstName());
        report.setCurrentApproverLastName(firstState.getApproverLastName());

        report.setActionStatus(firstState.getStatus());

        logger.info("submit: Saving the report");
        documentApprovalContainerRepository.saveAndFlush(report);
        logger.info("submit: Save successful; exiting");

        // In case of PR based PO set consumed PR items aggregated amount to PR
        consumptionService.handleConsumption(report);

        emailNotificationService.transmitSubmitNotification(report);

        DocumentApprovalContainerSubmitResultViewModel viewModel = new DocumentApprovalContainerSubmitResultViewModel();
        viewModel.setDocumentNumber(report.getDocumentIdentifier());
        viewModel.setFirstApproverFirstName(firstState.getApproverFirstName());
        viewModel.setFirstApproverLastName(firstState.getApproverLastName());
        viewModel.setFirstApproverFirstName(report.getReportStates().get(0).getApproverFirstName());
        viewModel.setFirstApproverLastName(report.getReportStates().get(0).getApproverLastName());

        //Create tat entry for invoice approve
        if (report.getDocumentType().equals(DocumentType.INVOICE)) {
            if (!iInvoiceTatReportService.create(ApproverType.UPLOADER, report.getId(), auth)) {
                logger.info("Failed to create Invoice TAT Report for approver");
            }
        }

        documentApprovalContainerRepository.flush();
        return viewModel;
    }


    private void performDocumentSpecificAction(Integer documentCategoryId, Document document) {
        switch (documentCategoryId) {
            case 1:
                InvoiceHeader invoiceHeader = document.getInvoiceHeader();
                InvoiceReceived invoiceReceived = invoiceHeader.getInvoiceReceived();
                if (null == Optional.ofNullable(invoiceReceived).map(InvoiceReceived::getDocumentUrl).orElse(null)) {
                    throw new DomainInvariantException("Report has no pdf attached, please upload a invoice");
                }
                if (null != invoiceHeader.getHasPrecedingDocument() && invoiceHeader.getHasPrecedingDocument()) {
                    for (InvoiceItem item : invoiceHeader.getInvoiceItems()) {
                        // Updated to check many-to-many relationship
                        if (item.getPurchaseOrderItems() == null || item.getPurchaseOrderItems().isEmpty()) {
                            throw new DomainInvariantException("Item " + item.getDescription() + " is not mapped with Purchase Order Item");
                        }
                    }
                }
                break;
            case 4:
                //  set isLock true on add-less submit
                ActionType action = document.getDocumentApprovalContainer().getDocumentMetadata().getActionType();
                if (action == ActionType.ADD || action == ActionType.LESS || action == ActionType.TRANSFER) {
                    Budget budget = document.getBudgetDocumentAction().getBudget();
                    if (null != budget.getBudgetLocked() && budget.getBudgetLocked())
                        throw new DomainInvariantException("Another transaction is in progress");
                    budget.setBudgetLocked(true);
                    budgetRepository.saveAndFlush(budget);
                }
                break;
            // TODO: handle other cases if necessary
            default:
                logger.info("No document present for document category with id {}", documentCategoryId);
        }
    }

    @Override
    public void revoke(long id, IAuthContextViewModel auth) {
        DocumentApprovalContainer report = documentApprovalContainerRepository
                .findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new RecordNotFoundException("Could not find Report with id: " + id));

        DocumentType documentType;
        try {
            documentType = DocumentType.values()[report.getDocumentMetadata().getDocumentCategoryId() - 1];
        } catch (IllegalArgumentException e) {
            logger.trace("revoke: DAC id {} doesn't have a valid document type. Here is the document category id that the system received: {}", report.getId(), report.getDocumentMetadata().getDocumentCategoryId());
            throw new DomainInvariantException(String.format("User %s cannot revoke this document: %d", auth.getUserEmail(), report.getId()));
        }

        if (!documentType.equals(DocumentType.INVOICE)) {
            logger.trace("revoke: Checking if current user is the creator of the report");
            if (!report.getEmployeeEmail().trim().equalsIgnoreCase(auth.getUserEmail().trim())) {
                logger.trace("revoke: User {} cannot revoke this document: {}", auth.getUserEmail(), report.getId());
                throw new DomainInvariantException(
                        String.format("User %s cannot revoke this document: %d", auth.getUserEmail(), report.getId()));
            }
        } else {
            logger.trace("revoke: Document Type is Invoice, hence allowing someone other than creator to reject it.");
        }

        // Check if PO is already discarded (for Purchase Orders only)
        if (documentType.equals(DocumentType.PURCHASE_ORDER) && report.getReportStatus() == ReportStatus.REVOKED) {
            logger.info("revoke: Purchase Order {} is already in DISCARDED status. No state changes made.", report.getId());
            throw new ConflictException(StaticDataRegistry.ERPConstants.PO_ALREADY_DISCARDED);
        }

        logger.trace("revoke: Checking if report is in draft or sent-back status");
        if (report.getReportStatus() == ReportStatus.SENT_BACK || report.getReportStatus() == ReportStatus.DRAFT || report.getReportStatus() == ReportStatus.FAILED || report.getReportStatus() == ReportStatus.VALIDATION_FAILED) {
            logger.trace("revoke: Setting the report status to revoked");
            report.setReportStatus(ReportStatus.REVOKED);  // Todo: Hook up budget factory REVOKE here
            report.setActionLevel(StaticDataRegistry.REVOKED_STATUS_MARKER);

            report.setActionStatus(ExpenseActionStatus.UNACTIONED);
            report.setCurrentApproverFirstName(null);
            report.setCurrentApproverLastName(null);
            report.setCurrentApproverEmployeeCode(null);
            report.setSendBackRemarks(null);

            if (documentType.equals(DocumentType.PURCHASE_ORDER) && report.getReportStatus() == ReportStatus.VALIDATION_FAILED) {
                // This is Discard after approval - handle budget reversal with comprehensive error handling
                try {
                    logger.info("revoke: Starting budget consumption reversal for Purchase Order document: {}", report.getId());

                    // Step 1: Get document data with validation
                    List<DocumentData> documentDataList = getDocumentDataWithValidation(report);

                    // Step 2: Handle budget consumption reversal
                    handleBudgetConsumptionForRevoke(documentDataList, report.getId());

                    // Step 3: Handle consumption service reversal (revert DAC and DOC consumed amounts)
                    handleConsumptionServiceForRevoke(report);

                    logger.info("revoke: Successfully completed budget consumption reversal for document: {}", report.getId());

                } catch (Exception budgetException) {
                    logger.error("revoke: Budget consumption reversal failed for document: {}. Revoke operation aborted. Error: {}",
                                report.getId(), budgetException.getMessage(), budgetException);

                    // Throw appropriate exception to indicate revoke failure
                    throw new DomainInvariantException(
                        String.format(StaticDataRegistry.ERPConstants.ERROR_REVOKE_BUDGET_FAILURE,
                                     report.getId(), budgetException.getMessage())
                    );
                }
            }

            logger.trace("revoke: Saving the report");
            try {
                documentApprovalContainerRepository.saveAndFlush(report);
                logger.trace("revoke: Save successful; exiting");
            } catch (Exception dbException) {
                logger.error("revoke: Database save failed for document: {}. Error: {}",
                            report.getId(), dbException.getMessage(), dbException);
                throw new RuntimeException(
                    String.format(StaticDataRegistry.ERPConstants.ERROR_REVOKE_DATABASE_SAVE,
                                 report.getId(), dbException.getMessage()),
                    dbException
                );
            }
        } else {
            logger.trace("revoke: Report {} cannot be revoked as it is in {} status", report.getId(),
                    report.getReportStatus());
            throw new DomainInvariantException(String.format("Report %d cannot be revoked as it is in %s status",
                    report.getId(), report.getReportStatus()));
        }
    }

    /**
     * Gets document data with validation for budget consumption reversal
     * @param report the document approval container
     * @return list of document data
     * @throws RuntimeException if document data retrieval fails
     */
    private List<DocumentData> getDocumentDataWithValidation(DocumentApprovalContainer report) {
        try {
            logger.info("getDocumentDataWithValidation: Retrieving document data for document: {}", report.getId());

            List<DocumentData> documentDataList = documentApprovalContainerApproverService.getDocumentData(report);

            if (documentDataList == null || documentDataList.isEmpty()) {
                throw new RuntimeException(
                    String.format(StaticDataRegistry.ERPConstants.ERROR_NO_DOCUMENT_DATA_FOR_REVOKE, report.getId())
                );
            }

            logger.info("getDocumentDataWithValidation: Successfully retrieved {} document data items for document: {}",
                       documentDataList.size(), report.getId());
            return documentDataList;

        } catch (Exception e) {
            logger.error("getDocumentDataWithValidation: Failed to retrieve document data for document: {}. Error: {}",
                        report.getId(), e.getMessage(), e);
            throw new RuntimeException(
                String.format(StaticDataRegistry.ERPConstants.ERROR_DOCUMENT_DATA_RETRIEVAL,
                             report.getId(), e.getMessage()),
                e
            );
        }
    }

    /**
     * Handles budget consumption reversal with comprehensive error handling
     * @param documentDataList the document data list
     * @param documentId the document ID for logging
     * @throws RuntimeException if budget consumption reversal fails
     */
    private void handleBudgetConsumptionForRevoke(List<DocumentData> documentDataList, long documentId) {
        try {
            logger.info("handleBudgetConsumptionForRevoke: Processing budget consumption reversal for document: {}", documentId);

            Boolean success = budgetService.handleBudgetConsumption(documentDataList);

            if (success == null) {
                throw new RuntimeException(
                    String.format(StaticDataRegistry.ERPConstants.ERROR_BUDGET_SERVICE_NULL_RESPONSE, documentId)
                );
            }

            logger.info("handleBudgetConsumptionForRevoke: Budget consumption reversal successful for document: {}", documentId);

        } catch (Exception e) {
            logger.error("handleBudgetConsumptionForRevoke: Budget consumption reversal failed for document: {}. Error: {}",
                        documentId, e.getMessage(), e);

            // Determine specific error type for better error messages
            String errorMessage;
            if (e.getMessage() != null && e.getMessage().toLowerCase().contains("budget")) {
                errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_BUDGET_SYSTEM_DURING_REVERSAL, documentId, e.getMessage());
            } else if (e.getMessage() != null && e.getMessage().toLowerCase().contains("database")) {
                errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_DATABASE_DURING_BUDGET_REVERSAL, documentId, e.getMessage());
            } else {
                errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_UNEXPECTED_BUDGET_REVERSAL, documentId, e.getMessage());
            }
            throw new RuntimeException(errorMessage, e);
        }
    }

    /**
     * Handles consumption service reversal with comprehensive error handling
     * @param report the document approval container
     * @throws RuntimeException if consumption service reversal fails
     */
    private void handleConsumptionServiceForRevoke(DocumentApprovalContainer report) {
        try {
            logger.info("handleConsumptionServiceForRevoke: Processing consumption service reversal for document: {}", report.getId());

            consumptionService.handleConsumption(report);

            logger.info("handleConsumptionServiceForRevoke: Consumption service reversal successful for document: {}", report.getId());

        } catch (Exception e) {
            logger.error("handleConsumptionServiceForRevoke: Consumption service reversal failed for document: {}. Error: {}",
                        report.getId(), e.getMessage(), e);

            // Determine specific error type for better error messages
            String errorMessage;
            if (e.getMessage() != null && e.getMessage().toLowerCase().contains("consumption")) {
                errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_CONSUMPTION_SERVICE_REVERSAL, report.getId(), e.getMessage());
            } else if (e.getMessage() != null && e.getMessage().toLowerCase().contains("database")) {
                errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_DATABASE_DURING_CONSUMPTION_REVERSAL, report.getId(), e.getMessage());
            } else {
                errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_UNEXPECTED_CONSUMPTION_REVERSAL, report.getId(), e.getMessage());
            }
            throw new RuntimeException(errorMessage, e);
        }
    }

    @Override
    public void convertFailedToDraft(long reportId, IAuthContextViewModel auth) {

        DocumentApprovalContainer report = documentApprovalContainerRepository
                .findByCompanyCodeAndId(auth.getCompanyCode(), reportId)
                .orElseThrow(() -> new RecordNotFoundException("Could not find Report with id: " + reportId));

        if (!report.getReportStatus().equals(ReportStatus.FAILED)) {
            throw new DomainInvariantException("Only report with FAILED status can be converted to DRAFT!");
        }
        report.setReportStatus(ReportStatus.DRAFT);
        report.setStatusRemarks(null);
        report.setProcessMethod(ProcessMethod.MANUAL);

        logger.trace("convertFailedToDraft: Saving the report");
        documentApprovalContainerRepository.saveAndFlush(report);
        logger.trace("convertFailedToDraft: Save successful; exiting");
    }

    @Override
    public List<DocumentApprovalContainerViewModel> getQueue(IAuthContextViewModel auth) {


        return documentApprovalContainerRepository.getUserQueue(auth.getCompanyCode(), auth.getUserId()).stream().map(e -> {
            DocumentApprovalContainerViewModel viewModel = new DocumentApprovalContainerViewModel();
            BeanUtils.copyProperties(e, viewModel);
            viewModel.setDocumentType(e.getDocumentMetadata().getDocumentType());
            viewModel.setDocumentGroup(e.getDocumentMetadata().getDocumentGroup());

//                    if (e.getReportStatus() == ReportStatus.SENT_BACK) {
//                        ReportState state = stateRepository.getStateForResubmission(auth.getCompanyCode(), e.getId(), e.getActionLevel(), ExpenseActionStatus.SENT_BACK)
//                                .orElseThrow(() -> new RecordNotFoundException("Could not find the sent-back state for expense: " + e.getId()));
//
//                        viewModel.setSendBackRemarks(state.getRemarks());
//                        viewModel.setCurrentApproverEmployeeCode(state.getApproverEmployeeCode());
//                        viewModel.setCurrentApproverFirstName(state.getApproverFirstName());
//                        viewModel.setCurrentApproverLastName(state.getApproverLastName());
//                    }
//
//                    if (e.getReportStatus() == ReportStatus.DECLINED) {
//                        ReportState state = stateRepository.findFirstByCompanyCodeAndExpenseReportIdAndStatus(auth.getCompanyCode(), e.getId(), ExpenseActionStatus.REJECTED)
//                                .orElseThrow(() -> new RecordNotFoundException("Could not find the sent-back state for expense: " + e.getId()));
//
//                        viewModel.setRejectRemarks(state.getRemarks());
//                    }

            if (e.getReportStatus() == ReportStatus.SUBMITTED) {
                Optional<ReportState> currentState = stateRepository
                        .findFirstByCompanyCodeAndDocumentApprovalContainerIdAndStatusOrderByLevel(auth.getCompanyCode(), e.getId(),
                                ExpenseActionStatus.UNACTIONED);
                if (currentState.isPresent()) {
                    viewModel.setCurrentApproverEmployeeCode(currentState.get().getApproverEmployeeCode());
                    viewModel.setCurrentApproverFirstName(currentState.get().getApproverFirstName());
                    viewModel.setCurrentApproverLastName(currentState.get().getApproverLastName());
                }
            }

            return viewModel;
        }).collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    @Override
    public GenericPageableViewModel<DocumentApprovalContainerViewModel> getQueueV2(IAuthContextViewModel auth, QueueFilterViewModel viewModelReq) {

        GenericPageableViewModel<DocumentApprovalContainerViewModel> response = new GenericPageableViewModel<>();
        int pageNumber = viewModelReq.getPage() - 1;

        // query with filters
        StringBuilder searchWhereQuery = getQuery(viewModelReq);

        // get total elements
        int totalElements = queueRepo.getTotalElementsForUser(searchWhereQuery.toString(), auth.getCompanyCode(), auth.getUserId());

        // get results
        StringBuilder query = new StringBuilder(queueRepo.selectAllQueryForUser(auth.getCompanyCode(), auth.getUserId()))
                .append(searchWhereQuery.toString());
        List<Object[]> viewModelsFromDB = queueRepo.getUserQueueData(query.toString(), pageNumber,
                viewModelReq.getPageSize(), StringConstants.toSnakeCase(viewModelReq.getSortByColumnName()),
                StringConstants.getSortLevel(viewModelReq.getSortOrder()));

        List<DocumentApprovalContainerViewModel> formattedResponse = new ArrayList<>();
        viewModelsFromDB.forEach(obj -> {
            formattedResponse.add(setUserQueueResponse(obj, auth));
        });

        response.setPages(getTotalPages(totalElements, viewModelReq.getPageSize()));
        response.setTotalElements(totalElements);
        response.setData(formattedResponse);
        return response;

    }

    public GenericPageableViewModel<DocumentApprovalContainerViewModel> getUserQueueV3(IAuthContextViewModel auth, QueueFilterViewModel filterValues) {
        // Initialise the page
        GenericPageableViewModel<DocumentApprovalContainerViewModel> pagedQueue = new GenericPageableViewModel<>();
        Sort sort = null;

        String sortColumn = filterValues.getSortByColumnName() == null ? "default" : filterValues.getSortByColumnName();
        sort = switch (sortColumn) {
            case "reportStatus" -> Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "reportStatus");
            case "createdDate" -> Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "createdDate");
            case "approvalContainerTitle" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "approvalContainerTitle");
            case "approvalContainerClaimAmount" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "approvalContainerClaimAmount");
            case "firstName" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "firstName").and(Sort.by(Sort.Direction.DESC, "id"));
            default -> Sort.by(Sort.Direction.DESC, "updatedTimestamp").and(Sort.by(Sort.Direction.DESC, "id"));
        };

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize(), sort);

        Page<DocumentApprovalContainerViewModel> documentApprovalContainerPaged = customDocumentApprovalContainerRepository.getUserQueueV3(auth.getCompanyCode(), auth.getUserId(), filterValues, pageable);

        // Set response
        pagedQueue.setData(documentApprovalContainerPaged.getContent());
        pagedQueue.setPages(documentApprovalContainerPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(documentApprovalContainerPaged.getTotalElements()));

        return pagedQueue;
    }

    @Override
    public InvoicePreviewViewModel getInvoiceQueueById(IAuthContextViewModel auth, long id) {

        InvoicePreviewViewModel invoicePreviewViewModel = new InvoicePreviewViewModel();

        Document document = invoiceServiceImplementation.getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks(auth.getCompanyCode(), id);
        InvoiceHeader invoiceHeader = invoiceServiceImplementation.getInvoiceHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks(auth.getCompanyCode(), document);

        invoicePreviewViewModel.setId(id);
        Optional.ofNullable(invoiceHeader).ifPresent(value -> {
            String poType = value.getDocument().getDocumentSubgroup().getHasPrecedingDocument() ? "PO Based" : "NON-PO Based";
            invoicePreviewViewModel.setPoType(poType); // change as per points in P2P:865
//            invoicePreviewViewModel.setPoType(Optional.ofNullable(value.getInvoiceType()).map(LookupData::getValue).orElse(null));

            Optional.ofNullable(value.getSupplier()).ifPresent(supplier -> {
                invoicePreviewViewModel.setSupplierName(supplier.getSupplierCompanyName());
//            invoicePreviewViewModel.setNetPaybleAmount(value.getTotalAmount() );
                invoicePreviewViewModel.setMsmeStatus(supplier.getMsmeStatus());
            });

            List<InvoiceItemDetailsViewModel> invoiceItems = invoiceHeader.getInvoiceItems().stream()
                    .sorted(Comparator.comparing(InvoiceItem::getId)).map(poItem -> invoiceServiceImplementation.invoiceItemToDto(poItem, auth))
                    .collect(Collectors.toList());
            // get tax amount
//            BigDecimal totalGstAmountSum = invoiceItems.stream()
//                    .map(InvoiceItemDetailsViewModel::getTotalGstAmount)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalGstAmountSum = invoiceItems.stream()
                    .map(InvoiceItemDetailsViewModel::getTotalGstAmount)
                    .filter(Objects::nonNull) // Exclude null values
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            invoicePreviewViewModel.setTaxAmount(totalGstAmountSum);

            invoicePreviewViewModel.setItemList(invoiceItems);
        });

        Optional.of(document).ifPresent(value -> {

            String dateOfDocument = Optional.ofNullable(value.getDocumentDate()).map(DateTimeUtils::formatDate).orElse(null);
            if (null != value.getDocumentDate() && !dateOfDocument.isEmpty()) {
                int financialYearStart = DateTimeUtils.getFinancialYearStart(DateTimeUtils.parseFormattedDatetoLocalDate(dateOfDocument));
                invoicePreviewViewModel.setFinancialYear(financialYearStart + "-" + (financialYearStart + 1));
            }
            invoicePreviewViewModel.setDateOfDocument(Optional.ofNullable(value.getDocumentDate())
                    .map(DateTimeUtils::formatDateJPQL).orElse(null));

            invoicePreviewViewModel.setInvoiceAmount(value.getClaimAmount());
            invoicePreviewViewModel.setNetPaybleAmount(value.getClaimAmount()); // Todo Vishal: Check changes

            // set report status
            DocumentApprovalContainer dac = document.getDocumentApprovalContainer();
            invoicePreviewViewModel.setReportStatus(dac.getReportStatus());

        });


        return invoicePreviewViewModel;
    }

    @Override
    public GenericPageableViewModel<InvoiceContainerViewModel> getInvoiceQueue(IAuthContextViewModel auth, QueueFilterViewModel filterValues) {
        // Initialise the page
        GenericPageableViewModel<InvoiceContainerViewModel> pagedQueue = new GenericPageableViewModel<>();
        Sort sort = null;

        String sortColumn = filterValues.getSortByColumnName() == null ? "default" : filterValues.getSortByColumnName();
        sort = switch (sortColumn) {
            case "documentIdentifier" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "documentIdentifier");
            case "lastActionAt" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "lastActionedAt").and(Sort.by(Sort.Direction.DESC, "id"));
            default -> Sort.by(Sort.Direction.DESC, "updatedTimestamp").and(Sort.by(Sort.Direction.DESC, "id"));
        };

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize(), sort);

        // Business logic start
        LookupData invoiceLookup = lookupRepository.findByTypeAndValue(StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_INVOICE)
                .orElseThrow(() -> {
                    logger.error(String.format("getInvoiceQueue: Lookup table error. Could not find document of type: %s and value: %s", StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_INVOICE));
                    return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_NOT_FOUND);
                });

        // Get all metadata that lookup type
        List<Long> documentMetadataArray = metadataRepository
                .findByCompanyCodeAndDocumentCategoryIdOrderByIdDesc(
                        auth.getCompanyCode(),
                        invoiceLookup.getId()
                ).stream()
                .map(DocumentMetadata::getId)
                .distinct()
                .toList();

//        Page<DocumentApprovalContainer> dacPaged = documentApprovalContainerRepository.findByCompanyCodeAndCreatingUserIdAndDocumentMetadataIdsIn(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, pageable);
        String rbacKey = orgHierarchyRepository.findDistinctKeyByCompanyCode(auth.getCompanyCode());
        List<String> rbacValues = orgHierarchyService.getValuesForRbac(auth.getUserId(), auth.getCompanyCode()); // List of values for the key

        Page<InvoiceContainerViewModel> documentApprovalContainerPaged = customDocumentApprovalContainerRepository.getInvoiceQueue(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, filterValues, pageable, rbacKey, rbacValues);

        List<Long> documentApprovalContainerIds = documentApprovalContainerPaged.getContent().stream().map(InvoiceContainerViewModel::getId).collect(Collectors.toList());
        Page<DocumentApprovalContainer> dacPaged = documentApprovalContainerRepository.findByCompanyCodeAndIdsIn(auth.getCompanyCode(), documentApprovalContainerIds, pageable);

        List<Long> invoiceReceivedIds = documentApprovalContainerPaged.getContent()
                .stream()
                .map(InvoiceContainerViewModel::getInvoiceReceivedId)
                .filter(Objects::nonNull)
                .toList();
        List<InvoiceReceived> invoiceReceivedList = invoiceReceivedRepository.findAllById(invoiceReceivedIds);

        Map<Long, InvoiceReceived> invoiceReceivedMap = invoiceReceivedList.stream()
                .collect(Collectors.toMap(InvoiceReceived::getInvoiceReceivedId, Function.identity()));

        documentApprovalContainerPaged.getContent().forEach(viewModel -> {
            Long invoiceReceivedId = viewModel.getInvoiceReceivedId();
            if (invoiceReceivedId != null && invoiceReceivedMap.containsKey(invoiceReceivedId)) {
                InvoiceReceived invoiceReceived = invoiceReceivedMap.get(invoiceReceivedId);

                // Update fields

                viewModel.setRequestId(invoiceReceived.getDmrReferenceId() == null ? viewModel.getDocumentIdentifier() : invoiceReceived.getDmrReferenceId());
                viewModel.setIsOcrDone(invoiceReceived.getIsOcrDone());
                viewModel.setOcrStatus(invoiceReceived.getOcrStatus());

                // Step 4: Set signedUrl using GcpCSFileIOProvider
                String filePath = invoiceReceived.getDocumentUrl();
                if (filePath != null) {
                    URL signedUrl = gcpCSFileIOProvider.getSignedUrl(filePath);
                    viewModel.setSignedUrl(signedUrl != null ? signedUrl.toString() : null);
                }
            }
        });

        QueueStatisticsViewModel statistics = customDocumentApprovalContainerRepository.getStatistics(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, rbacKey, rbacValues);

        // Get and set last approver data
        updateLastApprovedBy(documentApprovalContainerPaged.getContent(), dacPaged.getContent());
        // Business logic end

        // Set response
        pagedQueue.setData(documentApprovalContainerPaged.getContent());
        pagedQueue.setPages(documentApprovalContainerPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(documentApprovalContainerPaged.getTotalElements()));
        pagedQueue.setStats(statistics);

        return pagedQueue;
    }

    public GenericPageableViewModel<BudgetContainerViewModel> getBudgetQueue(Long budgetStructureMasterId, IAuthContextViewModel auth,
                                                                             QueueFilterViewModel filterValues) {
        // Initialise the page
        GenericPageableViewModel<BudgetContainerViewModel> pagedQueue = new GenericPageableViewModel<>();

        Sort sort = null;

        String sortColumn = filterValues.getSortByColumnName() == null ? "default" : filterValues.getSortByColumnName();
        sort = switch (sortColumn) {
            case "details" -> Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "id");

            case "lastAction" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "lastActioned").and(Sort.by(Sort.Direction.DESC, "id"));
            case "createdBy" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "createdBy").and(Sort.by(Sort.Direction.DESC, "id"));
            default -> Sort.by(
                    Sort.Order.desc("id")
            );
        };

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize(), sort);
        // Business logic start
        LookupData budgetLookup = lookupRepository.findByTypeAndValue(StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_BUDGET)
                .orElseThrow(() -> {
                            logger.error(String.format("getBudgetQueue: Lookup table error. Could not find document of type: %s and value: %s", StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_PR));
                            return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_PR_NOT_FOUND);
                        }
                );
        ;
        // Get all metadata that lookup type
        List<Long> documentMetadataArray = metadataRepository
                .findByCompanyCodeAndDocumentCategoryIdOrderByIdDesc(
                        auth.getCompanyCode(),
                        budgetLookup.getId()
                ).stream()
                .map(DocumentMetadata::getId)
                .distinct()
                .toList();

//        String rbacKey = orgHierarchyRepository.findDistinctKeyByCompanyCode(auth.getCompanyCode());
//        List<String> rbacValues = orgHierarchyService.getValuesForRbac(auth.getUserId(), auth.getCompanyCode()); // List of values for the key

//        Page<DocumentApprovalContainer> dacPaged = documentApprovalContainerRepository.findByCompanyCodeAndCreatingUserIdAndDocumentMetadataIdsIn(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, pageable);
        Page<BudgetContainerViewModel> documentApprovalContainerPaged = customDocumentApprovalContainerRepository
                .getBudgetQueue(auth.getCompanyCode(), auth.getUserId(), budgetStructureMasterId, documentMetadataArray, filterValues, pageable, "", List.of());

        List<Long> documentApprovalContainerIds = documentApprovalContainerPaged.getContent().stream().map(BudgetContainerViewModel::getId).collect(Collectors.toList());
        Page<DocumentApprovalContainer> dacPaged = documentApprovalContainerRepository.findByCompanyCodeAndIdsIn(auth.getCompanyCode(), documentApprovalContainerIds, pageable);

        QueueStatisticsViewModel statistics = customDocumentApprovalContainerRepository.getStatistics(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, null, List.of());

        // Get and set last approver data
        updateLastApprovedBy(documentApprovalContainerPaged.getContent(), dacPaged.getContent());

        // Business logic end

        // Set response
        pagedQueue.setData(documentApprovalContainerPaged.getContent());
        pagedQueue.setPages(documentApprovalContainerPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(documentApprovalContainerPaged.getTotalElements()));
        pagedQueue.setStats(statistics);

        return pagedQueue;

    }

    public GenericPageableViewModel<PurchaseRequestContainerViewModel> getPurchaseRequestQueue(IAuthContextViewModel auth, QueueFilterViewModel filterValues) {
        // Initialise the page
        GenericPageableViewModel<PurchaseRequestContainerViewModel> pagedQueue = new GenericPageableViewModel<>();
        Sort sort = null;

        String sortColumn = filterValues.getSortByColumnName() == null ? "default" : filterValues.getSortByColumnName();
        sort = switch (sortColumn) {
            case "prDetails" -> Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "id");

            case "lastAction" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "lastActioned").and(Sort.by(Sort.Direction.DESC, "id"));
            default -> Sort.by(
                    Sort.Order.desc("id")
            );
        };
        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize(), sort);

        // Business logic start
        LookupData purchaseRequestLookup = lookupRepository.findByTypeAndValue(StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_PR)
                .orElseThrow(() -> {
                            logger.error(String.format("getPurchaseRequestQueue: Lookup table error. Could not find document of type: %s and value: %s", StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_PR));
                            return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_PR_NOT_FOUND);
                        }
                );
        ;
        // Get all metadata that lookup type - check if rbac is needed for metadata.
        List<Long> documentMetadataArray = metadataRepository
                .findByCompanyCodeAndDocumentCategoryIdOrderByIdDesc(
                        auth.getCompanyCode(),
                        purchaseRequestLookup.getId()
                ).stream()
                .map(DocumentMetadata::getId)
                .distinct()
                .toList();
        String rbacKey = orgHierarchyRepository.findDistinctKeyByCompanyCode(auth.getCompanyCode());
        List<String> rbacValues = orgHierarchyService.getValuesForRbac(auth.getUserId(), auth.getCompanyCode()); // List of values for the key

//        Page<DocumentApprovalContainer> dacPaged = documentApprovalContainerRepository.findByCompanyCodeAndCreatingUserIdAndDocumentMetadataIdsIn(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, pageable);
        Page<PurchaseRequestContainerViewModel> documentApprovalContainerPaged = customDocumentApprovalContainerRepository
                .getPRQueue(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, filterValues, pageable, rbacKey, rbacValues);

        List<Long> documentApprovalContainerIds = documentApprovalContainerPaged.getContent().stream().map(PurchaseRequestContainerViewModel::getId).collect(Collectors.toList());
        Page<DocumentApprovalContainer> dacPaged = documentApprovalContainerRepository.findByCompanyCodeAndIdsIn(auth.getCompanyCode(), documentApprovalContainerIds, pageable);


        QueueStatisticsViewModel statistics = customDocumentApprovalContainerRepository.getStatistics(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, rbacKey, rbacValues);

        // get converted PR count
        QueryResults<ProcurementViewModel> results = customDocumentApprovalContainerRepository.getProcurementViewModelQueryResults(auth.getCompanyCode(), documentMetadataArray, filterValues, pageable, true);
        List<Long> prIds = results.getResults().stream().map(ProcurementViewModel::getPrId).collect(Collectors.toList());
        List<Long> prItemIds = purchaseRequestHeaderRepository.findByIdsIn(prIds).stream()
                .flatMap(prh -> prh.getPrItems().stream())
                .map(PurchaseRequestItem::getId).collect(Collectors.toList());
        List<ConsumedItemAvailableCountViewModel> consumedItems = customDocumentApprovalContainerRepository.getPRItemsConsumedAndNotConsumedInPOsWithCounts(prItemIds);
        getConvertedPRStatistics(consumedItems, results.getResults(), statistics);
        // Get and set last approver data
        updateLastApprovedBy(documentApprovalContainerPaged.getContent(), dacPaged.getContent());
        // Business logic end

        // Set response
        pagedQueue.setData(documentApprovalContainerPaged.getContent());
        pagedQueue.setPages(documentApprovalContainerPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(documentApprovalContainerPaged.getTotalElements()));
        pagedQueue.setStats(statistics);

        return pagedQueue;
    }

    public QueueStatisticsViewModel getConvertedPRStatistics(List<ConsumedItemAvailableCountViewModel> consumedItems, List<ProcurementViewModel> procurementViewModels, QueueStatisticsViewModel stats) {
        // Group consumed items by prId
        Map<Long, List<ConsumedItemAvailableCountViewModel>> itemsGroupedByPrId = consumedItems.stream()
                .collect(Collectors.groupingBy(ConsumedItemAvailableCountViewModel::getPrId));

        Set<Long> fullyConvertedPRIds = itemsGroupedByPrId.entrySet().stream()
                .filter(entry -> entry.getValue().stream().allMatch(item -> item.getReportStatus() == ReportStatus.ACCEPTED))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        // Calculate the count of fully converted PRs
        long convertedPRCount = fullyConvertedPRIds.size();

        // Calculate the total amount of fully converted PRs
        BigDecimal convertedPRAmount = procurementViewModels.stream()
                .filter(procurementViewModel -> fullyConvertedPRIds.contains(procurementViewModel.getPrId()))
                .map(ProcurementViewModel::getPrAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        stats.setConvertedPRCount(convertedPRCount);
        stats.setConvertedPRAmount(convertedPRAmount);

        return stats;
    }

    @Override
    public GenericPageableViewModel<ProcurementViewModel> getPurchaseRequestProcurementQueue(IAuthContextViewModel auth, QueueFilterViewModel filterValues) {
        GenericPageableViewModel<ProcurementViewModel> pagedQueue = new GenericPageableViewModel<>();
        Sort sort = null;

        String sortColumn = filterValues.getSortByColumnName() == null ? "default" : filterValues.getSortByColumnName();
        sort = switch (sortColumn) {
            case "prDetails" -> Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "id");

            case "lastAction" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "lastActioned").and(Sort.by(Sort.Direction.DESC, "id"));
            default -> Sort.by(
                    Sort.Order.desc("id")
            );
        };

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize(), sort);

        // Business logic start
        LookupData purchaseRequestLookup = lookupRepository.findByTypeAndValue(StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_PR)
                .orElseThrow(() -> {
                            logger.error(String.format("getPurchaseRequestQueue: Lookup table error. Could not find document of type: %s and value: %s", StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_PR));
                            return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_PR_NOT_FOUND);
                        }
                );
        // Get all metadata that lookup type
        List<Long> documentMetadataArray = metadataRepository
                .findByCompanyCodeAndDocumentCategoryIdOrderByIdDesc(
                        auth.getCompanyCode(),
                        purchaseRequestLookup.getId()
                ).stream()
                .map(DocumentMetadata::getId)
                .distinct()
                .toList();

        Page<DocumentApprovalContainer> dacPaged = documentApprovalContainerRepository.findByCompanyCodeAndCreatingUserIdAndDocumentMetadataIdsIn(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, pageable);
        Page<ProcurementViewModel> documentApprovalContainerPaged = customDocumentApprovalContainerRepository
                .getProcurementData(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, filterValues, pageable, false);

        // get all data stats and not just paged
        ProcurementStatisticsViewModel statistics = calculateStatistics(customDocumentApprovalContainerRepository
                .getProcurementData(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, filterValues, pageable, true).getContent());

        // Get and set last approver data
        updateLastApprovedBy(documentApprovalContainerPaged.getContent(), dacPaged.getContent());

        // Set response
        pagedQueue.setData(documentApprovalContainerPaged.getContent());
        pagedQueue.setPages(documentApprovalContainerPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(documentApprovalContainerPaged.getTotalElements()));
        pagedQueue.setNewStats(statistics);

        return pagedQueue;
    }

    public ProcurementStatisticsViewModel calculateStatistics(List<ProcurementViewModel> viewModels) {
        ProcurementStatisticsViewModel stats = new ProcurementStatisticsViewModel();

        // Initialize counts and amounts
        Map<ProcurementStatus, Long> counts = new EnumMap<>(ProcurementStatus.class);
        Map<ProcurementStatus, BigDecimal> amounts = new EnumMap<>(ProcurementStatus.class);

        for (ProcurementStatus status : ProcurementStatus.values()) {
            counts.put(status, 0L);
            amounts.put(status, BigDecimal.ZERO);
        }

        // Aggregate counts and amounts
        for (ProcurementViewModel viewModel : viewModels) {
            ProcurementStatus status = viewModel.getProcurementStatus();
            counts.put(status, counts.get(status) + 1);
            amounts.put(status, amounts.get(status).add(viewModel.getPrAmount()));
        }

        // Set statistics in the view model
        stats.setReadyToConvertCount(counts.get(ProcurementStatus.READY_TO_CONVERT));
        stats.setReadyToConvertAmount(amounts.get(ProcurementStatus.READY_TO_CONVERT));

        // sent back is consodered as ready to convert to show stats - confirm
//        stats.setReadyToConvertCount(counts.get(ProcurementStatus.READY_TO_CONVERT) + counts.get(ProcurementStatus.SENT_BACK));
//        stats.setReadyToConvertAmount(amounts.get(ProcurementStatus.READY_TO_CONVERT).add(amounts.get(ProcurementStatus.SENT_BACK)));

        stats.setPartiallyConvertedCount(counts.get(ProcurementStatus.PARTIALLY_CONVERTED));
        stats.setPartiallyConvertedAmount(amounts.get(ProcurementStatus.PARTIALLY_CONVERTED));

        stats.setInProgressCount(counts.get(ProcurementStatus.IN_PROGRESS));
        stats.setInProgressAmount(amounts.get(ProcurementStatus.IN_PROGRESS));

        stats.setSentBackCount(counts.get(ProcurementStatus.SENT_BACK));
        stats.setSentBackAmount(amounts.get(ProcurementStatus.SENT_BACK));

        stats.setPartiallyClosedCount(counts.get(ProcurementStatus.PARTIALLY_CLOSED));
        stats.setPartiallyClosedAmount(amounts.get(ProcurementStatus.PARTIALLY_CLOSED));

        stats.setFullyConvertedCount(counts.get(ProcurementStatus.FULLY_CONVERTED));
        stats.setFullyConvertedAmount(amounts.get(ProcurementStatus.FULLY_CONVERTED));

        stats.setClosedCount(counts.get(ProcurementStatus.CLOSED));
        stats.setClosedAmount(amounts.get(ProcurementStatus.CLOSED));

        return stats;
    }

    @Override
    public POPreviewViewModel getPurchaseOrderQueueById(IAuthContextViewModel auth, long id) {
        POPreviewViewModel poPreviewViewModel = new POPreviewViewModel();

        DocumentApprovalContainer documentApprovalContainer = documentApprovalContainerRepository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> {
                    logger.error(String.format("getPurchaseOrderQueueById: DocumentApprovalContainer with id: {%s} could not be found.", id));
                    return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_PO_NOT_FOUND);
                });

        Document document = purchaseOrderService.getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks(auth.getCompanyCode(), id);

        PurchaseOrderHeader poHeader = purchaseOrderService.getPOHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks(auth.getCompanyCode(), document);
        List<PurchaseOrderItemDetailsViewModel> poItems = poHeader.getPurchaseOrderItems().stream()
                .map(poItem -> purchaseOrderService.poItemToDto(poItem, auth))
                .collect(Collectors.toList());

        populatePOPreviewViewModel(poPreviewViewModel, documentApprovalContainer, document, poHeader, poItems);

        return poPreviewViewModel;
    }

    // Utility method to get budget code and amount
    private Optional<Budget> getBudget(Document document) {
        return Optional.ofNullable(document)
                .map(Document::getDocumentSubgroup)
                .map(DocumentSubgroup::getBudget);
    }

    // Populate method
    public void populatePOPreviewViewModel(POPreviewViewModel poPreviewViewModel,
                                           DocumentApprovalContainer documentApprovalContainer,
                                           Document document, PurchaseOrderHeader poHeader,
                                           List<PurchaseOrderItemDetailsViewModel> poItems) {

        // DocumentApprovalContainer
        Optional.ofNullable(documentApprovalContainer).ifPresent(value -> {
            poPreviewViewModel.setId(value.getId());
            poPreviewViewModel.setRequestorName(Optional.ofNullable(value.getFirstName()).orElse("") + " " + Optional.ofNullable(value.getLastName()).orElse(""));
            poPreviewViewModel.setTotalPOAmount(value.getApprovalContainerClaimAmount());
            poPreviewViewModel.setReportStatus(value.getReportStatus());
        });

        // Document
        Optional.ofNullable(document).ifPresent(value -> {
            String dateOfDocument = Optional.ofNullable(value.getDocumentDate()).map(DateTimeUtils::formatDate).orElse(null);
            int financialYearStart = DateTimeUtils.getFinancialYearStart(DateTimeUtils.parseFormattedDatetoLocalDate(dateOfDocument));
            poPreviewViewModel.setFinancialYear(financialYearStart + "-" + (financialYearStart + 1));
            poPreviewViewModel.setDateOfDocument(dateOfDocument);
            poPreviewViewModel.setSubgroupFieldsJson(document.getDocumentSubgroup().getSubgroupFieldsJson());

//            getBudget(value).ifPresent(budget -> {
//                poPreviewViewModel.setBudgetCode(budget.getBudgetCode());
//                poPreviewViewModel.setBudgetAmount(budget.getTotal().subtract(budget.getLocked()));
//            });

            poPreviewViewModel.setDocNo(value.getDocNo());
        });

        // PurchaseOrderHeader
        Optional.ofNullable(poHeader).ifPresent(value -> {
            poPreviewViewModel.setHasPrecedingDocument(value.getHasPrecedingDocument());
            poPreviewViewModel.setPoType((null != value.getHasPrecedingDocument() && value.getHasPrecedingDocument()) ? "PR based" : "Non PR based");
            poPreviewViewModel.setSupplierName(Optional.ofNullable(value.getVendor()).map(Company::getSupplierCompanyName).orElse(null));
            poPreviewViewModel.setPaymentTerm(Optional.ofNullable(value.getPaymentTerms()).map(LookupData::getValue).orElse(null));
//            poPreviewViewModel.setPoAmountWOGst(value.getTotalAmount());
            poPreviewViewModel.setPrDescription(value.getPurchaseReason());
        });


        BigDecimal totalAmountWithoutGst = poItems.stream()
                .map(PurchaseOrderItemDetailsViewModel::getAmountWithoutGst)
                .filter(amount -> amount != null) // Filter out null values to avoid NPE
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        poPreviewViewModel.setPoAmountWOGst(totalAmountWithoutGst);

        BigDecimal claimAmountSumByPrItemIds = purchaseRequestItemRepository.getClaimAmountSumByPrItemIds(poItems.stream().map(PurchaseOrderItemDetailsViewModel::getPrItemId).toList());
        poPreviewViewModel.setTotalPRAmount(claimAmountSumByPrItemIds);
        poPreviewViewModel.setPoItems(poItems);


    }

    @Override
    public GenericPageableViewModel<PurchaseOrderContainerViewModel> getPurchaseOrderQueue(IAuthContextViewModel auth, QueueFilterViewModel filterValues) {
        // Initialise the page
        GenericPageableViewModel<PurchaseOrderContainerViewModel> pagedQueue = new GenericPageableViewModel<>();
        Sort sort = null;

        String sortColumn = filterValues.getSortByColumnName() == null ? "default" : filterValues.getSortByColumnName();
        sort = switch (sortColumn) {
            case "orderStatus" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "orderStatus").and(Sort.by(Sort.Direction.DESC, "id"));

            case "lastAction" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "lastActioned").and(Sort.by(Sort.Direction.DESC, "id"));
            default -> Sort.by(
                    Sort.Order.desc("id")
            );
        };

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize(), sort);

        // Business logic start
        LookupData purchaseOrderLookup = lookupRepository.findByTypeAndValue(StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_PO)
                .orElseThrow(() -> {
                            logger.error(String.format("getPurchaseOrderQueue: Lookup table error. Could not find document of type: %s and value: %s", StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_PO));
                            return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_PR_NOT_FOUND);
                        }
                );
        ;

        // Get all metadata that lookup type
        List<Long> documentMetadataArray = metadataRepository
                .findByCompanyCodeAndDocumentCategoryIdOrderByIdDesc(
                        auth.getCompanyCode(),
                        purchaseOrderLookup.getId()
                ).stream()
                .map(DocumentMetadata::getId)
                .distinct()
                .toList();

        String rbacKey = orgHierarchyRepository.findDistinctKeyByCompanyCode(auth.getCompanyCode());
        List<String> rbacValues = orgHierarchyService.getValuesForRbac(auth.getUserId(), auth.getCompanyCode()); // List of values for the key


//        Page<DocumentApprovalContainer> dacPaged = documentApprovalContainerRepository.findByCompanyCodeAndCreatingUserIdAndDocumentMetadataIdsIn(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, pageable);
        Page<PurchaseOrderContainerViewModel> documentApprovalContainerPaged = customDocumentApprovalContainerRepository
                .getPOQueue(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, filterValues, pageable, rbacKey, rbacValues);

        List<Long> documentApprovalContainerIds = documentApprovalContainerPaged.getContent().stream().map(PurchaseOrderContainerViewModel::getId).collect(Collectors.toList());
        Page<DocumentApprovalContainer> dacPaged = documentApprovalContainerRepository.findByCompanyCodeAndIdsIn(auth.getCompanyCode(), documentApprovalContainerIds, pageable);

        QueueStatisticsViewModel statistics = customDocumentApprovalContainerRepository.getStatistics(auth.getCompanyCode(), auth.getUserId(), documentMetadataArray, rbacKey, rbacValues);

        // Get and set last approver data
        updateLastApprovedBy(documentApprovalContainerPaged.getContent(), dacPaged.getContent());
        // Business logic end

        // Set response
        pagedQueue.setData(documentApprovalContainerPaged.getContent());
        pagedQueue.setPages(documentApprovalContainerPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(documentApprovalContainerPaged.getTotalElements()));
        pagedQueue.setStats(statistics);

        return pagedQueue;
    }

    public <T extends BaseContainerViewModel> void updateLastApprovedBy(List<T> documentApprovalContainerViewModel, List<DocumentApprovalContainer> dac) {
        Map<Long, DocumentApprovalContainer> dacMap = dac.stream()
                .collect(Collectors.toMap(DocumentApprovalContainer::getId, d -> d));

        for (T viewModel : documentApprovalContainerViewModel) {
            DocumentApprovalContainer docContainer = dacMap.get(viewModel.getId());
            if (docContainer != null) {
                ReportState recentApprovedState = docContainer.getReportStates().stream()
                        .filter(rs -> ((ExpenseActionStatus.APPROVED == rs.getStatus())) || (ExpenseActionStatus.SENT_BACK == rs.getStatus()) || (ExpenseActionStatus.REJECTED == rs.getStatus()) || (ExpenseActionStatus.VALIDATION_ERROR == rs.getStatus()))
                        .max(Comparator.comparing(ReportState::getUpdatedTimestamp))
                        .orElse(null);

                if (recentApprovedState != null) {
                    viewModel.setLastApprovedBy(recentApprovedState.getApproverFirstName() + " " + recentApprovedState.getApproverLastName());
                    viewModel.setLastActionedAt(recentApprovedState.getUpdatedTimestamp() != null ? recentApprovedState.getUpdatedTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null);
                }
            }
        }
    }

    @Override
    public EmployeeAllValuesForKeyViewModel getKeyValuesByMetadataIdAndAmount(IAuthContextViewModel auth, ApprovalKeyValuePairsViewModel approvalMatrixGetViewModel) {
        EmployeeAllValuesForKeyViewModel response = null;
        EmployeeKeyRequestViewModel requestViewModel = new EmployeeKeyRequestViewModel();

        logger.info(String.format("getKeyValuesByMetadataIdAndAmount: Creating the approvalContainer object for metadata: {%s} and amount: {%s}", approvalMatrixGetViewModel.getDocumentMetadataId(), approvalMatrixGetViewModel.getApprovalContainerClaimAmount()));
        // Only the below 2 values are required
        DocumentApprovalContainer approvalContainer = DocumentApprovalContainer.builder()
                .documentMetadataId(approvalMatrixGetViewModel.getDocumentMetadataId())
                .approvalContainerClaimAmount(approvalMatrixGetViewModel.getApprovalContainerClaimAmount())
                .build();

        logger.info(String.format("getKeyValuesByMetadataIdAndAmount: Getting the approval definitions for metadata: {%s} and amount: {%s}", approvalMatrixGetViewModel.getDocumentMetadataId(), approvalMatrixGetViewModel.getApprovalContainerClaimAmount()));
        List<ApprovalDefinition> approvalDefinitions = getApprovalDefinitions(approvalContainer, auth, false);
        // Reporting manager will always come from CEM (hence non-null filtering)
        List<String> keys = approvalDefinitions.stream()
                .map(ApprovalDefinition::getApprovalMatcher)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        requestViewModel.setKeys(keys);

        try {
            String cemResponse = webClientHelper.makeRequest(ExternalServicesIdentifier.CEM_SERVICE.ordinal(),
                    HttpMethod.PUT,
                    UrlConstants.KEY_VALUE_ALL_PAIRS_LOOKUP,
                    Optional.of(requestViewModel),
                    null,
                    null,
                    String.class,
                    auth.getToken()).block();

            response = objectMapper.readValue(cemResponse, ServerResponseWithBodyEmployeeKeyPairValues.class).getBody();
        } catch (Exception e) {
            logger.error(String.format("There was an exception in the getting of the key value maps %n: %s", e));
        }

        return response;
    }

    private int getTotalPages(int totalElements, int pageSize) {
        // calculate number of pages, varies with number of records ie pageSize
        int pages = 1;
        if (totalElements < pageSize)
            return pages;
        else
            return pages = (totalElements / pageSize) + ((totalElements % pageSize) == 0 ? 0 : 1);
    }


    private DocumentApprovalContainerViewModel setUserQueueResponse(Object[] obj, IAuthContextViewModel auth) {
        DocumentApprovalContainerViewModel view = new DocumentApprovalContainerViewModel();
        view.setCurrentApproverEmployeeCode(null == obj[0] ? null : String.valueOf(obj[0]));
        view.setCurrentApproverFirstName(null == obj[1] ? null : String.valueOf(obj[1]));
        view.setLevel((null == obj[2] || "null".equals(obj[2])) ? 0 : (Integer) (obj[2]));
        view.setCurrentApproverLastName(null == obj[3] ? null : String.valueOf(obj[3]));
        view.setId(null == obj[4] ? null : ((Long) (obj[4])));
        view.setDocumentIdentifier(null == obj[5] ? null : String.valueOf(obj[5]));
        view.setCreatedDate(null == obj[6] ? null : String.valueOf(obj[6]));
        view.setSubmitDate(null == obj[7] ? null : String.valueOf(obj[7]));
        view.setStartDate(null == obj[8] ? null : String.valueOf(obj[8]));
        view.setEndDate(null == obj[9] ? null : String.valueOf(obj[9]));
        view.setApprovalContainerTitle(null == obj[10] ? null : String.valueOf(obj[10]));
        view.setDescription(null == obj[11] ? null : String.valueOf(obj[11]));
        view.setPurpose(null == obj[12] ? null : String.valueOf(obj[12]));
        view.setApprovalContainerClaimAmount(null == obj[13] ? null : (BigDecimal) (obj[13]));
        view.setReportStatus(null == obj[14] ? null : ReportStatus.values()[enumHandler((Short) (obj[14]))]);
        view.setActionLevel(null == obj[15] ? null : (Integer) (obj[15]));
        view.setContainsDeviation(null == obj[16] ? null : (Boolean) (obj[16]));
        view.setDeviationRemarks(null == obj[17] ? null : String.valueOf(obj[17]));
        view.setApprovalContainerSgstAmount(null == obj[18] ? null : (BigDecimal) (obj[18]));
        view.setApprovalContainerCgstAmount(null == obj[19] ? null : (BigDecimal) (obj[19]));
        view.setApprovalContainerIgstAmount(null == obj[20] ? null : (BigDecimal) (obj[20]));
        view.setApprovalContainerTaxableAmount(null == obj[21] ? null : (BigDecimal) (obj[21]));
        view.setFirstName(null == obj[22] ? null : String.valueOf(obj[22]));
        view.setMiddleName(null == obj[23] ? null : String.valueOf(obj[23]));
        view.setLastName(null == obj[24] ? null : String.valueOf(obj[24]));
        view.setEmployeeEmail(null == obj[25] ? null : String.valueOf(obj[25]));
        view.setEmployeeCode(null == obj[26] ? null : String.valueOf(obj[26]));
        view.setEmployeeGrade(null == obj[27] ? null : String.valueOf(obj[27]));
        view.setEmployeeSystemId(null == obj[28] ? null : ((Long) (obj[28])));
        view.setExpenseType(null == obj[29] ? null : (obj[29]).toString());
        view.setDocumentMetadataId(null == obj[30] ? null : (Long) (obj[30]));
        view.setDocumentGroup(null == obj[31] ? null : String.valueOf(obj[31]));
        view.setDocumentType(null == obj[32] ? null : String.valueOf(obj[32]));
        view.setSendBackRemarks(null == obj[33] ? null : String.valueOf(obj[33]));
        view.setRejectRemarks(null == obj[34] ? null : String.valueOf(obj[34]));
        view.setDelegationRemarks(null == obj[35] ? null : String.valueOf(obj[35]));
        view.setDefaultApproverRemarks(null == obj[36] ? null : String.valueOf(obj[36]));
        view.setContainsSentBack(null == obj[37] ? null : (Boolean) (obj[37]));
        view.setGlPostingDate(null == obj[38] ? null : ((Date) (obj[38])).toLocalDate());
        view.setPaymentDate(null == obj[39] ? null : ((Date) (obj[39])).toLocalDate());
        return view;
    }

    private StringBuilder getQuery(QueueFilterViewModel viewModelReq) {
        StringBuilder searchWhereQuery = new StringBuilder();
        Map<String, List<String>> criteriaMap = viewModelReq.getSearchCriteria();

        for (Map.Entry<String, List<String>> entry : criteriaMap.entrySet()) {
            String searchCriteria = entry.getKey();
            List<String> values = entry.getValue();

            if (values == null || values.isEmpty()) continue;

            String andOperator = " AND (";
            String orOperator = " OR ";
            String param;

            switch (searchCriteria) {
                case StringConstants.EMPLOYEE_NAME:
                    param = "CONCAT(er1.first_name,' ',er1.last_name) LIKE ";
                    searchWhereQuery.append(buildWhereClause(param, andOperator, orOperator, values));
                    break;
                case StringConstants.EMPLOYEE_EMAIL:
                    param = "er1.employee_email LIKE ";
                    searchWhereQuery.append(buildWhereClause(param, andOperator, orOperator, values));
                    break;
                case StringConstants.EMPLOYEE_CODE:
                    param = "er1.employee_code LIKE ";
                    searchWhereQuery.append(buildWhereClause(param, andOperator, orOperator, values));
                    break;
                case StringConstants.APPROVER_NAME:
                    param = "CONCAT(rs.approver_first_name,' ',rs.approver_last_name) LIKE ";
                    searchWhereQuery.append(buildWhereClause(param, andOperator, orOperator, values));
                    break;
                case StringConstants.APPROVER_EMPLOYEE_CODE:
                    param = "rs.approver_employee_code LIKE ";
                    searchWhereQuery.append(buildWhereClause(param, andOperator, orOperator, values));
                    break;
                case StringConstants.DOCUMENT_IDENTIFIER:
                    param = "er1.document_identifier LIKE ";
                    searchWhereQuery.append(buildWhereClause(param, andOperator, orOperator, values));
                    break;
                case StringConstants.REPORT_TITLE:
                    param = "er1.approval_container_title LIKE ";
                    searchWhereQuery.append(buildWhereClause(param, andOperator, orOperator, values));
                    break;
                case StringConstants.CREATED_DATE:
                    param = "er1.created_date::TEXT LIKE ";
                    searchWhereQuery.append(buildWhereClause(param, andOperator, orOperator, values));
                    break;
                case StringConstants.REPORT_CLAIM_AMOUNT:
                    param = "CAST(er1.approval_container_claim_amount AS TEXT) LIKE ";
                    searchWhereQuery.append(buildWhereClause(param, andOperator, orOperator, values));
                    break;
                case StringConstants.REPORT_STATUS:
                    param = "CAST(er1.report_status AS TEXT) LIKE ";
                    searchWhereQuery.append(buildStatusClause(param, andOperator, orOperator, values));
                    break;
            }
        }
        return searchWhereQuery;
    }

    private String buildWhereClause(String param, String andOperator, String orOperator, List<String> values) {
        StringBuilder clause = new StringBuilder(andOperator);
        for (String value : values) {
            if (value == null || value.isBlank()) continue;
            clause.append(param).append("'%").append(escapeSql(value)).append("%'").append(orOperator);
        }

        if (clause.lastIndexOf(orOperator) != -1) {
            clause.delete(clause.lastIndexOf(orOperator), clause.length()).append(")");
        } else {
            clause.setLength(0); // clear if nothing added
        }
        return clause.toString();
    }

    private String buildStatusClause(String param, String andOperator, String orOperator, List<String> values) {
        StringBuilder clause = new StringBuilder(andOperator);
        for (String value : values) {
            if (value == null || value.isBlank()) continue;
            String matchedStatus = getStatus(value.toUpperCase());
            clause.append(param)
                    .append("'%")
                    .append(ReportStatus.valueOf(matchedStatus).ordinal())
                    .append("%'")
                    .append(orOperator);
        }

        if (clause.lastIndexOf(orOperator) != -1) {
            clause.delete(clause.lastIndexOf(orOperator), clause.length()).append(")");
        } else {
            clause.setLength(0);
        }
        return clause.toString();
    }

    private String getStatus(String matchStatus) {
        return Arrays.stream(ReportStatus.values())
                .map(Enum::name)
                .filter(name -> name.contains(matchStatus))
                .findFirst()
                .orElse(ReportStatus.ALL.name());
    }

    private String escapeSql(String input) {
        return input.replace("'", "''");
    }


    private void validateReportSubmission(DocumentApprovalContainer report, Integer documentCategoryId) {
        BigDecimal lineTotal = new BigDecimal("0.0");
        for (Document e : report.getDocuments()) {
            lineTotal = lineTotal.add(e.getClaimAmount());
        }

//		Math.ceil(report.getReportClaimAmount()) != Math.ceil(lineTotal)
        if ((report.getApprovalContainerClaimAmount().setScale(0, RoundingMode.CEILING)
                .compareTo(lineTotal.setScale(0, RoundingMode.CEILING))) != 0) {
            throw new DomainInvariantException("Report claim does not match with line expenses");
        }

        if (!documentCategoryId.equals(StaticDataRegistry.PR_CATEGORY_ID)) { // PR is allowed to be submitted with 0 amount
//		report.getReportClaimAmount() == 0
            if (report.getApprovalContainerClaimAmount().compareTo(BigDecimal.ZERO) == 0) {
                throw new DomainInvariantException("Report total claim cannot be zero");
            }

            for (Document e : report.getDocuments()) {
//			e.getClaimAmount() == 0
                if (e.getClaimAmount().compareTo(BigDecimal.ZERO) == 0) {
                    throw new DomainInvariantException("Report contains an empty expense");
                }
            }
        } else {
            // FOR PR, set document date = submitted date
            report.getDocuments().forEach(doc -> doc.setDocumentDate(LocalDate.now()));
        }
    }

    private boolean getDeviationStatus(DocumentApprovalContainer report) {
        for (Document e : report.getDocuments()) {
            if (e.isDeviated()) {
                logger.info("getDeviationStatus: Expense {} is deviated with remarks: {}", e.getId(),
                        e.getDeviationRemarks());
                return true;
            }
        }

        logger.trace("getDeviationStatus: No deviated expenses found in the report");
        return false;
    }

    private DocumentApprovalContainerViewModel getViewModel(DocumentApprovalContainer report) {
        DocumentApprovalContainerViewModel viewModel = new DocumentApprovalContainerViewModel();
        BeanUtils.copyProperties(report, viewModel);

        // Submitted date
        viewModel.setCreatedDate(DateTimeUtils.formatDateJPQL(report.getCreatedDate()));
        viewModel.setSubmitDate(DateTimeUtils.formatDateJPQL(report.getSubmitDate()));
        viewModel.setStartDate(DateTimeUtils.formatDateJPQL(report.getStartDate()));
        viewModel.setEndDate(DateTimeUtils.formatDateJPQL(report.getEndDate()));

        return viewModel;
    }

    private void validateReport(DocumentApprovalContainer report, DocumentApprovalContainerUpdateViewModel viewModel) {
        BigDecimal lineAmountSum = new BigDecimal("0");
        BigDecimal sgstAmountSum = new BigDecimal("0");
        BigDecimal cgstAmountSum = new BigDecimal("0");
        BigDecimal igstAmountSum = new BigDecimal("0");
        BigDecimal taxableAmountSum = new BigDecimal("0");
        for (Document document : report.getDocuments()) {
            lineAmountSum = lineAmountSum.add(document.getClaimAmount() != null ? document.getClaimAmount() : new BigDecimal("0"));
            sgstAmountSum = sgstAmountSum.add(document.getSgstAmount() != null ? document.getSgstAmount() : new BigDecimal("0"));
            cgstAmountSum = cgstAmountSum.add(document.getCgstAmount() != null ? document.getCgstAmount() : new BigDecimal("0"));
            igstAmountSum = igstAmountSum.add(document.getIgstAmount() != null ? document.getIgstAmount() : new BigDecimal("0"));
            taxableAmountSum = taxableAmountSum.add(document.getTaxableAmount() != null ? document.getTaxableAmount() : new BigDecimal("0"));
        }

//		Math.ceil(lineAmountSum) != Math.ceil(viewModel.getReportClaimAmount())
        if (lineAmountSum.setScale(0, RoundingMode.CEILING).compareTo(viewModel.getApprovalContainerClaimAmount().setScale(0, RoundingMode.CEILING)) != 0) {
            throw new DomainInvariantException(
                    "Report total claim amount doesn't tally with sum of expense claim amounts");
        }

//		Math.ceil(sgstAmountSum) != Math.ceil(viewModel.getReportSgstAmount())
        if (sgstAmountSum.setScale(0, RoundingMode.CEILING).compareTo(viewModel.getApprovalContainerSgstAmount().setScale(0, RoundingMode.CEILING)) != 0) {
            throw new DomainInvariantException(
                    "Report total SGST amount doesn't tally with sum of expense SGST amounts");
        }

//		Math.ceil(cgstAmountSum) != Math.ceil(viewModel.getReportCgstAmount())
        if (cgstAmountSum.setScale(0, RoundingMode.CEILING).compareTo(viewModel.getApprovalContainerCgstAmount().setScale(0, RoundingMode.CEILING)) != 0) {
            throw new DomainInvariantException(
                    "Report total CGST amount doesn't tally with sum of expense CGST amounts");
        }

//		Math.ceil(igstAmountSum) != Math.ceil(viewModel.getReportIgstAmount())
        if (igstAmountSum.setScale(0, RoundingMode.CEILING).compareTo(viewModel.getApprovalContainerIgstAmount().setScale(0, RoundingMode.CEILING)) != 0) {
            throw new DomainInvariantException(
                    "Report total IGST amount doesn't tally with sum of expense IGST amounts");
        }

//		Math.ceil(taxableAmountSum) != Math.ceil(viewModel.getReportTaxableAmount())
        if (taxableAmountSum.setScale(0, RoundingMode.CEILING).compareTo(viewModel.getApprovalContainerIgstAmount().setScale(0, RoundingMode.CEILING)) != 0) {
            throw new DomainInvariantException(
                    "Report total taxable amount doesn't tally with sum of expense taxable amounts");
        }

        if (report.getDocumentMetadata().isPurposeRequired()
                && StaticDataRegistry.isNullOrEmptyOrWhitespace(report.getPurpose())) {
            throw new DomainInvariantException("Report should mention the purpose");
        }

        validateMetadataLimits(report);
        validateExistingExpenseDateSanity(report);
    }

    private void validateExistingExpenseDateSanity(DocumentApprovalContainer report) {
        for (Document e : report.getDocuments()) {
            if (e.getDocumentDate() == null) {
                continue;
            }

            if (e.getDocumentDate().isBefore(report.getStartDate()) || e.getDocumentDate().isAfter(report.getEndDate())) {
                throw new DomainInvariantException("This report contains expenses that are not in this date range");
            }
        }
    }

    private void validateMetadataLimits(DocumentApprovalContainer report) {
        logger.info(
                String.format("validateMetadataLimits: getApplicable Rules For Expense Report id: %s", report.getId()));
        List<MetadataLimitRule> rules = getApplicableRule(report);

        if (rules.isEmpty()) {
            logger.info(String.format("validateMetadataLimits: Limit rules were not found for Expense Report id: %s",
                    report.getId()));
            return;
        }

        logger.info(String.format("validateMetadataLimits: Rules were found for for Expense Report id: %s",
                report.getId()));
        for (MetadataLimitRule rule : rules) {
            switch (rule.getIntervalMarker()) {
                case DAILY:
                    validateDailyLimit(report, rule);
                    break;
                case MONTHLY:
                    validateMonthlyLimit(report, rule);
                    break;
                case QUARTERLY:
                    validateQuarterlyLimit(report, rule);
                    break;
                case HALF_YEARLY:
                    validateHalfYearlyLimit(report, rule);
                    break;
                case YEARLY:
                    validateYearlyLimit(report, rule);
                    break;
                case PER_REPORT:
                    validatePerReportLimit(report, rule);
                    break;
                default:
                    break;
            }
        }
    }

    private void validatePerReportLimit(DocumentApprovalContainer report, MetadataLimitRule rule) {
        BigDecimal total = new BigDecimal("0");
        for (Document e : report.getDocuments()) {
            total = total.add(e.getClaimAmount());
        }

//		total > rule.getLimitAmount()
        if (total.compareTo(rule.getLimitAmount()) > 0) {
            throw new DomainInvariantException(
                    String.format("Report amount cannot exceed %.2f", rule.getLimitAmount()));
        }
    }

    private void validateDailyLimit(DocumentApprovalContainer report, MetadataLimitRule rule) {
        for (Document document : report.getDocuments()) {

            if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
                if (!rule.getLocationCategory().equals(document.getLocationCategory())) {
                    continue;
                }
            }

            if (document.getDocumentDate() == null) {
                continue;
            }

            BigDecimal totalClaim = documentRepository.getDocumentsOnDay(report.getCompanyCode(), report.getCreatingUserId(),
                    report.getDocumentMetadataId(), document.getDocumentDate());

            if (totalClaim == null) {
                totalClaim = new BigDecimal("0.0");
            }

//			totalClaim + report.getReportClaimAmount() > rule.getLimitAmount()
            if (totalClaim.add(report.getApprovalContainerClaimAmount()).compareTo(rule.getLimitAmount()) > 0) {
                throw new DomainInvariantException(String.format("Daily limit of %.2f for metadata %d is over",
                        rule.getLimitAmount(), report.getDocumentMetadataId()));
            }
        }
    }

    private void validateMonthlyLimit(DocumentApprovalContainer report, MetadataLimitRule rule) {

        // Only start the validations if there are any expenses in the first place (as
        // mobile calls save initally before creating the first expense)
        if (report.getDocuments().isEmpty()) {
            return;
        }

        logger.info(String.format("validateMonthlyLimit: Started monthly limit checks for Expense Report id %s",
                report.getId()));
        // Get the start and end dates of the expense report
        LocalDate start = report.getStartDate().withDayOfMonth(1);
        LocalDate end = report.getEndDate().withDayOfMonth(report.getEndDate().lengthOfMonth());

        List<IExpenseValidationAggregates> expensesCountByMonth = documentRepository
                .getDocumentsAggregatesByMonth(report.getCompanyCode(), report.getCreatingUserId(),
                        report.getDocumentMetadataId(), List.of(ReportStatus.SUBMITTED, ReportStatus.ACCEPTED,
                                ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM, ReportStatus.DRAFT),
                        start, end);

        List<IExpenseValidationAggregates> expensesSumByMonth = documentRepository.getDocumentsAggregatesByMonth(
                report.getCompanyCode(), report.getCreatingUserId(), report.getDocumentMetadataId(),
                List.of(ReportStatus.SUBMITTED, ReportStatus.ACCEPTED, ReportStatus.PAID,
                        ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
                start, end);

        HashMap<LocalDate, ExpenseValidationAggregates> expenseAggregates = new HashMap<>();

        // Initialize the HashMap to make sure that in cases of no values, null key
        // error is never encountered.
        start.datesUntil(end, Period.ofMonths(1)).map(date -> expenseAggregates.put(date.withDayOfMonth(1),
                new ExpenseValidationAggregates(0L, new BigDecimal("0.0"), date.withDayOfMonth(1)))).collect(Collectors.toList());

//         Running two separate streams as the count and sum may have completely separate months (only drafts in one month, means that there will be
//         no sum in that month but there will a count).

        expensesCountByMonth.forEach(aggregate -> {
            // All dates will be converted to the first day of that month to allow ignoring
            // of the day
            LocalDate date = aggregate.getExpenseDate().withDayOfMonth(1);
            Long nonNullCount = null == aggregate.getCount() ? 0L : aggregate.getCount();
            expenseAggregates.put(date,
                    new ExpenseValidationAggregates(nonNullCount, new BigDecimal("0.0"), aggregate.getExpenseDate().withDayOfMonth(1)));
        });

        logger.info(String.format(
                "validateMonthlyLimit: Performing the filling of the hashmap for expensesSumByMonth for Expense Report id %s",
                report.getId()));
        // If there is a count for that month, add the statistic for sum to it as well.
        // else create the statistic for
        // a new month with only the sum, with the count as null.
        expensesSumByMonth.forEach(aggregate -> {
            LocalDate date = aggregate.getExpenseDate().withDayOfMonth(1);
            // As sum() can return null.
            BigDecimal nonNullSum = null == aggregate.getSum() ? new BigDecimal("0.0") : aggregate.getSum();
            if (expenseAggregates.containsKey(date)) {
                expenseAggregates.get(date).setSum(nonNullSum);
            } else {
                expenseAggregates.put(aggregate.getExpenseDate(),
                        new ExpenseValidationAggregates(0L, nonNullSum, aggregate.getExpenseDate().withDayOfMonth(1)));
            }
        });

        logger.info(String.format("validateMonthlyLimit: The created hashmap is for Expense Report id %s \n\t%s",
                report.getId(), expenseAggregates));

        for (Document document : report.getDocuments()) {

            logger.info(String.format(
                    "validateMonthlyLimit: Checking if location category rule is applicable for Expense Report id %s",
                    report.getId()));
            if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
                if (!rule.getLocationCategory().equals(document.getLocationCategory())) {
                    logger.info(String.format(
                            "validateMonthlyLimit: Location category rule is not applicable for Expense Report id %s",
                            report.getId()));
                    continue;
                }
            }

            if (document.getDocumentDate() == null) {
                logger.info(String.format("validateMonthlyLimit: Expense Report id %s has no date", report.getId()));
                continue;
            }
            BigDecimal totalClaim = new BigDecimal("0.0");

            long nExpenses = expenseAggregates.get(document.getDocumentDate().withDayOfMonth(1)).getCount();

            if (rule.getDocumentCountLimit() > 0 && nExpenses > rule.getDocumentCountLimit()) {
                throw new DomainInvariantException(String.format(
                        "You have crossed the expense limit of %d for this period", rule.getDocumentCountLimit()));
            }

            totalClaim = expenseAggregates.get(document.getDocumentDate().withDayOfMonth(1)).getSum();


//			totalClaim + report.getReportClaimAmount() > rule.getLimitAmount()
            if (totalClaim.add(report.getApprovalContainerClaimAmount()).compareTo(rule.getLimitAmount()) > 0) {
                throw new DomainInvariantException(String.format(
                        "Monthly limit of %.2f for %s-%s is over; total claims so far: %.2f", rule.getLimitAmount(),
                        report.getDocumentMetadata().getDocumentType(), report.getDocumentMetadata().getDocumentGroup(),
                        totalClaim.add(report.getApprovalContainerClaimAmount())));
            }
        }
    }

    private void validateQuarterlyLimit(DocumentApprovalContainer report, MetadataLimitRule rule) {
        for (Document document : report.getDocuments()) {
            if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
                if (!rule.getLocationCategory().equals(document.getLocationCategory())) {
                    continue;
                }
            }

            if (document.getDocumentDate() == null) {
                continue;
            }

            LocalDate[] delimiters = getQuarterDelimiters(document.getDocumentDate());
            BigDecimal totalClaim = documentRepository.getDocumentsBetween(report.getCompanyCode(),
                    report.getCreatingUserId(), report.getDocumentMetadataId(), List.of(ReportStatus.SUBMITTED,
                            ReportStatus.ACCEPTED, ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
                    delimiters[0], delimiters[1]);

            if (totalClaim == null) {
                totalClaim = new BigDecimal("0.0");
            }

//			totalClaim + report.getReportClaimAmount() > rule.getLimitAmount()
            if (totalClaim.add(report.getApprovalContainerClaimAmount()).compareTo(rule.getLimitAmount()) > 0) {
                throw new DomainInvariantException(String.format(
                        "Quarterly limit of %.2f for %s-%s is over; total claims so far: %.2f", rule.getLimitAmount(),
                        report.getDocumentMetadata().getDocumentType(), report.getDocumentMetadata().getDocumentGroup(),
                        totalClaim.add(report.getApprovalContainerClaimAmount())));
            }
        }
    }

    private void validateHalfYearlyLimit(DocumentApprovalContainer report, MetadataLimitRule rule) {
        for (Document document : report.getDocuments()) {
            if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
                if (!rule.getLocationCategory().equals(document.getLocationCategory())) {
                    continue;
                }
            }

            if (document.getDocumentDate() == null) {
                continue;
            }

            LocalDate[] delimiters = getHalfYearDelimiters(document.getDocumentDate());
            BigDecimal totalClaim = documentRepository.getDocumentsBetween(report.getCompanyCode(),
                    report.getCreatingUserId(), report.getDocumentMetadataId(), List.of(ReportStatus.SUBMITTED,
                            ReportStatus.ACCEPTED, ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
                    delimiters[0], delimiters[1]);

            if (totalClaim == null) {
                totalClaim = new BigDecimal("0.0");
            }

//			totalClaim + report.getReportClaimAmount() > rule.getLimitAmount()
            if (totalClaim.add(report.getApprovalContainerClaimAmount()).compareTo(rule.getLimitAmount()) > 0) {
                throw new DomainInvariantException(String.format(
                        "Half-yearly limit of %.2f for %s-%s is over; total claims so far: %.2f", rule.getLimitAmount(),
                        report.getDocumentMetadata().getDocumentType(), report.getDocumentMetadata().getDocumentGroup(),
                        totalClaim.add(report.getApprovalContainerClaimAmount())));
            }
        }
    }

    private void validateYearlyLimit(DocumentApprovalContainer report, MetadataLimitRule rule) {
        for (Document document : report.getDocuments()) {
            if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
                if (!rule.getLocationCategory().equals(document.getLocationCategory())) {
                    continue;
                }
            }

            if (document.getDocumentDate() == null) {
                continue;
            }

            LocalDate[] delimiters = getYearDelimiters(document.getDocumentDate());
            BigDecimal totalClaim = documentRepository.getDocumentsBetween(report.getCompanyCode(),
                    report.getCreatingUserId(), report.getDocumentMetadataId(), List.of(ReportStatus.SUBMITTED,
                            ReportStatus.ACCEPTED, ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
                    delimiters[0], delimiters[1]);

            if (totalClaim == null) {
                totalClaim = new BigDecimal("0.0");
            }

//			totalClaim + report.getReportClaimAmount() > rule.getLimitAmount()
            if (totalClaim.add(report.getApprovalContainerClaimAmount()).compareTo(rule.getLimitAmount()) > 0) {
                throw new DomainInvariantException(String.format(
                        "Yearly limit of %.2f for %s-%s is over; total claims so far: %.2f", rule.getLimitAmount(),
                        report.getDocumentMetadata().getDocumentType(), report.getDocumentMetadata().getDocumentGroup(),
                        totalClaim.add(report.getApprovalContainerClaimAmount())));
            }
        }
    }

    private LocalDate[] getQuarterDelimiters(LocalDate markDate) {
        switch (markDate.getMonth()) {
            case JANUARY:
            case FEBRUARY:
            case MARCH:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.JANUARY, 1),
                        LocalDate.of(markDate.getYear(), Month.MARCH, 31)};
            case APRIL:
            case MAY:
            case JUNE:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.APRIL, 1),
                        LocalDate.of(markDate.getYear(), Month.JUNE, 30)};
            case JULY:
            case AUGUST:
            case SEPTEMBER:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.JULY, 1),
                        LocalDate.of(markDate.getYear(), Month.SEPTEMBER, 30)};
            default:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.OCTOBER, 1),
                        LocalDate.of(markDate.getYear(), Month.DECEMBER, 31)};
        }
    }

    private LocalDate[] getHalfYearDelimiters(LocalDate markDate) {
        switch (markDate.getMonth()) {
            case APRIL:
            case MAY:
            case JUNE:
            case JULY:
            case AUGUST:
            case SEPTEMBER:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.APRIL, 1),
                        LocalDate.of(markDate.getYear(), Month.SEPTEMBER, 30)};
            default:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.OCTOBER, 1),
                        LocalDate.of(markDate.getYear(), Month.MARCH, 31)};
        }
    }

    private LocalDate[] getYearDelimiters(LocalDate markDate) {
        switch (markDate.getMonth()) {
            case JANUARY:
            case FEBRUARY:
            case MARCH:
                return new LocalDate[]{LocalDate.of(markDate.getYear() - 1, Month.APRIL, 1),
                        LocalDate.of(markDate.getYear(), Month.MARCH, 31)};
            default:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.APRIL, 1),
                        LocalDate.of(markDate.getYear() + 1, Month.MARCH, 31)};
        }
    }

    private List<MetadataLimitRule> getApplicableRule(DocumentApprovalContainer report) {
        logger.trace("getApplicableRule: Finding the Metadata Limit Rules for Report with id: {}", report.getId());

        return ruleRepository
                .findByCompanyCodeAndDocumentMetadataId(report.getCompanyCode(), report.getDocumentMetadataId()).stream()
                .filter(r -> !r.isFrozen() && isRuleMatch(r, report)).collect(Collectors.toList());
    }

    private boolean isRuleMatch(MetadataLimitRule rule, DocumentApprovalContainer report) {
        boolean result = true;

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
            result = rule.getBranchCode().equals(report.getEmployeeBranch());
            if (!result)
                return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
            result = rule.getDepartmentCode().equals(report.getEmployeeDepartment());
            if (!result)
                return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
            result = rule.getCostCenterCode().equals(report.getEmployeeCostCenter());
            if (!result)
                return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
            result = rule.getEmployeeType().equals(report.getEmployeeType());
            if (!result)
                return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
            result = rule.getEmployeeGrade().equals(report.getEmployeeGrade());
            if (!result)
                return false;
        }

        if (!report.getDocuments().isEmpty()
                && StaticDataRegistry.isNotNullOrEmptyOrWhitespace(report.getDocuments().get(0).getLocationCategory())
                && StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
            result = rule.getLocationCategory().equals(report.getDocuments().get(0).getLocationCategory());
            if (!result)
                return false;
        }

        return result;
    }

    private boolean isSubmittable(DocumentApprovalContainer report) {
        return report.getReportStatus() == ReportStatus.DRAFT || report.getReportStatus() == ReportStatus.SENT_BACK || report.getReportStatus() == ReportStatus.VALIDATION_FAILED;
    }

    private void handleResubmission(DocumentApprovalContainer report, IEmployeeViewModel employeeViewModel,
                                    IAuthContextViewModel auth, long saveTime) {
        List<ReportState> actionedStates = stateRepository
                .getActionedStatesByDocumentApprovalContainer(auth.getCompanyCode(), report.getId(),
                        List.of(ExpenseActionStatus.APPROVED, ExpenseActionStatus.SENT_BACK))
                .stream().filter(distinctByKey(ReportState::getLevel))
                .sorted(Comparator.comparing(ReportState::getLevel)).collect(Collectors.toList());

        for (ReportState state : actionedStates) {
            // copy the state
            logger.trace("handleResubmission: Found the original state; proceeding with cloning");
            ReportState newState = new ReportState();
            BeanUtils.copyProperties(state, newState, "id", "remarks", "actionDate", "updatedTimestamp");

            logger.trace("handleResubmission: Setting status as unactioned");
            newState.setStatus(ExpenseActionStatus.UNACTIONED);

            logger.trace("handleResubmission: Setting the audit fields");
            newState.setCreatedTimestamp(ZonedDateTime.now());

            logger.trace("handleResubmission: Attaching the bidirectional mapping");
            newState.setDocumentApprovalContainer(report);
            newState.setDocumentApprovalContainerId(report.getId());
            newState.setSaveTime(saveTime);

            report.getReportStates().add(newState);

            stateRepository.saveAndFlush(newState);
        }

        report.setUpdatedTimestamp(ZonedDateTime.now());
        report.setUpdatingUserId(auth.getUserId());
        report.setActionLevel(StaticDataRegistry.FIRST_LEVEL);

        if (report.isContainsDeviation()) {
            attachDeviationStatesForResubmission(report, employeeViewModel, auth);
        } else {
            removeUnactionedDeviationStates(report, auth);
        }

        logger.trace("handleResubmission: Saving the entities");
        documentApprovalContainerRepository.saveAndFlush(report);
        logger.trace("handleResubmission: Save successful, exiting");
    }

    private void removeUnactionedDeviationStates(DocumentApprovalContainer report, IAuthContextViewModel auth) {
        List<ReportState> unactionedDeviationStates = stateRepository
                .getActionedStatesByDocumentApprovalContainer(auth.getCompanyCode(), report.getId(),
                        List.of(ExpenseActionStatus.UNACTIONED))
                .stream().filter(distinctByKey(ReportState::getLevel))
                .sorted(Comparator.comparing(ReportState::getLevel)).filter(ReportState::isDeviationAssignment)
                .collect(Collectors.toList());

        stateRepository.deleteAll(unactionedDeviationStates);
    }

    private void attachDeviationStatesForResubmission(DocumentApprovalContainer report, IEmployeeViewModel employeeViewModel,
                                                      IAuthContextViewModel auth) {
        List<ApprovalDefinition> approvalDefinitions = getApprovalDefinitions(report, auth, false);

        approvalDefinitions.removeIf(d -> !d.isForDeviation());

        approvalDefinitions.sort(Comparator.comparing(ApprovalDefinition::getLevel));

        logger.info("attachApprovers: Proceeding to get and attach approvers");
        getAndAttachApprovers(report, report.getDocumentMetadata(), auth, employeeViewModel, approvalDefinitions);
    }

    private List<ApprovalDefinition> getApprovalDefinitions(DocumentApprovalContainer report, IAuthContextViewModel auth, boolean includeFrozen) {
        List<ApprovalDefinition> approvalDefinitions = includeFrozen ? definitionRepository.findAllApplicableDefinitions(
                auth.getCompanyCode(), report.getDocumentMetadataId(), report.getApprovalContainerClaimAmount())
                : definitionRepository.findApplicableDefinitions(
                auth.getCompanyCode(), report.getDocumentMetadataId(), report.getApprovalContainerClaimAmount());

        List<ApprovalDefinition> approvalDefinitionsEqualAmount = includeFrozen ? definitionRepository
                .findAllApplicableDefinitionsEqualAmount(auth.getCompanyCode(), report.getDocumentMetadataId(),
                        report.getApprovalContainerClaimAmount())
                : definitionRepository.findApplicableDefinitionsEqualAmount(auth.getCompanyCode(), report.getDocumentMetadataId(),
                report.getApprovalContainerClaimAmount());

        approvalDefinitions.addAll(approvalDefinitionsEqualAmount);

        Optional<ApprovalDefinition> lastApplicableDefinition = Optional.empty();

        // If the report amount is exactly the limit, then the definition should stop at
        // that limit
        // If the amount is exactly the limit, approvalDefinitionsEqualAmount will NOT
        // be empty
        if (approvalDefinitionsEqualAmount.isEmpty()) {

            lastApplicableDefinition = definitionRepository.findFirstByCompanyCodeAndDocumentMetadataIdAndLimitAmountGreaterThanAndIsFrozenIsFalseOrderByLevel(auth.getCompanyCode(), report.getDocumentMetadataId(), report.getApprovalContainerClaimAmount());

        }

        if (lastApplicableDefinition.isPresent()) {
            BigDecimal limitAmount = lastApplicableDefinition.get().getLimitAmount();
            List<ApprovalDefinition> equalLimitAmountDefinitionsInLastBand = includeFrozen ? definitionRepository
                    .getAllEqualLimitAmountDefinitionsInLastBand(auth.getCompanyCode(), report.getDocumentMetadataId(),
                            limitAmount)
                    : definitionRepository.getEqualLimitAmountDefinitionsInLastBand(auth.getCompanyCode(), report.getDocumentMetadataId(),
                    limitAmount);

            approvalDefinitions.addAll(equalLimitAmountDefinitionsInLastBand);
        }

        return approvalDefinitions;
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    // Approver related

    private void attachInitialApprovers(DocumentApprovalContainer report, DocumentMetadata metadata, IAuthContextViewModel auth,
                                        IEmployeeViewModel employeeViewModel, Boolean isResubmission) {
        logger.trace("attachApprovers: Getting the approvers from definitions");

        // Get only non-frozen approvals
        List<ApprovalDefinition> approvalDefinitions = getApprovalDefinitions(report, auth, false);

        // Get all approvals (including frozen)
        List<ApprovalDefinition> allApprovalDefinitions = getApprovalDefinitions(report, auth, true);

        // No approval matrix set
        if (approvalDefinitions.isEmpty()) {
            throw new DomainInvariantException("The approval workflow for this document is not present, please check with your administrator.");
        }

        // Check frozen levels
        boolean allFrozen = allApprovalDefinitions.stream().allMatch(ApprovalDefinition::isFrozen);
        if (allFrozen) {
            throw new DomainInvariantException("All the approval workflow for this document is frozen, please check with your administrator.");
        }

        boolean firstOrMiddleFrozen = allApprovalDefinitions.stream()
                .anyMatch(def -> def.getLevel() < allApprovalDefinitions.size() && def.isFrozen());

        if (firstOrMiddleFrozen) {
            throw new DomainInvariantException("Invalid Metadata: There is neither a matching approver nor default approver defined for this workflow. Please contact your administrator for assistance.");
        }
        // Filter out frozen approvals
        approvalDefinitions.removeIf(ApprovalDefinition::isFrozen);

        //  All approvers are frozen
        if (approvalDefinitions.isEmpty()) {
            throw new DomainInvariantException("The approval workflow for this document is frozen, please check with your administrator.");
        }

        // This section is adapted from the handle resubmission. Main difference is
        // editing the previous states
        // instead of cloning the saving new ones (leaving the previous untouched).
        if (isResubmission) {
            // Get all the previous report states of the expense. and set them to rejected
            // if they were unactioned
            // This is so that they cannot be considered in the new approval flow (as
            // changed their status from 0 -> 2)
            List<ReportState> allPreviousReportStates = stateRepository
                    // Get all states, not just actioned ones
                    .getStatesByDocumentApprovalContainer(auth.getCompanyCode(), report.getId()).stream()
//                            .filter(distinctByKey(ReportState::getLevel)) //<- have removed this as want to go through all the effected states. Not completely sure why we use this.
                    .sorted(Comparator.comparing(ReportState::getLevel)).collect(Collectors.toList());
            // The setting of unactioned to rejected (0 -> 2)
            for (ReportState state : allPreviousReportStates) {
                if (state.getStatus() == ExpenseActionStatus.UNACTIONED) {
                    state.setStatus(ExpenseActionStatus.REJECTED);
                    state.setUpdatedTimestamp(ZonedDateTime.now());
                }
            }
            // Hence, here instead of creating new states and saving them, we update the
            // already present previous states.
            stateRepository.saveAll(allPreviousReportStates);

            // Rest lifted from handleResubmission(...) function.
            report.setUpdatedTimestamp(ZonedDateTime.now());
            report.setUpdatingUserId(auth.getUserId());
            report.setActionLevel(StaticDataRegistry.FIRST_LEVEL);

            // Keep previous unactioned deviation states within the history for now.
//            removeUnactionedDeviationStates(report, auth);

            // The below has been removed as we don't need to add deviations as we are
            // creating a submission approval matrix from scratch each time.
//            if (report.isContainsDeviation()) {
//                attachDeviationStatesForResubmission(report, employeeViewModel, auth);
//            } else {
//                removeUnactionedDeviationStates(report, auth);
//            }

            logger.trace("handleResubmission: Saving the entities");
            documentApprovalContainerRepository.save(report);
            logger.trace("handleResubmission: Save successful, exiting");

        }
        // check all scenarios here


        if (!report.isContainsDeviation()) {
            approvalDefinitions.removeIf(ApprovalDefinition::isForDeviation);
        }

        approvalDefinitions.sort(Comparator.comparing(ApprovalDefinition::getLevel));

        logger.info("attachApprovers: Proceeding to get and attach approvers");
        getAndAttachApprovers(report, metadata, auth, employeeViewModel, approvalDefinitions);
    }

    private void getAndAttachApprovers(DocumentApprovalContainer report, DocumentMetadata metadata, IAuthContextViewModel auth,
                                       IEmployeeViewModel employeeViewModel, List<ApprovalDefinition> approvalDefinitions) {
        logger.trace("getAndAttachApprovers: Processing the definitions");
        approvalDefinitions.forEach(d -> {
            logger.trace("getAndAttachApprovers: Checking if the approver should be the reporting manager");
            if (d.isShouldFetchFromEmployeeMaster()) {
                logger.trace("getAndAttachApprovers: Attaching reporting manager as the approver from employee master");
                attachFromEmployeeMaster(report, auth, employeeViewModel, d, false);
            } else {
                logger.trace("getAndAttachApprovers: Fetching the approver other than reporting manager");
                fetchAndAttachApprover(report, metadata, auth, employeeViewModel, d, report.isContainsDeviation(),
                        report.getDeviationRemarks());
            }
        });
    }

    private void fetchAndAttachApprover(DocumentApprovalContainer report, DocumentMetadata metadata, IAuthContextViewModel auth,
                                        IEmployeeViewModel employeeViewModel, ApprovalDefinition d, boolean isDeviated, String deviationRemarks) {
        logger.trace("fetchAndAttachApprover: Fetching approver by criteria: {}/{}/{}/{}", metadata.getCompanyCode(),
                d.getApprovalMatcher(), StaticDataRegistry.getEmployeeKVMap(report).get(d.getApprovalMatcher()),
                d.getApprovalTitle());

        Optional<Approver> optional = Optional.empty();

        if (d.getApprovalMatcher().equals(d.getApprovalTitle())) {
            // singularity
            optional = approverRepository.findSingularApproverByCriteria(metadata.getCompanyCode(),
                    d.getApprovalMatcher(), d.getApprovalTitle());
        } else {
            optional = approverRepository.findApproverByCriteria(metadata.getCompanyCode(), d.getApprovalMatcher(),
                    StaticDataRegistry.getEmployeeKVMap(report).get(d.getApprovalMatcher()), d.getApprovalTitle());
        }

        // there may not be any approvers in some cases (only checkers)
        if (optional.isEmpty()) {
            logger.trace("fetchAndAttachApprover: No approver is found, trying default approver");
            tryAttachingDefaultApprover(report, auth, employeeViewModel, d);
        } else {
            if (auth.getUserEmail().equalsIgnoreCase(optional.get().getApprover())) {
                logger.info("fetchAndAttachApprover: User and approver are same, hence attaching user's manager");
                attachFromEmployeeMaster(report, auth, employeeViewModel, d, false);
            } else {
                logger.info("fetchAndAttachApprover: Attaching the approver: {} at position: {}",
                        optional.get().getApprover(), d.getLevel());
                checkDelegationAndAttachApprover(optional.get(), optional.get().getApprover(), report, d, auth, false, d, isDeviated,
                        deviationRemarks);
            }
        }
    }

    private void tryAttachingDefaultApprover(DocumentApprovalContainer report, IAuthContextViewModel auth,
                                             IEmployeeViewModel employeeViewModel, ApprovalDefinition d) {
        logger.trace("tryAttachingDefaultApprover: Finding the default approver");
        Optional<Approver> defaultApprover = approverRepository.findApproverByCriteria(report.getCompanyCode(),
                StaticDataRegistry.DEFAULT_APPROVER_MATCHER, StaticDataRegistry.DEFAULT_APPROVER_VALUE,
                StaticDataRegistry.DEFAULT_APPROVER_TITLE);
        if (defaultApprover.isPresent()) {
            logger.trace("tryAttachingDefaultApprover: Default approver is defined");
            if (auth.getUserEmail().equalsIgnoreCase(defaultApprover.get().getApprover())) {
                logger.info("tryAttachingDefaultApprover: Default approver is the user, hence attaching the manager");
                attachFromEmployeeMaster(report, auth, employeeViewModel, d, true);
            } else {
                logger.info(
                        "tryAttachingDefaultApprover: Attaching the default approver: {} at position: {} for channel: {}",
                        defaultApprover.get().getApprover(), d.getLevel(), d.getChannel());
                attachApprover(defaultApprover.get(), defaultApprover.get().getApprover(), d.getLevel(), report, d.getChannel(), null, null,
                        null, true, d, auth);
            }
        } else {
            logger.trace("tryAttachingDefaultApprover: There is neither a matching aprrover or default approver. Preventing submission of expense report id: {}", report.getId());
            throw new DomainInvariantException("An approver has not set up for matcher "
                    + d.getApprovalMatcher() + ", for title " + d.getApprovalTitle() + " and for value " + StaticDataRegistry.getEmployeeKVMap(report).get(d.getApprovalMatcher()) + " Please contact your administrator for assistance.");

//            throw new DomainInvariantException("Invalid Metadata: There is neither a matching approver nor default approver defined for this workflow for matcher "
//                    + d.getApprovalMatcher()+", for title "+ d.getApprovalTitle()+" and for value "+ StaticDataRegistry.getEmployeeKVMap(report).get(d.getApprovalMatcher())+" Please contact your administrator for assistance.");

//            throw new DomainInvariantException("Invalid Metadata: There is neither a matching approver nor default approver defined for this workflow. Please contact your administrator for assistance.");
        }
    }

    private void attachFromEmployeeMaster(DocumentApprovalContainer report, IAuthContextViewModel auth,
                                          IEmployeeViewModel employeeViewModel, ApprovalDefinition d, boolean isDefault) {
        logger.info(
                "attachFromEmployeeMaster: Found that the approver has to be fetched from employee master; getting the reporting manager to attach at level {}",
                d.getLevel());
        try {
            IEmployeeViewModel manager = employeeService
                    .getEmployeeMasterDataByCode(employeeViewModel.getReportingManager(), auth);
            logger.trace(
                    "attachFromEmployeeMaster: Proceeding to check approver delegation and attaching the approver");
            // In this case as approval matrix points the creator, create a scarecrow approver object containing an
            // -- Approver object with only the ApprovalDefinition details instead
            Approver approverDetailsOnly = Approver.generateDummyApproverFromApprovalDefinition(d, StaticDataRegistry.getEmployeeKVMap(report).get(d.getApprovalMatcher()), auth);
            checkDelegationAndAttachApprover(approverDetailsOnly, manager.getEmail(), report, d, auth, isDefault, d,
                    report.isContainsDeviation(), null);
        } catch (RuntimeException exception) {
            logger.info(
                    "attachFromEmployeeMaster: Could not find the manager of this employee from CEM master, attaching the default");
            tryAttachingDefaultApprover(report, auth, employeeViewModel, d);
        }
    }

    private void checkDelegationAndAttachApprover(Approver approverData, String approver, DocumentApprovalContainer report, ApprovalDefinition defintion,
                                                  IAuthContextViewModel auth, boolean isDefault, ApprovalDefinition definition, boolean isDeviated,
                                                  String deviationRemarks) {
        logger.trace("checkDelegationAndAttachApprover: Checking if the delegation exists for {}", approver);
        Optional<ApprovalDelegation> delegationOptional = delegationRepository.findTheDelegateFor(auth.getCompanyCode(),
                approver, LocalDate.now());

        if (delegationOptional.isPresent()) {
            logger.trace(
                    "checkDelegationAndAttachApprover: Found a delegation for {}; proceeding to attach the delegated approver {} at level {} in {} channel",
                    approver, delegationOptional.get().getAssignedTo(), defintion.getLevel(), defintion.getChannel());

            logger.trace(
                    "checkDelegationAndAttachApprover: Checking whether the delegated approver and the creator of the report are same");
            if (delegationOptional.get().getAssignedTo().equalsIgnoreCase(report.getEmployeeEmail())) {
                logger.trace(
                        "checkDelegationAndAttachApprover: Found the delegated approver and creator of the report are same; bypassing delegation");
                attachApprover(approverData, delegationOptional.get().getOriginator(), defintion.getLevel(), report,
                        defintion.getChannel(), approver, null, null, isDefault, definition, auth);
            } else {
                logger.trace(
                        "checkDelegationAndAttachApprover: Delegated approver and creator of the report are different; allowing the delegation");
                attachApprover(approverData, delegationOptional.get().getAssignedTo(), defintion.getLevel(), report,
                        defintion.getChannel(), approver, delegationOptional.get().getId(),
                        delegationOptional.get().getCreatedTimestamp(), isDefault, definition, auth);
            }
        } else {
            logger.trace(
                    "checkDelegationAndAttachApprover: No delegations; proceeding to attach the original approver {} at level {} in {} channel",
                    approver, defintion.getLevel(), defintion.getChannel());
            attachApprover(approverData, approver, defintion.getLevel(), report, defintion.getChannel(), null, null, null, isDefault,
                    definition, auth);
        }
    }

    private void attachApprover(Approver approverData, String approver, int level, DocumentApprovalContainer report, StateChannel channel,
                                String originalApprover, Long delegationId, ZonedDateTime delegationTimestamp, boolean isDefault,
                                ApprovalDefinition definition, IAuthContextViewModel auth) {

        logger.trace("attachApprover: Attaching {} at level {}", approver, level);

        if (report.getEmployeeEmail().equalsIgnoreCase(approver)) {
            logger.trace("attachApprover: The creator {} and approver {} are the same at level {}. Moving to default", report.getEmployeeEmail(), approver, level);
            throw new DomainInvariantException("Error: Invalid Metadata\n" +
                    "The user is currently part of the approval workflow for this voucher. Please contact your administrator for assistance.");
        }

        logger.trace("attachApprover: Creating a new expense state");
        ReportState state = new ReportState();

        IEmployeeViewModel employeeDetails = employeeService.getEmployeeMasterData(approver, auth);

//        Optional<ReportState> lastLevelState =
//                stateRepository.findFirstByCompanyCodeAndExpenseReportIdOrderByLevelDesc(report.getCompanyCode(), report.getId());

        Optional<ReportState> lastLevelState = stateRepository
                .findFirstByCompanyCodeAndDocumentApprovalContainerIdAndStatusOrderByLevelDesc(report.getCompanyCode(),
                        report.getId(), ExpenseActionStatus.UNACTIONED);

        logger.trace("attachApprover: Setting fields");
        if (report.isContainsDeviation() && definition.isForDeviation()) {
            state.setDeviationAssignment(true);
            state.setDeviationRemarks(report.getDeviationRemarks());
        }

//        if (lastLevelState.isPresent()) {
//            state.setLevel(lastLevelState.get().getLevel() + StaticDataRegistry.DEFINITION_LEVEL_INCREMENT_FACTOR);
//        } else {
//            state.setLevel(level);
//        }

        if (lastLevelState.isPresent()) {
            state.setLevel(lastLevelState.get().getLevel() + StaticDataRegistry.DEFINITION_LEVEL_INCREMENT_FACTOR);
        } else {
            state.setLevel(level);
        }

        // Here we now always set the level to the level of the approver in the approval
        // definition. The previous code
        // is now not being used as we are now treat the re-submission as a new
        // submission that is, each time we create
        // a new approval matrix with new approvers.
//        state.setLevel(level);

        state.setApprover(approver);
        state.setCompanyCode(report.getCompanyCode());
        state.setStatus(ExpenseActionStatus.UNACTIONED);
        state.setCreatedTimestamp(ZonedDateTime.now());
        state.setChannel(channel);
        state.setDelegated(false);

        state.setApproverFirstName(employeeDetails.getFirstName());
        state.setApproverLastName(employeeDetails.getLastName());
        state.setApproverEmployeeCode(employeeDetails.getEmployeeCode());

        state.setApprovalTitle(approverData.getApprovalTitle());
        state.setApprovalMatcher(approverData.getApprovalMatcher());
        state.setApprovalMatchValue(approverData.getApprovalMatchValue());

        if (isDefault) {
            state.setDefaultTriggerRemarks(String.format("Default assignment for %s -> %s",
                    definition.getApprovalMatcher(), definition.getApprovalTitle()));
        }

        if (delegationId != null) {
            logger.trace("attachApprover: Setting delegation specific fields");
            state.setDelegationRemarks(
                    StaticDataRegistry.getApprovalDelegationMarker(originalApprover, approver, level, delegationId));
            state.setDelegationTimestamp(delegationTimestamp);
            state.setDelegated(true);
        }

        logger.trace("attachApprover: Setting bi-directional mapping for expense and state");
        state.setDocumentApprovalContainer(report);
        state.setDocumentApprovalContainerId(report.getId());
        report.getReportStates().add(state);

        logger.trace("attachApprover: Saving the state");
        stateRepository.saveAndFlush(state);
        documentApprovalContainerRepository.saveAndFlush(report);
        logger.trace("attachApprover: Save successful, returning");
    }

    private DocumentRuleViewModel getRuleViewModel(DocumentRule rule) {
        logger.trace("getRuleViewModel: Copying rule to view-model and returning");
        DocumentRuleViewModel viewModel = new DocumentRuleViewModel();
        BeanUtils.copyProperties(rule, viewModel);
        return viewModel;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void markAsDraft(DocumentApprovalContainer documentApprovalContainer) {
        documentApprovalContainer.setReportStatus(ReportStatus.DRAFT);
        documentApprovalContainerRepository.saveAndFlush(documentApprovalContainer);
    }

}
