package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.cem.expense.account.implementations.CemExpenseAccountListResponse;
import in.taxgenie.pay_expense_pvv.cem.expense.account.implementations.CemExpenseAccountSingularResponse;
import in.taxgenie.pay_expense_pvv.cem.expense.account.interfaces.IExpenseAccountViewModel;
import in.taxgenie.pay_expense_pvv.services.interfaces.IExpenseAccountMasterDataService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IRestConsumer;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

@Service
public class ExpenseAccountMasterDataServiceImplementation
        implements
        IExpenseAccountMasterDataService {
    @Value("${CEM_URL}")
    private String cemUrl;
    
    private final IRestConsumer<CemExpenseAccountListResponse> listConsumer;
    private final IRestConsumer<CemExpenseAccountSingularResponse> singularConsumer;

    public ExpenseAccountMasterDataServiceImplementation(
            IRestConsumer<
                    CemExpenseAccountListResponse
                    > listConsumer,
            IRestConsumer<
                    CemExpenseAccountSingularResponse
                    >
                    singularConsumer
    ) {
        this.listConsumer = listConsumer;
        this.singularConsumer = singularConsumer;
    }

    @Override
    public IExpenseAccountViewModel getExpenseAccount(long companyCode, long groupId, long accountId) {
        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                StaticDataRegistry.CEM_SERVICE_EXPENSE_ACCOUNT_FRAGMENT,
                companyCode,
                groupId,
                accountId
        );

        return singularConsumer.read(
                        HttpMethod.GET,
                        null,
                        url,
                        CemExpenseAccountSingularResponse.class
                )
                .getBody();
    }

    @Override
    public IExpenseAccountViewModel[] getAllExpenseAccounts(long companyCode) {
        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                StaticDataRegistry.CEM_SERVICE_EXPENSE_ACCOUNT_FRAGMENT,
                companyCode
        );

        return listConsumer.read(
                        HttpMethod.GET,
                        null,
                        url,
                        CemExpenseAccountListResponse.class
                )
                .getBody();
    }

    @Override
    public IExpenseAccountViewModel[] getAllExpenseAccountsByGroup(long companyCode, long groupId) {
        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                StaticDataRegistry.CEM_SERVICE_EXPENSE_ACCOUNT_FRAGMENT,
                companyCode,
                StaticDataRegistry.CEM_SERVICE_BY_GROUP_FRAGMENT,
                groupId
        );

        return listConsumer.read(
                        HttpMethod.GET,
                        null,
                        url,
                        CemExpenseAccountListResponse.class
                )
                .getBody();
    }
}
