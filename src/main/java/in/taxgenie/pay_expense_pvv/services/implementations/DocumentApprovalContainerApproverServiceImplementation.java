package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetFrequencyMapping;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetStructureMaster;
import in.taxgenie.pay_expense_pvv.entities.invoice.InvoiceItem;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderItem;
import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestItem;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.MasterRecordNotFoundException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.factory.budgetaction.DocumentData;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.IReportStateRepository;
import in.taxgenie.pay_expense_pvv.repositories.QueueV2Repository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetStructureMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentApprovalContainerApproverService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmailNotificationService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IInvoiceTatReportService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetFrequencyMappingService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetService;
import in.taxgenie.pay_expense_pvv.services.interfaces.consumption.IConsumptionService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static in.taxgenie.pay_expense_pvv.utils.MappersUtil.convertSortOrderToJPASort;
import static in.taxgenie.pay_expense_pvv.utils.MappersUtil.enumHandler;

@Service
public class DocumentApprovalContainerApproverServiceImplementation implements IDocumentApprovalContainerApproverService {
    @Value("${spring.jpa.properties.hibernate.default_schema}")
    private String schema;
    private final IDocumentApprovalContainerRepository reportRepository;
    private final CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository;
    private final IReportStateRepository stateRepository;
    private final IBudgetStructureMasterRepository budgetStructureMasterRepository;
    private final IBudgetRepository budgetRepository;
    private final IBudgetFrequencyMappingService budgetFrequencyMappingService;
    private final IInvoiceTatReportService iInvoiceTatReportService;
    private final IBudgetService budgetService;
    //	private final IReportStateService stateService;
    private final IEmailNotificationService emailNotificationService;
    private final IConsumptionService consumptionService;
    private final Logger logger;

    public DocumentApprovalContainerApproverServiceImplementation(IDocumentApprovalContainerRepository reportRepository, CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository, IReportStateRepository stateRepository, IBudgetStructureMasterRepository budgetStructureMasterRepository, IBudgetRepository budgetRepository, IBudgetFrequencyMappingService budgetFrequencyMappingService, IInvoiceTatReportService iInvoiceTatReportService, IBudgetService budgetService, IEmailNotificationService emailNotificationService, IConsumptionService consumptionService) {
        this.reportRepository = reportRepository;
        this.customDocumentApprovalContainerRepository = customDocumentApprovalContainerRepository;
        this.stateRepository = stateRepository;
        this.budgetStructureMasterRepository = budgetStructureMasterRepository;
        this.budgetRepository = budgetRepository;
        this.budgetFrequencyMappingService = budgetFrequencyMappingService;
        this.iInvoiceTatReportService = iInvoiceTatReportService;
        this.budgetService = budgetService;
        this.emailNotificationService = emailNotificationService;
        this.consumptionService = consumptionService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Autowired
    QueueV2Repository queueRepo;

    @Transactional
    @Override
    public void approve(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth) {
        DocumentApprovalContainer report = reportRepository
                .findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getExpenseReportId())
                .orElseThrow(() -> new MasterRecordNotFoundException(
                        String.format("Could not find %s with id: %d and Company Code: %d", DocumentApprovalContainer.class,
                                viewModel.getExpenseReportId(), auth.getCompanyCode())));

        List<ReportState> stateList = stateRepository.findByDocumentApprovalContainerIdSorted(auth.getCompanyCode(), viewModel.getExpenseReportId(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK));
//        List<ReportState> stateList = stateService.findByExpenseReportIdSortedByLevel(auth.getCompanyCode(), viewModel.getExpenseReportId(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK));

        ReportState first =
                stateList
                        .stream()
                        .filter(s -> s.getStatus() == ExpenseActionStatus.UNACTIONED).min(Comparator.comparing(ReportState::getLevel))
                        .orElseThrow(() -> new DomainInvariantException("No approvable expenses found for report: " + viewModel.getExpenseReportId()));

        ReportState last =
                stateList
                        .stream()
                        .filter(s -> s.getStatus() == ExpenseActionStatus.UNACTIONED)
                        .max(Comparator.comparingInt(ReportState::getLevel))
                        .orElseThrow(() -> new DomainInvariantException("No approvable expenses found for report: " + viewModel.getExpenseReportId()));


        handleApproval(viewModel, auth, report, stateList, first, last);
    }

    @Transactional
    @Override
    public void approveBulk(BulkApproveContainerViewModel viewModel, IAuthContextViewModel auth) {
        for (BulkApproveLineViewModel line : viewModel.getLines()) {
            ReportStateCreateViewModel stateCreateViewModel = new ReportStateCreateViewModel();
            stateCreateViewModel.setExpenseReportId(line.getId());
            stateCreateViewModel.setStatus(ExpenseActionStatus.APPROVED);
            stateCreateViewModel.setLevel(line.getLevel());

            this.approve(stateCreateViewModel, auth);
        }
    }

//	private List<ReportState> findByExpenseReportIdSorted(long companyCode, long id, List<ReportStatus> statusList) {
//		Set<Long> actionableExpenseReportIds = reportRepository
//				.findByCompanyCodeAndReportStatusIn(companyCode, statusList)
//				.stream()
//				.map(ExpenseReport::getId)
//				.collect(Collectors.toSet());
//
//		return  stateRepository.findByExpenseReportIdSorted(companyCode, id)
//				.stream()
//				.filter(s -> actionableExpenseReportIds.contains(s.getExpenseReportId()))
//				.collect(Collectors.toList());
//	}

    //  Withdrawn for ABFL
    @Transactional
    @Override
    public void reject(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.info("reject: Ensuring the existence of Expense record for this request");
        DocumentApprovalContainer report =
                reportRepository
                        .findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getExpenseReportId())
                        .orElseThrow(() -> new MasterRecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", Document.class, viewModel.getExpenseReportId(), auth.getCompanyCode())));

        logger.trace("reject: Getting the states of this expense");
        List<ReportState> stateList = stateRepository.findByDocumentApprovalContainerIdSorted(auth.getCompanyCode(), viewModel.getExpenseReportId(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK));
//		List<ReportState> stateList = findByExpenseReportIdSorted(auth.getCompanyCode(), viewModel.getExpenseReportId(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK));

        logger.trace("reject: Getting the first unactioned state");
        ReportState first =
                stateList
                        .stream()
                        .filter(s -> s.getStatus() == ExpenseActionStatus.UNACTIONED)
                        .findFirst()
                        .orElseThrow(() -> new DomainInvariantException("No approvable expenses found for expense: " + viewModel.getExpenseReportId()));

        logger.trace("reject: Checking if the level and approver match with the ones provided in the view-model");
        if (first.getLevel() != viewModel.getLevel() || !first.getApprover().equalsIgnoreCase(auth.getUserEmail())) {
            throw new DomainInvariantException(String.format("User %s could not be found at level %d for expense: %d", auth.getUserEmail(), viewModel.getLevel(), viewModel.getExpenseReportId()));
        }

        logger.trace("reject: Setting the status to rejected and setting audit fields");
        first.setStatus(ExpenseActionStatus.REJECTED);
        first.setRemarks(viewModel.getRemarks());
        first.setActionDate(LocalDate.now());
        first.setUpdatedTimestamp(ZonedDateTime.now());

        logger.trace("reject: Setting the expense status to declined");
        report.setReportStatus(ReportStatus.DECLINED);
        report.setActionLevel(StaticDataRegistry.DECLINED_STATUS_MARKER);

        logger.trace("reject: Saving both the state and expense");
        stateRepository.saveAndFlush(first);
        reportRepository.saveAndFlush(report);
        logger.trace("reject: Save successful; exiting");
    }

    @Transactional
    @Override
    public DocumentApprovalContainerSendBackResultViewModel approverSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth) {
        return handleAndFinalizeSendBack(viewModel, auth);
    }

    @Transactional
    @Override
    public void checkerConsent(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth) {
        DocumentApprovalContainer report =
                reportRepository
                        .findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getExpenseReportId())
                        .orElseThrow(() -> new MasterRecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", Document.class, viewModel.getExpenseReportId(), auth.getCompanyCode())));

        List<ReportState> stateList = stateRepository.findByDocumentApprovalContainerIdSorted(auth.getCompanyCode(), viewModel.getExpenseReportId(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK));

        ReportState first =
                stateList
                        .stream()
                        .filter(s -> s.getStatus() == ExpenseActionStatus.UNACTIONED)
                        .findFirst()
                        .orElseThrow(() -> new DomainInvariantException("No approvable expenses found for expense: " + viewModel.getExpenseReportId()));

        ReportState last =
                stateList
                        .stream()
                        .filter(s -> s.getStatus() == ExpenseActionStatus.UNACTIONED)
                        .max(Comparator.comparingInt(ReportState::getLevel))
                        .orElseThrow(() -> new DomainInvariantException("No approvable expenses found for expense: " + viewModel.getExpenseReportId()));


        handleApproval(viewModel, auth, report, stateList, first, last);
    }

    @Transactional
    @Override
    public DocumentApprovalContainerSendBackResultViewModel checkerSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth) {
        return handleAndFinalizeSendBack(viewModel, auth);
    }

    private Map<Long, Object[]> createMapOfReportStateInformation(List<ReportState> reportStates) {
        Map<Long, Object[]> reportStateInfoEntry = new HashMap<>();
        for (ReportState reportState : reportStates) {
            reportStateInfoEntry.put(reportState.getDocumentApprovalContainerId(),
                    new Object[]{
                            reportState.getApproverEmployeeCode(),
                            reportState.getApproverFirstName(),
                            reportState.getApproverLastName()});
        }
        return reportStateInfoEntry;
    }

    private List<DocumentApprovalContainerViewModel> queueResultProcessor(long companyCode, List<Long> voucherIdsPendingApproval, Map<Long, Object[]> reportStateInfoEntries) {
        return reportRepository.findByCompanyCodeAndIdsIn(companyCode,
                        voucherIdsPendingApproval)
                .stream()
                .map(s -> {
                    DocumentApprovalContainerViewModel viewModel = new DocumentApprovalContainerViewModel();
                    viewModel.setExpenseType(null == s.getExpenseType() ? null : s.getExpenseType().name());
                    BeanUtils.copyProperties(s, viewModel, "expenseType");
                    viewModel.setDocumentType(s.getDocumentMetadata().getDocumentType());
                    viewModel.setDocumentGroup(s.getDocumentMetadata().getDocumentGroup());
                    viewModel.setCurrentApproverEmployeeCode((String) reportStateInfoEntries.get(s.getId())[0]);
                    viewModel.setCurrentApproverFirstName((String) reportStateInfoEntries.get(s.getId())[1]);
                    viewModel.setCurrentApproverLastName((String) reportStateInfoEntries.get(s.getId())[2]);
                    return viewModel;
                })
                .collect(Collectors.toList());

    }

    @Override
    public List<DocumentApprovalContainerViewModel> getApproverQueue(IAuthContextViewModel auth) {
//		// Below code is to avoid running join query for each expense report with report state
//		List<ReportState> reportStates = stateService.getApproverQueue(auth.getCompanyCode(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK), StateChannel.APPROVER, ExpenseActionStatus.UNACTIONED, auth.getUserEmail());
//
//		Map<Long, Object[]> reportStateInfoEntry = createMapOfReportStateInformation(reportStates);
//		List<Long> voucherIdsPendingApproval = new ArrayList<>(reportStateInfoEntry.keySet());
//		return queueResultProcessor(auth.getCompanyCode(), voucherIdsPendingApproval, reportStateInfoEntry);
        return stateRepository
                .getApproverQueue(auth.getCompanyCode(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK), StateChannel.APPROVER, ExpenseActionStatus.UNACTIONED, auth.getUserEmail())
                .stream()
                .map(s -> {
                    DocumentApprovalContainer approvalContainer = s.getDocumentApprovalContainer();
                    DocumentMetadata documentMetadata = approvalContainer.getDocumentMetadata();

                    DocumentApprovalContainerViewModel viewModel = new DocumentApprovalContainerViewModel();
                    viewModel.setExpenseType(null == approvalContainer.getExpenseType() ? null : approvalContainer.getExpenseType().name());
                    BeanUtils.copyProperties(approvalContainer, viewModel, "expenseType");

                    viewModel.setDocumentType(documentMetadata.getDocumentType());
                    viewModel.setDocumentGroup(documentMetadata.getDocumentGroup());
                    viewModel.setDocumentCategory(documentMetadata.getDocumentCategory());
//                    viewModel.setDelegationRemarks(s.getDelegationRemarks());  // Not being used in the front-end
//                    viewModel.setDefaultApproverRemarks(s.getDefaultTriggerRemarks()); . // Not being used in the front-end
                    viewModel.setCurrentApproverEmployeeCode(s.getApproverEmployeeCode());
                    viewModel.setCurrentApproverFirstName(s.getApproverFirstName());
                    viewModel.setCurrentApproverLastName(s.getApproverLastName());
                    return viewModel;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public GenericPageableViewModel<DocumentApprovalContainerViewModel> getApproverQueueV2(IAuthContextViewModel auth,
                                                                                           QueueFilterViewModel viewModelReq) {
        return getApproverQueueDataV3(StateChannel.APPROVER, auth, viewModelReq);
//        return getApproverQueueData(StateChannel.valueOf(StateChannel.APPROVER.name()).ordinal(), auth, viewModelReq);
    }

    @Override
    public List<DocumentApprovalContainerViewModel> getCheckerQueue(IAuthContextViewModel auth) {
//		List<ReportState> reportStates = stateService.getApproverQueue(auth.getCompanyCode(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK),
//				StateChannel.CHECKER, ExpenseActionStatus.UNACTIONED, auth.getUserEmail());
//		Map<Long, Object[]> reportStateInfoEntry = createMapOfReportStateInformation(reportStates);
//		List<Long> voucherIdsPendingApproval = new ArrayList<>(reportStateInfoEntry.keySet());
//		return queueResultProcessor(auth.getCompanyCode(), voucherIdsPendingApproval, reportStateInfoEntry);

        return stateRepository
                .getApproverQueue(auth.getCompanyCode(), List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK),
                        StateChannel.CHECKER, ExpenseActionStatus.UNACTIONED, auth.getUserEmail())
                .stream().map(s -> {
                    DocumentApprovalContainerViewModel viewModel = new DocumentApprovalContainerViewModel();

                    DocumentApprovalContainer approvalContainer = s.getDocumentApprovalContainer();
                    DocumentMetadata documentMetadata = s.getDocumentApprovalContainer().getDocumentMetadata();

                    viewModel.setExpenseType(null == approvalContainer.getExpenseType() ? null : approvalContainer.getExpenseType().name());
                    BeanUtils.copyProperties(approvalContainer, viewModel, "expenseType");

                    viewModel.setDocumentType(documentMetadata.getDocumentType());
                    viewModel.setDocumentGroup(documentMetadata.getDocumentGroup());
//                    viewModel.setDelegationRemarks(s.getDelegationRemarks());
                    viewModel.setCurrentApproverEmployeeCode(s.getApproverEmployeeCode());
                    viewModel.setCurrentApproverFirstName(s.getApproverFirstName());
                    viewModel.setCurrentApproverLastName(s.getApproverLastName());
                    return viewModel;
                }).collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    @Override
    public GenericPageableViewModel<DocumentApprovalContainerViewModel> getCheckerQueueV2(IAuthContextViewModel auth,
                                                                                          QueueFilterViewModel viewModelReq) {
        return getApproverQueueDataV3(StateChannel.CHECKER, auth, viewModelReq);
//        return getApproverQueueData(StateChannel.valueOf(StateChannel.CHECKER.name()).ordinal(), auth, viewModelReq);

    }

    private GenericPageableViewModel<DocumentApprovalContainerViewModel> getApproverQueueData(int stateChannel,
                                                                                              IAuthContextViewModel auth, QueueFilterViewModel viewModelReq) {
        GenericPageableViewModel<DocumentApprovalContainerViewModel> response = new GenericPageableViewModel<DocumentApprovalContainerViewModel>();
        int pageNumber = viewModelReq.getPage() - 1;

        // query with filters
        StringBuilder searchWhereQuery = getQuery(viewModelReq);

        // get total elements
        StringBuilder query = new StringBuilder(queueRepo.selectCountQueryForChecker(auth.getCompanyCode(),
                stateChannel, ExpenseActionStatus.valueOf(ExpenseActionStatus.UNACTIONED.name()).ordinal(),
                auth.getUserEmail())).append(searchWhereQuery.toString());
        int totalElements = queueRepo.getCheckerTotalElements(query.toString(),
                List.of(ReportStatus.valueOf(ReportStatus.SUBMITTED.name()).ordinal(),
                        ReportStatus.valueOf(ReportStatus.SENT_BACK.name()).ordinal()));

        // get results
        query = new StringBuilder(queueRepo.selectAllQueryForChecker(auth.getCompanyCode(), stateChannel,
                ExpenseActionStatus.valueOf(ExpenseActionStatus.UNACTIONED.name()).ordinal(), auth.getUserEmail()))
                .append(searchWhereQuery.toString());
        List<Object[]> viewModelsFromDB = queueRepo.getCheckerQueueData(query.toString(),
                List.of(ReportStatus.valueOf(ReportStatus.SUBMITTED.name()).ordinal(),
                        ReportStatus.valueOf(ReportStatus.SENT_BACK.name()).ordinal()),
                pageNumber, viewModelReq.getPageSize(), StringConstants.toSnakeCase(viewModelReq.getSortByColumnName()),
                StringConstants.getSortLevel(viewModelReq.getSortOrder()));

        List<DocumentApprovalContainerViewModel> formattedResponse = new ArrayList<>();

        viewModelsFromDB.forEach(obj -> {
            formattedResponse.add(setQueueResponse(obj));
        });

        Map<Long, DocumentApprovalContainer> documentApprovalContainerMap = new HashMap<>();
        List<DocumentApprovalContainer> documentApprovalContainers = reportRepository.findByCompanyCodeAndIdsIn(auth.getCompanyCode(), formattedResponse.stream().map(DocumentApprovalContainerViewModel::getId).toList());

        for (DocumentApprovalContainer dac : documentApprovalContainers) {
            documentApprovalContainerMap.put(dac.getId(), dac);
        }

        for (DocumentApprovalContainerViewModel dac : formattedResponse) {
            if (dac != null && documentApprovalContainerMap.containsKey(dac.getId())) {
                List<Document> documents = documentApprovalContainerMap.get(dac.getId()).getDocuments();
                if (!documents.isEmpty()) {
                    dac.setDocumentSubgroupId(documents.get(0).getDocumentSubgroupId());
                }
            }
        }

        response.setPages(getTotalPages(totalElements, viewModelReq.getPageSize()));
        response.setTotalElements(totalElements);
        response.setData(formattedResponse);
        return response;
    }

    public GenericPageableViewModel<DocumentApprovalContainerViewModel> getApproverQueueDataV3(StateChannel stateChannel, IAuthContextViewModel auth, QueueFilterViewModel filterValues) {
        // Initialise the page
        GenericPageableViewModel<DocumentApprovalContainerViewModel> pagedQueue = new GenericPageableViewModel<>();
        Sort sort = null;

        String sortColumn = filterValues.getSortByColumnName() == null ? "default" : filterValues.getSortByColumnName();
        sort = switch (sortColumn) {
            case "reportStatus" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "reportStatus");
            case "createdDate" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "createdDate");
            case "approvalContainerTitle" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "approvalContainerTitle");
            case "approvalContainerClaimAmount" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "approvalContainerClaimAmount");
            case "firstName" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "firstName").and(Sort.by(Sort.Direction.DESC, "id"));
            default -> Sort.by(Sort.Direction.DESC, "updatedTimestamp").and(Sort.by(Sort.Direction.DESC, "id"));
        };

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize(), sort);

        Page<DocumentApprovalContainerViewModel> documentApprovalContainerPaged = customDocumentApprovalContainerRepository.getApproverQueueDataV3(stateChannel, auth.getCompanyCode(), auth.getUserEmail(), filterValues, pageable);

        Map<Long, DocumentApprovalContainer> documentApprovalContainerMap = new HashMap<>();
        List<DocumentApprovalContainer> documentApprovalContainers = reportRepository.findByCompanyCodeAndIdsIn(auth.getCompanyCode(), documentApprovalContainerPaged.getContent().stream().map(DocumentApprovalContainerViewModel::getId).toList());

        for (DocumentApprovalContainer dac : documentApprovalContainers) {
            documentApprovalContainerMap.put(dac.getId(), dac);
        }

        for (DocumentApprovalContainerViewModel dac : documentApprovalContainerPaged.getContent()) {
            if (dac != null && documentApprovalContainerMap.containsKey(dac.getId())) {
                List<Document> documents = documentApprovalContainerMap.get(dac.getId()).getDocuments();
                if (!documents.isEmpty()) {
                    dac.setDocumentSubgroupId(documents.get(0).getDocumentSubgroupId());
                }
            }
        }
        // Set response
        pagedQueue.setData(documentApprovalContainerPaged.getContent());
        pagedQueue.setPages(documentApprovalContainerPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(documentApprovalContainerPaged.getTotalElements()));

        return pagedQueue;
    }

//    @Override
//    public List<ExpenseReportViewModel> getAdminQueue(IAuthContextViewModel auth) {
//        return reportRepository
//                .getAdminQueue(auth.getCompanyCode())
//                .stream()
//                .map(e -> {
//                    ExpenseReportViewModel viewModel = new ExpenseReportViewModel();
//                    BeanUtils.copyProperties(e, viewModel);
//                    viewModel.setExpenseType(e.getExpenseMetadata().getExpenseType());
//                    viewModel.setExpenseGroup(e.getExpenseMetadata().getExpenseGroup());
//
////                    if (e.getReportStatus() == ReportStatus.SENT_BACK) {
////                        ReportState state = stateRepository.getStateForResubmission(auth.getCompanyCode(), e.getId(), e.getActionLevel(), ExpenseActionStatus.SENT_BACK)
////                                .orElseThrow(() -> new RecordNotFoundException("Could not find the sent-back state for expense: " + e.getId()));
////
////                        viewModel.setSendBackRemarks(state.getRemarks());
////                    }
////
////                    if (e.getReportStatus() == ReportStatus.DECLINED) {
////                        ReportState state = stateRepository.findFirstByCompanyCodeAndExpenseReportIdAndStatus(auth.getCompanyCode(), e.getId(), ExpenseActionStatus.REJECTED)
////                                .orElseThrow(() -> new RecordNotFoundException("Could not find the sent-back state for expense: " + e.getId()));
////
////                        viewModel.setRejectRemarks(state.getRemarks());
////                    }
////
//                    Optional<ReportState> currentState = stateRepository.findFirstByCompanyCodeAndExpenseReportIdAndStatusOrderByLevel(auth.getCompanyCode(), e.getId(), ExpenseActionStatus.UNACTIONED);
//                    if (currentState.isPresent()) {
//                        viewModel.setCurrentApproverEmployeeCode(currentState.get().getApproverEmployeeCode());
//                        viewModel.setCurrentApproverFirstName(currentState.get().getApproverFirstName());
//                        viewModel.setCurrentApproverLastName(currentState.get().getApproverLastName());
//                    }
//
//                    return viewModel;
//                })
//                .collect(Collectors.toList());
//    }

    @Override
    @Transactional(readOnly = true)
    public List<DocumentApprovalContainerViewModel> getAdminQueue(IAuthContextViewModel auth) {

//		List<AdminQueueViewModelV2> res = customDocumentApprovalContainerRepository.getAdminQueueV3(auth.getCompanyCode());
        List<AdminQueueViewModel> res = reportRepository.getAdminQueueV2(auth.getCompanyCode());
        List<DocumentApprovalContainerViewModel> formattedResponse = res.stream().map(e -> {
            DocumentApprovalContainerViewModel viewModel = new DocumentApprovalContainerViewModel();
            viewModel.setExpenseType(null == e.getExpenseType() ? null : e.getExpenseType().name());
            BeanUtils.copyProperties(e, viewModel, "reportStatus", "expenseType");
            if (e.getReportStatus() != null) {
                viewModel.setReportStatus(ReportStatus.values()[e.getReportStatus()]);  // Assuming it's an enum
            }
            return viewModel;
        }).collect(Collectors.toList());

        return formattedResponse;
    }

    @Override
    public GenericPageableViewModel<DocumentApprovalContainerViewModel> getAdminQueueV2(IAuthContextViewModel auth,
                                                                                        QueueFilterViewModel viewModelReq) {
        GenericPageableViewModel<DocumentApprovalContainerViewModel> response = new GenericPageableViewModel<DocumentApprovalContainerViewModel>();
        int pageNumber = viewModelReq.getPage() - 1;

        // query with filters
        StringBuilder searchWhereQuery = getQuery(viewModelReq);

        // get total elements

        int totalElements = queueRepo.getTotalElements(searchWhereQuery.toString(), auth.getCompanyCode());

        // get results

        List<Object[]> viewModelsFromDB = queueRepo.getAdminQueueData(searchWhereQuery.toString(), auth.getCompanyCode(), pageNumber,
                viewModelReq.getPageSize(), StringConstants.toSnakeCase(viewModelReq.getSortByColumnName()),
                StringConstants.getSortLevel(viewModelReq.getSortOrder()));

        List<DocumentApprovalContainerViewModel> formattedResponse = new ArrayList<>();
        viewModelsFromDB.forEach(obj -> {
            formattedResponse.add(setQueueResponse(obj));
        });

        response.setPages(getTotalPages(totalElements, viewModelReq.getPageSize()));
        response.setTotalElements(totalElements);
        response.setData(formattedResponse);
        return response;
    }

    @Override
    public GenericPageableViewModel<DocumentApprovalContainerViewModel> adminQueueViewModelV3(IAuthContextViewModel auth, QueueFilterViewModel filterValues) {
        // Initialise the page
        GenericPageableViewModel<DocumentApprovalContainerViewModel> pagedQueue = new GenericPageableViewModel<>();
        Sort sort = null;

        String sortColumn = filterValues.getSortByColumnName() == null ? "default" : filterValues.getSortByColumnName();
        sort = switch (sortColumn) {
            case "reportStatus" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "reportStatus");
            case "createdDate" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "createdDate");
            case "approvalContainerTitle" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "approvalContainerTitle");
            case "approvalContainerClaimAmount" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "approvalContainerClaimAmount");
            case "firstName" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "firstName").and(Sort.by(Sort.Direction.DESC, "id"));
            default -> Sort.by(Sort.Direction.DESC, "updatedTimestamp").and(Sort.by(Sort.Direction.DESC, "id"));
        };

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize(), sort);

        Page<DocumentApprovalContainerViewModel> documentApprovalContainerPaged = customDocumentApprovalContainerRepository.getAdminQueueV3(auth.getCompanyCode(), filterValues, pageable);

        // Set response
        pagedQueue.setData(documentApprovalContainerPaged.getContent());
        pagedQueue.setPages(documentApprovalContainerPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(documentApprovalContainerPaged.getTotalElements()));

        return pagedQueue;
    }

    private int getTotalPages(int totalElements, int pageSize) {
        // calculate number of pages, varies with number of records ie pageSize
        int pages = 1;
        if (totalElements < pageSize)
            return pages;
        else
            return pages = (totalElements / pageSize) + ((totalElements % pageSize) == 0 ? 0 : 1);
    }

    private StringBuilder getQuery(QueueFilterViewModel viewModelReq) {
        StringBuilder searchWhereQuery = new StringBuilder();
        Set<String> filters = viewModelReq.getSearchCriteria().keySet();
        filters.forEach(searchCriteria -> {
            String orOperator = " OR ";
            String andOperator = " AND (";
            String param = "";
            switch (searchCriteria) {
                case StringConstants.EMPLOYEE_NAME:
                    param = "CONCAT(er1.first_name,' ',er1.last_name) LIKE ";
                    searchWhereQuery.append(getWhereQuery(param, andOperator, orOperator, searchCriteria, viewModelReq));
                    break;
                case StringConstants.EMPLOYEE_EMAIL:
                    param = " er1.employee_email LIKE ";
                    searchWhereQuery.append(getWhereQuery(param, andOperator, orOperator, searchCriteria, viewModelReq));
                    break;
                case StringConstants.EMPLOYEE_CODE:
                    param = " er1.employee_code LIKE ";
                    searchWhereQuery.append(getWhereQuery(param, andOperator, orOperator, searchCriteria, viewModelReq));
                    break;
                case StringConstants.APPROVER_NAME:
                    param = "CONCAT(rs.approver_first_name,' ',rs.approver_last_name) LIKE ";
                    searchWhereQuery.append(getWhereQuery(param, andOperator, orOperator, searchCriteria, viewModelReq));
                    break;
                case StringConstants.APPROVER_EMPLOYEE_CODE:
                    param = " rs.approver_employee_code LIKE ";
                    searchWhereQuery.append(getWhereQuery(param, andOperator, orOperator, searchCriteria, viewModelReq));
                    break;
                case StringConstants.DOCUMENT_IDENTIFIER:
                    param = " er1.document_identifier LIKE ";
                    searchWhereQuery.append(getWhereQuery(param, andOperator, orOperator, searchCriteria, viewModelReq));
                    break;
                case StringConstants.REPORT_TITLE:
                    param = " er1.approval_container_title LIKE ";
                    searchWhereQuery.append(getWhereQuery(param, andOperator, orOperator, searchCriteria, viewModelReq));
                    break;
                case StringConstants.CREATED_DATE:
                    param = " er1.created_date LIKE ";
                    searchWhereQuery.append(getWhereQuery(param, andOperator, orOperator, searchCriteria, viewModelReq));
                    break;
                case StringConstants.REPORT_CLAIM_AMOUNT:
                    param = " er1.approval_container_claim_amount LIKE ";
                    searchWhereQuery.append(getWhereQuery(param, andOperator, orOperator, searchCriteria, viewModelReq));
                    break;
                case StringConstants.REPORT_STATUS:
                    param = " CAST(er1.report_status as TEXT) LIKE ";
                    searchWhereQuery
                            .append(getWhereQueryForStatus(param, andOperator, orOperator, searchCriteria, viewModelReq));
                    break;
                default:// need to check
            }
        });
        return searchWhereQuery;
    }

    private String getWhereQueryForStatus(String param, String andOperator, String orOperator, String searchCriteria,
                                          QueueFilterViewModel viewModelReq) {

        StringBuilder searchWhereQuery = new StringBuilder();
        searchWhereQuery.append(andOperator);
        viewModelReq.getSearchCriteria().get(searchCriteria).forEach(v -> {
            if (null == v || v.isEmpty() || v.isBlank())
                return;
            searchWhereQuery.append(param).append("\'%")
                    .append(ReportStatus.valueOf(getStatus(v.toUpperCase())).ordinal()).append("%\'")
                    .append(orOperator);
        });
        // remove last occurrence of orSeperator
        if (searchWhereQuery.lastIndexOf(orOperator) != -1) {
            searchWhereQuery.delete(searchWhereQuery.lastIndexOf(orOperator), searchWhereQuery.length());
            searchWhereQuery.append(")");
        } else {
            // to delete andOperator
            searchWhereQuery.delete(0, searchWhereQuery.length());
        }
        return searchWhereQuery.toString();
    }

    private String getStatus(String matchStatus) {
        String status = "DRAFT";
        for (ReportStatus value : ReportStatus.values()) {
            if (value.name().contains(matchStatus)) {
                status = value.name();
                break;
            }
        }
        return status;
    }

    private String getWhereQuery(String param, String andOperator, String orOperator, String searchCriteria,
                                 QueueFilterViewModel viewModelReq) {
        StringBuilder searchWhereQuery = new StringBuilder();
        searchWhereQuery.append(andOperator);
        List<String> values = viewModelReq.getSearchCriteria().get(searchCriteria);
        values.forEach(v -> {
            if (null == v || v.isEmpty() || v.isBlank())
                return;
            searchWhereQuery.append(param).append("\'%").append(v).append("%\'").append(orOperator);
        });
        // remove last occurrence of orSeperator
        if (searchWhereQuery.lastIndexOf(orOperator) != -1) {
            searchWhereQuery.delete(searchWhereQuery.lastIndexOf(orOperator), searchWhereQuery.length());
            searchWhereQuery.append(")");
        } else {
            // to delete andOperator
            searchWhereQuery.delete(0, searchWhereQuery.length());
        }

        return searchWhereQuery.toString();
    }

    private DocumentApprovalContainerViewModel setQueueResponse(Object[] obj) {
        DocumentApprovalContainerViewModel view = new DocumentApprovalContainerViewModel();
        view.setCurrentApproverEmployeeCode(null == obj[0] ? null : String.valueOf(obj[0]));
        view.setCurrentApproverFirstName(null == obj[1] ? null : String.valueOf(obj[1]));
        view.setLevel((null == obj[2] || "null".equals(obj[2])) ? 0 : (Integer) (obj[2]));
        view.setCurrentApproverLastName(null == obj[3] ? null : String.valueOf(obj[3]));
        view.setId(null == obj[4] ? null : ((Long) (obj[4])));
        view.setDocumentIdentifier(null == obj[5] ? null : String.valueOf(obj[5]));
        view.setCreatedDate(null == obj[6] ? null : String.valueOf(obj[6]));
        view.setSubmitDate(null == obj[7] ? null : String.valueOf(obj[7]));
        view.setStartDate(null == obj[8] ? null : String.valueOf(obj[8]));
        view.setEndDate(null == obj[9] ? null : String.valueOf(obj[9]));
        view.setApprovalContainerTitle(null == obj[10] ? null : String.valueOf(obj[10]));
        view.setDescription(null == obj[11] ? null : String.valueOf(obj[11]));
        view.setPurpose(null == obj[12] ? null : String.valueOf(obj[12]));
        view.setApprovalContainerClaimAmount(null == obj[13] ? null : (BigDecimal) (obj[13]));
        view.setReportStatus(null == obj[14] ? null : ReportStatus.values()[enumHandler((Short) (obj[14]))]);
        view.setActionLevel(null == obj[15] ? null : (Integer) (obj[15]));
        view.setContainsDeviation(null == obj[16] ? null : (Boolean) (obj[16]));
        view.setDeviationRemarks(null == obj[17] ? null : String.valueOf(obj[17]));
        view.setApprovalContainerSgstAmount(null == obj[18] ? null : (BigDecimal) (obj[18]));
        view.setApprovalContainerCgstAmount(null == obj[19] ? null : (BigDecimal) (obj[19]));
        view.setApprovalContainerIgstAmount(null == obj[20] ? null : (BigDecimal) (obj[20]));
        view.setApprovalContainerTaxableAmount(null == obj[21] ? null : (BigDecimal) (obj[21]));
        view.setFirstName(null == obj[22] ? null : String.valueOf(obj[22]));
        view.setMiddleName(null == obj[23] ? null : String.valueOf(obj[23]));
        view.setLastName(null == obj[24] ? null : String.valueOf(obj[24]));
        view.setEmployeeEmail(null == obj[25] ? null : String.valueOf(obj[25]));
        view.setEmployeeCode(null == obj[26] ? null : String.valueOf(obj[26]));
        view.setEmployeeGrade(null == obj[27] ? null : String.valueOf(obj[27]));
        view.setEmployeeSystemId(null == obj[28] ? null : ((Long) (obj[28])));
        view.setExpenseType(null == obj[29] ? null : (obj[29]).toString());
        view.setDocumentMetadataId(null == obj[30] ? null : ((Long) (obj[30])));
        view.setDocumentGroup(null == obj[31] ? null : String.valueOf(obj[31]));
        view.setDocumentType(null == obj[32] ? null : String.valueOf(obj[32]));
        view.setSendBackRemarks(null == obj[33] ? null : String.valueOf(obj[33]));
        view.setRejectRemarks(null == obj[34] ? null : String.valueOf(obj[34]));
        view.setDelegationRemarks(null == obj[35] ? null : String.valueOf(obj[35]));
        view.setDefaultApproverRemarks(null == obj[36] ? null : String.valueOf(obj[36]));
        view.setContainsSentBack(null == obj[37] ? null : (Boolean) (obj[37]));
        view.setGlPostingDate(null == obj[38] ? null : ((Date) (obj[38])).toLocalDate());
        view.setPaymentDate(null == obj[39] ? null : ((Date) (obj[39])).toLocalDate());
        view.setDocumentCategory(null == obj[40] ? null : String.valueOf(obj[40]));
        view.setDocumentSubgroupId(null == obj[41] ? null : (Long) (obj[41]));
        view.setUtrNumber(null == obj[42] ? null : String.valueOf(obj[42]));
        return view;
    }

    @Override
    public List<DocumentApprovalContainerViewModel> getSimilarExpenses(long reportId, IAuthContextViewModel auth) {
        DocumentApprovalContainer referenceReport = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), reportId)
                .orElseThrow(() -> new RecordNotFoundException("Could not find the report: " + reportId));

        if (referenceReport.getStartDate() == null || referenceReport.getEndDate() == null) {
            return new ArrayList<>();
        }

        return reportRepository
                .getReportsByMetadataUserStatus(auth.getCompanyCode(), referenceReport.getCreatingUserId(),
                        referenceReport.getDocumentMetadataId(),
                        List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK, ReportStatus.ACCEPTED,
                                ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM))
                .stream().filter(r -> r.getId() != referenceReport.getId())
                // Commented date check on 6 July 2022
                // .filter(r -> r.getStartDate().getMonth() ==
                // referenceReport.getStartDate().getMonth()
                // || r.getEndDate().getMonth() == referenceReport.getEndDate().getMonth())
                .filter(r -> r.getStartDate().isAfter(referenceReport.getStartDate().minusDays(1))
                        && r.getEndDate().isBefore(referenceReport.getEndDate().plusDays(1)))
                .map(r -> {
                    DocumentApprovalContainerViewModel viewModel = new DocumentApprovalContainerViewModel();
                    viewModel.setExpenseType(null == r.getExpenseType() ? null : r.getExpenseType().name());
                    BeanUtils.copyProperties(r, viewModel, "expenseType");
                    viewModel.setDocumentType(r.getDocumentMetadata().getDocumentType());
                    viewModel.setDocumentGroup(r.getDocumentMetadata().getDocumentGroup());
                    return viewModel;
                }).collect(Collectors.toList());
    }

    private void handleApproval(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth, DocumentApprovalContainer report, List<ReportState> stateList, ReportState first, ReportState last) {
        if (first.getLevel() != viewModel.getLevel() || !first.getApprover().equalsIgnoreCase(auth.getUserEmail())) {
            throw new DomainInvariantException(String.format("User %s could not be found at level %d for expense: %d", auth.getUserEmail(), viewModel.getLevel(), viewModel.getExpenseReportId()));
        }

        first.setStatus(ExpenseActionStatus.APPROVED);
        first.setRemarks(viewModel.getRemarks());
        first.setActionDate(LocalDate.now());
        first.setUpdatedTimestamp(ZonedDateTime.now());
        stateRepository.save(first);
        report.setActionLevel(report.getActionLevel() + StaticDataRegistry.APPROVAL_INCREMENT_FACTOR);

        ReportState currentState = first;

        Optional<ReportState> nextStateOptional = stateList
                .stream()
                .filter(s -> s.getLevel() == report.getActionLevel() && s.getStatus() == ExpenseActionStatus.UNACTIONED)
                .findFirst();

        while (nextStateOptional.isPresent()) {

//            if (currentState.equals(last)) {
//                report.setReportStatus(ReportStatus.ACCEPTED);
//                report.setActionLevel(StaticDataRegistry.ACCEPTED_STATUS_MARKER);
//
//                report.setActionStatus(ExpenseActionStatus.APPROVED);
//                report.setCurrentApproverFirstName(null);
//                report.setCurrentApproverLastName(null);
//                report.setCurrentApproverEmployeeCode(null);
//                report.setSendBackRemarks(null);
//            } else {
            if (nextStateOptional.get().getApprover().equals(currentState.getApprover())) {
                ReportState duplicateApprover = nextStateOptional.get();
                duplicateApprover.setStatus(ExpenseActionStatus.APPROVED);
                duplicateApprover.setRemarks(StaticDataRegistry.DUPLICATE_APPROVER_MESSAGE);
                duplicateApprover.setActionDate(LocalDate.now());
                duplicateApprover.setUpdatedTimestamp(ZonedDateTime.now());

//                    stateRepository.saveAndFlush(duplicateApprover);
                stateRepository.save(duplicateApprover);

                report.setActionLevel(report.getActionLevel() + StaticDataRegistry.APPROVAL_INCREMENT_FACTOR);

                currentState = nextStateOptional.get();
                nextStateOptional = stateList
                        .stream()
                        .filter(s -> s.getLevel() == report.getActionLevel() && s.getStatus() == ExpenseActionStatus.UNACTIONED)
                        .findFirst();

            } else {
                report.setActionStatus(ExpenseActionStatus.UNACTIONED);
                report.setCurrentApproverFirstName(nextStateOptional.get().getApproverFirstName());
                report.setCurrentApproverLastName(nextStateOptional.get().getApproverLastName());
                report.setCurrentApproverEmployeeCode(nextStateOptional.get().getApproverEmployeeCode());
                report.setSendBackRemarks(null);
                break;
            }
        }
//        }

        if (currentState.equals(last)) {
            report.setReportStatus(ReportStatus.ACCEPTED);
            report.setActionLevel(StaticDataRegistry.ACCEPTED_STATUS_MARKER);

            report.setActionStatus(ExpenseActionStatus.APPROVED);
            report.setCurrentApproverFirstName(null);
            report.setCurrentApproverLastName(null);
            report.setCurrentApproverEmployeeCode(null);
            report.setSendBackRemarks(null);

            if (report.getDocumentMetadata().getActionType().equals(ActionType.SUBMIT)
                    && DocumentType.BUDGET.toString().equalsIgnoreCase(report.getDocumentMetadata().getDocumentCategory())) {
                Optional<BudgetStructureMaster> budgetStructureMasterOptional = budgetStructureMasterRepository.findById(report.getDocuments().get(0).getBudgetDocumentAction().getBudgetStructureMasterId());

                if (budgetStructureMasterOptional.isPresent()) {
                    BudgetStructureMaster budgetStructureMaster = budgetStructureMasterOptional.get();

                    budgetStructureMaster.setIsApproved(true);
                    budgetStructureMaster.setStatus(ReportStatus.ACCEPTED);
                    budgetStructureMasterRepository.saveAndFlush(budgetStructureMaster);
                }
            }

            if ((report.getDocumentMetadata().getActionType().equals(ActionType.ADD) || report.getDocumentMetadata().getActionType().equals(ActionType.LESS))
                    && DocumentType.BUDGET.toString().equalsIgnoreCase(report.getDocumentMetadata().getDocumentCategory())) {

                budgetService.addLessBudget(report.getDocuments().get(0).getBudgetDocumentAction().getBudgetId(), report.getApprovalContainerClaimAmount(), report.getDocumentMetadata().getActionType());
            } else {
                List<DocumentData> documentDataList = getDocumentData(report);
                Boolean success = budgetService.handleBudgetConsumption(documentDataList);
                //TODO : Handel success of budget consumption
            }

        }

        stateRepository.flush();
        reportRepository.saveAndFlush(report);

        //Create TAT entry for invoice approve
        if(report.getDocumentType().equals(DocumentType.INVOICE)) {
            if (!iInvoiceTatReportService.create(ApproverType.APPROVER, report.getId(), auth)) {
                logger.info("Failed to create Invoice TAT Report for approver");
            }
        }

        emailNotificationService.transmitApprovedNotification(report);
    }

    @Override
    public List<DocumentData> getDocumentData(DocumentApprovalContainer report) {
        DocumentType documentType = DocumentType.values()[report.getDocumentMetadata().getDocumentCategoryId() - 1];
        ReportStatus reportStatus = report.getReportStatus();

        List<DocumentData> documentDataList = new ArrayList<>();

        if (documentType.equals(DocumentType.PURCHASE_REQUEST)) {
            List<PurchaseRequestItem> purchaseRequestItems = report.getDocuments().get(0).getPrHeader().getPrItems();
            purchaseRequestItems.forEach(purchaseRequestItem -> {
                Budget budget = purchaseRequestItem.getBudgetNode();
                if (null != budget) {
                    BudgetFrequencyMapping budgetFrequencyMapping = budgetFrequencyMappingService.getCurrentFrequency(budget);

                    DocumentData documentData = new DocumentData();
                    documentData.setDocumentType(documentType);
                    documentData.setAction(reportStatus);
                    documentData.setBudget(budget);
                    documentData.setBudgetFrequencyMapping(budgetFrequencyMapping);

                    documentData.setAmount(purchaseRequestItem.getAmountWithoutGst());
                    documentDataList.add(documentData);
                }
            });
        }

        if (documentType.equals(DocumentType.PURCHASE_ORDER)) {
            Document document = report.getDocuments().get(0);
            PurchaseOrderHeader purchaseOrderHeader = document.getPoHeader();
            boolean hasPrecedingDocument = purchaseOrderHeader.getHasPrecedingDocument();
            List<PurchaseOrderItem> purchaseOrderItems = purchaseOrderHeader.getPurchaseOrderItems();
            purchaseOrderItems.forEach(purchaseOrderItem -> {
                Budget budget = purchaseOrderItem.getBudgetNode();
                if (null != budget) {
                    BudgetFrequencyMapping budgetFrequencyMapping = budgetFrequencyMappingService.getCurrentFrequency(budget);

                    DocumentData documentData = new DocumentData();
                    documentData.setDocumentType(documentType);
                    documentData.setAction(reportStatus);
                    documentData.setBudget(budget);

                    documentData.setBudgetFrequencyMapping(budgetFrequencyMapping);

                    //if hasPrecedingDocument is true then po budget need to be set
                    documentData.setIsBase(hasPrecedingDocument);
                    if (hasPrecedingDocument) {

                        BudgetFrequencyMapping baseBudgetFrequencyMapping = budgetFrequencyMappingService.getCurrentFrequency(purchaseOrderItem.getBudgetNode());
                        documentData.setBaseBudget(purchaseOrderItem.getPurchaseRequestItem().getBudgetNode());
                        documentData.setBaseBudgetFrequencyMapping(baseBudgetFrequencyMapping);

                    }

                    //TODO : Check which amount to be consumed from budget.
                    documentData.setAmount(purchaseOrderItem.getAmountWithoutGst());

                    documentDataList.add(documentData);
                }
            });
        }

        if (documentType.equals(DocumentType.INVOICE)) {

            Document document = report.getDocuments().get(0);
            LookupData invoiceType = document.getInvoiceHeader().getInvoiceType();

            boolean hasPrecedingDocument = document.getInvoiceHeader().getHasPrecedingDocument();
            List<InvoiceItem> invoiceItems = document.getInvoiceHeader().getInvoiceItems();
            invoiceItems.forEach(invoiceItem -> {
                Budget budget = invoiceItem.getBudgetNode();
                if (null != budget) {

                    BudgetFrequencyMapping budgetFrequencyMapping = budgetFrequencyMappingService.getCurrentFrequency(budget);

                    DocumentData documentData = new DocumentData();
                    documentData.setDocumentType(documentType);
                    documentData.setInvoiceType(invoiceType.getValue());
                    documentData.setAction(reportStatus);
                    documentData.setBudget(budget);

                    documentData.setBudgetFrequencyMapping(budgetFrequencyMapping);

                    //if hasPrecedingDocument is true then po budget need to be set
                    documentData.setIsBase(hasPrecedingDocument);
                    if (hasPrecedingDocument) {
                        // Updated to use many-to-many relationship - use first PO item for backward compatibility
                        if (!invoiceItem.getPurchaseOrderItems().isEmpty()) {
                            PurchaseOrderItem firstPoItem = invoiceItem.getPurchaseOrderItems().iterator().next();

                            if (invoiceType.getValue().equals("CNR") || invoiceType.getValue().equals("CNUR")) {
                                //TODO : VISHAL/HRUSHI if CN and has preceding document. Then, find CN->Invoice_>>PO->Budget
                                BudgetFrequencyMapping baseBudgetFrequencyMapping = budgetFrequencyMappingService.getCurrentFrequency(invoiceItem.getBudgetNode());
                                documentData.setBaseBudget(firstPoItem.getBudgetNode());
                                documentData.setBaseBudgetFrequencyMapping(baseBudgetFrequencyMapping);
                            } else {
                                BudgetFrequencyMapping baseBudgetFrequencyMapping = budgetFrequencyMappingService.getCurrentFrequency(firstPoItem.getBudgetNode());
                                documentData.setBaseBudget(firstPoItem.getBudgetNode());
                                documentData.setBaseBudgetFrequencyMapping(baseBudgetFrequencyMapping);
                            }
                        }
                    }

                    //TODO : Check which amount to be consumed from budget.
                    documentData.setAmount(invoiceItem.getAssessableAmount());

                    documentDataList.add(documentData);
                }

            });
        }


        return documentDataList;
    }

    private DocumentApprovalContainerSendBackResultViewModel handleAndFinalizeSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth) {
        DocumentApprovalContainer report =
                reportRepository
                        .findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getExpenseReportId())
                        .orElseThrow(() -> new MasterRecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", Document.class, viewModel.getExpenseReportId(), auth.getCompanyCode())));

        List<ReportState> stateList = stateRepository.findByDocumentApprovalContainerIdSorted(auth.getCompanyCode(), viewModel.getExpenseReportId(), List.of(ReportStatus.SUBMITTED));

        return handleSendBack(viewModel, auth, report, stateList);
    }

    private DocumentApprovalContainerSendBackResultViewModel handleSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth, DocumentApprovalContainer report, List<ReportState> stateList) {
        ReportState first = processSendBack(viewModel, auth, report, stateList);
        return finalizeSendBack(viewModel, report, first, auth);
    }

    private ReportState processSendBack(ReportStateCreateViewModel viewModel, IAuthContextViewModel auth, DocumentApprovalContainer report, List<ReportState> stateList) {
        ReportState first =
                stateList
                        .stream()
                        .filter(s -> s.getStatus() == ExpenseActionStatus.UNACTIONED)
                        .min(Comparator.comparing(ReportState::getLevel))
                        .orElseThrow(() -> new DomainInvariantException("No approvable expenses found for expense: " + viewModel.getExpenseReportId()));

        if (first.getLevel() != viewModel.getLevel() || !first.getApprover().equalsIgnoreCase(auth.getUserEmail())) {
            throw new DomainInvariantException(String.format("User %s could not be found at level %d for expense: %d", auth.getUserEmail(), viewModel.getLevel(), viewModel.getExpenseReportId()));
        }

        first.setStatus(ExpenseActionStatus.SENT_BACK);
        first.setRemarks(viewModel.getRemarks());
        first.setActionDate(LocalDate.now());
        first.setUpdatedTimestamp(ZonedDateTime.now());

        report.setReportStatus(ReportStatus.SENT_BACK);
        report.setActionLevel(StaticDataRegistry.FIRST_LEVEL);
        return first;
    }

    private DocumentApprovalContainerSendBackResultViewModel finalizeSendBack(ReportStateCreateViewModel viewModel, DocumentApprovalContainer report, ReportState first, IAuthContextViewModel auth) {
        report.setContainsSentBack(true);

        report.setActionStatus(ExpenseActionStatus.SENT_BACK);
        report.setCurrentApproverFirstName(null);
        report.setCurrentApproverLastName(null);
        report.setCurrentApproverEmployeeCode(null);
        report.setSendBackRemarks(viewModel.getRemarks());

        consumptionService.handleConsumption(report);

        DocumentType documentType = DocumentType.values()[report.getDocumentMetadata().getDocumentCategoryId() - 1];

        if (documentType == DocumentType.BUDGET) {

            Optional<BudgetStructureMaster> budgetStructureMasterOptional = budgetStructureMasterRepository.findById(report.getDocuments().get(0).getBudgetDocumentAction().getBudgetStructureMasterId());

            if (budgetStructureMasterOptional.isPresent()) {
                BudgetStructureMaster budgetStructureMaster = budgetStructureMasterOptional.get();

                budgetStructureMaster.setIsApproved(false);
                budgetStructureMaster.setStatus(ReportStatus.SENT_BACK);
                budgetStructureMasterRepository.saveAndFlush(budgetStructureMaster);
            }
        }

        //Create tat entry for invoice approve
        if(report.getDocumentType().equals(DocumentType.INVOICE)) {
            if (!iInvoiceTatReportService.create(ApproverType.APPROVER, report.getId(), auth)) {
                logger.info("Failed to create Invoice TAT Report for approver");
            }
        }

        stateRepository.saveAndFlush(first);
        reportRepository.saveAndFlush(report);


        emailNotificationService.transmitSendBackNotification(report);

        DocumentApprovalContainerSendBackResultViewModel resultViewModel = new DocumentApprovalContainerSendBackResultViewModel();
        resultViewModel.setDocumentNumber(report.getDocumentIdentifier());
        resultViewModel.setCreatorFirstName(report.getFirstName());
        resultViewModel.setCreatorLastName(report.getLastName());

        return resultViewModel;
    }
}
