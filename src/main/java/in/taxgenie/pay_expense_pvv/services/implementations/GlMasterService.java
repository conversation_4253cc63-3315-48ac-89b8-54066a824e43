package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.context.ContextAwareRunnable;
import in.taxgenie.pay_expense_pvv.entities.gl_master.ExcelHistory;
import in.taxgenie.pay_expense_pvv.entities.gl_master.GlMaster;
import in.taxgenie.pay_expense_pvv.multitenancy.context.TenantContext;
import in.taxgenie.pay_expense_pvv.repositories.IExcelHistoryRepository;
import in.taxgenie.pay_expense_pvv.repositories.IGlMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.gl_master.CustomGLMasterRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IGlMasterService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericPageableViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.gl_master.*;
import in.taxgenie.pay_expense_pvv.viewmodels.item_master.ItemMasterQueueViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

@Service
public class GlMasterService implements IGlMasterService {

    private final IGlMasterRepository glMasterRepository;
    private final CustomGLMasterRepository customGLMasterRepository;
    private final IExcelHistoryRepository excelHistoryRepository;
    private final Logger logger;
    private final Executor tenantAwareExecutor;

    private final ObjectMapper objectMapper;

    public GlMasterService(IGlMasterRepository glMasterRepository, CustomGLMasterRepository customGLMasterRepository, IExcelHistoryRepository excelHistoryRepository, Executor tenantAwareExecutor, ObjectMapper objectMapper) {
        this.glMasterRepository = glMasterRepository;
        this.customGLMasterRepository = customGLMasterRepository;
        this.excelHistoryRepository = excelHistoryRepository;
        this.tenantAwareExecutor = tenantAwareExecutor;
        this.objectMapper = objectMapper;
        this.logger = LoggerFactory.getLogger(GlMasterService.class);
    }

    @Override
    public GenericPageableViewModel<GLMasterViewModel> getQueue(QueueFilterViewModel filterValues, IAuthContextViewModel auth) {
        logger.info(String.format("BudgetQueueServiceImplementation-getQueue: Called for company id: %s", auth.getCompanyCode()));
        GenericPageableViewModel<GLMasterViewModel> pagedQueue = new GenericPageableViewModel<>();

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize());

        Page<GLMasterViewModel> viewModelPaged = customGLMasterRepository.getGLMasterQueue(auth.getCompanyCode(), filterValues, pageable);

        // Set response
        pagedQueue.setData(viewModelPaged.getContent());
        pagedQueue.setPages(viewModelPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(viewModelPaged.getTotalElements()));

        return pagedQueue;

    }

    @Override
    @Transactional
    public StoreGlMasterResponseViewModel storeGlMasterData(List<GlMaster> glMasterList, IAuthContextViewModel auth) {

        StoreGlMasterResponseViewModel storeGlMasterResponseViewModel = new StoreGlMasterResponseViewModel();
        try {
            // Generate a unique reference ID
            Long referenceId = UUID.randomUUID().getMostSignificantBits() & Long.MAX_VALUE;

            // Convert GL Master list to JSON
            String sourceJson = objectMapper.writeValueAsString(glMasterList);

            // Create ExcelHistory entry
            ExcelHistory excelHistory = new ExcelHistory();
            excelHistory.setReferenceId(referenceId);
            excelHistory.setSourceJson(sourceJson);
            excelHistory.setStatus("PENDING");
            excelHistory.setType("GL_MASTER");

            if(auth.isCompanyToken()){
                excelHistory.setCreatingUserId(0l);
            } else{
                excelHistory.setCreatingUserId(auth.getUserId());
            }
            excelHistory.setCreatedTimestamp(DateTimeUtils.getCurrentZonedDateTime());


            excelHistoryRepository.save(excelHistory);

            CompletableFuture.runAsync(
                    new ContextAwareRunnable(() -> {
                        // Your async task logic
                        System.out.println("Tenant ID: " + TenantContext.getCurrentTenant());
                        // Perform data create/update process in the background
                        processGlMasterDataAsync(glMasterList, referenceId, auth);
                    }),
                    tenantAwareExecutor // Use the custom executor
            );

//            CompletableFuture.runAsync(() -> {
//                processGlMasterDataAsync(glMasterList, referenceId, auth);
//            });

            storeGlMasterResponseViewModel.setReferenceId(referenceId);
            return storeGlMasterResponseViewModel;
        } catch (Exception exception) {
            //Log exception
            return null;
        }

    }

    public void processGlMasterDataAsync(List<GlMaster> glMasterList, Long referenceId, IAuthContextViewModel auth) {

        List<GLMasterStatusViewModel> glMasterStatusViewModelList = new ArrayList<>();

        try{

            // Process each GL Master entry asynchronously
            for (GlMaster glMaster : glMasterList) {
                glMasterStatusViewModelList.add(syncGlMaster(glMaster, auth));
            }

            // Update status to "COMPLETED" after processing
            Optional<ExcelHistory> excelHistoryOptional = excelHistoryRepository.findByReferenceId(referenceId);
            if (excelHistoryOptional.isPresent()) {
                ExcelHistory excelHistory = excelHistoryOptional.get();
                excelHistory.setStatus("COMPLETED");
                String sourceJson = objectMapper.writeValueAsString(glMasterStatusViewModelList);
                excelHistory.setReport(sourceJson);
                if(auth.isCompanyToken()){
                    excelHistory.setUpdatingUserId(0l);
                } else{
                    excelHistory.setUpdatingUserId(auth.getUserId());
                }
                excelHistory.setUpdatedTimestamp(DateTimeUtils.getCurrentZonedDateTime());
                excelHistory.setUploadEnd(DateTimeUtils.getCurrentZonedDateTime());
                excelHistoryRepository.save(excelHistory);
            }

        }catch (Exception exception){
            logger.error("Exception occurred in processGlMasterDataAsync method while storing GL Master. Message : {}", exception.getMessage());
        }
    }


    @Override
    @Transactional
    public GLMasterStatusViewModel syncGlMaster(GlMaster glMaster, IAuthContextViewModel auth) {

        GlMaster glMasterDb;
        GLMasterStatusViewModel glMasterStatusViewModel = new GLMasterStatusViewModel();

        try{
            Optional<GlMaster> existingEntry = glMasterRepository.findByGlAndCompanyCode(glMaster.getGl(), auth.getCompanyCode());

            if (existingEntry.isPresent()) {
                GlMaster existingGlMaster = existingEntry.get();
                existingGlMaster.setParent(glMaster.getParent());
                existingGlMaster.setPlGroup(glMaster.getPlGroup());
                existingGlMaster.setGlName(glMaster.getGlName());
                existingGlMaster.setUnary(glMaster.getUnary());
                existingGlMaster.setHsnCode(glMaster.getHsnCode());
                existingGlMaster.setValidFrom(glMaster.getValidFrom());
                existingGlMaster.setValidTo(glMaster.getValidTo());
                existingGlMaster.setStatus(glMaster.getStatus());

                glMasterDb = glMasterRepository.save(existingGlMaster);
                glMasterStatusViewModel.setMessage("Saved successfully");
            } else {
                glMaster.setCompanyCode(auth.getCompanyCode());
                glMasterDb = glMasterRepository.save(glMaster);
                glMasterStatusViewModel.setMessage("Updated successfully ");
            }
            glMasterStatusViewModel.setSuccess(true);

        }catch (Exception exception){
            glMasterStatusViewModel.setSuccess(false);
            glMasterStatusViewModel.setMessage("Exception occurred. Message : " + exception.getMessage());
        }

        return glMasterStatusViewModel;
    }

    @Override
    public List<GlMasterSummary> searchGlMaster(GLMasterRequestViewModel glMasterRequestViewModel, IAuthContextViewModel auth) {

        try {
            if(glMasterRequestViewModel.getSearchKey().isEmpty()) {

                return glMasterRepository.findTop30ByCompanyCodeOrderByCreatedTimestampDesc(auth.getCompanyCode());
            } else {
                return glMasterRepository.findByGlIgnoreCaseContainingOrGlNameIgnoreCaseContainingAndCompanyCodeOrderByGlNameAsc(
                        glMasterRequestViewModel.getSearchKey(), glMasterRequestViewModel.getSearchKey(),
                        auth.getCompanyCode());
            }

        }catch (Exception exception){
            return List.of();
        }
    }

    @Override
    public List<GlMaster> fullSearchGlMaster(String searchKey, IAuthContextViewModel auth) {
        try {
            if(null != searchKey && !searchKey.isEmpty()) {
                return glMasterRepository.findByGlNameStartingWithIgnoreCaseAndCompanyCodeOrderByGlNameAsc(searchKey, auth.getCompanyCode());
            } else{
                return List.of();
            }

        }catch (Exception exception){
            return List.of();
        }
    }
}