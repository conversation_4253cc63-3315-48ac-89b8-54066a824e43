package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.cem.expense.groupinfo.implementations.CemExpenseGroupInfoListResponse;
import in.taxgenie.pay_expense_pvv.cem.expense.groupinfo.implementations.CemExpenseGroupInfoSingularResponse;
import in.taxgenie.pay_expense_pvv.cem.expense.groupinfo.interfaces.IExpenseGroupInfoViewModel;
import in.taxgenie.pay_expense_pvv.services.interfaces.IExpenseGroupInfoMasterDataService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IRestConsumer;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

@Service
public class ExpenseGroupInfoMasterDataServiceImplementation
        implements
        IExpenseGroupInfoMasterDataService {
    @Value("${CEM_URL}")
    private String cemUrl;
    
    private final IRestConsumer<CemExpenseGroupInfoSingularResponse> singularRestConsumer;
    private final IRestConsumer<CemExpenseGroupInfoListResponse> listRestConsumer;

    public ExpenseGroupInfoMasterDataServiceImplementation(
            IRestConsumer<
                    CemExpenseGroupInfoSingularResponse
                    > singularRestConsumer,
            IRestConsumer<
                    CemExpenseGroupInfoListResponse
                    > listRestConsumer
    ) {
        this.singularRestConsumer = singularRestConsumer;
        this.listRestConsumer = listRestConsumer;
    }

    @Override
    public IExpenseGroupInfoViewModel getExpenseGroupInfo(long companyCode, long groupId) {
        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                StaticDataRegistry.CEM_SERVICE_EXPENSE_GROUP_INFO_FRAGMENT,
                companyCode,
                groupId
        );

        return singularRestConsumer.read(
                        HttpMethod.GET,
                        null,
                        url,
                        CemExpenseGroupInfoSingularResponse.class
                )
                .getBody();
    }

    @Override
    public IExpenseGroupInfoViewModel[] getAllExpenseGroupInfo(long companyCode) {
        String url = StaticDataRegistry.getUrl(
                cemUrl,
                "/",
                StaticDataRegistry.CEM_SERVICE_EXPENSE_GROUP_INFO_FRAGMENT,
                companyCode
        );

        return listRestConsumer.read(
                        HttpMethod.GET,
                        null,
                        url,
                        CemExpenseGroupInfoListResponse.class
                )
                .getBody();
    }
}
