package in.taxgenie.pay_expense_pvv.services.implementations;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.taxgenie.utils.api.MultiServiceClient;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Company;
import in.taxgenie.pay_expense_pvv.entities.ExternalServicesIdentifier;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.govrndto.QRResponseDTO;
import in.taxgenie.pay_expense_pvv.invoice.request.InvoiceDetailsReq;
import in.taxgenie.pay_expense_pvv.repositories.ICompanyRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.ICompanyService;
import in.taxgenie.pay_expense_pvv.utils.MultiServiceClientFactory;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.utils.UrlConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.CompanyRequestBySupplierNameViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel;
import in.taxgenie.pay_expense_pvv.viewmodels.CompanyViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.company.CamCompanyLogoResponse;
import in.taxgenie.pay_expense_pvv.viewmodels.company.CompanyGstinMasterGetDataResponse;
import in.taxgenie.pay_expense_pvv.viewmodels.company.SellerTemplateResponseViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.company.SellerTemplateViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.io.IOException;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Service
public class CompanyServiceServiceImplementation implements ICompanyService {
    private final ICompanyRepository companyRepository;
    private final MultiServiceClientFactory multiServiceClientFactory;
    private final MultiServiceClient webClientHelper;
    private final ObjectMapper objectMapper;
    private final Logger logger;

    public CompanyServiceServiceImplementation(MultiServiceClientFactory multiServiceClientFactory, ICompanyRepository companyRepository, ObjectMapper objectMapper) {
        this.companyRepository = companyRepository;
        this.multiServiceClientFactory = multiServiceClientFactory;
        this.webClientHelper = multiServiceClientFactory.createMultiServiceClient(WebClient.builder());
        this.objectMapper = objectMapper;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public List<LookupDataModel> getAllAvailableGstins(IAuthContextViewModel auth) {
        logger.info("getAllAvailableGstins: Getting available vendors for company {}", auth.getCompanyCode());
        return companyRepository.getVendorGstinDetailsByCamCompanyId(auth.getCompanyCode(), StaticDataRegistry.VENDOR_GSTIN_NOT_APPLICABLE);
    }

    @Override
    public List<SellerTemplateViewModel> getAllAvailableGstinsWithTemplateStatus(IAuthContextViewModel auth) {
        logger.info("getAllAvailableGstinsWithTemplateStatus: Getting available vendors for company with template status {}", auth.getCompanyCode());
        List<SellerTemplateViewModel> result = new ArrayList<>();
        List<SellerTemplateViewModel> availableGstins = companyRepository.getFullVendorGstinDetailsByCamCompanyIdWithNonApplicable(auth.getCompanyCode());
        WebClient webClient = multiServiceClientFactory.getClient(ExternalServicesIdentifier.QR_READER_SERVICE.ordinal());

        try {
            logger.info("getAllAvailableGstinsWithTemplateStatus: Calling Document Service to get gstin's with mapped templates");
            SellerTemplateResponseViewModel gstinWithTemplates = webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path("/ocr/check_existed_template")
                            .queryParam("transactor_type", "buyer")
                            .build())
                    .headers(headers -> headers.setBearerAuth(auth.getToken()))
                    .retrieve()
                    .bodyToMono(SellerTemplateResponseViewModel.class)
                    .block();

            Set<String> gstinWithTemplateMappedSet = new HashSet<>(gstinWithTemplates.getSellerIdentifiers());
            logger.info("getAllAvailableGstinsWithTemplateStatus: Found {} gstin's with templates mapped", gstinWithTemplateMappedSet.size());
            availableGstins.forEach(gstin -> {
                result.add(new SellerTemplateViewModel(gstin.getId(), gstin.getValue(), gstinWithTemplateMappedSet.contains(gstin.getValue()), gstin.getDisplayName()));
            });

            logger.info("getAllAvailableGstinsWithTemplateStatus: Returning");
            return result;
        } catch (WebClientResponseException e) {
            // Handle exception as needed
            e.printStackTrace();
            throw new DomainInvariantException("Unable to get Seller Gstins");
        }

    }

    @Override
    public List<LookupDataModel> getAllSupplierNames(IAuthContextViewModel auth) {
        logger.info("getAllSupplierNames: Getting available vendors for company {}", auth.getCompanyCode());
        return companyRepository.getVendorNameDetailsIncludingNotApplicableGstByCamCompanyId(auth.getCompanyCode());
    }

    @Override
    public List<CompanyViewModel> getBySupplierName(IAuthContextViewModel auth, CompanyRequestBySupplierNameViewModel supplierName) {
        logger.info("getBySupplierName: Getting available vendors for company {}", auth.getCompanyCode());
        return companyRepository.findByCamCompanyIdAndSupplierCompanyNameAndGstinStatusAndGstNot(
                auth.getCompanyCode(), supplierName.getSupplierCompanyName(), StaticDataRegistry.VENDOR_ACTIVE_STATUS, StaticDataRegistry.VENDOR_GSTIN_NOT_APPLICABLE
        ).stream().map(this::getViewModel).toList();
    }

    @Override
    public CompanyViewModel getById(IAuthContextViewModel auth, Integer id) {
        logger.info("getById: Getting the vendor details for id {} belonging to company {}", id, auth.getCompanyCode());
        Company entity = companyRepository.findByCamCompanyIdAndCompanyId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new DomainInvariantException("Vendor details could not be found"));
        return getViewModel(entity);
    }

    @Override
    public List<LookupDataModel> getAllAvailableGstinsBySupplier(Long supplierId, IAuthContextViewModel auth) {
        logger.info("getAllAvailableGstins: Getting available vendors for company {}", auth.getCompanyCode());
        return companyRepository.getVendorGstinDetailsIncludingNotApplicableGstByCamCompanyIdAndCompanyId(supplierId, auth.getCompanyCode());
    }

    @Override
    public List<CompanyViewModel> getAllSupplierDetailsByGstin(Long supplierId, String gstin, IAuthContextViewModel auth) {
        logger.info("getById: Getting the vendor details for supplierId {} and for gstin {} for company code {}", supplierId, gstin, auth.getCompanyCode());
        List<Company> companies = companyRepository.findFirstByCompanyIdAndCamCompanyIdAndGst(supplierId, auth.getCompanyCode(), gstin);

        if (!gstin.equalsIgnoreCase(StaticDataRegistry.VENDOR_GSTIN_NOT_APPLICABLE)) {
            LocalDate currentDate = LocalDate.now();
            syncGstinStatus(companies, currentDate, auth);
        }

        return companies.stream().map(this::getViewModel).toList();
    }

    private void syncGstinStatus(List<Company> companies, LocalDate currentDate, IAuthContextViewModel auth) {
        for (Company company : companies) {
            LocalDate lastSync = company.getGstinStatusLastSync();
            boolean isSyncNeeded = lastSync == null || ChronoUnit.DAYS.between(lastSync, currentDate) > 3;

            if (isSyncNeeded) {
                try {
                    CompanyGstinMasterGetDataResponse gstinDetails = getGstinDetails(company.getGst(), auth);
                    if (gstinDetails != null && gstinDetails.getGstinStatus() != null) {
                        company.setGstinStatus(gstinDetails.getGstinStatus());
                        company.setGstinStatusLastSync(currentDate);
                        companyRepository.save(company);
                    }
                } catch (Exception e) {
                    logger.warn("Failed to fetch GSTIN details for GSTIN {}: {}", company.getGst(), e.getMessage());
                }
            }
        }
    }

    @Override
    public byte[] getCamCompanyLogo(IAuthContextViewModel auth) {
        logger.info("Fetching company logo for companyId:" + auth.getCompanyCode());
        CamCompanyLogoResponse camCompanyLogoResponse = webClientHelper.makeRequest(ExternalServicesIdentifier.CAM_SERVICE.ordinal(),
                HttpMethod.GET,
                "/co/getlogo",
                Optional.empty(),
                Map.of("companyId", String.valueOf(auth.getCompanyCode())),
                null,
                CamCompanyLogoResponse.class,
                auth.getToken()).block();
        return camCompanyLogoResponse.getCompanyLogo();
    }

    private CompanyViewModel getViewModel(Company entity) {
        CompanyViewModel viewModel = new CompanyViewModel();
        viewModel.setId(entity.getCompanyId());
        viewModel.setAddress1(entity.getAddr1());
        viewModel.setAddress2(entity.getAddr2());
        viewModel.setLocation(entity.getLoc());
        viewModel.setPin(entity.getPin());
        if (entity.getStateCode() != null) {
            viewModel.setStateCodeId(entity.getStateCode().getId());
        }
        viewModel.setLegalName(entity.getLegalName());
        viewModel.setPhoneNumber(entity.getPhoneNo());
        viewModel.setEmail(entity.getEmailId());
        viewModel.setVendorCode(entity.getVendorCode());
        viewModel.setGstin(entity.getGst());
        viewModel.setGstinStatus(entity.getGstinStatus());
        viewModel.setSupplierCompanyName(entity.getSupplierCompanyName());
        return viewModel;
    }

    private CompanyGstinMasterGetDataResponse getGstinDetails(String gstin, IAuthContextViewModel auth) throws IOException {

        logger.info("request param {} ", gstin);
        try {
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("gstin", gstin);
            String qrResponseJson = webClientHelper.makeRequest(
                    ExternalServicesIdentifier.MDM_SERVICE.ordinal(),
                    HttpMethod.GET,
                    UrlConstants.MDM_GSTIN_URL,
                    Optional.empty(),
                    queryParams,
                    null,
                    String.class,
                    auth.getToken()
            ).block();

            return objectMapper.readValue(qrResponseJson, CompanyGstinMasterGetDataResponse.class);
        } catch (Exception e) {
            logger.warn("QR Code service call failed: {}", e.getMessage());
            return new CompanyGstinMasterGetDataResponse(); // Return empty response
        }
    }
}
