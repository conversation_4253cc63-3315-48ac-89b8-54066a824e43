package in.taxgenie.pay_expense_pvv.services.implementations;

import com.google.api.core.ApiFuture;
import com.google.cloud.pubsub.v1.Publisher;
import com.google.protobuf.ByteString;
import com.google.pubsub.v1.PubsubMessage;
import com.google.pubsub.v1.TopicName;
import in.taxgenie.pay_expense_pvv.services.interfaces.IPubSubPublisherService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Service
public class PubSubPublisherService implements IPubSubPublisherService {

    Logger log = LoggerFactory.getLogger(PubSubPublisherService.class);

    @Value("${spring.cloud.gcp.project-id}")
    private String projectId;

    @Value("${gcp.topic.name}")
    private String topicName;


    @Override
    public String publishMessage(String uuid, String action, PubsubMessage message) {
        String messageId = null;
        log.info("For UUID: {} Inside publishMessage method of PubSubPublisherService with data as Action : " + action + " Message Input : " + message + "UUID" + uuid);

        try {


            // Create a publisher instance
            Publisher publisher = Publisher.newBuilder(TopicName.of(projectId, topicName)).build();
            log.info("For UUID: {} publishing message", uuid, publisher);

            // Publish the message
            ApiFuture<String> future = publisher.publish(message);
            log.info("For UUID: {} future is done?", uuid, future.isDone());

            // Block until the message is published
            messageId = future.get();
            log.info("For UUID: {} messageId", uuid, messageId);

            // Shutdown the publisher to release resources
            publisher.shutdown();
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("Error publishing message: " + e.getMessage());
            e.printStackTrace();
            messageId = null;
        } catch (Exception exception) {
            exception.printStackTrace();
            messageId = null;
        }
        return messageId;
    }

    @Override
    public PubsubMessage createMessage(String dataType, String requestId, String sourceId, String message) {

        ByteString data = ByteString.copyFromUtf8(message);
        PubsubMessage pubsubMessage = PubsubMessage.newBuilder()
                .setData(data)
                .build();
        return pubsubMessage;
    }
}