package in.taxgenie.pay_expense_pvv.services.implementations;

import com.amazonaws.services.s3.model.ObjectMetadata;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cloud.AwsS3FileIOProvider;
import in.taxgenie.pay_expense_pvv.cloud.AwsS3Provider;
import in.taxgenie.pay_expense_pvv.entities.Document;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IFileIOService;
import in.taxgenie.pay_expense_pvv.utils.FileUtils;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URL;
import java.util.UUID;

@Service
@Profile({"tg-internal", "ses"})
public class AwsS3FileIOServiceImplementation implements IFileIOService {
    @Value("${cloud.uploadEnv}")
    private String uploadEnv;

    private final AwsS3FileIOProvider s3FileIOProvider;
    private final AwsS3Provider s3Provider;
    private final IDocumentRepository expenseRepository;
    private final Logger logger;

    public AwsS3FileIOServiceImplementation(AwsS3FileIOProvider s3FileIOProvider, AwsS3Provider s3Provider, IDocumentRepository expenseRepository) {
        this.s3FileIOProvider = s3FileIOProvider;
        this.s3Provider = s3Provider;
        this.expenseRepository = expenseRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public void upload(MultipartFile file, String identifier, int index, Document document) {
        logger.trace("upload: Checking if the file is empty");
        if (file.isEmpty()) {
            logger.info("upload: File is empty");
            throw new DomainInvariantException("File is empty");
        }

        try {
            logger.trace("upload: Checking if the expense already has the document uploaded");

            if (index == 1) {
                handleDocument1Updload(file, identifier, document);
            }

            if (index == 2) {
                handleDocument2Upload(file, identifier, document);
            }

            if (index == 3) {
                handleDocument3Upload(file, identifier, document);
            }


        } catch (IOException ioException) {
            logger.error("upload: Exception caught: cannot write: {}", ioException.getMessage());
            throw new RuntimeException("Failed to upload the file");
        }
    }

    @Override
    public ResponseEntity<Resource> getAttachment1(String identifier, long expenseId, IAuthContextViewModel auth) {
        logger.trace("getFile: Checking if the expense exists");
        Document document = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

        logger.trace("getFile: Checking if the document upload url exists");
        if (document.getDocument1UploadUrl() == null) {
            logger.info("getFile: Document upload path is missing (no upload)");
            throw new DomainInvariantException("Expense doesn't have a document uploaded");
        }

        logger.trace("getFile: Reading the file");
        InputStreamResource resource = new InputStreamResource(s3FileIOProvider.download(document.getDocument1UploadUrl()));

        logger.trace("getFile: Preparing the response");
        return ResponseEntity.ok()
                .contentType(MediaType.valueOf(document.getDocument1UploadContentType()))
                .body(resource);
    }

    @Override
    public ResponseEntity<Resource> getAttachment2(String identifier, long expenseId, IAuthContextViewModel auth) {
        logger.trace("getFile: Checking if the expense exists");
        Document document = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

        logger.trace("getFile: Checking if the document upload url exists");
        if (document.getDocument2UploadUrl() == null) {
            logger.info("getFile: Document upload path is missing (no upload)");
            throw new DomainInvariantException("Expense doesn't have a document uploaded");
        }

        logger.trace("getFile: Reading the file");
        InputStreamResource resource = new InputStreamResource(s3FileIOProvider.download(document.getDocument2UploadUrl()));

        logger.trace("getFile: Preparing the response");
        return ResponseEntity.ok()
                .contentType(MediaType.valueOf(document.getDocument2UploadContentType()))
                .body(resource);
    }

    @Override
    public ResponseEntity<Resource> getAttachment3(String identifier, long expenseId, IAuthContextViewModel auth) {
        logger.trace("getFile: Checking if the expense exists");
        Document document = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find the Expense with id: %d and Company Code: %d", expenseId, auth.getCompanyCode())));

        logger.trace("getFile: Checking if the document upload url exists");
        if (document.getDocument3UploadUrl() == null) {
            logger.info("getFile: Document upload path is missing (no upload)");
            throw new DomainInvariantException("Expense doesn't have a document uploaded");
        }

        logger.trace("getFile: Reading the file");
        InputStreamResource resource = new InputStreamResource(s3FileIOProvider.download(document.getDocument3UploadUrl()));

        logger.trace("getFile: Preparing the response");
        return ResponseEntity.ok()
                .contentType(MediaType.valueOf(document.getDocument3UploadContentType()))
                .body(resource);
    }

    @Override
    public void delete(Document document) {
        if (document.getDocument1UploadUrl() != null) {
            s3FileIOProvider.delete(document.getDocument1UploadUrl());
        }

        if (document.getDocument2UploadUrl() != null) {
            s3FileIOProvider.delete(document.getDocument2UploadUrl());
        }

        if (document.getDocument3UploadUrl() != null) {
            s3FileIOProvider.delete(document.getDocument3UploadUrl());
        }
    }

    @Override
    public void delete(int index, Document document) {
        if (index == 1 && document.getDocument1UploadUrl() != null) {
            s3FileIOProvider.delete(document.getDocument1UploadUrl());
        }

        if (index == 2 && document.getDocument2UploadUrl() != null) {
            s3FileIOProvider.delete(document.getDocument2UploadUrl());
        }

        if (index == 3 && document.getDocument3UploadUrl() != null) {
            s3FileIOProvider.delete(document.getDocument3UploadUrl());
        }
    }

    @Override
    public String handleInvoiceUpload(MultipartFile file, long companyCode) throws IOException {
        logger.trace("Dummy Implementation of invoice upload");
        return null;
    }

    @Override
    public String handleMoveInvoice(String tempFilePath, String name, long companyCode) throws IOException {
        logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
        try {
            // Generate the target file path
            String fileName = FileUtils.processFilePath(name, uploadEnv, companyCode, StaticDataRegistry.CloudStorageConstans.CATEGORY_INVOICE);

            logger.trace("upload: Moving {} to AWS S3", fileName);

            s3FileIOProvider.moveFile(tempFilePath, fileName);

            logger.trace("upload: Move to AWS S3 succeeded");

            return fileName;
        } catch (Exception e) {
            logger.error("Error occurred while moving invoice file to S3: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to move file to AWS S3", e);
        }
    }

    @Override
    public String handleDocumentUpload(MultipartFile doc, long companyCode, String category, String fileHash, Integer categoryId, Long entityId) throws IOException {
        logger.trace("Dummy Implementation of document upload");
        return null;
    }

    @Override
    public URL getSignedUrl(String filename) {
        return s3FileIOProvider.getSignedUrl(filename);
    }

    private void handleDocument1Updload(MultipartFile file, String identifier, Document document) throws IOException {
        if (document.getDocument1UploadUrl() != null) {
            s3FileIOProvider.delete(document.getDocument1UploadUrl());
        }

        logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
        if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
            throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
        }

        UUID uuid = UUID.randomUUID();
        String fileName = String.format("%s/%d/%d/%d/%s", s3Provider.getRootDirectory(), document.getCompanyCode(), document.getDocumentApprovalContainerId(), document.getId(), uuid);

        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(file.getContentType());
        objectMetadata.setContentLength(file.getSize());

        logger.trace("upload: Uploading {} to AWS S3", fileName);
        s3FileIOProvider.upload(fileName, new ByteArrayInputStream(file.getBytes()), objectMetadata);
        logger.trace("upload: Upload to AWS S3 succeeded");

        logger.trace("upload: Setting relevant fields on the expense");
        document.setDocument1UploadMarker(identifier);
        document.setDocument1UploadUuid(uuid.toString());
        document.setDocument1UploadContentType(file.getContentType());
        document.setDocument1UploadUrl(fileName);

        logger.trace("upload: Saving the expense");
        expenseRepository.saveAndFlush(document);

        logger.trace("upload: Save successful");
    }

    private void handleDocument2Upload(MultipartFile file, String identifier, Document document) throws IOException {
        if (document.getDocument2UploadUrl() != null) {
            s3FileIOProvider.delete(document.getDocument2UploadUrl());
        }

        logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
        if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
            throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
        }

        UUID uuid = UUID.randomUUID();
        String fileName = String.format("%s/%d/%d/%d/%s", s3Provider.getRootDirectory(), document.getCompanyCode(), document.getDocumentApprovalContainerId(), document.getId(), uuid);

        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(file.getContentType());
        objectMetadata.setContentLength(file.getSize());

        logger.trace("upload: Uploading {} to AWS S3", fileName);
        s3FileIOProvider.upload(fileName, new ByteArrayInputStream(file.getBytes()), objectMetadata);
        logger.trace("upload: Upload to AWS S3 succeeded");

        logger.trace("upload: Setting relevant fields on the expense");
        document.setDocument2UploadMarker(identifier);
        document.setDocument2UploadUuid(uuid.toString());
        document.setDocument2UploadContentType(file.getContentType());
        document.setDocument2UploadUrl(fileName);

        logger.trace("upload: Saving the expense");
        expenseRepository.saveAndFlush(document);

        logger.trace("upload: Save successful");
    }

    private void handleDocument3Upload(MultipartFile file, String identifier, Document document) throws IOException {
        if (document.getDocument3UploadUrl() != null) {
            s3FileIOProvider.delete(document.getDocument3UploadUrl());
        }

        logger.trace("upload: Checking if the uploaded file has the allowed content type (media type)");
        if (!StaticDataRegistry.uploadFileContentTypeSet.contains(file.getContentType())) {
            throw new DomainInvariantException("Unsupported media type for the uploaded file: " + file.getContentType());
        }

        UUID uuid = UUID.randomUUID();
        String fileName = String.format("%s/%d/%d/%d/%s", s3Provider.getRootDirectory(), document.getCompanyCode(), document.getDocumentApprovalContainerId(), document.getId(), uuid);

        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(file.getContentType());
        objectMetadata.setContentLength(file.getSize());

        logger.trace("upload: Uploading {} to AWS S3", fileName);
        s3FileIOProvider.upload(fileName, new ByteArrayInputStream(file.getBytes()), objectMetadata);
        logger.trace("upload: Upload to AWS S3 succeeded");

        logger.trace("upload: Setting relevant fields on the expense");
        document.setDocument3UploadMarker(identifier);
        document.setDocument3UploadUuid(uuid.toString());
        document.setDocument3UploadContentType(file.getContentType());
        document.setDocument3UploadUrl(fileName);

        logger.trace("upload: Saving the expense");
        expenseRepository.saveAndFlush(document);

        logger.trace("upload: Save successful");
    }

}
