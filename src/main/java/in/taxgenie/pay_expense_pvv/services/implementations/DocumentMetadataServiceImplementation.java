package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ActionType;
import in.taxgenie.pay_expense_pvv.entities.DocumentMetadata;
import in.taxgenie.pay_expense_pvv.entities.LookupData;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentMetadataRepository;
import in.taxgenie.pay_expense_pvv.repositories.ILookupRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentMetadataService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DocumentMetadataServiceImplementation implements IDocumentMetadataService {
    private final IDocumentMetadataRepository repository;
    private final ILookupRepository lookupRepository;
    private final Logger logger;

    public DocumentMetadataServiceImplementation(IDocumentMetadataRepository repository, ILookupRepository lookupRepository) {
        this.repository = repository;
        this.lookupRepository = lookupRepository;
        this.logger = LoggerFactory.getLogger(DocumentMetadataServiceImplementation.class);
    }

    public DocumentMetadataViewModel create(DocumentMetadataCreateViewModel viewModel, IAuthContextViewModel auth) {
        return createSingleMetadata(viewModel, auth); // Main entry point for creation
    }

    @Override
    public DocumentMetadataViewModel createSingleMetadata(DocumentMetadataCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("create: Ensuring uniqueness by type, group, and subgroup");
        if (repository.getCountOfTypeAndGroup(auth.getCompanyCode(), viewModel.getDocumentType(), viewModel.getDocumentGroup()) > 0) {
            throw new DomainInvariantException(String.format("Expense Metadata for Company Code: %d with expense type: %s, and group: %s already exists", auth.getCompanyCode(), viewModel.getDocumentType(), viewModel.getDocumentGroup()));
        }

        logger.trace("create: Ensuring uniqueness by prefixes for type, group, and subgroup");
        if (repository.getCountByPrefixes(auth.getCompanyCode(), viewModel.getDocumentTypePrefix(), viewModel.getDocumentGroupPrefix()) > 0) {
            throw new DomainInvariantException(String.format("Expense Metadata for Company Code: %d with prefixes for expense type: %s, and group: %s already exists", auth.getCompanyCode(), viewModel.getDocumentTypePrefix(), viewModel.getDocumentGroupPrefix()));
        }

        logger.trace("create: Creating new entity");
        DocumentMetadata entity = new DocumentMetadata();
        entity.setApplicableExpenseType(null == viewModel.getApplicableExpenseType() ? null : ExpenseType.valueOf(viewModel.getApplicableExpenseType()));
        BeanUtils.copyProperties(viewModel, entity, "applicableExpenseType");

        logger.trace("create: Setting audit fields");
        entity.setCreatingUserId(auth.getUserId());
        entity.setCreatedTimestamp(ZonedDateTime.now());
        entity.setCompanyCode(auth.getCompanyCode());

        logger.trace("create: Set Document category");
        LookupData documentType = lookupRepository.findById(viewModel.getDocumentCategoryId())
                .orElseThrow(() -> {
                    logger.error(String.format("create: Lookup table error. Could not find document type with id : %d ", viewModel.getDocumentCategoryId()));
                    return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
                });
        entity.setDocumentCategory(documentType.getValue());
        logger.trace("create: Saving entity");
        DocumentMetadata savedEntity = repository.saveAndFlush(entity);
        logger.trace("create: Save successful");

        return getViewModel(savedEntity);
    }

    @Override
    public DocumentMetadataViewModel update(long id, DocumentMetadataUpdateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("update: Matching the passed id with that of the view model");
        if (id != viewModel.getId()) {
            throw new DomainInvariantException("Passed id doesn't match with that of the view model");
        }

        logger.trace("update: Find the source record");
        DocumentMetadata entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getId())
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", DocumentMetadata.class, viewModel.getId(), auth.getCompanyCode())));


        logger.trace("update: Ensuring uniqueness by type, group, and subgroup");
        if (!entity.getDocumentType().equals(viewModel.getDocumentType()) &&
                !entity.getDocumentGroup().equals(viewModel.getDocumentGroup()) &&
                repository.getCountOfTypeAndGroup(auth.getCompanyCode(), viewModel.getDocumentType(), viewModel.getDocumentGroup()) > 0) {
            throw new DomainInvariantException(String.format("Expense Metadata for Company Code: %d with expense type: %s, and group: %s already exists", auth.getCompanyCode(), viewModel.getDocumentType(), viewModel.getDocumentGroup()));
        }

        logger.trace("update: Ensuring uniqueness by prefixes for type, group, and subgroup");
        if (!entity.getDocumentTypePrefix().equals(viewModel.getDocumentTypePrefix()) &&
                !entity.getDocumentGroupPrefix().equals(viewModel.getDocumentGroupPrefix()) &&
                repository.getCountByPrefixes(auth.getCompanyCode(), viewModel.getDocumentTypePrefix(), viewModel.getDocumentGroupPrefix()) > 0) {
            throw new DomainInvariantException(String.format("Expense Metadata for Company Code: %d with prefixes for expense type: %s, and group: %s already exists", auth.getCompanyCode(), viewModel.getDocumentTypePrefix(), viewModel.getDocumentGroupPrefix()));
        }

        logger.trace("update: Source record found, applying the updates");
        entity.setApplicableExpenseType(null == viewModel.getApplicableExpenseType() ? null : ExpenseType.valueOf(viewModel.getApplicableExpenseType()));
        BeanUtils.copyProperties(viewModel, entity, "applicableExpenseType");

        logger.trace("update: Setting the audit fields");
        entity.setUpdatingUserId(auth.getUserId());
        entity.setUpdatedTimestamp(ZonedDateTime.now());

        logger.trace("update: Saving the updated entity");
        DocumentMetadata savedEntity = repository.save(entity);
        logger.trace("update: Save successful");

        return getViewModel(savedEntity);
    }

    @Override
    public List<DocumentMetadataViewModel> getAll(IAuthContextViewModel auth, Integer documentCategoryId, String action) {
        logger.trace("getAll: Returning view model list");
        List<DocumentMetadata> metadata;

        if (null != documentCategoryId) {
            if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(action)) {
                metadata = repository.findByCompanyCodeAndDocumentCategoryIdAndDocumentTypeContainingOrderByIdDesc(auth.getCompanyCode(), documentCategoryId, action);
            } else {

                metadata = repository.findByCompanyCodeAndDocumentCategoryIdOrderByIdDesc(auth.getCompanyCode(), documentCategoryId);
            }
        } else {
            metadata = repository.findByCompanyCodeOrderByIdDesc(auth.getCompanyCode());
        }

        return metadata.stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public List<DocumentMetadataViewModel> getAllByCategoryAndByActionAndByExpenseType(IAuthContextViewModel auth, Integer documentCategoryId, String action, String expenseType) {
        logger.trace("getAll: Returning view model list");
        List<DocumentMetadata> metadata = repository.findByCompanyCodeAndDocumentCategoryIdAndActionTypeAndApplicableExpenseTypeAndIsFrozenFalseOrderByIdDesc(auth.getCompanyCode(), documentCategoryId, ActionType.valueOf(action.toUpperCase()), ExpenseType.fromString(expenseType));

        return metadata.stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public List<DocumentMetadataViewModel> getPolicyByMetadata(IAuthContextViewModel auth, GetPolicyDataModel getPolicyDataModel) {
        logger.trace("getPolicyByMetadata: Returning view model list");

        if (null != getPolicyDataModel.getMetadataId() && !getPolicyDataModel.getAction().isEmpty()) {
            List<DocumentMetadata> metadata;
            Optional<DocumentMetadata> documentMetadataOptional = repository.findByCompanyCodeAndId(auth.getCompanyCode(), getPolicyDataModel.getMetadataId());
            if (documentMetadataOptional.isPresent()) {
                List<ActionType> types = getPolicyDataModel.getAction().stream()
                        .map(ActionType::valueOf)  // Convert each String to MetadataType
                        .collect(Collectors.toList());

                metadata = repository.findByCompanyCodeAndUuidAndActionTypeIn(auth.getCompanyCode(), documentMetadataOptional.get().getUuid(), types);

                return metadata.stream()
                        .filter((doc -> doc.getApprovalDefinitions() != null && !doc.getApprovalDefinitions().isEmpty())) // TODO: do we need to check group rule as well?
                        .map(this::getViewModel)
                        .collect(Collectors.toList());
            }
            logger.info(String.format("getPolicyByMetadata: The DocumentMetadata with id: %d could not found.", getPolicyDataModel.getMetadataId()));
            throw new DomainInvariantException(String.format("The DocumentMetadata with id: %d could not found.", getPolicyDataModel.getMetadataId()));

        } else {
            logger.info("Invalid request.");
            throw new DomainInvariantException("Invalid request");

        }

    }

    @Override
    public DocumentMetadataViewModel getById(long id, IAuthContextViewModel auth) {
        logger.trace("getById: Find the source record");
        DocumentMetadata entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", DocumentMetadata.class, id, auth.getCompanyCode())));

        return getViewModel(entity);
    }

    @Override
    public List<KeyValuePairViewModel<Long, String>> getKeyValueCollection(IAuthContextViewModel auth) {
        return repository
                .findByCompanyCodeOrderByIdDesc(auth.getCompanyCode())
                .stream()
                .filter(e -> !e.isFrozen())
                .map(e -> new KeyValuePairViewModel<>(
                        e.getId(),
                        String.format("%s > %s", e.getDocumentType(), e.getDocumentGroup())
                ))
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsByTypeAndGroup(String type, String group, IAuthContextViewModel auth) {
        return repository.getCountOfTypeAndGroup(auth.getCompanyCode(), type, group) > 0;
    }

    @Override
    public boolean existsByPrefixesOfTypeAndGroup(String typePrefix, String groupPrefix, IAuthContextViewModel auth) {
        return repository.getCountByPrefixes(auth.getCompanyCode(), typePrefix, groupPrefix) > 0;
    }

    @Override
    public List<DocumentMetadataViewModel> getBudgetMetadataByExpenseType(String expenseType, IAuthContextViewModel auth) {
        logger.trace("getBudgetMetadataByExpenseType: Returning view model list");
        return repository
                .findByCompanyCodeAndDocumentCategoryIdAndApplicableExpenseTypeAndDocumentType(auth.getCompanyCode(), StringConstants.budgetCategoryId, expenseType, StringConstants.budgetMainDocType)
                .stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    private DocumentMetadataViewModel getViewModel(DocumentMetadata entity) {
        DocumentMetadataViewModel viewModel = new DocumentMetadataViewModel();
        viewModel.setApplicableExpenseType(null == entity.getApplicableExpenseType() ? null : entity.getApplicableExpenseType().name());
        BeanUtils.copyProperties(entity, viewModel, "applicableExpenseType");

//        viewModel.setDefinitionsCount(entity.getApprovalDefinitions().size());
//        viewModel.setSubgroupsCount(entity.getExpenseSubgroups().size());
//        viewModel.setRulesCount(entity.getLimitRules().size());
        return viewModel;
    }
}
