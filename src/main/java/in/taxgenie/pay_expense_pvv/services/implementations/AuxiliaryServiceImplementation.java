package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cem.employee.implementations.EmployeeStatus;
import in.taxgenie.pay_expense_pvv.cem.employee.interfaces.IEmployeeMasterBase;
import in.taxgenie.pay_expense_pvv.cem.employee.interfaces.IEmployeeViewModel;
import in.taxgenie.pay_expense_pvv.services.interfaces.IAuxiliaryService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmployeeMasterDataService;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.utils.StringUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.ApproverPersonViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.EmployeeMasterLookupViewModel;
import org.hibernate.internal.util.StringHelper;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AuxiliaryServiceImplementation implements IAuxiliaryService {
    private final IEmployeeMasterDataService employeeMasterDataService;

    public AuxiliaryServiceImplementation(IEmployeeMasterDataService employeeMasterDataService) {
        this.employeeMasterDataService = employeeMasterDataService;
    }

    @Override
    public EmployeeMasterLookupViewModel getEmployeeMasterLookup(IAuthContextViewModel auth) {
        List<IEmployeeViewModel> allEmployeeMasterData = employeeMasterDataService
                .getAllEmployeeMasterData(auth);

        List<String> designations = allEmployeeMasterData
                .stream()
                .map(IEmployeeMasterBase::getDesignation)
                .distinct()
                .collect(Collectors.toList());

        List<String> employeeTypes = allEmployeeMasterData
                .stream()
                .map(IEmployeeMasterBase::getType)
                .distinct()
                .collect(Collectors.toList());

        List<String> grades = allEmployeeMasterData
                .stream()
                .map(IEmployeeMasterBase::getGrade)
                .distinct()
                .collect(Collectors.toList());

        List<String> branches = allEmployeeMasterData
                .stream()
                .map(IEmployeeMasterBase::getBranchCode)
                .distinct()
                .collect(Collectors.toList());

        List<String> departments = allEmployeeMasterData
                .stream()
                .map(IEmployeeMasterBase::getDepartmentCode)
                .distinct()
                .collect(Collectors.toList());

        List<String> costCenters = allEmployeeMasterData
                .stream()
                .map(IEmployeeMasterBase::getCostCentre)
                .distinct()
                .collect(Collectors.toList());

        return new EmployeeMasterLookupViewModel(designations, employeeTypes, grades, branches, departments, costCenters);
    }

    @Override
    public List<ApproverPersonViewModel> getApprovers(boolean includeEndOfService, IAuthContextViewModel auth) {
        return getApproverList(includeEndOfService, auth);
    }

    @Override
    public boolean isApproverExist(String email, boolean includeEndOfService, IAuthContextViewModel auth) {
        if (StringUtils.isNullOrEmpty(email))
            return false;
        else
            return getApproverList(includeEndOfService, auth).stream()
                    .anyMatch(approver -> approver.getEmail().equalsIgnoreCase(email));
    }

    public List<ApproverPersonViewModel> getApproverList(boolean includeEndOfService, IAuthContextViewModel auth) {
        return employeeMasterDataService
                .getAllApproversMasterData(auth)
                .stream()
                .filter(e -> e.getStatus() == EmployeeStatus.ACTIVE && e.isCanBeDelegated())
                .map(e -> {
                    ApproverPersonViewModel viewModel = new ApproverPersonViewModel();
                    viewModel.setEmail(e.getEmail());
                    viewModel.setFirstName(e.getFirstName());
                    viewModel.setLastName(e.getLastName());
                    viewModel.setMiddleName(e.getMiddleName());
                    viewModel.setEmployeeCode(e.getEmployeeCode());
                    return viewModel;
                })
                .sorted(Comparator.comparing(ApproverPersonViewModel::getFirstName))
                .collect(Collectors.toList());
    }


}
