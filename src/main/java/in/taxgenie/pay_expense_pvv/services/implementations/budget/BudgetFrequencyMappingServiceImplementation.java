package in.taxgenie.pay_expense_pvv.services.implementations.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.LookupData;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetFrequencyMapping;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetStructureMaster;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.repositories.ILookupRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetFrequencyMappingRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetStructureMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomBudgetRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetFrequencyMappingService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetStepperService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.budget.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetColumnsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetSheetViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetRecordViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels.BudgetTotalsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.frequencymapping.NodeUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels.MonthFinancialYearModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.IntStream;

@Service
public class BudgetFrequencyMappingServiceImplementation implements IBudgetFrequencyMappingService {
    private final IBudgetFrequencyMappingRepository repository;
    private final IBudgetRepository budgetRepository;
    private final IBudgetStructureMasterRepository budgetStructureMasterRepository;
    private final ILookupRepository lookupRepository;
    private final IBudgetStepperService budgetStepperService;
    private final CustomBudgetRepository customBudgetRepository;
    private final Logger logger;

    public BudgetFrequencyMappingServiceImplementation(IBudgetFrequencyMappingRepository repository,
                                                       IBudgetRepository budgetRepository,
                                                       IBudgetStructureMasterRepository budgetStructureMasterRepository,
                                                       ILookupRepository lookupRepository, IBudgetStepperService budgetStepperService, CustomBudgetRepository customBudgetRepository) {
        this.repository = repository;
        this.budgetRepository = budgetRepository;
        this.budgetStructureMasterRepository = budgetStructureMasterRepository;
        this.lookupRepository = lookupRepository;
        this.budgetStepperService = budgetStepperService;
        this.customBudgetRepository = customBudgetRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public BudgetSheetViewModel get(long structureId, IAuthContextViewModel auth) {
        return getBudgetSheetViewModel(structureId, auth.getCompanyCode());
    }

    @Override
    public BudgetViewModel initaliseFrequencyMapping(long structureId, String period, BigDecimal budgetTotal, Boolean resetStages, IAuthContextViewModel auth) {
        if (Boolean.TRUE.equals(resetStages)) {
            budgetStepperService.initiateReset(StaticDataRegistry.SCREEN_FOUR, structureId, auth);
        }
        budgetStepperService.setLockScrren(resetStages, StaticDataRegistry.SCREEN_FOUR, structureId, auth);

        BudgetStructureMaster structureMaster = budgetStructureMasterRepository.findByCompanyCodeAndIdAndIsActive(auth.getCompanyCode(),
                structureId, true).orElseThrow(() -> {
                    logger.info("initaliseFrequencyMapping: Structure id: {} cannot be found company code: {}", structureId, auth.getCompanyCode());
                    return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_CREATION_ERROR);
        });

        Budget root = getRootNodeForStructure(structureId, auth.getCompanyCode());
        performCheckAndWipePreviousFrequencyMap(structureId, auth.getCompanyCode());

        List<BudgetFrequencyMapping> budgetFrequencyMappings = new ArrayList<>();

        LookupData interval = getIntervalCount(period);
        addBudgetFrequencyLists(root, auth.getCompanyCode(), auth.getUserId(), budgetFrequencyMappings, Integer.parseInt(interval.getValue()), budgetTotal);
        root.setTotal(budgetTotal);
        root.setTreasury(budgetTotal);

        structureMaster.setLookupData(interval);
        structureMaster.setLookupDataId(interval.getId());
        repository.saveAllAndFlush(budgetFrequencyMappings);
        return null;
    }


    @Override
    public BudgetSheetViewModel updateNodesCSV(MultipartFile csvFile, long structureId, IAuthContextViewModel auth) {
        validateCsvFile(csvFile);

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(csvFile.getInputStream()))) {
            List<BudgetRecordViewModel> records = parseCsvFile(reader);
            updateDatabase(records, auth);
            return getBudgetSheetViewModel(structureId, auth.getCompanyCode());
        } catch (Exception e) {
            logger.error("Error processing CSV file: {}", e.getMessage(), e);
            throw new DomainInvariantException("Upload Failed: The file's columns do not match the expected structure for the selected budget frequency. Please check your file and try again.");
        }
    }

    private void validateCsvFile(MultipartFile csvFile) {
        if (csvFile.isEmpty()) {
            throw new IllegalArgumentException("Uploaded file is empty");
        }
        if (!csvFile.getOriginalFilename().endsWith(".csv")) {
            throw new IllegalArgumentException("Invalid file type. Please upload a CSV file.");
        }
    }

    private List<BudgetRecordViewModel> parseCsvFile(BufferedReader reader){

        try{
            String line;
            List<BudgetRecordViewModel> records = new ArrayList<>();
            String[] headers = null;

            while ((line = reader.readLine()) != null) {
                String[] values = line.split(",");
                if (headers == null) {
                    headers = values;
                    continue;
                }
                records.add(mapToRecord(headers, values));
            }

            return records;
        }catch (Exception exception){
            throw new IllegalArgumentException("Upload Failed: The file's columns do not match the expected structure for the selected budget frequency. Please check your file and try again.");
        }

    }

    private BudgetRecordViewModel mapToRecord(String[] headers, String[] values) {

        try
        {
            Map<String, BigDecimal> monthlyBudgets = new HashMap<>();
            for (int i = 4; i < values.length; i++) {
                monthlyBudgets.put(headers[i], new BigDecimal(values[i]));
            }

            return new BudgetRecordViewModel(
                    values[0],  // Organisation
                    values[1],  // Budget Code
                    Long.parseLong(values[2]),  // UUID1
                    Long.parseLong(values[3]),  // UUID2
                    monthlyBudgets
            );
        }catch (Exception exception){
            throw new IllegalArgumentException("Upload Failed: The file's columns do not match the expected structure for the selected budget frequency. Please check your file and try again.");
        }
    }

    private void updateDatabase(List<BudgetRecordViewModel> records, IAuthContextViewModel auth) {
        records.forEach(record -> {
            List<BudgetFrequencyMapping> budgetFrequencyMappings = repository.findByCompanyCodeAndBudgetIdAndIsActiveTrue(auth.getCompanyCode(), record.getUuid1());
            Budget budget = budgetFrequencyMappings.get(0).getBudget();

            BigDecimal total = record.getMonthlyBudgets().values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            budget.setTotal(total);
            budget.setBudgetCode(record.getBudgetCode());

            if (record.getMonthlyBudgets().size() == budgetFrequencyMappings.size()) {
                IntStream.range(0, budgetFrequencyMappings.size()).forEach(i ->
                        budgetFrequencyMappings.get(i).setTotal(record.getMonthlyBudgets().values().toArray(new BigDecimal[0])[i])
                );
            } else{
                throw new IllegalArgumentException("Upload Failed: The file's columns do not match the expected structure for the selected budget frequency. Please check your file and try again.");
            }

            budgetRepository.save(budget);
            repository.saveAll(budgetFrequencyMappings);
        });
    }
    @Override
    public BudgetSheetViewModel updateNodes(List<NodeUpdateViewModel> updateViewModels, long structureId, IAuthContextViewModel auth) {
        logger.info("UpdateNodes: Entered function for structure id: {} from company id: {}", structureId, auth.getCompanyCode());
        // Initialise data stores.
        HashMap<Long, BudgetFrequencyMapping> store = new HashMap<>();
        List<BudgetFrequencyMapping> updatedNodes = new ArrayList<>();

        HashMap<Long, Budget> storeBudgetNode = new HashMap<>();
        List<Budget> updatedBudgetNodes = new ArrayList<>();

        HashMap<Long, BigDecimal> differenceAmountMap = new HashMap<>();

        repository.findByCompanyCodeAndIdInAndIsActiveTrue(auth.getCompanyCode(), updateViewModels.stream().map(NodeUpdateViewModel::getId).toList())
                .forEach(i -> {
                    store.put(i.getId(), i);
                });

        logger.info("UpdateNodes: Entered function for structure id: {} from company id: {}", structureId, auth.getCompanyCode());
        updateViewModels.forEach(i -> {
            BudgetFrequencyMapping entity = store.get(i.getId());
            BigDecimal currentDifference = i.getTotal().subtract(entity.getTotal());
            if (differenceAmountMap.containsKey(i.getBudgetId())) {
                differenceAmountMap.put(i.getBudgetId(), differenceAmountMap.get(i.getBudgetId()).add(currentDifference));
            } else {
                differenceAmountMap.put(i.getBudgetId(), i.getTotal().subtract(entity.getTotal()));
            }
            entity.updateAmounts(i);
            updatedNodes.add(entity);
        });

        budgetRepository.findByCompanyCodeAndIdInAndIsActiveTrue(auth.getCompanyCode(), updateViewModels.stream().map(NodeUpdateViewModel::getBudgetId).toList())
                .forEach(i -> {
                    storeBudgetNode.put(i.getId(), i);
                });
        updateViewModels.forEach(i -> {
            Budget entity = storeBudgetNode.get(i.getBudgetId());
            entity.setBudgetCode(i.getBudgetCode());


            BigDecimal totalFreq = BigDecimal.ZERO;
            BigDecimal treasuryFreq = BigDecimal.ZERO;

            for (BudgetFrequencyMapping freq : repository.findByCompanyCodeAndBudgetIdAndIsActiveTrue(auth.getCompanyCode(), entity.getId())) {
                // Use a temporary variable to update values

                if(store.get(freq.getId()) != null) {
                    totalFreq = totalFreq.add(store.get(freq.getId()).getTotal() != null ? store.get(freq.getId()).getTotal() : BigDecimal.ZERO);
                    treasuryFreq = treasuryFreq.add(store.get(freq.getId()).getTreasury() != null ? store.get(freq.getId()).getTreasury() : BigDecimal.ZERO);
                } else {
                    totalFreq = totalFreq.add(freq.getTotal() != null ? freq.getTotal() : BigDecimal.ZERO);
                    treasuryFreq = treasuryFreq.add(freq.getTreasury() != null ? freq.getTreasury() : BigDecimal.ZERO);
                }
            }

            // Check if totalFreq is non-zero and perform updates
            //if (totalFreq.compareTo(BigDecimal.ZERO) != 0) {
                entity.updateTotal(totalFreq);
                entity.updateTreasury(treasuryFreq);
//            }else {
//                // Sanity check
//                logger.info("UpdateNodes: Entered function updating budget code from company id: {}");
//                if (differenceAmountMap.containsKey(i.getBudgetId())) {
//                    BigDecimal difference = differenceAmountMap.get(i.getBudgetId());
//                    entity.updateTotal(difference);
//                    entity.updateTreasury(difference);
//                } else {
//                    throw new DomainInvariantException("updateNodes: There one or more of the budget ids did not show a difference.");
//                }
//            }

            updatedBudgetNodes.add(entity);
        });

        repository.saveAllAndFlush(updatedNodes);
        budgetRepository.saveAllAndFlush(updatedBudgetNodes);

        logger.info("UpdateNodes: returning: {}");
        return getBudgetSheetViewModel(structureId, auth.getCompanyCode());
    }


    // --- HELPER FUNCTIONS START ---

    private BudgetSheetViewModel getBudgetSheetViewModel(long structureId, long companyCode) {
        BudgetSheetViewModel viewModel = new BudgetSheetViewModel();
        Budget root = getRootNodeForStructure(structureId, companyCode);
        int numberOfIntervals = root.getBudgetFrequencyMappings().size();
        int monthIntervalIncrementFactor = 0;

        // Structure
        BudgetStructureMaster structureMaster = budgetStructureMasterRepository.findByCompanyCodeAndIdAndIsActive(companyCode, structureId, true)
                .orElseThrow(() -> {
                    logger.info("getBudgetSheetViewModel: StructureId: %d and companyId: %d", structureId, companyCode);
                    return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
                });

        // Sanity Checks
        if (numberOfIntervals == StaticDataRegistry.BUDGET_FREQUENCY_START_INDEX) {
            logger.info(String.format("getBudgetSheetViewModel: The number of budget frequency mappings is ZERO, for structureId: %d and company code: %d. This might be a new structure.", structureId, companyCode));
            return new BudgetSheetViewModel();
        } else if (StaticDataRegistry.YEAR_LENGTH % numberOfIntervals != 0) {
            logger.info(String.format("getBudgetSheetViewModel: The number of budget frequency mappings was zero for structureId: %d", structureId));
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        }

        monthIntervalIncrementFactor = StaticDataRegistry.YEAR_LENGTH / numberOfIntervals;
        List<BudgetColumnsViewModel> budgetColumns = generateBudgetSheetColumns(structureMaster.getId(), companyCode, numberOfIntervals, monthIntervalIncrementFactor);
        Map<Integer, BudgetColumnsViewModel> intervalToBudgetFrequencyMap = new HashMap<>();
        budgetColumns.forEach(i -> {
            intervalToBudgetFrequencyMap.put(i.getInterval(), i);
        });

        BudgetTotalsViewModel totals = customBudgetRepository.getBudgetTotals(companyCode, structureId, root.getId());

        LookupData intervalLookup = getIntervalName(numberOfIntervals);
        // Is a singleton list
        viewModel.setTreeData(List.of(Budget.createBudgetNodeViewModel(root, intervalToBudgetFrequencyMap)));
        viewModel.setStructureId(structureMaster.getId());
        viewModel.setColumns(budgetColumns);
        viewModel.setFrequencyId(intervalLookup.getId());

        viewModel.setTotalBudgetAmount(root.getTotal());
        viewModel.setBalanceBudgetAmount(root.getTreasury());

        return viewModel;
    }

     @Override
      public Budget getRootNodeForStructure(long structureId, long companyCode) {
        List<Budget> availableRoots = budgetRepository.findByCompanyCodeAndBudgetStructureMasterIdAndParentIsNullAndIsActiveTrue(companyCode, structureId);
        if (availableRoots.isEmpty()) {
            logger.error(String.format("initaliseFrequencyMapping: Budget for structure id: %d could not be found", structureId));
            throw new DomainInvariantException("The requested org budget tree could not be found.");
        } else if (availableRoots.size() > 1) {
            logger.error(String.format("Multiple org trees found for structure id: %d", structureId));
            throw new DomainInvariantException("The requested org budget tree could not be found.");
        }

        return availableRoots.get(0);
    }

    private List<BudgetColumnsViewModel> generateBudgetSheetColumns(long structureId, long companyCode,int numberOfIntervals, int monthIntervalIncrementFactor) {
        // Initialise variables
        BudgetStructureMaster budgetStructureMaster = budgetStructureMasterRepository.findByCompanyCodeAndIdAndIsActive(companyCode, structureId, true)
                .orElseThrow(() -> {
                    logger.info(String.format("generateBudgetSheetColumns: The following structure id: %d could not be found", structureId));
                    return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
                });
        // Struct : 2024-2025 -> [2024, 2025]
        int[] finYearArray;
        try {
            finYearArray = DateTimeUtils.parseFinancialYearForBudget(budgetStructureMaster.getFinancialYear());
        } catch (IllegalArgumentException e) {
            logger.info(String.format("generateBudgetSheetColumns: There was an issue parsing the date\t%s", e.getMessage()));
            throw  new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        } catch (Exception e) {
            logger.info(String.format("generateBudgetSheetColumns: There was an unhandled exception\t%s", e.getMessage()));
            throw  new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        }

        return getBudgetColumnsViewModels(numberOfIntervals, monthIntervalIncrementFactor, budgetStructureMaster, finYearArray);
    }

    private void performCheckAndWipePreviousFrequencyMap(long structureId, long companyId) {
        List<BudgetFrequencyMapping> budgetFrequencyMappingsToClean = repository.findByCompanyCodeAndBudgetStructureMasterIdAndIsActiveTrue(companyId, structureId);
        repository.deleteAll(budgetFrequencyMappingsToClean);
    }

    private void addBudgetFrequencyLists(Budget node, long companyCode, long userId, List<BudgetFrequencyMapping> budgetFrequencyMappings,
                                         Integer intervalCode, BigDecimal budgetTotal) {
        if (node == null) {
            return;
        }
        //As per requirement don't split budget amount
        budgetTotal = BigDecimal.ZERO;
        // When initialising separate the provided amount into equal segments for each period
        BigDecimal totalAmountFM = budgetTotal.divide(BigDecimal.valueOf(intervalCode),2, RoundingMode.HALF_EVEN);
        BigDecimal budgetTreasuryAmountFM = BigDecimal.valueOf(0);

        //set amount to budget node
        node.setTotal(budgetTotal);

        if(!node.getChildren().isEmpty()){
            node.setTreasury(BigDecimal.valueOf(0));

            budgetTreasuryAmountFM = BigDecimal.valueOf(0);
        } else{
            //Don't have child i.e.leaf node
            node.setTreasury(budgetTotal);

            budgetTreasuryAmountFM = totalAmountFM;


        }
        List<BudgetFrequencyMapping> budgetFrequencyMappingCollector = new ArrayList<>();



        for (int i = 0; i < intervalCode; i++ ) {
            BudgetFrequencyMapping budgetFrequencyMapping = new BudgetFrequencyMapping(i+1, companyCode, userId);
            // Data mapping
            budgetFrequencyMapping.setTotal(totalAmountFM);
            budgetFrequencyMapping.setTreasury(budgetTreasuryAmountFM);

            // bidirectional mapping
            budgetFrequencyMapping.setBudget(node);
            budgetFrequencyMapping.setBudgetId(node.getId());
            budgetFrequencyMapping.setBudgetStructureMasterId(node.getBudgetStructureMasterId());
            budgetFrequencyMapping.setBudgetStructureMaster(node.getBudgetStructureMaster());

            node.getBudgetFrequencyMappings().add(budgetFrequencyMapping);
            budgetFrequencyMappingCollector.add(budgetFrequencyMapping);
        }
        budgetFrequencyMappings.addAll(budgetFrequencyMappingCollector);

        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            BigDecimal numberOfChildrenToAllocateBudgetTo = BigDecimal.valueOf(node.getChildren().size());
            for (Budget child : node.getChildren()) {
                addBudgetFrequencyLists(child, companyCode, userId, budgetFrequencyMappings, intervalCode, budgetTotal.divide(numberOfChildrenToAllocateBudgetTo,2, RoundingMode.HALF_EVEN));
            }
        }
    }

    @Override
    public LookupData getIntervalCount(String interval) {
        List<LookupData> availableValues = lookupRepository.findByType(interval);
        if (availableValues.isEmpty()) {
            throw new DomainInvariantException(String.format("create: The requested period %s could not be found", interval));
        } else if (availableValues.size() > 1) {
            throw new DomainInvariantException(String.format("create: More then one value for the given period %s was found", interval));
        }
        try {
            Integer.parseInt(availableValues.get(0).getValue()); // To enable validations.
            return availableValues.get(0);
        } catch (NumberFormatException numberFormatException) {
            logger.error(String.format("create: Cannot convert %s into a number", availableValues.get(0).getValue()));
            throw new DomainInvariantException("create: Error...please try again later");
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw new DomainInvariantException("create: Error...please try again later");
        }
    }

    @Override
    public LookupData getIntervalName(int numberOfIntervals) {

        String Interval = Integer.toString(numberOfIntervals);
        List<LookupData> availableValues = lookupRepository.findByValue(Interval);
        if (availableValues.isEmpty()) {
            throw new DomainInvariantException(String.format("create: The requested period number %s could not be found", Interval));
        } else if (availableValues.size() > 1) {
            throw new DomainInvariantException(String.format("create: More then one value for the given period number %s was found", Interval));
        }

        return availableValues.get(0);
    }

    @Override
    public BudgetFrequencyMapping getCurrentFrequency(Budget budget) {

      //Remove this. Added for testing
//        Optional<Budget> budgetOptional = budgetRepository.findById(3682l);
//        Budget budget = budgetOptional.get();

        if(null == budget){
            return null;
        }

        List<BudgetFrequencyMapping> budgetFrequencyMappingList = budget.getSortedBudgetFrequencyMappings();

        LocalDate currentDate = LocalDate.now();
        String month = currentDate.getMonth().getDisplayName(TextStyle.SHORT, Locale.ENGLISH);
        String currentMonthYear = month + currentDate.getYear();

        int intervalCount = budgetFrequencyMappingList.size();
        Integer startMonthIndex = budget.getBudgetStructureMaster().getStartMonthIndex();

        int monthIntervalIncrementFactor = StaticDataRegistry.YEAR_LENGTH / intervalCount;
        List<BudgetColumnsViewModel> budgetColumns = generateBudgetSheetColumns(
                budget.getBudgetStructureMasterId(),
                budget.getCompanyCode(),
                intervalCount,
                monthIntervalIncrementFactor);

        for(BudgetColumnsViewModel budgetColumn :budgetColumns){
            List<String> headerList = budgetColumn.getAllHeader();

            for(String allHeader : headerList){
                if(currentMonthYear.equals(allHeader)){
                    return budgetFrequencyMappingList.get(budgetColumn.getInterval() - 1);
                }
            }
        }

        return null;
    }

    private boolean isStructureIsEditable(long structureId, long companyId) {
        return true;
    }

    private static List<BudgetColumnsViewModel> getBudgetColumnsViewModels(int numberOfIntervals, int monthIntervalIncrementFactor, BudgetStructureMaster budgetStructureMaster, int[] finYearArray) {
        // Note: This is because for monthly intervals the col names are singular months (e.g. jan2024, feb2024) else in all other cases col names will display with
        // hypen in between e.g (nov2024-jan2025).
        if (numberOfIntervals == StaticDataRegistry.YEAR_LENGTH) {
            return createMonthlyList(numberOfIntervals, budgetStructureMaster, finYearArray);
        } else {
            return createRangedIntervalList(numberOfIntervals, monthIntervalIncrementFactor, budgetStructureMaster, finYearArray);
        }
    }

    private static List<BudgetColumnsViewModel> createRangedIntervalList(int numberOfIntervals, int monthInterval, BudgetStructureMaster budgetStructureMaster, int[] finYearArray) {
        List<BudgetColumnsViewModel> budgetColumnsViewModels = new ArrayList<>();

        // Population logic : Initalise looping index to provided start month or default to 0.
        int monthIndex = budgetStructureMaster.getStartMonthIndex() == null ? 0 : budgetStructureMaster.getStartMonthIndex();
        monthIndex = monthIndex -1;

        MonthFinancialYearModel monthStart = new MonthFinancialYearModel(monthIndex, finYearArray[0]);
        MonthFinancialYearModel monthEnd = monthStart.add(monthInterval);

        String headerName = null;
        int interval = StaticDataRegistry.BUDGET_FREQUENCY_START_INDEX;
        for (int i = interval; i < numberOfIntervals; i++) {
            interval  = (i + StaticDataRegistry.KEY_VALUE_INCREMENT_FACTOR);
            BudgetColumnsViewModel budgetColumnsViewModel = new BudgetColumnsViewModel();
            headerName = String.format("%s%d - %s%d", monthStart.getMonthInclusive().getMonthName(), monthStart.getMonthInclusive().getYear(),
                    monthEnd.getMonthExclusive().getMonthName(), monthEnd.getMonthExclusive().getYear());

            int startMonth = monthStart.getMonthInclusive().getMonthIndex();
            int endMonth = monthEnd.getMonthInclusive().getMonthIndex();

            List<String> allHeaderName = new ArrayList<>();
            int count = 1;
            for(int monthCount = 0 ; monthCount<=monthInterval-1 ; monthCount++){
                MonthFinancialYearModel month = monthStart.add(count);
                allHeaderName.add(month.getMonthExclusive().getMonthName()+ month.getMonthExclusive().getYear());
                count++;
            }

            budgetColumnsViewModel.setAllHeader(allHeaderName);
            // For month get from the starting index
            budgetColumnsViewModel.setHeader(headerName);
            // For key Values pairs
            budgetColumnsViewModel.setKey(StaticDataRegistry.KEY_VALUE_BASE + interval);
            budgetColumnsViewModel.setInterval(interval);

            budgetColumnsViewModels.add(budgetColumnsViewModel);

            // Increment both
            monthStart = monthStart.add(monthInterval);
            monthEnd = monthEnd.add(monthInterval);
        }
        return budgetColumnsViewModels;
    }

    private static List<BudgetColumnsViewModel> createMonthlyList(int numberOfIntervals, BudgetStructureMaster budgetStructureMaster, int[] finYearArray) {
        List<BudgetColumnsViewModel> budgetColumnsViewModels = new ArrayList<>();

        // Population logic : Initalise looping index to provided start month or default to 0.
        int monthIndex = budgetStructureMaster.getStartMonthIndex() == null ? 0 : budgetStructureMaster.getStartMonthIndex();
        monthIndex = monthIndex -1;
        MonthFinancialYearModel monthStart = new MonthFinancialYearModel(monthIndex, finYearArray[0]);

        String headerName = null;
        int interval = StaticDataRegistry.BUDGET_FREQUENCY_START_INDEX;
        for (int i = interval; i < numberOfIntervals; i++) {
            BudgetColumnsViewModel budgetColumnsViewModel = new BudgetColumnsViewModel();
            headerName = String.format("%s%d", monthStart.getMonthInclusive().getMonthName(), monthStart.getMonthInclusive().getYear());
            interval = (i + StaticDataRegistry.KEY_VALUE_INCREMENT_FACTOR);

            // For month get from the starting index
            budgetColumnsViewModel.setHeader(headerName);
            budgetColumnsViewModel.setAllHeader(List.of(headerName));
            // For key Values pairs
            budgetColumnsViewModel.setKey(StaticDataRegistry.KEY_VALUE_BASE + interval);
            budgetColumnsViewModel.setInterval(interval);

            budgetColumnsViewModels.add(budgetColumnsViewModel);
            monthStart = monthStart.add(StaticDataRegistry.MONTH_INCREMENT_FACTOR);
        }
        return budgetColumnsViewModels;
    }

    // --- HELPER FUNCTIONS END ---

}
