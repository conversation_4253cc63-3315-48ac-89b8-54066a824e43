package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.entities.DocumentApprovalContainer;
import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.entities.ReportState;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.IReportStateRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmailNotificationService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.comeng.ApprovalPendingLineViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.comeng.ApprovalPendingReminderEmailContainerViewModel;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Profile("prod")
@Component
public class ReminderEmailNotificationScheduledRunner {
    private final IDocumentApprovalContainerRepository reportRepository;
    private final IReportStateRepository stateRepository;
    private final IEmailNotificationService emailNotificationService;


    public ReminderEmailNotificationScheduledRunner(
            IDocumentApprovalContainerRepository reportRepository, IReportStateRepository stateRepository,
            IEmailNotificationService emailNotificationService
    ) {
        this.reportRepository = reportRepository;
        this.stateRepository = stateRepository;
        this.emailNotificationService = emailNotificationService;
    }

    //  Spring uses cron expression of 6 units. Starting unit is second, which is absent in traditional cron.
    //  https://docs.spring.io/spring-framework/docs/current/reference/html/integration.html#scheduling-cron-expression
    @Scheduled(cron = "0 0 0 * * *")
    @Transactional(propagation = Propagation.REQUIRED, readOnly = true, noRollbackFor = Exception.class)
    public void dispatchSentBackEmailReminders() {
        List<DocumentApprovalContainer> sentBackReports = reportRepository.findAllByReportStatus(ReportStatus.SENT_BACK);

        for (DocumentApprovalContainer report : sentBackReports) {
            if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(report.getEmployeeEmail())) {
                emailNotificationService.transmitSentBackReminderNotification(report);
            }
        }
    }

    @Scheduled(cron = "0 0 2 * * *")
    @Transactional(propagation = Propagation.REQUIRED, readOnly = true, noRollbackFor = Exception.class)
    public void dispatchApprovalPendingEmailReminders() {
        reportRepository
                .findAllByReportStatus(ReportStatus.SUBMITTED)
                .stream()
//                .map(r -> stateRepository.findByCompanyCodeAndExpenseReportId(r.getCompanyCode(), r.getId())
                .map(r -> r.getReportStates()
                        .stream()
                        .filter(s -> {
                            return s.getStatus() == ExpenseActionStatus.UNACTIONED;
                        }).min(Comparator.comparing(ReportState::getLevel))
                )
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.groupingBy(ReportState::getApprover, Collectors.mapping(s -> {
                    ApprovalPendingLineViewModel viewModel = new ApprovalPendingLineViewModel();
                    viewModel.setDefaultRemarks(s.getDefaultTriggerRemarks());
                    viewModel.setDeviationRemarks(s.getDeviationRemarks());
                    viewModel.setLevel(s.getLevel());
                    viewModel.setLineMarker(String.format("%s | %s", s.getDocumentApprovalContainer().getDocumentMetadata().getDocumentType(), s.getDocumentApprovalContainer().getDocumentMetadata().getDocumentGroup()));
                    viewModel.setEmployeeName(String.format("%s %s", s.getDocumentApprovalContainer().getFirstName(), s.getDocumentApprovalContainer().getLastName()));
                    viewModel.setEndDate(s.getDocumentApprovalContainer().getEndDate().format(DateTimeFormatter.ofPattern("dd-MM-yyyy")));
                    viewModel.setStartDate(s.getDocumentApprovalContainer().getStartDate().format(DateTimeFormatter.ofPattern("dd-MM-yyyy")));
                    viewModel.setReportClaimAmount(String.format("? %.2f", s.getDocumentApprovalContainer().getApprovalContainerClaimAmount()));
                    viewModel.setApproverName(String.format("%s %s", s.getApproverFirstName(), s.getApproverLastName()));

                    return viewModel;
                }, Collectors.toList())))
                .entrySet()
                .stream()
                .map(es -> new ApprovalPendingReminderEmailContainerViewModel(
                                es.getValue().get(es.getValue().size() - 1).getApproverName().startsWith("null")
                                        ? "Approver"
                                        : es.getValue()
                                        .get(es.getValue().size() - 1)
                                        .getApproverName(),
                                es.getKey(),
                                es.getValue()
                        )
                )
                .collect(Collectors.toList())
                .forEach(emailNotificationService::transmitApprovalReminderNotification);
    }
}
