package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.MasterDataJsonStore;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMaster;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetSyncMaster;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetSyncMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.masters.IMasterDataJsonStoreRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IMasterDataStoreService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.MasterDataStoreViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class MasterDataStoreServiceImplementation implements IMasterDataStoreService {

    private final IBudgetMasterRepository budgetMasterRepository;
    private final IBudgetSyncMasterRepository budgetSyncMasterRepository;
    private final IMasterDataJsonStoreRepository masterDataStoreRepository;
    private final Logger logger;

    public MasterDataStoreServiceImplementation(IBudgetMasterRepository budgetMasterRepository,
                                                IBudgetSyncMasterRepository budgetSyncMasterRepository,
                                                IMasterDataJsonStoreRepository masterDataStoreRepository) {
        this.budgetMasterRepository = budgetMasterRepository;
        this.budgetSyncMasterRepository = budgetSyncMasterRepository;
        this.masterDataStoreRepository = masterDataStoreRepository;
        this.logger = LoggerFactory.getLogger(MasterDataStoreServiceImplementation.class);
    }

    @Override
    @Transactional
    public List<MasterDataStoreViewModel> syncMasterDataStoresV1(
            IAuthContextViewModel auth,
            List<MasterDataStoreViewModel> masterDataStores) {

        logger.info("syncMasterDataStoresV1: Start syncing for companyCode: {}", auth.getCompanyCode());
        Map<String, BudgetMaster> budgetMasterMap = new HashMap<>();

        try {
            for (MasterDataStoreViewModel viewModel : masterDataStores) {
                String key = generateKey(String.valueOf(auth.getCompanyCode()), viewModel.getMasterDataType());

                BudgetMaster budgetMaster = budgetMasterMap.computeIfAbsent(key, k -> fetchOrCreateBudgetMaster(viewModel, auth));
                if (budgetMaster != null) {
                    createOrUpdateBudgetSyncMaster(viewModel, budgetMaster, auth);
                }
            }
        } catch (Exception e) {
            logger.error("syncMasterDataStoresV1: Error during master data synchronization for companyCode: {}", auth.getCompanyCode(), e);
            throw e;
        }

        logger.info("syncMasterDataStoresV1: Completed syncing for companyCode: {}", auth.getCompanyCode());
        return masterDataStores;
    }

    private String generateKey(String companyCode, String masterDataType) {
        return companyCode + "_" + masterDataType;
    }

    private BudgetMaster fetchOrCreateBudgetMaster(MasterDataStoreViewModel viewModel, IAuthContextViewModel auth) {
        List<BudgetMaster> budgetMasterList = budgetMasterRepository.findByCompanyCodeAndName(auth.getCompanyCode(), viewModel.getMasterDataType());

        if (budgetMasterList.isEmpty()) {
            logger.info("fetchOrCreateBudgetMaster: No BudgetMaster found for companyCode {} and type {}. Creating new BudgetMaster.", auth.getCompanyCode(), viewModel.getMasterDataType());
            return createNewBudgetMaster(viewModel, auth);
        } else if (budgetMasterList.size() == 1) {
            return budgetMasterList.get(0);
        } else {
            logger.warn("fetchOrCreateBudgetMaster: Multiple BudgetMasters found for companyCode {} and type {}", auth.getCompanyCode(), viewModel.getMasterDataType());
            return null;
        }
    }

    private BudgetMaster createNewBudgetMaster(MasterDataStoreViewModel viewModel, IAuthContextViewModel auth) {
        BudgetMaster budgetMaster = new BudgetMaster();
        budgetMaster.setCompanyCode(auth.getCompanyCode());
        budgetMaster.setCreatedTimestamp(DateTimeUtils.getCurrentZonedDateTime());
        budgetMaster.setCreatingUserId(auth.getUserId());
        budgetMaster.setActive(true);
        budgetMaster.setUuid(UUID.randomUUID());
        budgetMaster.setName(viewModel.getMasterDataType());
        budgetMaster.setDisplayName(viewModel.getMasterDataType());

        return budgetMasterRepository.save(budgetMaster);
    }

    private void createOrUpdateBudgetSyncMaster(MasterDataStoreViewModel viewModel, BudgetMaster budgetMaster, IAuthContextViewModel auth) {
        Optional<BudgetSyncMaster> existingSyncMaster = budgetSyncMasterRepository
                .findByCompanyCodeAndBudgetMasterIdAndCode(auth.getCompanyCode(), budgetMaster.getId(), viewModel.getCode())
                .stream()
                .findFirst();

        if (existingSyncMaster.isEmpty()) {
            BudgetSyncMaster budgetSyncMaster = new BudgetSyncMaster();
            populateBudgetSyncMaster(budgetSyncMaster, viewModel, budgetMaster, auth);
            logger.info("createOrUpdateBudgetSyncMaster: Created new BudgetSyncMaster for companyCode: {} and budgetMasterId: {}", auth.getCompanyCode(), budgetMaster.getId());
        } else {
            logger.info("createOrUpdateBudgetSyncMaster: BudgetSyncMaster already exists for companyCode: {} and budgetMasterId: {}", auth.getCompanyCode(), budgetMaster.getId());
            BudgetSyncMaster budgetSyncMaster = existingSyncMaster.get();
            updateBudgetSyncMaster(viewModel, auth, budgetSyncMaster);
        }
    }

    private void updateBudgetSyncMaster(MasterDataStoreViewModel viewModel, IAuthContextViewModel auth, BudgetSyncMaster budgetSyncMaster) {
        budgetSyncMaster.setUpdatedTimestamp(DateTimeUtils.getCurrentZonedDateTime());
        budgetSyncMaster.setUpdatingUserId(auth.getUserId());
        budgetSyncMaster.setAddress(viewModel.getAddress());
        budgetSyncMaster.setContactDetails(viewModel.getContactDetails());
        budgetSyncMaster.setDescription(viewModel.getDescription());
        budgetSyncMaster.setSource(viewModel.getSource() != null ? viewModel.getSource() : "EXT");

        // Update original JSON store
        Optional<MasterDataJsonStore> optionalBody = masterDataStoreRepository.findJsonStoreBySyncMasterId(budgetSyncMaster.getId());
        MasterDataJsonStore jsonStore = optionalBody.orElse(new MasterDataJsonStore());
        jsonStore.setBody(viewModel.getBody());
        MasterDataJsonStore storedJson = masterDataStoreRepository.save(jsonStore);
        budgetSyncMaster.setMasterDataJsonStore(storedJson);
        budgetSyncMasterRepository.save(budgetSyncMaster);

    }

    private void populateBudgetSyncMaster(BudgetSyncMaster budgetSyncMaster, MasterDataStoreViewModel viewModel, BudgetMaster budgetMaster, IAuthContextViewModel auth) {
        budgetSyncMaster.setCompanyCode(auth.getCompanyCode());
        budgetSyncMaster.setCreatedTimestamp(DateTimeUtils.getCurrentZonedDateTime());
        budgetSyncMaster.setCreatingUserId(auth.getUserId());
        budgetSyncMaster.setActive(true);
        budgetSyncMaster.setUuid(UUID.randomUUID());
        budgetSyncMaster.setBudgetMaster(budgetMaster);
        budgetSyncMaster.setBudgetMasterId(budgetMaster.getId());
        budgetSyncMaster.setCode(viewModel.getCode());
        budgetSyncMaster.setAddress(viewModel.getAddress());
        budgetSyncMaster.setContactDetails(viewModel.getContactDetails());
        budgetSyncMaster.setDescription(viewModel.getDescription());
        budgetSyncMaster.setSource(viewModel.getSource() != null ? viewModel.getSource() : "EXT");

        // Create new JSON store
        MasterDataJsonStore jsonStore = new MasterDataJsonStore();
        jsonStore.setBody(viewModel.getBody());
        MasterDataJsonStore storedJson = masterDataStoreRepository.save(jsonStore);
        budgetSyncMaster.setMasterDataJsonStore(storedJson);
        budgetSyncMasterRepository.save(budgetSyncMaster);
    }
}
