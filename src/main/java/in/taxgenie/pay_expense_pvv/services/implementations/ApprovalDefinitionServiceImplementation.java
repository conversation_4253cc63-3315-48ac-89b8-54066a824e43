package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ApprovalDefinition;
import in.taxgenie.pay_expense_pvv.entities.DocumentMetadata;
import in.taxgenie.pay_expense_pvv.entities.StateChannel;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.MasterRecordNotFoundException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.IApprovalDefinitionRepository;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentMetadataRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IApprovalDefinitionService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.ApprovalDefinitionCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ApprovalDefinitionUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ApprovalDefinitionViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.KeyValuePairViewModel;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ApprovalDefinitionServiceImplementation implements IApprovalDefinitionService {
    private final IApprovalDefinitionRepository repository;
    private final IDocumentMetadataRepository headerRepository;
    private final Logger logger;

    public ApprovalDefinitionServiceImplementation(
            IApprovalDefinitionRepository repository,
            IDocumentMetadataRepository headerRepository
    ) {
        this.repository = repository;
        this.headerRepository = headerRepository;
        this.logger = LoggerFactory.getLogger(ApprovalDefinitionServiceImplementation.class);
    }


    @Transactional
    @Override
    public ApprovalDefinitionViewModel create(ApprovalDefinitionCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("Ensuring the existence of Expense Metadata record for this request");
        DocumentMetadata headerEntity =
                headerRepository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getDocumentMetadataId())
                        .orElseThrow(() -> new MasterRecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", ApprovalDefinition.class, viewModel.getDocumentMetadataId(), auth.getCompanyCode())));

        logger.trace("create: Ensuring uniqueness by level");
        if (repository.getCountByCriteria(auth.getCompanyCode(), viewModel.getDocumentMetadataId(), viewModel.getLevel(), StaticDataRegistry.DEFAULT_ENTITY_ID) > 0) {
            throw new DomainInvariantException(String.format("Approval definition already exists for Metadata: %d at level: %d", viewModel.getDocumentMetadataId(), viewModel.getLevel()));
        }

        logger.trace("create: Ensuring matcher and title exists if reporting manager is not selected");
        if (!viewModel.isShouldFetchFromEmployeeMaster() && (StaticDataRegistry.isNullOrEmptyOrWhitespace(viewModel.getApprovalMatcher()) || StaticDataRegistry.isNullOrEmptyOrWhitespace(viewModel.getApprovalTitle()))) {
            throw new DomainInvariantException("Approval definition must have matcher and title");
        }

        logger.trace("create: Ensuring uniqueness by designation");
        if (!viewModel.isShouldFetchFromEmployeeMaster() && repository.countByDocumentMetadataIdAndApprovalMatcherAndApprovalTitleAndCompanyCodeAndIsFrozenIsFalse(viewModel.getDocumentMetadataId(), viewModel.getApprovalMatcher(), viewModel.getApprovalTitle(), auth.getCompanyCode()) > 0) {
            throw new DomainInvariantException(String.format("Approval definition already exists for Metadata: %d at approval matcher: %s and approval title: %s", viewModel.getDocumentMetadataId(), viewModel.getApprovalMatcher(), viewModel.getApprovalTitle()));
        }

        logger.trace("create: Ensuring channel validations are met");
        if (viewModel.getChannel() == StateChannel.APPROVER && viewModel.isExternalToCem()) {
            throw new DomainInvariantException("Approver cannot be external to the CEM");

        }

        if (viewModel.getChannel() == StateChannel.CHECKER && viewModel.isShouldFetchFromEmployeeMaster()) {
            throw new DomainInvariantException("Checker cannot be the reporting manager of the user");
        }

        logger.trace("create: Creating a new entity");
        ApprovalDefinition entity = new ApprovalDefinition();
        BeanUtils.copyProperties(viewModel, entity);

        logger.trace("create: Adding header entity");
        entity.setDocumentMetadata(headerEntity);
        headerEntity.getApprovalDefinitions().add(entity);

        logger.trace("create: Setting audit fields");
        entity.setCreatingUserId(auth.getUserId());
        entity.setCreatedTimestamp(ZonedDateTime.now());
        entity.setCompanyCode(headerEntity.getCompanyCode());

        headerEntity.setDefinitionsCount(headerEntity.getDefinitionsCount() + StaticDataRegistry.COUNT_INCREMENT_FACTOR);

        logger.trace("create: Saving entity");
        headerRepository.saveAndFlush(headerEntity);
        ApprovalDefinition savedEntity = repository.saveAndFlush(entity);

        logger.trace("create: Save successful");

        logger.trace("create: Returning the view-model");
        return getViewModel(savedEntity);
    }

    @Override
    public ApprovalDefinitionViewModel update(long id, ApprovalDefinitionUpdateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("update: Matching id with that of the view model");
        if (id != viewModel.getId()) {
            throw new DomainInvariantException("Passed id doesn't match with that of the view model");
        }

        logger.trace("update: Finding the source record");
        ApprovalDefinition entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getId())
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", ApprovalDefinition.class, viewModel.getId(), auth.getCompanyCode())));

        logger.trace("update: Ensuring uniqueness by level");
        if (repository.getCountByCriteria(auth.getCompanyCode(), viewModel.getDocumentMetadataId(), viewModel.getLevel(), viewModel.getId()) > 0
        ) {
            throw new DomainInvariantException(String.format("Approval definition already exists for Metadata: %d at level: %d", viewModel.getDocumentMetadataId(), viewModel.getLevel()));
        }

        logger.trace("create: Ensuring matcher and title exists if reporting manager is not selected");
        if (!viewModel.isShouldFetchFromEmployeeMaster() && (StaticDataRegistry.isNullOrEmptyOrWhitespace(viewModel.getApprovalMatcher()) || StaticDataRegistry.isNullOrEmptyOrWhitespace(viewModel.getApprovalTitle()))) {
            throw new DomainInvariantException("Approval definition must have matcher and title");
        }

        logger.trace("update: Ensuring uniqueness by designation");
        if (!viewModel.isShouldFetchFromEmployeeMaster()
                && (entity.getDocumentMetadataId() != viewModel.getDocumentMetadataId())
                && !entity.getApprovalMatcher().equals(viewModel.getApprovalMatcher())
                && !entity.getApprovalTitle().equals(viewModel.getApprovalTitle())
                && repository.countByDocumentMetadataIdAndApprovalMatcherAndApprovalTitleAndCompanyCodeAndIsFrozenIsFalse(viewModel.getDocumentMetadataId(), viewModel.getApprovalMatcher(), viewModel.getApprovalTitle(), auth.getCompanyCode()) > 0
        ) {
            throw new DomainInvariantException(String.format("Approval definition already exists for Metadata: %d at approval matcher: %s and approval title: %s", viewModel.getDocumentMetadataId(), viewModel.getApprovalMatcher(), viewModel.getApprovalTitle()));
        }

        logger.trace("update: Ensuring the channel validations are met");
        if (viewModel.getChannel() == StateChannel.APPROVER && viewModel.isExternalToCem()) {
            throw new DomainInvariantException("Approver cannot be external to the CEM");

        }

        if (viewModel.getChannel() == StateChannel.CHECKER && viewModel.isShouldFetchFromEmployeeMaster()) {
            throw new DomainInvariantException("Checker cannot be the reporting manager of the user");
        }

        if (viewModel.getChannel() != entity.getChannel()) {
            throw new DomainInvariantException("Channel cannot be modified");
        }

        logger.trace("update: Updating the source record");
        BeanUtils.copyProperties(viewModel, entity, "expenseMetadataId", "channel");

        logger.trace("update: Setting the audit fields");
        entity.setUpdatingUserId(auth.getUserId());
        entity.setUpdatedTimestamp(ZonedDateTime.now());

        logger.trace("update: Saving the update");
        ApprovalDefinition savedEntity = repository.saveAndFlush(entity);
        logger.trace("update: Save successful");

        logger.trace("update: Returning the view-model");
        return getViewModel(savedEntity);
    }

    @Override
    public ApprovalDefinitionViewModel getById(long id, IAuthContextViewModel auth) {
        logger.trace("getById: Finding the source record");
        ApprovalDefinition entity = repository.findByCompanyIdAndDefinitionId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", ApprovalDefinition.class, id, auth.getCompanyCode())));

        logger.trace("getById: Returning the view-model");
        return getViewModel(entity);
    }

    @Override
    public List<ApprovalDefinitionViewModel> getAllByMetadataId(long metadataId, IAuthContextViewModel auth) {
        logger.trace("getAllByMetadataId: Returning the view-model list by metadata id");
        return repository
                .findByCompanyCodeAndDocumentMetadataId(auth.getCompanyCode(), metadataId)
                .stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public List<KeyValuePairViewModel<Long, String>> getKeyValuePairCollection(IAuthContextViewModel auth) {
        logger.trace("getKeyValuePairCollection: Returning the key-value pair collection");
        return repository
                .findAllActiveDefinitions(auth.getCompanyCode())
                .stream()
                .map(e -> new KeyValuePairViewModel<>(
                        e.getId(),
                        StaticDataRegistry.getApprovalDefinitionMarker(e)
                ))
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsByLevel(int level, long metadataId, IAuthContextViewModel auth) {
        logger.trace(String.format("existsByLevel: Returning whether level %d exists for metadata %d", level, metadataId));
        return repository.countByDocumentMetadataIdAndLevelAndCompanyCodeAndIsFrozenIsFalse(metadataId, level, auth.getCompanyCode()) > 0;
    }

    @Override
    public boolean existsByMatcherAndTitle(String matcher, String title, long metadataId, IAuthContextViewModel auth) {
        logger.trace(String.format("existsByLevel: Returning whether matcher %s exists for metadata %d", matcher, metadataId));
        return repository.countByDocumentMetadataIdAndApprovalMatcherAndApprovalTitleAndCompanyCodeAndIsFrozenIsFalse(metadataId, matcher, title, auth.getCompanyCode()) > 0;
    }

    private ApprovalDefinitionViewModel getViewModel(ApprovalDefinition entity) {
        logger.trace("getViewModel: Preparing view model");
        ApprovalDefinitionViewModel returnViewModel = new ApprovalDefinitionViewModel();

        logger.trace("getViewModel: Copying fields to view model");
        BeanUtils.copyProperties(entity, returnViewModel);

        returnViewModel.setDocumentMetadataMarker(StaticDataRegistry.getExpenseMetadataMarker(entity.getDocumentMetadata()));

        logger.trace("getViewModel: Returning the view model");
        return returnViewModel;
    }
}
