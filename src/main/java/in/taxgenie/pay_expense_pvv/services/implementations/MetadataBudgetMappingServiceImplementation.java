package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetDocumentAction;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetStructureMaster;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.*;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetDocumentActionRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetStructureMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomBudgetRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentApprovalContainerUserService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IMetadataBudgetMappingService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetFrequencyMappingService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentCreateResponse;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetAddLessViewModel;
import jakarta.persistence.EntityNotFoundException;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Optional;

@Service
public class MetadataBudgetMappingServiceImplementation implements IMetadataBudgetMappingService {

    private final IMetadataBudgetStructureMappingRepository repository;
    private final IDocumentMetadataRepository metadataRepository;
    private final IDocumentApprovalContainerRepository reportRepository;
    private final IDocumentApprovalContainerUserService documentApprovalContainerUserService;
    private final IBudgetFrequencyMappingService budgetFrequencyMappingService;
    private final IDocumentRepository documentRepository;
    private final IDocumentSubgroupRepository subgroupRepository;
    private final IBudgetStructureMasterRepository budgetStructureMasterRepository;
    private final IBudgetMasterRepository budgetMasterRepository;
    private final CustomBudgetRepository customBudgetRepository;
    private final IBudgetRepository budgetRepository;
    private final IBudgetDocumentActionRepository budgetDocumentActionRepository;
    private final ILookupRepository lookupRepository;

    private final Logger logger;

    public MetadataBudgetMappingServiceImplementation(IMetadataBudgetStructureMappingRepository repository, IDocumentMetadataRepository metadataRepository, IDocumentApprovalContainerRepository reportRepository, IDocumentApprovalContainerUserService documentApprovalContainerUserService, IBudgetFrequencyMappingService budgetFrequencyMappingService, IDocumentRepository documentRepository, IDocumentSubgroupRepository subgroupRepository, IBudgetStructureMasterRepository budgetStructureMasterRepository, IBudgetMasterRepository budgetMasterRepository, CustomBudgetRepository customBudgetRepository, IBudgetRepository budgetRepository, IBudgetDocumentActionRepository budgetDocumentActionRepository, ILookupRepository lookupRepository) {
        this.repository = repository;
        this.metadataRepository = metadataRepository;
        this.reportRepository = reportRepository;
        this.documentApprovalContainerUserService = documentApprovalContainerUserService;
        this.budgetFrequencyMappingService = budgetFrequencyMappingService;
        this.documentRepository = documentRepository;
        this.subgroupRepository = subgroupRepository;
        this.budgetStructureMasterRepository = budgetStructureMasterRepository;
        this.budgetMasterRepository = budgetMasterRepository;
        this.customBudgetRepository = customBudgetRepository;
        this.budgetRepository = budgetRepository;
        this.budgetDocumentActionRepository = budgetDocumentActionRepository;
        this.lookupRepository = lookupRepository;
        this.logger = LoggerFactory.getLogger(MetadataBudgetMappingServiceImplementation.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public DocumentCreateResponse createBudgetDocumentForPublishAndSendForApproval(Long structureId, IAuthContextViewModel auth) {
        DocumentCreateResponse response = new DocumentCreateResponse();

        BudgetStructureMaster budgetStructureMaster = budgetStructureMasterRepository.findByCompanyCodeAndIdAndIsActive(auth.getCompanyCode(), structureId, true)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", BudgetStructureMaster.class, structureId, auth.getCompanyCode())));
        // get main policy then get associated actions policies and make entry
        DocumentMetadata documentMetadata = metadataRepository.findByCompanyCodeAndId(auth.getCompanyCode(), budgetStructureMaster.getDocumentMetadataId())
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", DocumentMetadata.class, budgetStructureMaster.getDocumentMetadataId(), auth.getCompanyCode())));

        if (documentMetadata.getDocumentType().contains("transfer")) {
            response.setMessage("Action not supported");
            return response;
        }

//        List<DocumentMetadata> budgetPolicies =  metadataRepository
//                .findByCompanyCodeAndDocumentTypePrefixAndDocumentGroupPrefix(auth.getCompanyCode(), publishBudgetMetadata.getDocumentTypePrefix(), publishBudgetMetadata.getDocumentGroupPrefix());
//        budgetPolicies.forEach(metadata -> saveMetadataBudgetMapping(metadata, structureId));

        // create document
        Long documentContainerId = create(documentMetadata.getId(), documentMetadata.getDocumentSubgroups().get(0).getId(), structureId, null, auth, ActionType.SUBMIT);

        // submit for approval
        response = submit(documentContainerId, auth);

        budgetStructureMaster.setIsApproved(true);
        budgetStructureMaster.setStatus(ReportStatus.SUBMITTED);
        budgetStructureMasterRepository.saveAndFlush(budgetStructureMaster);

        return response;
    }

    @Override
    public DocumentCreateResponse createBudgetDocumentAction(BudgetAddLessViewModel addLessViewModel, IAuthContextViewModel auth) {
        DocumentCreateResponse response = new DocumentCreateResponse();

        DocumentMetadata documentMetadata = metadataRepository.findByCompanyCodeAndId(auth.getCompanyCode(), addLessViewModel.getDocumentMetadataId())
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", DocumentMetadata.class, addLessViewModel.getDocumentMetadataId(), auth.getCompanyCode())));

        Long documentContainerId = create(documentMetadata.getId(), documentMetadata.getDocumentSubgroups().get(0).getId(), addLessViewModel.getStructureId(), addLessViewModel, auth, ActionType.ADD_LESS);
        response.setContainerId(documentContainerId);

        return response;
    }

    @Override
    public DocumentCreateResponse save(BudgetAddLessViewModel addLessViewModel, IAuthContextViewModel auth) {
        DocumentCreateResponse response = new DocumentCreateResponse();

        BudgetDocumentAction documentAction =
                budgetDocumentActionRepository.findById(addLessViewModel.getId())
                        .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find BudgetDocumentAction with id: %d", addLessViewModel.getId())));

        logger.info(String.format("save: Getting document and document header for BudgetDocumentAction id: {%d}", addLessViewModel.getId()));
        Document document = documentAction.getDocument();
        DocumentApprovalContainer documentApprovalContainer = document.getDocumentApprovalContainer();

        document.setClaimAmount(addLessViewModel.getAddLessAmount());
        document.setTaxableAmount(addLessViewModel.getAddLessAmount());
        document.setUpdatedTimestamp(ZonedDateTime.now());

        logger.info(String.format("save: Preparing the documentApprovalContainer with id: {%d} from the BudgetDocumentAction id: {%d}", document.getDocumentApprovalContainerId(), addLessViewModel.getId()));
        documentApprovalContainer.setApprovalContainerClaimAmount(addLessViewModel.getAddLessAmount());
        documentApprovalContainer.setUpdatedTimestamp(ZonedDateTime.now());

        logger.info("create: Saving both Document and Document Approval Container");
        documentRepository.saveAndFlush(document);
        reportRepository.saveAndFlush(documentApprovalContainer);

        documentAction.setTotalAmount(addLessViewModel.getAmountAvailable());
        documentAction.setUpdatedTimestamp(ZonedDateTime.now());
        documentAction.setUpdatingUserId(auth.getUserId());

        logger.info("create: Save successful");
        budgetDocumentActionRepository.saveAndFlush(documentAction);

        response.setContainerId(documentApprovalContainer.getId());

        return response;
    }

    @Override
    public DocumentCreateResponse delete(long containerId, IAuthContextViewModel auth) {
        logger.info("In deleteDocumentApprovalContainer with ID: {}", containerId);
        return deleteDocumentApprovalContainer(auth.getCompanyCode(), containerId);
    }

    @Transactional
    public DocumentCreateResponse deleteDocumentApprovalContainer(Long companyCode, Long containerId) {
        DocumentCreateResponse response = new DocumentCreateResponse();
        response.setContainerId(containerId);
        try {
            DocumentApprovalContainer container = reportRepository.findByCompanyCodeAndId(companyCode, containerId)
                    .orElseThrow(() -> new EntityNotFoundException("DocumentApprovalContainer not found with ID: " + containerId));

            Document document = container.getDocuments().get(0);
            if (document != null) {
                // Remove from DocumentSubgroup
                DocumentSubgroup subgroup = document.getDocumentSubgroup();
                if (subgroup != null) {
                    logger.info("Removing document from DocumentSubgroup ID And save: {}", subgroup.getId());
                    subgroup.getDocuments().remove(document); // maintain consistency
                    subgroupRepository.save(subgroup);
                }

                BudgetDocumentAction action = document.getBudgetDocumentAction();
                if (action != null) {
                    logger.info("Deleting BudgetDocumentAction with ID: {}", action.getId());
                    budgetDocumentActionRepository.delete(action);
                }

                logger.info("Deleting Document with ID: {}", document.getId());
                documentRepository.delete(document);
            }

            logger.info("Deleting DocumentApprovalContainer with ID: {}", container.getId());
            Hibernate.initialize(container.getReportStates());
            Hibernate.initialize(container.getPaymentAdvices());
            reportRepository.delete(container);

            logger.info("Successfully deleted DocumentApprovalContainer and related entities.");
            response.setMessage("Deleted Successfully");
            return response;

        } catch (EntityNotFoundException ex) {
            logger.warn("Deletion failed: {}", ex.getMessage());
        } catch (Exception ex) {
            logger.error("Unexpected error while deleting DocumentApprovalContainer with ID: {}", containerId, ex);
        }
        response.setMessage("Delete Failed");
        return response;
    }


    private void saveMetadataBudgetMapping(DocumentMetadata entity, Long structureId) {

        MetadataBudgetStructureMapping metadataBudget = new MetadataBudgetStructureMapping();
        metadataBudget.setMetadataId(entity.getId());
        metadataBudget.setStructureId(structureId);
        metadataBudget.setCreatedAt(DateTimeUtils.getCurrentTimestamp());

        logger.trace("mapping: Saving MetadataBudgetStructureMapping entity");

        try {
            repository.saveAndFlush(metadataBudget);
        } catch (Exception e) {
            logger.error("Issue in Saving MetadataBudgetStructureMapping with metadataId %d ", entity.getId());
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Long create(Long documentMetadataId, Long subgroupId, Long structureId, BudgetAddLessViewModel addLessViewModel, IAuthContextViewModel auth, ActionType actionType) {

        Budget budget;
        Long budgetId = null;
        BigDecimal budgetAmount = null;
        BigDecimal claimAmount = null;
        BudgetStructureMaster budgetStructureMaster = null;

        if (null != addLessViewModel) {
            budgetId = addLessViewModel.getBudgetId();
            budgetAmount = addLessViewModel.getAmountAvailable();
            claimAmount = addLessViewModel.getAddLessAmount(); // amount to be added or less
        }
        if(null != structureId){
            Optional<BudgetStructureMaster> budgetStructureMasterOptional = budgetStructureMasterRepository.findById(structureId);
            if(budgetStructureMasterOptional.isPresent()){
                budgetStructureMaster = budgetStructureMasterOptional.get();
            }
        }

        long documentApprovalContainerId = documentApprovalContainerUserService.create(documentMetadataId, auth).getId();

        DocumentApprovalContainer documentApprovalContainer = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), documentApprovalContainerId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find documentApprovalContainer with id: %d", documentMetadataId)));

        /*
        TODO: add enddate and appproverclaimamoount to report, and claim amount to doc
        how to fetch amount from budget structure master    */
        // BigDecimal budgetAmount = budgetRepository.findTotalByCompanyCodeAndBudgetStructureMasterId(auth.getCompanyCode(), structureId).orElse(new BigDecimal(0.0));
        if (budgetId != null) {
            budget = budgetRepository.findByCompanyCodeAndIdAndIsActiveTrue(auth.getCompanyCode(), budgetId)
                    .orElseThrow(() -> new RecordNotFoundException("Create budget document action: Could not find budget"));
        } else {
            budget = budgetFrequencyMappingService.getRootNodeForStructure(structureId, auth.getCompanyCode());
            budgetAmount = customBudgetRepository.getBudgetTotals(auth.getCompanyCode(), structureId, budget.getId()).getTotal();
            claimAmount = budgetAmount;
        }

        if (!isParentExpenseReportSubmittable(documentApprovalContainer)) {
            throw new DomainInvariantException("Parent expense documentApprovalContainer is neither in draft or sent-back state; hence not submittable");
        }
        DocumentSubgroup subgroup = subgroupRepository.findByCompanyCodeAndId(auth.getCompanyCode(), subgroupId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find subgroup with id: %d", subgroupId)));

        Document document = new Document();
        createDocument(document, documentApprovalContainer, subgroup, auth);
        if (null != addLessViewModel) {
            document.setDescription(addLessViewModel.getReason());
        }

        logger.info("create: Creating a new BudgetDocumentAction entity");
        BudgetDocumentAction budgetDocumentAction = new BudgetDocumentAction();
        createBudgetDocumentAction(budgetDocumentAction, document, auth, budget, budgetStructureMaster, actionType);

        manageAmounts(budgetAmount, claimAmount, documentApprovalContainer, document, budgetDocumentAction);

        logger.info("create: Saving both Document and Document Approval Container");
        reportRepository.saveAndFlush(documentApprovalContainer);
        documentRepository.saveAndFlush(document);

        subgroupRepository.saveAndFlush(subgroup);

        logger.info("create: Save successful");
        budgetDocumentActionRepository.saveAndFlush(budgetDocumentAction);

        return documentApprovalContainer.getId();
    }

    private void manageAmounts(BigDecimal amount, BigDecimal claimAmount, DocumentApprovalContainer documentApprovalContainer, Document document, BudgetDocumentAction budgetDocumentAction) {
        documentApprovalContainer.setApprovalContainerClaimAmount(claimAmount);
        documentApprovalContainer.setApprovalContainerTaxableAmount(claimAmount);
        document.setClaimAmount(claimAmount);
        document.setTaxableAmount(claimAmount);

        if (null == amount) {
            budgetDocumentAction.setTotalAmount(claimAmount);
        } else {
            budgetDocumentAction.setTotalAmount(amount); // available amount in case of add-less
//            budgetDocumentAction.setOriginalAmount(amount);
        }
    }

    DocumentCreateResponse submit(Long documentApprovalContainerId, IAuthContextViewModel auth) {
        DocumentCreateResponse response = new DocumentCreateResponse();
        documentApprovalContainerUserService.submit(documentApprovalContainerId, auth, 0l, null);
        response.setContainerId(documentApprovalContainerId);
        return response;
    }

    public void createBudgetDocumentAction(BudgetDocumentAction budgetDocumentAction, Document document, IAuthContextViewModel auth, Budget budget, BudgetStructureMaster budgetStructureMaster, ActionType actionType) {
        budgetDocumentAction.setCreatedTimestamp(ZonedDateTime.now());
        budgetDocumentAction.setDocument(document);
        budgetDocumentAction.setCreatingUserId(auth.getUserId());
        //Add Less case
        if(null!=budget && actionType== ActionType.ADD_LESS){
            budgetDocumentAction.setBudget(budget);
            budgetDocumentAction.setBudgetId(budget.getId());
        }
        //Budget approval case
        if(null != budgetStructureMaster ){
            budgetDocumentAction.setBudgetStructureMaster(budgetStructureMaster);
            budgetDocumentAction.setBudgetStructureMasterId(budgetStructureMaster.getId());
        }


        // Budget Action - Publish - TODO : static publish
        LookupData budgetType = lookupRepository.findByTypeAndValue(StaticDataRegistry.BUDGET_TYPE, StaticDataRegistry.PUBLISH_BUDGET)
                .orElseThrow(() -> {
                            logger.error(String.format("createBudgetDocumentAction: Could not find PR of type: %s and value: %s", StaticDataRegistry.BUDGET_TYPE, StaticDataRegistry.PUBLISH_BUDGET));
                            return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
                        }
                );
        budgetDocumentAction.setBudgetType(budgetType);
        // ---
        LookupData docType = lookupRepository.findByTypeAndValue(StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_BUDGET)
                .orElseThrow(() -> {
                            logger.error(String.format("createBudgetDocumentAction: Could not find doc type: %s and value: %s", StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_BUDGET));
                            return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
                        }
                );
        budgetDocumentAction.setDocType(docType);

        // create docNo - might not be needed
        Long lasRecordId =
                budgetDocumentActionRepository.findByLastBudgetDocumentActionId();
        document.setDocNo(StringConstants.createDocNo(StaticDataRegistry.LOOKUP_VALUE_BUDGET, lasRecordId));
        document.setDocumentDate(LocalDate.now());
    }

    private void createDocument(Document document, DocumentApprovalContainer documentApprovalContainer, DocumentSubgroup subgroup, IAuthContextViewModel auth) {

        document.setDocumentApprovalContainer(documentApprovalContainer);
        // Bi-directional mapping
        documentApprovalContainer.getDocuments().add(document);
        subgroup.getDocuments().add(document);

        document.setDocumentApprovalContainerId(documentApprovalContainer.getId());
        document.setDocumentSubgroup(subgroup);
        document.setDocumentSubgroupId(subgroup.getId());
        document.setFrequency(subgroup.getFrequency());

        logger.info("create: Finding the applicable Expense Rule");
        DocumentRule applicableRule = getApplicableRule(document);

        logger.info("create: Attaching the applicable Expense Rule as id");
        document.setDocumentRuleId(applicableRule.getId());

        document.setCreatingUserId(auth.getUserId());
        document.setCompanyCode(auth.getCompanyCode());
        document.setEmployeeEmail(auth.getUserEmail());
        document.setStartDate(LocalDate.now());
        document.setEndDate(LocalDate.now());
        document.setCreatedTimestamp(ZonedDateTime.now());
        documentRepository.save(document);
        reportRepository.save(documentApprovalContainer);
    }

    private DocumentRule getApplicableRule(Document document) {
        logger.trace("getApplicableRule: Finding the Expense with id: {}", document.getId());

        DocumentSubgroup subgroup = document.getDocumentSubgroup();

        Optional<DocumentRule> rule = subgroup.getRules().stream().filter(r -> !r.isFrozen()).filter(r -> isRuleMatch(r, document)).findFirst();

        return rule.orElseThrow(() -> new RecordNotFoundException("Could not find applicable rule for expense"));
    }

    private boolean isRuleMatch(DocumentRule r, Document document) {
        return true;
    }

    private boolean isParentExpenseReportSubmittable(DocumentApprovalContainer report) {
        return report.getReportStatus() == ReportStatus.DRAFT || report.getReportStatus() == ReportStatus.SENT_BACK;
    }

}
