package in.taxgenie.pay_expense_pvv.services.implementations.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.budget.*;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.repositories.budget.*;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.*;
import in.taxgenie.pay_expense_pvv.utils.budget.StaticDataRegistry;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class BudgetStepperServiceImplementation implements IBudgetStepperService {

    private final IBudgetMappingMasterRepository budgetMappingMasterRepository;
    private final IBudgetRepository budgetRepository;
    private final IBudgetFrequencyMappingRepository budgetFrequencyMappingRepository;
    private final IBudgetStructureMasterRepository budgetStructureMasterRepository;
    private final ISafeDeletionService deletionService;
    private final Logger logger;

    public BudgetStepperServiceImplementation(IBudgetMappingMasterRepository budgetMappingMasterRepository,
                                              IBudgetRepository budgetRepository,
                                              IBudgetFrequencyMappingRepository budgetFrequencyMappingRepository,
                                              IBudgetStructureMasterRepository budgetStructureMasterRepository,
                                              ISafeDeletionService deletionService
    ) {
        this.budgetMappingMasterRepository = budgetMappingMasterRepository;
        this.budgetRepository = budgetRepository;
        this.budgetFrequencyMappingRepository = budgetFrequencyMappingRepository;
        this.budgetStructureMasterRepository = budgetStructureMasterRepository;
        this.deletionService = deletionService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }


    @Transactional
    @Override
    public void setLockScrren(boolean resetStages, int currentStep, long structureId, IAuthContextViewModel auth) {

        BudgetStructureMaster budgetStructureMaster =
                budgetStructureMasterRepository.findByCompanyCodeAndIdAndIsActive(auth.getCompanyCode(), structureId, true)
                .orElseThrow(() -> new DomainInvariantException("This BudgetStructureMaster could not be found."));

        if(resetStages || currentStep>budgetStructureMaster.getIndexFromBudgetStep()){
            budgetStructureMaster.setCurrentStep(BudgetStructureMaster.getBudgetStepFromIndex(currentStep));
            budgetStructureMasterRepository.saveAndFlush(budgetStructureMaster);
        }

    }

    @Override
    public void initiateReset(int startingStep, long structureId, IAuthContextViewModel auth) {

        long companyCode = auth.getCompanyCode();
        // Get Current Locked Step
        // Replaced with Enum later
        int step = getCurrentLockedStep(structureId, auth.getCompanyCode());

        for (int i = step; i >= startingStep; i--) { // Have to go reverse as the later stages depend on the earlier stages.
            performReset(i, structureId, auth);
        }

    }

    @Override
    public void performReset(int stage, long structureId, IAuthContextViewModel auth) {
        long companyCode = auth.getCompanyCode();
        switch (stage) {
            case(1):
                performResetMappingMaster(structureId, companyCode);
                break;
            case(2):
                performResetBudget(structureId, companyCode);
                break;
            case(3):
                performResetBudgetFrequency(structureId, companyCode);
                break;
            default:
                logger.info(String.format("performReset: Invalid stage: %d received for reset", stage));
                break;
        }
    }

    @Override
    public int getCurrentLockedStep(long structureId, long companyCode) {
        logger.info("getCurrentLockedStep : structureId = " + structureId + " companyCode = " + companyCode);
        Optional<BudgetStructureMaster> budgetStructureMasterOptional = budgetStructureMasterRepository.findByCompanyCodeAndIdAndIsActive(companyCode, structureId, true);

        if(budgetStructureMasterOptional.isPresent()){
            BudgetStructureMaster budgetStructureMaster = budgetStructureMasterOptional.get();
            return budgetStructureMaster.getStartMonthIndex();
        } else {
            return 0;
        }
    }

    public void performResetMappingMaster(long structureId, long companyCode) {
        logger.info("performResetMappingMaster : structureId = " + structureId + " companyCode = " + companyCode);
        List<BudgetMappingMaster> entities = budgetMappingMasterRepository.findActiveBudgetMappingsForStructure(companyCode, structureId);
        deletionService.deleteBudgetMappingMasterEntries(entities);
    }
    public void performResetBudget(long structureId, long companyCode) {
        logger.info("performResetBudget : structureId = " + structureId + " companyCode = " + companyCode);

        List<Long> budgetIdList = budgetRepository.getBudgetByCompanyCodeAndBudgetStructureMasterId(companyCode, structureId);

        List<Budget> entities = budgetRepository.findByCompanyCodeAndBudgetStructureMasterIdAndIsActiveTrue(companyCode, structureId);
        deletionService.deleteBudgetEntries(entities);
    }

    public void performResetBudgetFrequency(long structureId, long companyCode) {
        logger.info("performResetBudgetFrequency : structureId = " + structureId + " companyCode = " + companyCode);
        List<BudgetFrequencyMapping> entities = budgetFrequencyMappingRepository.findByCompanyCodeAndBudgetStructureMasterIdAndIsActiveTrue(companyCode, structureId);
        deletionService.deleteBudgetFrequencyMappingEntries(entities);
    }
}
