package in.taxgenie.pay_expense_pvv.services.implementations.dynamic.form;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextFactory;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.DocumentType;
import in.taxgenie.pay_expense_pvv.entities.InvoiceHeader;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetSyncMaster;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.invoice.repository.IInvoiceHeaderRepository;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetSyncMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.po.IPurchaseOrderHeaderRepository;
import in.taxgenie.pay_expense_pvv.repositories.pr.IPurchaseRequestHeaderRepository;
import in.taxgenie.pay_expense_pvv.response.interfaces.factory.IServerResponseFactory;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentSubgroupService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetMasterService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetSyncMasterService;
import in.taxgenie.pay_expense_pvv.services.interfaces.dynamic.form.IDynamicFormService;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentSubgroupViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.MultiBudgetMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dynamic.form.DynamicFieldViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dynamic.form.DynamicFormDataRequest;
import in.taxgenie.pay_expense_pvv.viewmodels.dynamic.form.DynamicFormViewResponseViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

@Service
public class DynamicFormServiceImplementation implements IDynamicFormService {

    private final IBudgetMasterService budgetMasterService;
    private final IBudgetSyncMasterService budgetSyncMasterService;
    private final IDocumentSubgroupService documentSubgroupService;
    private final IBudgetService service;
    private final IBudgetSyncMasterRepository budgetSyncMasterRepository;
    private final IPurchaseOrderHeaderRepository purchaseOrderHeaderRepository;
    private final IPurchaseRequestHeaderRepository purchaseRequestHeaderRepository;
    private final IInvoiceHeaderRepository invoiceHeaderRepository;
    private final IAuthContextFactory authContextFactory;
    private final IServerResponseFactory serverResponseFactory;
    private final Logger logger;

    public DynamicFormServiceImplementation(IBudgetMasterService budgetMasterService, IBudgetSyncMasterService budgetSyncMasterService, IDocumentSubgroupService documentSubgroupService, IBudgetService service, IBudgetSyncMasterRepository budgetSyncMasterRepository, IPurchaseOrderHeaderRepository purchaseOrderHeaderRepository, IPurchaseRequestHeaderRepository purchaseRequestHeaderRepository, IInvoiceHeaderRepository invoiceHeaderRepository, IAuthContextFactory authContextFactory, IServerResponseFactory serverResponseFactory) {
        this.budgetMasterService = budgetMasterService;
        this.budgetSyncMasterService = budgetSyncMasterService;
        this.documentSubgroupService = documentSubgroupService;
        this.service = service;
        this.budgetSyncMasterRepository = budgetSyncMasterRepository;
        this.purchaseOrderHeaderRepository = purchaseOrderHeaderRepository;
        this.purchaseRequestHeaderRepository = purchaseRequestHeaderRepository;
        this.invoiceHeaderRepository = invoiceHeaderRepository;
        this.authContextFactory = authContextFactory;
        this.serverResponseFactory = serverResponseFactory;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public DynamicFormViewResponseViewModel getDynamicFormData(IAuthContextViewModel auth, DynamicFormDataRequest request) {
        // Section details
        DynamicFormViewResponseViewModel responseViewModel = new DynamicFormViewResponseViewModel();
        responseViewModel.setSectionName("Business Details");
        responseViewModel.setSelected(true);
        responseViewModel.setSectionSelector("businessDetails");

        List<DynamicFieldViewModel> fields = new ArrayList<>();
        if (request.getIsConfig()) {
            MultiBudgetMasterViewModel budgetMasterResponse = budgetMasterService.getAllBudgetMaster(auth, true);
            budgetMasterResponse.getData().forEach(budgetMaster -> {
                DynamicFieldViewModel field = getDynamicFieldViewModel(budgetMaster, StringConstants.toCamelCase(budgetMaster.getName()));
                fields.add(field);
            });
        } else {
            DocumentSubgroupViewModel subgroup = documentSubgroupService.getByDocumentContainerId(request.getDocumentContainerId(), auth);

            // Parse the JSON
            String subgroupFieldsJson = subgroup.getSubgroupFieldsJson();
            extractedFields(auth, subgroupFieldsJson, fields, false);
        }

        responseViewModel.setFields(fields);
        return responseViewModel;
    }

    private void extractedFields(IAuthContextViewModel auth, String subgroupFieldsJson, List<DynamicFieldViewModel> fields, boolean isStaticView) {
        if(null == subgroupFieldsJson){
            logger.error("subgroup field json is null for company {}", auth.getCompanyCode());
            return;
        }

        JsonNode subgroupFields = parseJson(subgroupFieldsJson);

        JsonNode businessDetails = subgroupFields.get("businessDetails");

        if (businessDetails != null) {
            if (!isStaticView && !businessDetails.get("isSelected").asBoolean()) {
                return;
            }

            MultiBudgetMasterViewModel budgetMasterResponse = budgetMasterService.getAllBudgetMaster(auth, true);

            budgetMasterResponse.getData().forEach(budgetMaster -> {
                String fieldName = StringConstants.toCamelCase(budgetMaster.getName());

                // Check if businessDetails has a matching key
                if (businessDetails.has(fieldName)) {

                    int value = businessDetails.get(fieldName).asInt();
                    if (isStaticView) {
                        DynamicFieldViewModel field = getDynamicFieldViewModel(budgetMaster, fieldName);
                        fields.add(field);
                    } else
                        // Only consider fields with values 0 or 1 i.e. 'O' and 'M'
                        if (value == 0 || value == 2) {
                            DynamicFieldViewModel field = getDynamicFieldViewModel(budgetMaster, fieldName);
                            fields.add(field);
                        }
                }
            });
        }
    }

    private static DynamicFieldViewModel getDynamicFieldViewModel(BudgetMasterViewModel budgetMaster, String fieldName) {
        DynamicFieldViewModel field = new DynamicFieldViewModel();

        // Generate fieldName using budget master name - camelCase
        field.setFieldName(fieldName);

        // Set fieldLabel as the name of the budget master
        field.setFieldLabel(budgetMaster.getName());
        field.setComponentType("SELECT_BUTTON"); // static
        field.setId(budgetMaster.getId()); // master id
        return field;
    }

    private JsonNode parseJson(String jsonString) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readTree(jsonString);
        } catch (IOException e) {
            throw new RuntimeException("Failed to parse JSON", e);
        }
    }

    @Override
    public Map<String, Object> getBusinessDetailsFromJson(String dynamicFieldsJson) {
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> businessDetailsMap = new HashMap<>();
        if (null == dynamicFieldsJson) return businessDetailsMap;
        try {
            // Parse the dynamicFieldsJson string into a JsonNode
            JsonNode rootNode = objectMapper.readTree(dynamicFieldsJson);
            if (null == rootNode) {
                logger.info("getBusinessDetailsFromJson : rootNode is null");
                return businessDetailsMap;
            }
            JsonNode businessDetailsNode = rootNode.get("businessDetails");

            if (businessDetailsNode != null) {
                // Iterate over fields
                Iterator<Map.Entry<String, JsonNode>> fields = businessDetailsNode.fields();

                while (fields.hasNext()) {
                    Map.Entry<String, JsonNode> field = fields.next();
                    String fieldName = field.getKey();
                    JsonNode valueNode = field.getValue();

                    if (valueNode.isInt()) {
                        businessDetailsMap.put(fieldName, valueNode.asInt());
                    } else if (valueNode.isTextual()) {
                        businessDetailsMap.put(fieldName, valueNode.asText());
                    } else if (valueNode.isLong()) {
                        businessDetailsMap.put(fieldName, valueNode.asLong());
                    } else if (valueNode.isBoolean()) {
                        businessDetailsMap.put(fieldName, valueNode.asBoolean());
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to deserialize and map business details", e);
        }

        return businessDetailsMap;
    }

    @Override
    public Map<String, Object> getBusinessDetailsFromJsonForStaticView(String dynamicFieldsJson, IAuthContextViewModel auth) {

        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> businessDetailsMap = new HashMap<>();
        List<DynamicFieldViewModel> fields = new ArrayList<>();
        if (dynamicFieldsJson == null) return businessDetailsMap;

        try {
            JsonNode rootNode = objectMapper.readTree(dynamicFieldsJson);
            if (rootNode == null) {
                logger.info("getBusinessDetailsFromJson: rootNode is null");
                return businessDetailsMap;
            }

            JsonNode businessDetailsNode = rootNode.get("businessDetails");
            if (businessDetailsNode != null) {
                // Extract the relevant field names and budget master IDs
                extractedFields(auth, dynamicFieldsJson, fields, true);

                // Loop through each field and fetch the corresponding values
                Iterator<Map.Entry<String, JsonNode>> fieldsIterator = businessDetailsNode.fields();
                while (fieldsIterator.hasNext()) {
                    Map.Entry<String, JsonNode> field = fieldsIterator.next();
                    String fieldName = field.getKey();
                    JsonNode valueNode = field.getValue();

                    // Get the corresponding field object to find the budgetMasterId
                    DynamicFieldViewModel dynamicField = fields.stream()
                            .filter(f -> f.getFieldName().equals(fieldName))
                            .findFirst()
                            .orElse(null);

                    if (dynamicField != null) {
                        long budgetMasterId = dynamicField.getId();
                        long valueId = valueNode.asLong();

                        // Fetch the description for the corresponding budgetMasterId
                        Optional<BudgetSyncMaster> budgetSyncMaster = budgetSyncMasterRepository
                                .findByCompanyCodeAndBudgetMasterIdAndIdAndIsActive(
                                        auth.getCompanyCode(), budgetMasterId, valueId, true,
                                        Sort.by(Sort.Direction.ASC, "description")
                                );

                        String description = budgetSyncMaster
                                .map(BudgetSyncMaster::getDescription)
                                .orElse("NA");

                        businessDetailsMap.put(fieldName, description);
                    } else {
                        // Handle null or non-integer values
                        businessDetailsMap.put(fieldName, valueNode.isNull() ? "NA" : valueNode.asText());
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to deserialize and map business details", e);
        }
        return businessDetailsMap;
    }

    @Override
    public DynamicFormViewResponseViewModel getDynamicFormDataForTypeAndEntity(IAuthContextViewModel auth, String type, Long documentApprovalContainerId) {
        // Section details
        DynamicFormViewResponseViewModel responseViewModel = new DynamicFormViewResponseViewModel();
        responseViewModel.setSectionName("Business Details");
        responseViewModel.setSelected(true);
        responseViewModel.setSectionSelector("businessDetails");

        List<DynamicFieldViewModel> fields = new ArrayList<>();
        DocumentType documentType = DocumentType.valueOf(null != type ? type.toUpperCase() : "");
        String dynamicFieldsJson = "";
        switch (documentType) {
            case PURCHASE_ORDER -> {
                PurchaseOrderHeader purchaseOrderHeader = purchaseOrderHeaderRepository.findByDocumentApprovalContainerId(documentApprovalContainerId)
                        .orElseThrow(() -> new RecordNotFoundException(
                                String.format("Could not find the PurchaseOrderHeader with documentApprovalContainerId: %d and Company Code: %d", documentApprovalContainerId, auth.getCompanyCode())));
                //TODO: move this item level once requirement and development is confirmed
                dynamicFieldsJson = purchaseOrderHeader.getDynamicFieldsJson();
            }
            case INVOICE -> {
                InvoiceHeader invoiceHeader = invoiceHeaderRepository.findByDocumentApprovalContainerId(documentApprovalContainerId)
                        .orElseThrow(() -> new RecordNotFoundException(
                                String.format("Could not find the InvoiceHeader with documentApprovalContainerId: %d and Company Code: %d", documentApprovalContainerId, auth.getCompanyCode())));
                //TODO: move this item level once requirement and development is confirmed
                dynamicFieldsJson = invoiceHeader.getDynamicFieldsJson();
            }
            default ->
                    logger.error("Unknown document type with documentApprovalContainerId {}", documentApprovalContainerId);
        }
        // Parse the JSON
        extractedFields(auth, dynamicFieldsJson, fields, true);
        responseViewModel.setFields(fields);
        return responseViewModel;
    }
}
