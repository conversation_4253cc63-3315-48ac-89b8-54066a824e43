package in.taxgenie.pay_expense_pvv.services.implementations;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.taxgenie.utils.api.MultiServiceClient;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.company.GstinDetail;
import in.taxgenie.pay_expense_pvv.entities.erp.ERPGrn;
import in.taxgenie.pay_expense_pvv.entities.erp.MessageMaster;
import in.taxgenie.pay_expense_pvv.entities.erp.POGrnMapping;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.exceptions.BudgetConsumptionException;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.factory.budgetaction.DocumentData;
import in.taxgenie.pay_expense_pvv.govrndto.InvoiceDTO;
import in.taxgenie.pay_expense_pvv.govrndto.TranDtlsDTO;
import in.taxgenie.pay_expense_pvv.invoice.mapper.InvoiceHeaderMapper;
import in.taxgenie.pay_expense_pvv.invoice.repository.IInvoiceHeaderRepository;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentRepository;
import in.taxgenie.pay_expense_pvv.repositories.IPaymentRepository;
import in.taxgenie.pay_expense_pvv.repositories.company.IGstinRepository;
import in.taxgenie.pay_expense_pvv.repositories.erp.IERPGrnRepository;
import in.taxgenie.pay_expense_pvv.repositories.erp.IMessageMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.erp.IPOGrnMappingRepository;
import in.taxgenie.pay_expense_pvv.repositories.masters.IMasterDataJsonStoreRepository;
import in.taxgenie.pay_expense_pvv.repositories.po.IPurchaseOrderHeaderRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.*;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetService;
import in.taxgenie.pay_expense_pvv.services.interfaces.consumption.IConsumptionService;
import in.taxgenie.pay_expense_pvv.services.interfaces.dynamic.form.IDynamicFormService;
import in.taxgenie.pay_expense_pvv.services.interfaces.po.IPurchaseOrderService;
import in.taxgenie.pay_expense_pvv.utils.*;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.EmployeeGlDetailUpdateContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.cem.lookup.EmployeeGlDetailUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.erp.*;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice.InvoiceWrapper;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice.SyncInvoiceToERPResponseViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.MasterDataForERPViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.SyncPOToERPResponseViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ErpIntegrationServiceImplementation implements IErpIntegrationService {
    private final IDocumentApprovalContainerRepository documentApprovalContainerRepository;
    private final IDocumentApprovalContainerRepository reportRepository;
    private final IDocumentRepository documentRepository;
    private final IPaymentRepository paymentAdviceRepository;
    private final IEmployeeMasterDataService employeeMasterDataService;
    private final IInvoiceHeaderRepository iInvoiceHeaderRepository;
    private final IPurchaseOrderHeaderRepository purchaseOrderHeaderRepository;
    private final IMasterDataJsonStoreRepository iMasterDataJsonStoreRepository;
    private final IMessageMasterRepository messageMasterRepository;
    private final IEmailNotificationService emailNotificationService;
    private final IDynamicFormService dynamicFormService;
    private final IBudgetService budgetService;
    private final IDocumentApprovalContainerApproverService documentApprovalContainerApproverService;
    private final IDocumentApprovalContainerUserService documentApprovalContainerUserService;
    private final IErpStatusInjectionService erpStatusInjectionService;
    private final IConsumptionService consumptionService;
    private final IGstinRepository gstinRepository;
    private final IERPGrnRepository erpGrnRepository;
    private final IPOGrnMappingRepository grnMappingRepository;
    private final IInvoiceTatReportService iInvoiceTatReportService;
    private final IPurchaseOrderService purchaseOrderService;
    private final MultiServiceClient webClientHelper;
    private final MultiServiceClientFactory multiServiceClientFactory;
    private final ObjectMapper objectMapper;

    private final Logger logger;

    public ErpIntegrationServiceImplementation(
            IDocumentApprovalContainerRepository documentApprovalContainerRepository, IDocumentApprovalContainerRepository reportRepository, IDocumentRepository documentRepository,
            IPaymentRepository paymentAdviceRepository,
            IEmployeeMasterDataService employeeMasterDataService,
            IEmailNotificationService emailNotificationService, IInvoiceHeaderRepository iInvoiceHeaderRepository, IPurchaseOrderHeaderRepository iPurchaseOrderHeaderRepository,
            IMasterDataJsonStoreRepository iMasterDataJsonStoreRepository, IMessageMasterRepository messageMasterRepository,
            IDynamicFormService dynamicFormService, IBudgetService budgetService, IDocumentApprovalContainerApproverService documentApprovalContainerApproverService, IDocumentApprovalContainerUserService documentApprovalContainerUserService, IErpStatusInjectionService erpStatusInjectionService, IConsumptionService consumptionService, IGstinRepository gstinRepository, IERPGrnRepository erpGrnRepository, IPOGrnMappingRepository grnMappingRepository, IInvoiceTatReportService iInvoiceTatReportService, IPurchaseOrderService purchaseOrderService,
            MultiServiceClientFactory multiServiceClientFactory, ObjectMapper objectMapper) {
        this.documentApprovalContainerRepository = documentApprovalContainerRepository;
        this.reportRepository = reportRepository;
        this.documentRepository = documentRepository;
        this.paymentAdviceRepository = paymentAdviceRepository;
        this.employeeMasterDataService = employeeMasterDataService;
        this.emailNotificationService = emailNotificationService;
        this.iInvoiceHeaderRepository = iInvoiceHeaderRepository;
        this.purchaseOrderHeaderRepository = iPurchaseOrderHeaderRepository;
        this.iMasterDataJsonStoreRepository = iMasterDataJsonStoreRepository;
        this.messageMasterRepository = messageMasterRepository;
        this.dynamicFormService = dynamicFormService;
        this.budgetService = budgetService;
        this.documentApprovalContainerApproverService = documentApprovalContainerApproverService;
        this.documentApprovalContainerUserService = documentApprovalContainerUserService;
        this.erpStatusInjectionService = erpStatusInjectionService;
        this.consumptionService = consumptionService;
        this.gstinRepository = gstinRepository;
        this.erpGrnRepository = erpGrnRepository;
        this.grnMappingRepository = grnMappingRepository;
        this.iInvoiceTatReportService = iInvoiceTatReportService;
        this.purchaseOrderService = purchaseOrderService;
        this.webClientHelper = multiServiceClientFactory.createMultiServiceClient(WebClient.builder());
        this.multiServiceClientFactory = multiServiceClientFactory;
        this.objectMapper = objectMapper;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }


    @Override
    public List<ExpenseReportErpViewModel> getReportsForPosting(IAuthContextViewModel auth) {
        logger.trace("getReportsForPosting: Preparing all the accepted but pending (at ERP) reports");
        return reportRepository
                .getReportsForErpProcessing(auth.getCompanyCode(), ReportStatus.ACCEPTED)
                .stream()
                .filter(r -> {
                    boolean status = true;

                    for (Document e : r.getDocuments()) {
                        if (StaticDataRegistry.isNullOrEmptyOrWhitespace(e.getDocumentSubgroup().getGlAccountCode())) {
                            logger.trace("getReportsForPosting: Excluding report id: {} because expense {} does not have GL code", r.getId(), e.getId());
                            status = false;
                        }
                    }

                    return status;
                })
                .map(r -> {
                    ExpenseReportErpViewModel viewModel = getReportErpViewModel(r);
                    viewModel.setLineItems(r.getDocuments().stream().map(this::getExpenseLineViewModel).collect(Collectors.toList()));
                    return viewModel;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void processTransmissionAcknowledgement(ErpInwardAcknowledgementContainerViewModel container, IAuthContextViewModel auth) {
        logger.trace("processTransmissionAcknowledgement: Processing inward acknowledgement from ERP");
        for (ErpInwardAcknowledgementLineViewModel line : container.getLines()) {
            Optional<DocumentApprovalContainer> reportOptional =
                    reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), line.getReportId());

            if (reportOptional.isPresent()) {
                logger.trace("processTransmissionAcknowledgement: Marking report: {} as acknowledged by the ERP", line.getReportId());
                DocumentApprovalContainer report = reportOptional.get();
                report.setAcknowledgedByErp(true);
                report.setErpAcknowledgementTimestamp(ZonedDateTime.now());

                reportRepository.saveAndFlush(report);
            }
        }
    }

    @Override
    public void processGlPostingAcknowledgement(ErpGlPostingContainerViewModel container, IAuthContextViewModel auth) {
        logger.trace("processGlPostingAcknowledgement: Processing GL posting at ERP");
        List<ReportStatus> reportStatus = new ArrayList<>();
        reportStatus.add(ReportStatus.PAID);
        reportStatus.add(ReportStatus.POSTED_TO_DESTINATION_SYSTEM);
        reportStatus.add(ReportStatus.DRAFT);

        for (ErpGlPostingLineViewModel line : container.getLines()) {
            Optional<DocumentApprovalContainer> reportOptional =
                    reportRepository.getReportsForErpPosting(auth.getCompanyCode(), line.getReportId(), reportStatus);

            if (reportOptional.isPresent()) {
                logger.trace("processGlPostingAcknowledgement: Marking report: {} as posted to GL at the ERP", line.getReportId());
                DocumentApprovalContainer report = reportOptional.get();
                report.setPostedToGl(true);
                report.setReportStatus(ReportStatus.POSTED_TO_DESTINATION_SYSTEM);
                report.setGlPostingDate(line.getPostingDate());
                report.setGlDocumentReference(line.getGlDocumentReference());

                reportRepository.saveAndFlush(report);

                emailNotificationService.transmitPostedNotification(report, auth);
            }
        }
    }

    @Override
    public void processPayments(ErpPaymentContainerViewModel container, IAuthContextViewModel auth) {
        logger.trace("processPayments: Processing payment advices from ERP");
        boolean sendEmail = true;

        for (ErpPaymentLineViewModel line : container.getLines()) {
            Optional<DocumentApprovalContainer> reportOptional =
                    reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), line.getReportId());

            if (reportOptional.isPresent()) {
                logger.trace("processPayments: Payment advice added to report: {} for amount {} ", line.getReportId(), line.getPaidAmount());
                DocumentApprovalContainer report = reportOptional.get();

                Payment advice = new Payment();
                advice.setCompanyCode(auth.getCompanyCode());
                advice.setErpCompCode(line.getCompanyCode());
                advice.setDocumentApprovalContainerId(line.getReportId());
                advice.setDocumentApprovalContainer(report);
                advice.setPaymentDate(line.getPaymentDate());
                advice.setCreatedTimestamp(ZonedDateTime.now());
                advice.setRemarks(line.getRemarks());
                advice.setPaidAmount(line.getPaidAmount());
                advice.setTdsAmount(line.getTdsAmount());
                advice.setPaymentReference(line.getPaymentReference());

                if (line.isFullPayment()) {
                    logger.trace("processPayments: Report {} is fully paid", line.getReportId());
                    report.setPaidStatus(PaidStatus.FULLY_PAID);
                } else {
                    BigDecimal paidAmount = report.getTotalPaidAmount();
                    BigDecimal claimAmount = report.getApprovalContainerClaimAmount();
                    BigDecimal currentAmount = line.getPaidAmount();

                    BigDecimal totalAmountRounded = paidAmount.add(currentAmount).setScale(0, RoundingMode.CEILING);

                    // Math.ceil(paidAmount + currentAmount) >= Math.ceil(claimAmount)
                    if (totalAmountRounded.compareTo(claimAmount.setScale(0, RoundingMode.CEILING)) >= 0) {
                        logger.trace("processPayments: Payment of report {} is now fully paid", line.getReportId());
                        report.setPaidStatus(PaidStatus.FULLY_PAID);
                    }

                    logger.trace("processPayments: Report {} is partially paid", line.getReportId());
                    report.setPaidStatus(PaidStatus.PARTIALLY_PAID);
                }

                Optional<Payment> paymentOptional = paymentAdviceRepository.findFirstByCompanyCodeAndDocumentApprovalContainerIdOrderByCreatedTimestampDesc(auth.getCompanyCode(), line.getReportId());
                if (paymentOptional.isEmpty()) {
                    System.out.println(" In TRUE");
                    sendEmail = true;
                } else {
                    if (paymentOptional.get().getPaymentReference().equalsIgnoreCase(line.getPaymentReference()) &&
                            paymentOptional.get().getPaidAmount() == line.getPaidAmount()) {
                        System.out.println(" FALSE");
                        sendEmail = false;
                    }
                }

                report.setTotalPaidAmount(report.getTotalPaidAmount().add(line.getPaidAmount()));
                report.setTotalTdsAmount(report.getTotalTdsAmount().add(line.getTdsAmount()));
                report.setReportStatus(ReportStatus.PAID);
                report.setPaymentDate(line.getPaymentDate());

                if (sendEmail) {
                    System.out.println(" IN SEND EMAiL");
                    paymentAdviceRepository.saveAndFlush(advice);
                    reportRepository.saveAndFlush(report);
                    emailNotificationService.transmitPaidNotification(report, auth);
                }

                //Create tat entry for invoice approve
                if (report.getDocumentType().equals(DocumentType.INVOICE)) {
                    if (!iInvoiceTatReportService.create(ApproverType.PAYER, report.getId(), auth)) {
                        logger.info("Failed to create Invoice TAT Report for approver");
                    }
                }
            }
        }
    }

    @Override
    public void processEmployeeGlDetailUpdate(EmployeeGlDetailUpdateContainerViewModel container, IAuthContextViewModel auth) {
        for (EmployeeGlDetailUpdateViewModel viewModel : container.getList()) {
            List<DocumentApprovalContainer> reportsByEmployee = reportRepository.getByCompanyCode(auth.getCompanyCode())
                    .stream()
                    .filter(r -> r.getEmployeeCode().equals(viewModel.getEmployeeSystemIdCode()) && !r.isPostedToGl())
                    .collect(Collectors.toList());

            for (DocumentApprovalContainer report : reportsByEmployee) {
                if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(report.getEmployeeGlMainAccountCode()) &&
                        StaticDataRegistry.isNotNullOrEmptyOrWhitespace(report.getEmployeeProfitCenter()) &&
                        StaticDataRegistry.isNotNullOrEmptyOrWhitespace(report.getEmployeeCostCenter()) &&
                        StaticDataRegistry.isNotNullOrEmptyOrWhitespace(report.getDimension02()) &&
                        !viewModel.isModified()
                ) {
                    continue;
                }

                report.setEmployeeGlMainAccountCode(viewModel.getGlVendorCode());
                report.setEmployeeProfitCenter(viewModel.getProfitCenterCode());
                report.setEmployeeCostCenter(viewModel.getCostCenterCode());
                report.setDimension02(viewModel.getSegmentCode());

                reportRepository.saveAndFlush(report);
            }
        }

        employeeMasterDataService.updateEmployeeGlDetails(container, auth);
    }

    @Override
    @Transactional
    public SyncInvoiceToERPResponseViewModel getInvoiceForERPSync(IAuthContextViewModel auth) {
        logger.trace("getInvoiceForERPSync: Getting invoices to send to ERP");
        SyncInvoiceToERPResponseViewModel responseViewModel = new SyncInvoiceToERPResponseViewModel();
        try {
            List<InvoiceWrapper> invoicesToSendToERP = new ArrayList<>();
            Pageable pageable = PageRequest.of(0, 10);

            logger.info("fetching invoices with AcknowledgedByErpIsFalse - batch size {}", pageable.getPageSize());
            List<InvoiceHeader> invoices = iInvoiceHeaderRepository.findInvoicesPendingForSyncToERP(pageable);

            logger.info("prepare response to send to ERP ");
            invoicesToSendToERP = invoices.stream()
                    .map(invoiceHeader -> {
                        InvoiceDTO invoiceDTO = InvoiceHeaderMapper.mapToInvoiceDTO(invoiceHeader);

                        // Set the ISD Value via GSTIN
                        if (invoiceHeader.getBuyerGstin() != null && invoiceHeader.getBuyerGstinId() != null) {
                            Optional<GstinDetail> gstinDetailBuyerOpt = gstinRepository.findById(invoiceHeader.getBuyerGstinId());

                            // Sanity checks
                            if (gstinDetailBuyerOpt.isEmpty()) {
                                logger.error("erp-sync invoice - gstinDetailBuyerOpt - This invoice {} contains an invalid Buyer GSTIN", invoiceDTO.getDocDtls().getNo());
                            }
                            GstinDetail gstinDetailBuyer = gstinDetailBuyerOpt.get();
                            if (!gstinDetailBuyer.getGstinNo().equals(invoiceHeader.getBuyerGstin().getGstinNo())) {
                                logger.error("erp-sync invoice - gstinDetailBuyer - This invoice {} contains an invalid Buyer GSTIN", invoiceDTO.getDocDtls().getNo());
                            }

                            // Extracting the data
                            TranDtlsDTO currentTransactionDetails = invoiceDTO.getTranDtls();
                            currentTransactionDetails.setIsIsd(gstinDetailBuyer.getGstinType().equals(GstinType.ISD));
                            invoiceDTO.setTranDtls(currentTransactionDetails);

                        } else {
                            logger.error("erp-sync invoice - gstinDetailBuyer - This invoice {} does not contain either Buyer Gstin or Buyer Gstin id", invoiceDTO.getDocDtls().getNo());
                        }

                        invoiceDTO.getItemList().forEach(i -> {
                            if (i.getHsnCode() == null) {
                                logger.error("erp-sync invoice - itemDTO - This invoice {} contains an item without an Hsn", invoiceDTO.getDocDtls().getNo());
                            } else {
                                List<MasterDataForERPViewModel> mastersWithJson = getMasterDataForERPViewModels(invoiceHeader.getDynamicFieldsJson());
                                // Set the mastersWithJson in the corresponding ItemDTO
                                i.setMasters(mastersWithJson);
                            }
                        });
                        return new InvoiceWrapper(invoiceDTO);
                    })
                    .toList();
            responseViewModel.setInvoices(invoicesToSendToERP);

            // Update acknowledgedByErp to true for each invoice
            logger.info("Increment Sync count");
            invoices.forEach(invoice -> {
                DocumentApprovalContainer dac = invoice.getDocument().getDocumentApprovalContainer();
                if (dac != null) {
                    invoice.setPriority(1);// processed once; default 0 -> higher precedence
                    int syncAttempt = invoice.getSyncAttempt() == null ? 0 : invoice.getSyncAttempt();
                    invoice.setSyncAttempt(syncAttempt + 1);
//                    dac.setSentToErp(true); -- check this
                    dac.setAcknowledgedByErp(true);
                    reportRepository.save(dac);
                }
            });
        } catch (Exception e) {
            logger.error("Exception occurred in getInvoiceForERPSync {} ", e.getLocalizedMessage());
            throw new RuntimeException("Exception occurred in getInvoiceForERPSync ");
        }

        return responseViewModel;
    }

    @Override
    @Transactional
    public SyncPOToERPResponseViewModel getPOForERPSync(IAuthContextViewModel auth) {
        logger.trace("getPOForERPSync: Getting Purchase Orders to send to ERP");
        SyncPOToERPResponseViewModel responseViewModel = new SyncPOToERPResponseViewModel();
        try {
            List<PurchaseOrderCreateViewModel> purchaseOrderCreateViewModels = new ArrayList<>();

            Pageable pageable = PageRequest.of(0, 10);

            logger.info("fetching po with AcknowledgedByErpIsFalse - batch size {}", pageable.getPageSize());
            List<PurchaseOrderHeader> purchaseOrderHeaders = purchaseOrderHeaderRepository.findPOPendingForSyncToERP(pageable);

            logger.info("prepare response to send to ERP ");

            purchaseOrderCreateViewModels = purchaseOrderHeaders.stream()
                    .map(purchaseOrderHeader -> {
                        DocumentApprovalContainer documentApprovalContainer = purchaseOrderHeader.getDocument().getDocumentApprovalContainer();
                        logger.info("getPOForERPSync: Processing Document with documentApprovalContainerId =  "+documentApprovalContainer.getId());
                        // Sanity check in case of any data inconsistency (if unrequired can be removed later)
                        purchaseOrderService.performApprovalContainerAndDocumentContainerSantityCheck(documentApprovalContainer, documentApprovalContainer.getId());

                        Document document = purchaseOrderService.getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks(auth.getCompanyCode(), documentApprovalContainer.getId());
                        PurchaseOrderHeader poHeader = purchaseOrderService.getPOHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks(auth.getCompanyCode(), document);

                        List<MasterDataForERPViewModel> mastersWithJson = getMasterDataForERPViewModels(purchaseOrderHeader.getDynamicFieldsJson());
                        PurchaseOrderCreateViewModel purchaseOrderCreateViewModel = purchaseOrderService.preparePODetailsResponseData(documentApprovalContainer, poHeader, document, null, auth);

                        // Set the mastersWithJson in the corresponding PO
                        purchaseOrderCreateViewModel.getItemDetails().forEach(poItemView -> {
                            if (poItemView.getHsn() == null) {
                                logger.error("erp-sync PO - itemDTO - This PO {} contains an item without an Hsn", purchaseOrderHeader.getId());
                            } else {
                                poItemView.setMasters(mastersWithJson);
                            }
                        });
                        return purchaseOrderCreateViewModel;

                    }).toList();

            responseViewModel.setPos(purchaseOrderCreateViewModels);


            // Update acknowledgedByErp to true for each invoice
            logger.info("Increment Sync count");
            purchaseOrderHeaders.forEach(purchaseOrderHeader -> {
                DocumentApprovalContainer dac = purchaseOrderHeader.getDocument().getDocumentApprovalContainer();
                if (dac != null) {
                    purchaseOrderHeader.setPriority(1);// processed once; default 0 -> higher precedence
                    int syncAttempt = purchaseOrderHeader.getSyncAttempt() == null ? 0 : purchaseOrderHeader.getSyncAttempt();
                    purchaseOrderHeader.setSyncAttempt(syncAttempt + 1);
//                    dac.setSentToErp(true); -- set on reverse feed
                    reportRepository.save(dac);
                }
            });
        } catch (Exception e) {
            logger.error("Exception occurred in getPOForERPSync {} ", e.getLocalizedMessage());
            throw new RuntimeException("Exception occurred in getPOForERPSync ");
        }

        return responseViewModel;
    }

    private List<MasterDataForERPViewModel> getMasterDataForERPViewModels(String purchaseOrderHeader) {
        Map<String, Object> masters = dynamicFormService.getBusinessDetailsFromJson(purchaseOrderHeader);
        // prepare List<MasterDataForERPViewModel> mastersWithJson
        List<MasterDataForERPViewModel> mastersWithJson = masters.entrySet().stream()
                .map(entry -> {
                    MasterDataForERPViewModel masterData = new MasterDataForERPViewModel();
                    String key = entry.getKey(); // e.g., "branch"
                    try {
                        if (entry.getValue() != null) {
                            Integer syncMasterId = (Integer) entry.getValue();
                            Optional<MasterDataJsonStore> jsonStoreOptional = iMasterDataJsonStoreRepository.findJsonStoreBySyncMasterId(syncMasterId.longValue());
                            if (jsonStoreOptional.isPresent()) {
                                masterData.setType(key);
                                // Get the raw json and set as part of the final master json.
                                masterData.setBody(jsonStoreOptional.get().getBody());
                            } else {
                                logger.error("getInvoiceForERPSync: The linked generic master with Type: {} does not have a json linked to it.", key);
                                masterData.setType(key);
                                masterData.setBody(null);
                            }
                        }
                    } catch (Exception e) {
                        logger.error("getInvoiceForERPSync: There was an error processing the master json for entry {}", entry);
                    }

                    return masterData;
                })
                .collect(Collectors.toList());
        return mastersWithJson;
    }

    @Override
    @Transactional
    public ErpReverseFeedResponseViewModel processPOReverseFeed(List<ERPPurchaseOrderData> requestViewModelList, IAuthContextViewModel auth) {
        ErpReverseFeedResponseViewModel responseViewModel = new ErpReverseFeedResponseViewModel();

        logger.info("Processing PO reverse feed from ERP to P2P");
        logger.info("Request Data: {}", requestViewModelList);

        responseViewModel.setCompanyCode(auth.getCompanyCode());
        responseViewModel.setType(StringConstants.ERPConstants.PO_SYNC);
        responseViewModel.setStatus(StringConstants.ERPConstants.IN_PROCESS);
//        responseViewModel.setGstin(requestViewModel.getGstinNo());
        try {
            // Create CEM Excel history and set Reference ID
            responseViewModel.setReferenceID(createCemExcelHistory(requestViewModelList, StringConstants.ERPConstants.PO_SYNC, StringConstants.ERPConstants.IN_PROCESS, auth));
            String message = updatePODetails(requestViewModelList, auth);
            responseViewModel.setStatus(StringConstants.ERPConstants.PROCESSED);

            ERPPurchaseOrderDataRequest request = new ERPPurchaseOrderDataRequest();
            request.setData(requestViewModelList);
            updateCemExcelHistory(request, StringConstants.ERPConstants.PO_SYNC, responseViewModel.getReferenceID(), message.contains("Error") ? "Error" : "Success", auth);

        } catch (Exception e) {
            logger.error("Error during reverse feed processing: {}", e.getMessage(), e);
            responseViewModel.setStatus(StringConstants.ERPConstants.ERROR);
        }

        return responseViewModel;
    }

    @Override
    @Transactional
    public ErpReverseFeedResponseViewModel processReverseFeed(MessageMasterRequestViewModel requestViewModel, IAuthContextViewModel auth) {
        ErpReverseFeedResponseViewModel responseViewModel = new ErpReverseFeedResponseViewModel();

        logger.info("Processing PO Message Master for reverse feed from ERP to P2P");
        logger.info("Request Data: {}", requestViewModel);

        responseViewModel.setCompanyCode(auth.getCompanyCode());
        responseViewModel.setType(StringConstants.ERPConstants.MESSAGE_MASTER);
        responseViewModel.setStatus(StringConstants.ERPConstants.IN_PROCESS);
        responseViewModel.setGstin(requestViewModel.getGstinNo());

        try {
            // Create CEM Excel history and set Reference ID
            responseViewModel.setReferenceID(createCemExcelHistory(requestViewModel, StringConstants.ERPConstants.PO_SYNC, StringConstants.ERPConstants.IN_PROCESS, auth));

            List<MessageMasterViewModel> data = requestViewModel.getData();
            if (data == null || data.isEmpty()) {
                logger.warn("No data found in the request for processing.");
                return responseViewModel;
            }
            String message = updateDocumentStatus(data, auth);

            responseViewModel.setStatus(StringConstants.ERPConstants.PROCESSED);
            updateCemExcelHistory(requestViewModel, StringConstants.ERPConstants.MESSAGE_MASTER, responseViewModel.getReferenceID(), message, auth);

        } catch (Exception e) {
            logger.error("Error during reverse feed processing: {}", e.getMessage(), e);
            responseViewModel.setStatus(StringConstants.ERPConstants.ERROR);
        }

        return responseViewModel;
    }

    private String updateGRNDetails(List<ERPGrnViewModel> grnList, PurchaseOrderHeader purchaseOrderHeader,
                                    IAuthContextViewModel auth) {
        logger.info("Inside updateGRNDetails on ERP PO reverse feed");
        StringBuilder message = new StringBuilder();
        List<POGrnMapping> poGrnMappingsToSave = new ArrayList<>();
        try {
            for (ERPGrnViewModel requestViewModel : grnList) {

                Optional<ERPGrn> existingGrn = erpGrnRepository.findByGrnNumberAndCompanyCode(requestViewModel.getGrnNumber(), auth.getCompanyCode());
                if (existingGrn.isEmpty()) {
                    POGrnMapping poGrnMapping = new POGrnMapping();

                    ERPGrn grn = ERPGrn.mapToEntity(requestViewModel);
                    grn.setCompanyCode(auth.getCompanyCode());
                    erpGrnRepository.save(grn);

                    poGrnMapping.setErpGrn(grn);
                    poGrnMapping.setErpGrnId(grn.getId());
                    poGrnMapping.setPurchaseOrderHeader(purchaseOrderHeader);
                    poGrnMapping.setPurchaseOrderHeaderId(purchaseOrderHeader.getId());
                    poGrnMapping.setCreatedAt(DateTimeUtils.getCurrentTimestamp());

                    poGrnMappingsToSave.add(poGrnMapping);
                }else{
                    logger.warn("GRN entry is already present with grnNumber {}",existingGrn.get().getGrnNumber());
                }
            }
            grnMappingRepository.saveAll(poGrnMappingsToSave);
        } catch (Exception e) {
            logger.error("Error during reverse feed processing: {}", e.getMessage(), e);
            message.append("Error during reverse feed processing: ").append(e.getMessage());
        }
        return message.toString();

    }

    private String updatePODetails(List<ERPPurchaseOrderData> requestViewModelList, IAuthContextViewModel auth) {
        logger.info("Inside updatePODetails on ERP PO reverse feed");
        StringBuilder message = new StringBuilder();
        for (ERPPurchaseOrderData requestViewModel : requestViewModelList) {
            ERPPurchaseOrderHeaderViewModel poHeaderViewModel = requestViewModel.getPurchaseOrderHeaderViewModel();

            if (requestViewModel.getPurchaseOrderHeaderViewModel() == null) {
                message.append("Error - No data found in the request for processing.");
                logger.warn(message.toString());
                return message.toString();
            }
            String documentIdentifier = poHeaderViewModel.getDocumentIdentifier();
            if (documentIdentifier != null) {
                documentIdentifier = documentIdentifier.trim();
            }
            List<DocumentApprovalContainer> documentApprovalContainerList = reportRepository
                    .findByCompanyCodeAndDocumentIdentifier(auth.getCompanyCode(), documentIdentifier);
            try {

                DocumentApprovalContainer documentApprovalContainer = CommonUtility.getSingleRecordOrThrow(
                        documentApprovalContainerList,
                        "No document found for identifier %s in company %s",
                        "Multiple documents found for identifier %s in company %s",
                        documentIdentifier,
                        auth.getCompanyCode()
                );

                Document document = documentApprovalContainer.getDocuments().get(0);
                PurchaseOrderHeader poHeader = document.getPoHeader();

                document.setDocNo(poHeaderViewModel.getOrderNumber());
                poHeader.setPurchasingDocTypeFromErp(poHeaderViewModel.getPurchasingDocType());

                poHeader.setPoDate(DateTimeUtils.parseDatetoLocalDate(poHeaderViewModel.getOrderCreationDate()));
                poHeader.setOrderValidFrom(DateTimeUtils.parseDatetoLocalDate(poHeaderViewModel.getOrderValidFrom()));
                poHeader.setOrderValidTo(DateTimeUtils.parseDatetoLocalDate(poHeaderViewModel.getOrderValidTo()));

                // update dac
                if (poHeaderViewModel.getPoReleaseIndicator() != null && poHeaderViewModel.getPoReleaseIndicator().equalsIgnoreCase(StaticDataRegistry.PO_RELEASE_INDICATOR)) {
                    documentApprovalContainer.setReportStatus(ReportStatus.RELEASED);
                } else {
                    documentApprovalContainer.setReportStatus(ReportStatus.POSTED_TO_DESTINATION_SYSTEM);
                }
                documentApprovalContainer.setAcknowledgedByErp(true);
                documentApprovalContainer.setUpdatedTimestamp(ZonedDateTime.now());

                updateGRNDetails(requestViewModel.getGrnList(), poHeader, auth);

                logger.info("Saving PO reverse feed from ERP for dac {}", documentApprovalContainer.getId());
                documentRepository.save(document);
                purchaseOrderHeaderRepository.save(poHeader);
                documentApprovalContainerRepository.save(documentApprovalContainer);
                message.append(StringConstants.SUCCESS);
                poHeaderViewModel.setStatus(StringConstants.SUCCESS);
            } catch (Exception e) {
                logger.error("Error on saving PO reverse feed from ERP for identifier {}", documentIdentifier);
                message.append(StringConstants.FAILURE);
                poHeaderViewModel.setStatus(StringConstants.FAILURE);
                poHeaderViewModel.setReason("Error on saving PO reverse feed from ERP for identifier " + documentIdentifier);
            }
        }
        return message.toString();
    }

    private String updateDocumentStatus(List<MessageMasterViewModel> requestViewModelList, IAuthContextViewModel auth) {
        logger.info("Inside Message Master on ERP PO reverse feed");

        StringBuilder message = new StringBuilder();

        Map<String, List<MessageMasterViewModel>> docIdToMessagesMap = requestViewModelList.stream()
                .collect(Collectors.groupingBy(MessageMasterViewModel::getDocumentIdentifier));

        for (Map.Entry<String, List<MessageMasterViewModel>> entry : docIdToMessagesMap.entrySet()) {

            StringBuilder notificationMessageBuilder = new StringBuilder();
            String notificationCode = entry.getValue().get(0).getNotificationCode();

            if (entry.getKey() == null) {
                message.append("Failure - No doc_id found in the request for processing.");
                logger.warn(message.toString());
                return message.toString();
            }
            String documentIdentifier = entry.getKey().trim();
            try {
                List<DocumentApprovalContainer> documentApprovalContainerList = reportRepository
                        .findByCompanyCodeAndDocumentIdentifier(auth.getCompanyCode(), documentIdentifier);

                DocumentApprovalContainer documentApprovalContainer = CommonUtility.getSingleRecordOrThrow(
                        documentApprovalContainerList,
                        "No document found for identifier %s in company %s",
                        "Multiple documents found for identifier %s in company %s",
                        documentIdentifier,
                        auth.getCompanyCode()
                );
                for (MessageMasterViewModel viewModel : entry.getValue()) {
                    saveMessageMaster(viewModel, documentApprovalContainer.getId(), notificationMessageBuilder);
                }
                String statusMessage = "";
                if (documentApprovalContainer.getDocumentType() == DocumentType.INVOICE) {
                    // Invoice message master
                    statusMessage = updateERPStatusToDocument(notificationCode, documentApprovalContainer, notificationMessageBuilder.toString(), auth);
                } else {
                    // mark error if PO is not already in posted or released state
                    if (documentApprovalContainer.getReportStatus() != ReportStatus.POSTED_TO_DESTINATION_SYSTEM && documentApprovalContainer.getReportStatus() != ReportStatus.RELEASED && documentApprovalContainer.getReportStatus() != ReportStatus.VALIDATION_FAILED) {
                        // added check if PO doc is in accepted status then only process - because from ERP same doc reprocessing issue for validation error
                        statusMessage = updateERPStatusToDocumentForPO(notificationCode, documentApprovalContainer, notificationMessageBuilder.toString());
                    }else{
                        logger.info("Document not in Accepted state (i.e. can be either posted, relesed or validation error for doc_id {}", documentIdentifier);
                    }
                }
                message.append(statusMessage);
            } catch (Exception e) {
                logger.error("Error on saving message master reverse feed from ERP for identifier {}", documentIdentifier);
                message.append(StringConstants.FAILURE);
            }
        }

        if (message.toString().contains(StringConstants.FAILURE))
            return StringConstants.FAILURE;
        else
            return StringConstants.SUCCESS;
    }

    @Override
    @Transactional
    public ErpReverseFeedResponseViewModel processPaymentDetails(PaymentDetailsRequestViewModel requestViewModel, IAuthContextViewModel auth) {

        logger.info("Process payment details sync ERP to P2P");
        logger.info("Request Data: {}", requestViewModel);

        ErpReverseFeedResponseViewModel responseViewModel = new ErpReverseFeedResponseViewModel();
        responseViewModel.setCompanyCode(auth.getCompanyCode());
        responseViewModel.setType(StringConstants.ERPConstants.PAYMENT_DETAILS);
        responseViewModel.setStatus(StringConstants.ERPConstants.IN_PROCESS);
//        responseViewModel.setGstin(requestViewModel.getGstinNo());
        try {

            responseViewModel.setReferenceID(createCemExcelHistory(requestViewModel.getPaymentDetails(), StringConstants.ERPConstants.PAYMENT_DETAILS, StringConstants.ERPConstants.PROCESSED, auth));

            String message = markPaymentDetails(requestViewModel, auth); // TODO: As message will have status from multiple invoices - discuss what will be the message.

            responseViewModel.setStatus(StringConstants.ERPConstants.PROCESSED);
            // Generate reference ID for CEM Excel history
            responseViewModel.setReferenceID(updateCemExcelHistoryForPaymentDetails(requestViewModel, StringConstants.ERPConstants.PAYMENT_DETAILS, responseViewModel.getReferenceID(), message.contains("Error") ? "Error" : "Success", auth));


        } catch (Exception e) {
            logger.error("Error during reverse feed payment details sync: {}", e.getMessage(), e);
            responseViewModel.setStatus(StringConstants.ERPConstants.ERROR);
        }

        return responseViewModel;

    }

    private String markPaymentDetails(PaymentDetailsRequestViewModel requestViewModel, IAuthContextViewModel auth) {
        StringBuilder message = new StringBuilder();
        for (PaymentsDetailsViewModel paymentDetail : requestViewModel.getPaymentDetails()) {

            List<DocumentApprovalContainer> documentApprovalContainerList = reportRepository
                    .findByCompanyCodeAndDocumentIdentifier(auth.getCompanyCode(), paymentDetail.getDocId());

            DocumentApprovalContainer documentApprovalContainer = CommonUtility.getSingleRecordOrThrow(
                    documentApprovalContainerList,
                    "No document found for identifier %s in company %s",
                    "Multiple documents found for identifier %s in company %s",
                    paymentDetail.getDocId(),
                    auth.getCompanyCode()
            );
            if (documentApprovalContainer != null) {
                Payment payment = null;
                if (DocumentType.INVOICE.equals(documentApprovalContainer.getDocumentType()) &&
                        !ReportStatus.PAID.equals(documentApprovalContainer.getReportStatus())) {

                    Optional<Payment> paymentOptional = paymentAdviceRepository.findByCompanyCodeAndDocumentApprovalContainerId(auth.getCompanyCode(), documentApprovalContainer.getId());
                    payment = paymentOptional.orElseGet(Payment::new);
                    payment.setCompanyCode(auth.getCompanyCode());
                    payment.setPaymentDate(DateTimeUtils.convertToLocalDate(paymentDetail.getUtrdate()));
                    payment.setPaymentReference(paymentDetail.getUtrNumber());
                    payment.setErpCompCode(paymentDetail.getCompanyCode()); //?
                    payment.setPaidAmount(paymentDetail.getPaymentAmount());
                    payment.setTdsAmount(paymentDetail.getTds());
                    payment.setRemarks("Payment processed");
                    paymentDetail.setStatus("Success");
                    payment.setCreatedTimestamp(ZonedDateTime.now());
                    payment.setDocumentApprovalContainerId(documentApprovalContainer.getId());
                    documentApprovalContainer.setPaymentDate(payment.getPaymentDate());

                    paymentAdviceRepository.save(payment);

                    // Update Report Status to PAID
                    documentApprovalContainer.setReportStatus(ReportStatus.PAID);
                    documentApprovalContainerRepository.save(documentApprovalContainer);

                    try {
                        // Invoices that are paid should add their time to the Payment TAT Report
                        if (paymentOptional.isEmpty()) {
                            if (!iInvoiceTatReportService.create(ApproverType.PAYER, documentApprovalContainer.getId(), auth)) {
                                logger.info("Failed to create Invoice TAT Report for PAYER");
                                paymentDetail.setReason("Failed to create Invoice TAT Report for PAYER");
                            }
                        }
                        logger.info("handling budget consumption for id : " + documentApprovalContainer.getId());
                        if (paymentOptional.isEmpty()) {
                            handleBudgetConsumption(documentApprovalContainer);
                        }
                    } catch (Exception e) {
                        logger.error("Exception occurred while handling budget consumption for id : " + documentApprovalContainer.getId() + " reason: " + e.getMessage() + ", Marked as Paid");
                        String errorMessage = "Payment processed with Error on handling budget consumption " + e.getMessage();
                        if (paymentDetail.getReason() != null)
                            paymentDetail.setReason(paymentDetail.getReason() + ", " + errorMessage);
                        else
                            paymentDetail.setReason(errorMessage);
                    }
                    message.append("Success");
                } else {
                    // TODO : handle other document type
                    String reason = "Received Document is of type " + documentApprovalContainer.getDocumentType().name() + " with status " + documentApprovalContainer.getReportStatus().name();
                    paymentDetail.setStatus("Failure");
                    // assumed the reason as already paid
                    paymentDetail.setReason(reason + ", Invoice is already marked as paid");
                    logger.warn(reason);
                    message.append("Error");
                }
            }
        }
        return message.toString();
    }

    private <T> String createCemExcelHistory(T requestObject, String type, String status, IAuthContextViewModel auth) {
        try {
            ExcelHistoryRequestViewModel excelHistoryRequest = createExcelHistoryRequest(requestObject, type, status, auth);
            ExcelHistoryResponseViewModel cemResponse = saveToCemExcelHistory(excelHistoryRequest, auth);
            return cemResponse != null ? cemResponse.getReferenceID() : null;
        } catch (Exception e) {
            logger.error("Error creating CEM Excel history: {}", e.getMessage(), e);
            return null;
        }
    }

    private <T> ExcelHistoryRequestViewModel createExcelHistoryRequest(T requestObject, String type, String status, IAuthContextViewModel auth) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        ExcelHistoryRequestViewModel excelHistoryRequest = new ExcelHistoryRequestViewModel();
        excelHistoryRequest.setType(type);
        excelHistoryRequest.setTemplate(StringConstants.ERPConstants.TEMPLATE);
        excelHistoryRequest.setCompanyCode(auth.getCompanyCode());
        excelHistoryRequest.setSourceJson(objectMapper.writeValueAsString(requestObject));
        excelHistoryRequest.setStatus(status);
        return excelHistoryRequest;
    }

    private void saveMessageMaster(MessageMasterViewModel viewModel, Long documentApprovalId, StringBuilder notificationMessageBuilder) {
        MessageMaster entity = new MessageMaster();
        BeanUtils.copyProperties(viewModel, entity);
        entity.setDocReferenceId(documentApprovalId);
        notificationMessageBuilder.append(viewModel.getNotificationMessage()).append(System.lineSeparator());
        logger.info("Saving MessageMaster entity...");
        messageMasterRepository.save(entity);
    }

    @Transactional
    private String updateERPStatusToDocument(String notificationCode, DocumentApprovalContainer documentApprovalContainer, String notificationMessage, IAuthContextViewModel auth) {
        String message = null;

        try {
            logger.info("updateERPStatusToDocument: Processing notification code: {} for document: {}",
                       notificationCode, documentApprovalContainer.getId());

            documentApprovalContainer.setErpRemarks(notificationMessage);

            switch (notificationCode) {
                case StringConstants.ERPConstants.VALIDATION_FAILED -> {
                    message = handleValidationFailedFlow(documentApprovalContainer, auth);
                }
                case StringConstants.ERPConstants.PARKED -> {
                    documentApprovalContainer.setReportStatus(ReportStatus.PARKED);
                }
                case StringConstants.ERPConstants.POSTED -> {
                    documentApprovalContainer.setReportStatus(ReportStatus.POSTED_TO_DESTINATION_SYSTEM);
                }
                case StringConstants.ERPConstants.CANCELLED, StringConstants.ERPConstants.DELETED -> {
                    documentApprovalContainer.setReportStatus(switch (notificationCode) {
                        case StringConstants.ERPConstants.CANCELLED -> ReportStatus.CANCELLED;
                        case StringConstants.ERPConstants.DELETED -> ReportStatus.DELETED;
                        default ->
                                throw new IllegalStateException("Unexpected value for notificationCode " + notificationCode + " in updateERPStatusToDocument for id : " + documentApprovalContainer.getId());
                    });
                    handleBudgetConsumptionWithErrorHandling(documentApprovalContainer);
                }
                default -> {
                    logger.warn("Unhandled notification code: {} for document: {}", notificationCode, documentApprovalContainer.getId());
                    throw new IllegalStateException("Unexpected notification code: " + notificationCode + " for document: " + documentApprovalContainer.getId());
                }
            }

            // Only update timestamp and save if no error occurred
            if (message == null) {
                documentApprovalContainer.setUpdatedTimestamp(ZonedDateTime.now());
                documentApprovalContainerRepository.saveAndFlush(documentApprovalContainer);
                logger.info("updateERPStatusToDocument: Successfully processed notification code: {} for document: {}",
                           notificationCode, documentApprovalContainer.getId());
            }

        } catch (Exception e) {

            message = "Failed to update ERP status for document " + documentApprovalContainer.getId() +
                     " with notification code " + notificationCode + ". Reason: " + e.getMessage();

            logger.error("updateERPStatusToDocument: Error processing notification code: {} for document: {}. Error: {}",
                        notificationCode, documentApprovalContainer.getId(), e.getMessage(), e);
        }
        return message;
    }

    /**
     * Handles the complete validation failed flow with comprehensive error handling and rollback
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private String handleValidationFailedFlow(DocumentApprovalContainer documentApprovalContainer, IAuthContextViewModel auth) {
        String errorMessage = null;

        try {
            logger.info("handleValidationFailedFlow: Starting validation failed flow for document: {}",
                       documentApprovalContainer.getId());

            // Step 1: Validate document state
            validateDocumentStateForValidationFailed(documentApprovalContainer);

            // Step 2: Set status to VALIDATION_FAILED
            safelySetDocumentStatusToValidationFailed(documentApprovalContainer);

            // Step 3: Ensure ERP remarks are set (bulletproof - must not fail)
            safelyPrepareValidationErrorRemarks(documentApprovalContainer);

            // Step 4: Try to inject ERP status and create validation error state (optional)
            tryInjectValidationErrorStatus(documentApprovalContainer, auth);

            // Step 5: Handle budget consumption (continue processing even if it fails)
            tryHandleBudgetConsumption(documentApprovalContainer);

            logger.info("handleValidationFailedFlow: Successfully completed validation failed flow for document: {}",
                       documentApprovalContainer.getId());

        } catch (Exception e) {
            errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_FAILED_VALIDATION_FLOW,
                                        documentApprovalContainer.getId(), e.getMessage());

            logger.error("handleValidationFailedFlow: Error in validation failed flow for document: {}. Error: {}",
                        documentApprovalContainer.getId(), e.getMessage(), e);

            throw new RuntimeException(errorMessage, e);
        }

        return errorMessage;
    }

    /**
     * Validates that the document is in the correct state for validation failed flow
     */
    private void validateDocumentStateForValidationFailed(DocumentApprovalContainer documentApprovalContainer) {
        if (documentApprovalContainer.getReportStatus() != ReportStatus.ACCEPTED) {
            throw new DomainInvariantException("Document must be in ACCEPTED status for validation failed flow. Current status: " +
                                             documentApprovalContainer.getReportStatus());
        }
    }

    /**
     * Safely sets the document status to VALIDATION_FAILED (bulletproof - catches all exceptions)
     */
    private void safelySetDocumentStatusToValidationFailed(DocumentApprovalContainer documentApprovalContainer) {
        try {
            logger.info("safelySetDocumentStatusToValidationFailed: Setting document status to VALIDATION_FAILED for document: {}",
                       documentApprovalContainer.getId());
            documentApprovalContainer.setReportStatus(ReportStatus.VALIDATION_FAILED);
        } catch (Exception e) {
            logger.error("safelySetDocumentStatusToValidationFailed: Failed to set status to VALIDATION_FAILED for document: {}. Error: {}",
                        documentApprovalContainer.getId(), e.getMessage(), e);
            // Continue processing even if status update fails
        }
    }

    /**
     * Handles budget consumption for validation failed flow
     * Throws exceptions for any budget consumption failures
     */
    private void handleBudgetConsumptionForValidationFailed(DocumentApprovalContainer documentApprovalContainer) {
        try {
            logger.info("handleBudgetConsumptionForValidationFailed: Handling budget consumption for document: {}",
                       documentApprovalContainer.getId());
            handleBudgetConsumptionWithErrorHandling(documentApprovalContainer);
            logger.info("handleBudgetConsumptionForValidationFailed: Budget consumption successful for document: {}",
                       documentApprovalContainer.getId());

        } catch (BudgetConsumptionException budgetException) {
            // Handle budget missing case by appending remarks but still throwing exception
            if (budgetException.isBudgetMissingError()) {
                logger.warn("handleBudgetConsumptionForValidationFailed: Budget missing for document: {}. Appending BUDGET_REVERSAL_SKIPPED error.",
                           documentApprovalContainer.getId());
                appendBudgetReversalSkippedRemark(documentApprovalContainer, budgetException);
            }
            // Re-throw to be handled at higher level
            throw budgetException;

        } catch (Exception otherException) {
            // Wrap non-budget exceptions in BudgetConsumptionException for consistency
            logger.error("handleBudgetConsumptionForValidationFailed: Unexpected error during budget consumption for document: {}. Error: {}",
                        documentApprovalContainer.getId(), otherException.getMessage(), otherException);
            throw new BudgetConsumptionException(
                "Unexpected error during budget consumption: " + otherException.getMessage(),
                StaticDataRegistry.ERPConstants.BUDGET_CONSUMPTION_SYSTEM_ERROR,
                documentApprovalContainer.getId(),
                otherException
            );
        }
    }

    /**
     * Appends budget reversal skipped remark to document
     */
    private void appendBudgetReversalSkippedRemark(DocumentApprovalContainer documentApprovalContainer, BudgetConsumptionException budgetException) {
        String currentRemarks = documentApprovalContainer.getErpRemarks();
        String budgetSkippedMessage = String.format(StaticDataRegistry.ERPConstants.BUDGET_REVERSAL_SKIPPED_FORMAT,
                                                   budgetException.getErrorCode(), budgetException.getMessage());
        documentApprovalContainer.setErpRemarks(
            currentRemarks != null ? currentRemarks + StaticDataRegistry.ERPConstants.REMARKS_SEPARATOR + budgetSkippedMessage : budgetSkippedMessage
        );
    }

    /**
     * Safely prepares validation error remarks (bulletproof - catches all exceptions)
     */
    private void safelyPrepareValidationErrorRemarks(DocumentApprovalContainer documentApprovalContainer) {
        try {
            logger.info("safelyPrepareValidationErrorRemarks: Preparing validation error remarks for document: {}",
                       documentApprovalContainer.getId());

            String currentRemarks = documentApprovalContainer.getErpRemarks();
            String validationErrorMessage = StaticDataRegistry.ERPConstants.SENT_BACK_DUE_TO_VALIDATION_ERRORS;

            if (StringUtils.isNotNullOrEmpty(currentRemarks)) {
                documentApprovalContainer.setErpRemarks(validationErrorMessage +
                                                       StaticDataRegistry.ERPConstants.COLON_SPACE +
                                                       currentRemarks);
            } else {
                documentApprovalContainer.setErpRemarks(validationErrorMessage);
            }

            logger.info("safelyPrepareValidationErrorRemarks: Successfully set ERP remarks for document: {}",
                       documentApprovalContainer.getId());
        } catch (Exception e) {
            logger.error("safelyPrepareValidationErrorRemarks: Failed to set ERP remarks for document: {}. Error: {}",
                        documentApprovalContainer.getId(), e.getMessage(), e);
            // Continue processing even if remarks update fails
        }
    }

    /**
     * Tries to inject validation error status (returns success/failure but doesn't throw)
     */
    private void tryInjectValidationErrorStatus(DocumentApprovalContainer documentApprovalContainer, IAuthContextViewModel auth) {
        try {
            logger.info("tryInjectValidationErrorStatus: Attempting to inject ERP status for document: {}",
                       documentApprovalContainer.getId());

            erpStatusInjectionService.injectERPStatus(
                documentApprovalContainer.getId(),
                documentApprovalContainer.getErpRemarks(),
                ExpenseActionStatus.VALIDATION_ERROR,
                auth
            );

            logger.info("tryInjectValidationErrorStatus: Successfully injected ERP status for document: {}",
                       documentApprovalContainer.getId());

        } catch (Exception stateException) {
            logger.error("tryInjectValidationErrorStatus: Failed to inject ERP status for document: {}. Error: {}",
                        documentApprovalContainer.getId(), stateException.getMessage(), stateException);
        }
    }

    /**
     * Tries to handle budget consumption (returns success/failure but doesn't throw)
     */
    private void tryHandleBudgetConsumption(DocumentApprovalContainer documentApprovalContainer) {
        try {
            handleBudgetConsumptionForValidationFailed(documentApprovalContainer);
            logger.info("tryHandleBudgetConsumption: Budget consumption successful for document: {}",
                       documentApprovalContainer.getId());

        } catch (Exception budgetException) {
            logger.warn("tryHandleBudgetConsumption: Budget consumption failed for document: {}, but continuing with validation failed flow. Error: {}",
                       documentApprovalContainer.getId(), budgetException.getMessage());
        }
    }

    /**
     * Handles budget consumption with proper error handling and logging
     * @param documentApprovalContainer the document container
     * @throws BudgetConsumptionException if budget consumption fails
     */
    private void handleBudgetConsumptionWithErrorHandling(DocumentApprovalContainer documentApprovalContainer) {
        long documentId = documentApprovalContainer.getId();

        try {
            logger.info("handleBudgetConsumptionWithErrorHandling: Processing budget consumption for document: {}", documentId);

            List<DocumentData> documentDataList = documentApprovalContainerApproverService.getDocumentData(documentApprovalContainer);

            if (documentDataList == null || documentDataList.isEmpty()) {
                throw new BudgetConsumptionException(
                    StaticDataRegistry.ERPConstants.ERROR_NO_DOCUMENT_DATA,
                    StaticDataRegistry.ERPConstants.BUDGET_NO_DOCUMENT_DATA,
                    documentId
                );
            }

            Boolean success = budgetService.handleBudgetConsumption(documentDataList);

            if (success == null || !success) {
                throw new BudgetConsumptionException(
                    StaticDataRegistry.ERPConstants.ERROR_BUDGET_CONSUMPTION_FAILED,
                    StaticDataRegistry.ERPConstants.BUDGET_CONSUMPTION_FAILED,
                    documentId
                );
            }

            logger.info("handleBudgetConsumptionWithErrorHandling: Successfully processed budget consumption for document: {}", documentId);

        } catch (BudgetConsumptionException e) {
            // Re-throw our custom exception as-is
            logger.error("handleBudgetConsumptionWithErrorHandling: Budget consumption error for document: {}. Error: {}",
                        documentId, e.toString(), e);
            throw e;

        } catch (Exception e) {
            // Wrap other exceptions in our custom exception
            String errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_UNEXPECTED_BUDGET_CONSUMPTION, documentId);
            String errorCode = determineBudgetErrorCode(e);

            logger.error("handleBudgetConsumptionWithErrorHandling: Unexpected error for document: {}. Error: {}",
                        documentId, e.getMessage(), e);

            throw new BudgetConsumptionException(errorMessage, errorCode, documentId, e);
        }
    }

    /**
     * Determines the appropriate error code based on the exception
     */
    private String determineBudgetErrorCode(Exception e) {
        if (e == null || e.getMessage() == null) {
            return StaticDataRegistry.ERPConstants.BUDGET_UNKNOWN_ERROR;
        }

        String message = e.getMessage().toLowerCase();

        if (message.contains("budget") && (message.contains("not found") || message.contains("missing"))) {
            return StaticDataRegistry.ERPConstants.BUDGET_GENERIC_ERROR_NOT_FOUND;
        } else if (message.contains("budget") && message.contains("insufficient")) {
            return StaticDataRegistry.ERPConstants.BUDGET_INSUFFICIENT_FUNDS;
        } else if (message.contains("budget") && message.contains("locked")) {
            return StaticDataRegistry.ERPConstants.BUDGET_LOCKED_ERROR;
        } else if (message.contains("budget") && message.contains("expired")) {
            return StaticDataRegistry.ERPConstants.BUDGET_EXPIRED_ERROR;
        } else if (message.contains("budget")) {
            return StaticDataRegistry.ERPConstants.BUDGET_GENERIC_ERROR;
        } else {
            return StaticDataRegistry.ERPConstants.BUDGET_CONSUMPTION_SYSTEM_ERROR;
        }
    }

    private String updateERPStatusToDocumentForPO(String notificationCode, DocumentApprovalContainer documentApprovalContainer, String notificationMessage) {
        try {
            // released status will be marked in reverse-feed PO data
            documentApprovalContainer.setErpRemarks(notificationMessage);
            switch (notificationCode) {
                case StringConstants.ERPConstants.VALIDATION_FAILED ->{
                    documentApprovalContainer.setReportStatus(ReportStatus.VALIDATION_FAILED);
                }
                case StringConstants.ERPConstants.PARKED ->
                        documentApprovalContainer.setReportStatus(ReportStatus.PARKED);
                case StringConstants.ERPConstants.POSTED ->
                        documentApprovalContainer.setReportStatus(ReportStatus.POSTED_TO_DESTINATION_SYSTEM);
                case StringConstants.ERPConstants.CANCELLED ->
                        documentApprovalContainer.setReportStatus(ReportStatus.CANCELLED);
                case StringConstants.ERPConstants.DELETED ->
                        documentApprovalContainer.setReportStatus(ReportStatus.DELETED);
                default -> logger.warn("Unhandled notification code: {}", notificationCode);
            }

            documentApprovalContainer.setAcknowledgedByErp(true);
            documentApprovalContainer.setUpdatedTimestamp(ZonedDateTime.now());
            documentApprovalContainerRepository.save(documentApprovalContainer);

            // Only attempt DAC handling after status is persisted
            if (StringConstants.ERPConstants.VALIDATION_FAILED.equals(notificationCode)) {
                try {
                    // Step 1: handleDACAmountConsumption
                    handleDACAmountConsumption(documentApprovalContainer);

                    // Step 2: Get document data with validation
                    List<DocumentData> documentDataList = getDocumentDataWithValidation(documentApprovalContainer);

                    // Step 3: Handle budget consumption reversal
                    handleBudgetConsumptionForRevoke(documentDataList, documentApprovalContainer.getId());
                } catch (Exception e) {
                    logger.error("Handling DAC amounts or budget consumption failed post-persist for validation failed status for PO: {} reason -> {}",
                            documentApprovalContainer.getId(), e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("Error updating ERP status to document container: {}", documentApprovalContainer.getDocumentIdentifier(), e);
            return StringConstants.FAILURE;
        }
        return StringConstants.SUCCESS;
    }

    /**
     * Gets document data with validation for budget consumption reversal
     *
     * @param report the document approval container
     * @return list of document data
     * @throws RuntimeException if document data retrieval fails
     */
    private List<DocumentData> getDocumentDataWithValidation(DocumentApprovalContainer report) {
        try {
            logger.info("getDocumentDataWithValidation: Retrieving document data for document: {}", report.getId());

            List<DocumentData> documentDataList = documentApprovalContainerApproverService.getDocumentData(report);

            if (documentDataList == null || documentDataList.isEmpty()) {
                throw new RuntimeException(
                        String.format(StaticDataRegistry.ERPConstants.ERROR_NO_DOCUMENT_DATA_FOR_REVOKE, report.getId())
                );
            }

            logger.info("getDocumentDataWithValidation: Successfully retrieved {} document data items for document: {}",
                    documentDataList.size(), report.getId());
            return documentDataList;

        } catch (Exception e) {
            logger.error("getDocumentDataWithValidation: Failed to retrieve document data for document: {}. Error: {}",
                    report.getId(), e.getMessage(), e);
            throw new RuntimeException(
                    String.format(StaticDataRegistry.ERPConstants.ERROR_DOCUMENT_DATA_RETRIEVAL,
                            report.getId(), e.getMessage()),
                    e
            );
        }
    }

    /**
     * Handles budget consumption reversal with comprehensive error handling
     *
     * @param documentDataList the document data list
     * @param documentApprovalContainerId       the documentApprovalContainer ID for logging
     * @throws RuntimeException if budget consumption reversal fails
     */
    private void handleBudgetConsumptionForRevoke(List<DocumentData> documentDataList, long documentApprovalContainerId) {
        try {
            logger.info("handleBudgetConsumptionForRevoke: Processing budget consumption reversal for document: {}", documentApprovalContainerId);

            Boolean success = budgetService.handleBudgetConsumption(documentDataList);

            if (success == null) {
                throw new RuntimeException(
                        String.format(StaticDataRegistry.ERPConstants.ERROR_BUDGET_SERVICE_NULL_RESPONSE, documentApprovalContainerId)
                );
            }

            logger.info("handleBudgetConsumptionForRevoke: Budget consumption reversal successful for document: {}", documentApprovalContainerId);

        } catch (Exception e) {
            logger.error("handleBudgetConsumptionForRevoke: Budget consumption reversal failed for document: {}. Error: {}",
                    documentApprovalContainerId, e.getMessage(), e);

            // Determine specific error type for better error messages
            String errorMessage;
            if (e.getMessage() != null && e.getMessage().toLowerCase().contains("budget")) {
                errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_BUDGET_SYSTEM_DURING_REVERSAL, documentApprovalContainerId, e.getMessage());
            } else if (e.getMessage() != null && e.getMessage().toLowerCase().contains("database")) {
                errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_DATABASE_DURING_BUDGET_REVERSAL, documentApprovalContainerId, e.getMessage());
            } else {
                errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_UNEXPECTED_BUDGET_REVERSAL, documentApprovalContainerId, e.getMessage());
            }
            throw new RuntimeException(errorMessage, e);
        }
    }

    private void handleBudgetConsumption(DocumentApprovalContainer documentApprovalContainer) {
        List<DocumentData> documentDataList = documentApprovalContainerApproverService.getDocumentData(documentApprovalContainer);
        budgetService.handleBudgetConsumption(documentDataList);
    }
    /**
     * Handles consumption service reversal with comprehensive error handling
     * @param report the document approval container
     * @throws RuntimeException if consumption service reversal fails
     */
    private void handleDACAmountConsumption(DocumentApprovalContainer report) {
        try {
            logger.info("handleDACAmountConsumption: Processing consumption service reversal for document: {}", report.getId());

            consumptionService.handleConsumption(report);

            logger.info("handleDACAmountConsumption: Consumption service reversal successful for document: {}", report.getId());

        } catch (Exception e) {
            logger.error("handleDACAmountConsumption: Consumption service reversal failed for document: {}. Error: {}",
                    report.getId(), e.getMessage(), e);

            // Determine specific error type for better error messages
            String errorMessage;
            if (e.getMessage() != null && e.getMessage().toLowerCase().contains("consumption")) {
                errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_CONSUMPTION_SERVICE_REVERSAL, report.getId(), e.getMessage());
            } else if (e.getMessage() != null && e.getMessage().toLowerCase().contains("database")) {
                errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_DATABASE_DURING_CONSUMPTION_REVERSAL, report.getId(), e.getMessage());
            } else {
                errorMessage = String.format(StaticDataRegistry.ERPConstants.ERROR_UNEXPECTED_CONSUMPTION_REVERSAL, report.getId(), e.getMessage());
            }
            throw new RuntimeException(errorMessage, e);
        }
    }
    private void updateCemExcelHistory(Object requestViewModel, String type, String referenceId, String errorMessage, IAuthContextViewModel auth) {
        try {
            ExcelHistoryRequestViewModel excelHistoryRequest = createExcelHistoryRequest(requestViewModel, type,
                    (StringUtils.isNullOrEmpty(errorMessage) || errorMessage.equalsIgnoreCase(StringConstants.SUCCESS)) ? StringConstants.ERPConstants.PROCESSED : StringConstants.ERPConstants.ERROR, auth);

            ExcelHistoryReportViewModel report = new ExcelHistoryReportViewModel();
            report.setRefId(referenceId);
            report.setStatus(excelHistoryRequest.getStatus());
            report.setType(type);

            if (requestViewModel instanceof MessageMasterRequestViewModel) {
                report.setReport(getReport(((MessageMasterRequestViewModel) requestViewModel).getData(),
                        report.getStatus().equals(StringConstants.ERPConstants.ERROR) ? StringConstants.FAILURE : StringConstants.SUCCESS, errorMessage));
            } else if (requestViewModel instanceof ERPPurchaseOrderDataRequest) {
                report.setReport(getReport(((ERPPurchaseOrderDataRequest) requestViewModel).getData(),
                        report.getStatus().equals(StringConstants.ERPConstants.ERROR) ? StringConstants.FAILURE : StringConstants.SUCCESS, errorMessage));

            }

            excelHistoryRequest.setReport(objectMapper.writeValueAsString(report));
            excelHistoryRequest.setReferenceID(referenceId);
            excelHistoryRequest.setStatus(report.getStatus());
            excelHistoryRequest.setUploadEnd(new Date());

            updateToCemExcelHistory(excelHistoryRequest, auth);
        } catch (Exception e) {
            logger.error("Error updating CEM Excel history: {}", e.getMessage(), e);
        }
    }

    private <T extends BaseReportViewModel> List<T> getReport(List<T> items, String status, String reason) {
        return items.stream().peek(view -> {
            view.setStatus(status);
            view.setReason(reason);
        }).toList();
    }

    private String updateCemExcelHistoryForPaymentDetails(PaymentDetailsRequestViewModel requestViewModel, String type, String referenceId, String errorMessage, IAuthContextViewModel auth) {
        try {
            ExcelHistoryRequestViewModel excelHistoryRequest = createExcelHistoryRequest(requestViewModel, type, errorMessage.contains("Error") ? StringConstants.ERPConstants.PROCESSED_AND_ERROR : StringConstants.ERPConstants.PROCESSED, auth);
            ExcelHistoryReportViewModel report = new ExcelHistoryReportViewModel();
            report.setRefId(referenceId);
            report.setStatus(excelHistoryRequest.getStatus());
            report.setType(type);
            report.setReport(requestViewModel.getPaymentDetails());

            ObjectMapper objectMapper = new ObjectMapper();
            excelHistoryRequest.setReport(objectMapper.writeValueAsString(report));
            excelHistoryRequest.setReferenceID(referenceId);
            excelHistoryRequest.setStatus(report.getStatus());
            excelHistoryRequest.setUploadEnd(new Date());

            updateToCemExcelHistory(excelHistoryRequest, auth);
        } catch (Exception e) {
            logger.error("Error updating CEM Excel history: {}", e.getMessage(), e);
        }
        return referenceId;
    }


    private ExcelHistoryResponseViewModel saveToCemExcelHistory(ExcelHistoryRequestViewModel requestViewModel, IAuthContextViewModel auth) {
        ExcelHistoryResponseViewModel responseViewModel = new ExcelHistoryResponseViewModel();
        try {
            String cemResponse = webClientHelper.makeRequest(ExternalServicesIdentifier.CEM_SERVICE.ordinal(),
                    HttpMethod.POST,
                    UrlConstants.CREATE_EXCEL_HISTORY,
                    Optional.of(requestViewModel),
                    null,
                    null,
                    String.class,
                    auth.getToken()).block();

            responseViewModel = objectMapper.readValue(cemResponse, ExcelHistoryResponseViewModel.class);
        } catch (Exception e) {
            logger.error(String.format("There was an exception in sendToCemExcelHistory %n: %s", e));
        }
        return responseViewModel;
    }

    private ExcelHistoryReportViewModel updateToCemExcelHistory(ExcelHistoryRequestViewModel requestViewModel, IAuthContextViewModel auth) {
        ExcelHistoryReportViewModel responseViewModel = new ExcelHistoryReportViewModel();
        try {
            String cemResponse = webClientHelper.makeRequest(ExternalServicesIdentifier.CEM_SERVICE.ordinal(),
                    HttpMethod.PUT,
                    UrlConstants.UPDATE_EXCEL_HISTORY,
                    Optional.of(requestViewModel),
                    null,
                    null,
                    String.class,
                    auth.getToken()).block();

            responseViewModel = objectMapper.readValue(cemResponse, ExcelHistoryReportViewModel.class);
        } catch (Exception e) {
            logger.error(String.format("There was an exception in updateToCemExcelHistory %n: %s", e));
        }
        return responseViewModel;
    }

    private ExpenseReportErpViewModel getReportErpViewModel(DocumentApprovalContainer report) {
        ExpenseReportErpViewModel viewModel = new ExpenseReportErpViewModel();
        viewModel.setDocumentIdentifier(report.getDocumentIdentifier());
        viewModel.setCompanyCode(report.getCompanyCode());
        viewModel.setEmployeeGlMainAccountCode(report.getEmployeeGlMainAccountCode());
        viewModel.setEmployeeGlSubAccountCode(report.getEmployeeGlSubAccountCode());
        viewModel.setReportCgstAmount(report.getApprovalContainerCgstAmount());
        viewModel.setReportClaimAmount(report.getApprovalContainerClaimAmount());
        viewModel.setReportSgstAmount(report.getApprovalContainerSgstAmount());
        viewModel.setReportIgstAmount(report.getApprovalContainerIgstAmount());
        viewModel.setReportTaxableAmount(report.getApprovalContainerTaxableAmount());
        viewModel.setReportId(report.getId());
        viewModel.setEmployeeIdCode(report.getEmployeeCode());
        viewModel.setEmployeeBranch(report.getEmployeeBranch());
        viewModel.setEmployeeCostCenter(report.getEmployeeCostCenter());
        viewModel.setEmployeeDepartment(report.getEmployeeDepartment());
        viewModel.setEmployeeType(report.getEmployeeType());
        viewModel.setEmployeeGrade(report.getEmployeeGrade());
        viewModel.setEmployeeProfitCenter(report.getEmployeeProfitCenter());
        viewModel.setValue03(report.getValue03());
        viewModel.setDimension01(report.getDimension01());
        viewModel.setDimension02(report.getDimension02());
        viewModel.setDimension03(report.getDimension03());
        viewModel.setDimension04(report.getDimension04());
        viewModel.setDimension05(report.getDimension05());
        viewModel.setDimension06(report.getDimension06());
        viewModel.setDimension07(report.getDimension07());
        viewModel.setDimension08(report.getDimension08());
        viewModel.setDimension09(report.getDimension09());
        viewModel.setDimension10(report.getDimension10());

        return viewModel;
    }

    private ExpenseLineErpViewModel getExpenseLineViewModel(Document document) {
        ExpenseLineErpViewModel viewModel = new ExpenseLineErpViewModel();
        viewModel.setExpenseId(document.getId());
        viewModel.setCgstAmount(document.getCgstAmount());
        viewModel.setExpenseDate(document.getDocumentDate());
        viewModel.setExpenseType(document.getDocumentApprovalContainer().getDocumentMetadata().getDocumentType());
        viewModel.setExpenseGroup(document.getDocumentApprovalContainer().getDocumentMetadata().getDocumentGroup());
        viewModel.setExpenseSubgroup(document.getDocumentSubgroup().getDocumentSubgroup());
        viewModel.setReportId(document.getDocumentApprovalContainerId());
        viewModel.setGlAccountCode(document.getDocumentSubgroup().getGlAccountCode());
        viewModel.setGstin(document.getGstin());
        viewModel.setClaimAmount(document.getClaimAmount());
        viewModel.setIgstRate(document.getIgstRate());
        viewModel.setIgstAmount(document.getIgstAmount());
        viewModel.setCgstRate(document.getCgstRate());
        viewModel.setSgstRate(document.getSgstRate());
        viewModel.setSgstAmount(document.getSgstAmount());
        viewModel.setTaxableAmount(document.getTaxableAmount());

        return viewModel;
    }
}
