package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.MatchConfigurationData;
import in.taxgenie.pay_expense_pvv.exceptions.MatchConfigurationException;
import in.taxgenie.pay_expense_pvv.repositories.IMatchConfigurationRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IMatchConfigurationService;
import in.taxgenie.pay_expense_pvv.viewmodels.match.MatchConfigurationViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MatchConfigurationServiceImplementation implements IMatchConfigurationService {
    
    private final IMatchConfigurationRepository matchConfigurationRepository;
    private final Logger logger;
    
    public MatchConfigurationServiceImplementation(IMatchConfigurationRepository matchConfigurationRepository) {
        this.matchConfigurationRepository = matchConfigurationRepository;
        this.logger = LoggerFactory.getLogger(MatchConfigurationServiceImplementation.class);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<MatchConfigurationViewModel> getMatchConfigurations(IAuthContextViewModel auth) {
        logger.trace("getMatchConfigurations: Fetching match configurations for company: {}", auth.getCompanyCode());
        
        List<MatchConfigurationData> configurations = matchConfigurationRepository.findByCompanyCode(auth.getCompanyCode());
        logger.debug("getMatchConfigurations: Found {} match configurations for company: {}", 
                configurations.size(), auth.getCompanyCode());
        
        List<MatchConfigurationViewModel> viewModels = configurations.stream()
                .map(MatchConfigurationData::toViewModel)
                .collect(Collectors.toList());
        
        logger.info("getMatchConfigurations: Successfully retrieved {} match configurations for company: {}", 
                viewModels.size(), auth.getCompanyCode());
        
        return viewModels;
    }
    
    @Override
    @Transactional
    public MatchConfigurationViewModel getMatchConfigurationById(Long id, IAuthContextViewModel auth) {
        logger.trace("getMatchConfigurationById: Fetching match configuration with ID: {} for company: {}", 
                id, auth.getCompanyCode());
        
        MatchConfigurationData configuration = matchConfigurationRepository.findById(id)
                .orElseThrow(() -> {
                    logger.error("getMatchConfigurationById: Match configuration with ID: {} not found", id);
                    return MatchConfigurationException.notFound(id);
                });
        
        // Verify company access
        if (!configuration.getCompanyCode().equals(auth.getCompanyCode())) {
            logger.error("getMatchConfigurationById: Access denied for match configuration ID: {} - " +
                    "requested by company: {}, owned by company: {}", 
                    id, auth.getCompanyCode(), configuration.getCompanyCode());
            throw MatchConfigurationException.accessDenied(auth.getCompanyCode());
        }
        
        logger.info("getMatchConfigurationById: Successfully retrieved match configuration with ID: {}", id);
        return configuration.toViewModel();
    }
    
    @Override
    @Transactional
    public MatchConfigurationViewModel createMatchConfiguration(
            MatchConfigurationData matchConfiguration, 
            IAuthContextViewModel auth) {
        
        logger.trace("createMatchConfiguration: Creating new match configuration for company: {}", 
                auth.getCompanyCode());
        
        // Set audit fields
        matchConfiguration.setCompanyCode(auth.getCompanyCode());
        matchConfiguration.setCreatedTimestamp(ZonedDateTime.now());
        matchConfiguration.setCreatingUserId(auth.getUserId());
        matchConfiguration.setUpdatedTimestamp(ZonedDateTime.now());
        matchConfiguration.setUpdatingUserId(auth.getUserId());
        
        MatchConfigurationData savedConfiguration = matchConfigurationRepository.save(matchConfiguration);
        
        logger.info("createMatchConfiguration: Successfully created match configuration with ID: {} for company: {}", 
                savedConfiguration.getId(), auth.getCompanyCode());
        
        return savedConfiguration.toViewModel();
    }

    @Override
    @Transactional(readOnly = true)
    public List<MatchConfigurationViewModel> getMatchConfigurationsByIs2WayMatch(Boolean is2WayMatch, IAuthContextViewModel auth) {
        logger.trace("getMatchConfigurationsByIs2WayMatch: Fetching match configurations for company: {} with is2WayMatch: {}",
                auth.getCompanyCode(), is2WayMatch);

        List<MatchConfigurationData> configurations = matchConfigurationRepository.findByCompanyCodeAndIs2WayMatch(
                auth.getCompanyCode(), is2WayMatch);
        logger.debug("getMatchConfigurationsByIs2WayMatch: Found {} match configurations for company: {} with is2WayMatch: {}",
                configurations.size(), auth.getCompanyCode(), is2WayMatch);

        List<MatchConfigurationViewModel> viewModels = configurations.stream()
                .map(MatchConfigurationData::toViewModel)
                .collect(Collectors.toList());

        logger.info("getMatchConfigurationsByIs2WayMatch: Successfully retrieved {} match configurations for company: {} with is2WayMatch: {}",
                viewModels.size(), auth.getCompanyCode(), is2WayMatch);

        return viewModels;
    }
}
