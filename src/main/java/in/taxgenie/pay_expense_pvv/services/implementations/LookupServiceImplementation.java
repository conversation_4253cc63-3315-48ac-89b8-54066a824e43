package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.entities.LookupData;
import in.taxgenie.pay_expense_pvv.invoice.repository.ICountriesRepository;
import in.taxgenie.pay_expense_pvv.invoice.repository.IPortsRepository;
import in.taxgenie.pay_expense_pvv.invoice.repository.IStateCodeRepo;
import in.taxgenie.pay_expense_pvv.repositories.ILookupRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.ILookupService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class LookupServiceImplementation implements ILookupService {
    private final ILookupRepository lookupRepository;
    private final ICountriesRepository countriesRepository;
    private final IPortsRepository portsRepository;
    private final IStateCodeRepo stateCodeRepository;
    private final Logger logger;

    public LookupServiceImplementation(ILookupRepository lookupRepository, ICountriesRepository countriesRepository, IPortsRepository portsRepository, IStateCodeRepo stateCodeRepository) {
        this.lookupRepository = lookupRepository;
        this.countriesRepository = countriesRepository;
        this.portsRepository = portsRepository;
        this.stateCodeRepository = stateCodeRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public LookupViewModel lookupById(Integer id) {
        logger.info("LookupServiceImplementation: Performing lookup for id: {}", id);
        return lookupRepository.findById(id)
                .map(lookupData -> {
                    logger.info("LookupServiceImplementation: Found for id: {}", id);
                    return getViewModel(lookupData);
                })
                .orElseGet(() ->{
                    logger.error("LookupServiceImplementation: Record could not be found for id {}", id);
                    return null;
                });
    }

    @Override
    public List<LookupViewModel> lookupByType(String type) {
        logger.info(String.format("LookupServiceImplementation: Performing lookup for type: %s", type));
        return lookupRepository.findByType(type)
                .stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public Optional<LookupViewModel> lookUpByTypeAndValue(String type, String value) {
        logger.info(String.format("LookupServiceImplementation: Performing lookup for type and value : %s", type, value));
        return lookupRepository.findByTypeAndValue(type, value).map(this::getViewModel);

    }

    @Override
    public List<LookupViewModel> getCountries() {
        logger.info(String.format("LookupServiceImplementation: Get countries data"));

        return countriesRepository.findAll()
                .stream()
                .map(entity -> getViewModel(entity, LookupViewModel.class))
                .collect(Collectors.toList());
    }
    @Override
    public LookupData lookupByAttribute(String value) {
        logger.info(String.format("LookupServiceImplementation: Performing lookup for attribute: %s", value));
        return lookupRepository.findByAttribute(value);

    }

    @Override
    public List<LookupViewModel> getFinancialYears() {
        List<LookupViewModel> financialYears = new ArrayList<>();
        // Calculate the start and end year of the current financial year
        int financialYearStart = DateTimeUtils.getFinancialYearStart(LocalDate.now());

        // current and next two financial years
        for (int i = -1; i <= 2; i++) {
            LookupViewModel viewModel = getLookupViewModel(financialYearStart, i);
            financialYears.add(viewModel);
        }
        return financialYears;
    }

    private static LookupViewModel getLookupViewModel(int financialYearStart, int i) {
        int startYear = financialYearStart + i;
        int endYear = startYear + 1;
        String financialYear = startYear + "-" + endYear;

        LookupViewModel viewModel = new LookupViewModel();
        viewModel.setType("FinancialYear");
        viewModel.setValue(financialYear);
        return viewModel;
    }

    @Override
    public List<LookupViewModel> getPorts() {
        logger.info(String.format("LookupServiceImplementation: Get ports data"));
        return portsRepository.findAll()
                .stream()
                .map(entity -> getViewModel(entity, LookupViewModel.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<LookupViewModel> getStateCodes() {
        logger.info(String.format("LookupServiceImplementation: Get state codes data"));
        return stateCodeRepository.findAll()
                .stream()
                .map(entity -> getViewModel(entity, LookupViewModel.class))
                .collect(Collectors.toList());
    }

    private LookupViewModel getViewModel(Object entity) {
        LookupViewModel viewModel = new LookupViewModel();
        BeanUtils.copyProperties(entity, viewModel);
        return viewModel;
    }

    private <E, V> V getViewModel(E entity, Class<V> viewModelClass) {
        V viewModel = null;
        try {
            viewModel = viewModelClass.newInstance();
            BeanUtils.copyProperties(entity, viewModel);
        } catch (InstantiationException | IllegalAccessException e) {
            logger.error(String.format("Error in getViewModel: %s",entity.getClass().getName()));
        }
        return viewModel;
    }
}
