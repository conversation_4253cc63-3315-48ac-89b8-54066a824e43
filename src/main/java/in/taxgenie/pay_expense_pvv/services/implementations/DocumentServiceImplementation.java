package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cem.employee.interfaces.IEmployeeViewModel;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.*;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmployeeMasterDataService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IFileIOService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ValidationStatusViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ExpenseViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.ReportLimitConsumptionViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Month;
import java.time.Period;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DocumentServiceImplementation implements IDocumentService {
    private final IDocumentRepository expenseRepository;
    private final IDocumentApprovalContainerRepository reportRepository;
    private final IDocumentRuleRepository ruleRepository;
    private final IDocumentSubgroupRepository subgroupRepository;
    private final IEmployeeMasterDataService employeeService;
    private final ILocationRepository locationRepository;
    private final IMetadataLimitRuleRepository metadataLimitRuleRepository;
    private final IFileIOService fileIOService;
    private final Logger logger;

    public DocumentServiceImplementation(IDocumentRepository expenseRepository, IDocumentApprovalContainerRepository reportRepository,
                                         IDocumentRuleRepository ruleRepository, IDocumentSubgroupRepository subgroupRepository,
                                         IEmployeeMasterDataService employeeService, ILocationRepository locationRepository,
                                         IMetadataLimitRuleRepository metadataLimitRuleRepository, IFileIOService fileIOService) {
        this.expenseRepository = expenseRepository;
        this.reportRepository = reportRepository;
        this.ruleRepository = ruleRepository;
        this.subgroupRepository = subgroupRepository;
        this.employeeService = employeeService;
        this.locationRepository = locationRepository;
        this.metadataLimitRuleRepository = metadataLimitRuleRepository;
        this.fileIOService = fileIOService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public ExpenseViewModel create(long reportId, long subgroupId, Long locationId, IAuthContextViewModel auth) {
        DocumentApprovalContainer report = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), reportId).orElseThrow(
                () -> new RecordNotFoundException(String.format("Could not find report with id: %d", reportId)));

        if (!isParentExpenseReportSubmittable(report)) {
            throw new DomainInvariantException(
                    "Parent expense report is neither in draft or sent-back state; hence not submittable");
        }

        DocumentSubgroup subgroup = subgroupRepository.findByCompanyCodeAndId(auth.getCompanyCode(), subgroupId)
                .orElseThrow(() -> new RecordNotFoundException(
                        String.format("Could not find subgroup with id: %d", subgroupId)));

        if (subgroup.isFrozen()) {
            throw new DomainInvariantException("Subgroup is frozen");
        }

        if (subgroup.isLocationRequired() && locationId == null) {
            throw new DomainInvariantException("Location id is required for this expense");
        }

        if (!StaticDataRegistry.isEnumNull(subgroup.getApplicableExpenseType())
                && (subgroup.getApplicableExpenseType() != report.getExpenseType())) {
            throw new DomainInvariantException("Report and subgroup expense type mismatch");
//			throw new DomainInvariantException("This expense is gender restricted");
        }

        logger.info("create: Creating a new Expense entity");
        Document document = new Document();

        document.setDocumentApprovalContainer(report);

        if (!subgroup.isLocationRequired()
                && (subgroup.isSourceLocationApplicable() || subgroup.isDestinationLocationApplicable())) {
            IEmployeeViewModel employeeDetails = employeeService.getEmployeeMasterData(auth.getUserEmail(), auth);
            Location homeLocation = locationRepository.findByCompanyCode(auth.getCompanyCode()).stream()
                    .filter(l -> l.getLocation().equals(employeeDetails.getCityCode())).findFirst().orElseThrow(
                            () -> new DomainInvariantException("Could not find the home location for this employee"));

            document.setLocationCategory(homeLocation.getCategory());
            document.setLocation(homeLocation.getLocation());
        }

        document.setClaimAmount(new BigDecimal("0.0"));
        document.setInvoiceAmount(new BigDecimal("0.0"));
        document.setApplicableAmount(new BigDecimal("0.0"));
        document.setDocumentApprovalContainerId(reportId);
        document.setDocumentSubgroup(subgroup);
        document.setDocumentSubgroupId(subgroupId);
        document.setFrequency(subgroup.getFrequency());

        if (subgroup.isLocationRequired()) {
            Location location = locationRepository.findByCompanyCodeAndId(auth.getCompanyCode(), locationId)
                    .orElseThrow(() -> new RecordNotFoundException(
                            String.format("Location with id: %d could not be found", locationId)));

            document.setLocationCategory(location.getCategory());
            document.setLocation(location.getLocation());
        }

        logger.info("create: Finding the applicable Expense Rule");
        DocumentRule applicableRule = getApplicableRule(document);

        logger.info("create: Attaching the applicable Expense Rule as id");
        document.setDocumentRuleId(applicableRule.getId());

        document.setCreatingUserId(auth.getUserId());
        document.setCreatedTimestamp(ZonedDateTime.now());
        document.setCompanyCode(auth.getCompanyCode());
        document.setEmployeeEmail(auth.getUserEmail());

        report.getDocuments().add(document);
        report.setReportStatus(ReportStatus.DRAFT);
        subgroup.getDocuments().add(document);

        logger.info("create: Saving both Expense and Expense Metadata");
        expenseRepository.saveAndFlush(document);
        reportRepository.saveAndFlush(report);
        subgroupRepository.saveAndFlush(subgroup);
        logger.info("create: Save successful");

        return getViewModel(document);
    }

    @Override
    public ExpenseViewModel getById(long id, IAuthContextViewModel auth) {
        return getViewModel(expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), id).orElseThrow(
                () -> new RecordNotFoundException(String.format("Could not find expense with id: %d", id))));
    }

    @Override
    public void save(ExpenseUpdateViewModel viewModel, IAuthContextViewModel auth) {
        Document entity = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getId())
                .orElseThrow(() -> new RecordNotFoundException(
                        String.format("Could not find expense with id: %d", viewModel.getId())));

        DocumentApprovalContainer report = reportRepository
                .findByCompanyCodeAndId(auth.getCompanyCode(), entity.getDocumentApprovalContainerId())
                .orElseThrow(() -> new RecordNotFoundException(
                        String.format("Could not find expense report with id: %d", entity.getDocumentApprovalContainerId())));


        if (!isParentExpenseReportSubmittable(report)) {
            throw new DomainInvariantException(
                    "Parent expense report is neither in draft or sent-back state; hence not submittable");
        }
        logger.info("save: Fetching the applicable rule");
        DocumentRule rule = ruleRepository.findByCompanyCodeAndId(auth.getCompanyCode(), entity.getDocumentRuleId()).orElseThrow(() -> new RecordNotFoundException("Could not find the applicable Expense Rule by id: " + entity.getDocumentRuleId()));   //  temporary


        BeanUtils.copyProperties(viewModel, entity);

        if (viewModel.getDocument1() != null) {
            fileIOService.upload(viewModel.getDocument1(), viewModel.getDocument1Identifier(), 1, entity);
        }

        if (viewModel.getDocument2() != null) {
            fileIOService.upload(viewModel.getDocument2(), viewModel.getDocument2Identifier(), 2, entity);
        }

        if (viewModel.getDocument3() != null) {
            fileIOService.upload(viewModel.getDocument3(), viewModel.getDocument3Identifier(), 3, entity);
        }

        logger.info("save: Getting the validation status of the expense");
        ValidationStatusViewModel validation = getValidationStatus(entity, rule);

        logger.info("save: Checking if all validations are passing");
        if (!validation.isValid()) {
            logger.info("save: Validation errors present: {}", validation.getValidationErrors());
            throw new DomainInvariantException("Invalid expense, cannot save");
        }

        if (getDeviationStatus(entity, rule)) {
            entity.setDeviated(true);
            entity.setDeviationRemarks(String.format("Expense deviated from limit of %.2f by %.2f",
                    rule.getLimitAmount(), entity.getClaimAmount().subtract(rule.getLimitAmount())));
        } else {
            entity.setDeviated(false);
            entity.setDeviationRemarks(null);
        }

        logger.info("save: Saving the updated expense");
        expenseRepository.saveAndFlush(entity);
        logger.info("save: Save successful; exiting");
    }

    @Override
    public void delete(long id, IAuthContextViewModel auth) {
        Document document = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), id).orElseThrow(
                () -> new RecordNotFoundException(String.format("Could not find expense with id: %d", id)));

        DocumentApprovalContainer report = reportRepository
                .findByCompanyCodeAndId(auth.getCompanyCode(), document.getDocumentApprovalContainerId())
                .orElseThrow(() -> new RecordNotFoundException(
                        String.format("Could not find expense report with id: %d", document.getDocumentApprovalContainerId())));

        if (!isParentExpenseReportSubmittable(report)) {
            throw new DomainInvariantException(
                    "Parent expense report is neither in draft or sent-back state; hence not submittable");
        }

        fileIOService.delete(document);
        expenseRepository.delete(document);
    }

    @Override
    public List<ExpenseViewModel> getAllByReportId(long reportId, IAuthContextViewModel auth) {
        return expenseRepository.findAllByCompanyCodeAndDocumentApprovalContainerId(auth.getCompanyCode(), reportId).stream()
                .map(this::getViewModel).collect(Collectors.toList());
    }

    @Override
    public List<ReportLimitConsumptionViewModel> getConsumption(long expenseId, IAuthContextViewModel auth) {
        Document document = expenseRepository.findByCompanyCodeAndId(auth.getCompanyCode(), expenseId).orElseThrow(
                () -> new RecordNotFoundException(String.format("Could not find expense with id: %d", expenseId)));

        DocumentApprovalContainer report = document.getDocumentApprovalContainer();

        List<MetadataLimitRule> rules = getApplicableRule(report);
        List<ReportLimitConsumptionViewModel> consumptions = new ArrayList<>();

        for (MetadataLimitRule rule : rules) {
            switch (rule.getIntervalMarker()) {
                case DAILY:
                    consumptions.add(getDailyLimitConsumption(document, rule));
                    break;
                case MONTHLY:
                    consumptions.add(getMonthlyLimitConsumption(document, rule));
                    break;
                case QUARTERLY:
                    consumptions.add(getQuarterlyLimitConsumption(document, rule));
                    break;
                case HALF_YEARLY:
                    consumptions.add(getHalfYearlyLimitConsumption(document, rule));
                    break;
                case YEARLY:
                    consumptions.add(getYearlyLimitConsumption(document, rule));
                    break;
            }
        }

        return consumptions;
    }

    private boolean getDeviationStatus(Document document, DocumentRule rule) {
        if (!rule.isCanExceedLimit()) {
            return false;
        }

//		expense.getClaimAmount() > rule.getLimitAmount()
        return document.getClaimAmount().compareTo(rule.getLimitAmount()) > 0;
    }

    private ExpenseViewModel getViewModel(Document entity) {
        ExpenseViewModel viewModel = new ExpenseViewModel();
        BeanUtils.copyProperties(entity, viewModel);
        viewModel.setExpenseSubgroupMarker(StaticDataRegistry.getExpenseSubgroupMarker(entity.getDocumentSubgroup()));
        return viewModel;
    }

    private DocumentRule getApplicableRule(Document document) {
        logger.trace("getApplicableRule: Finding the Expense with id: {}", document.getId());

        DocumentSubgroup subgroup = document.getDocumentSubgroup();

        Optional<DocumentRule> rule = subgroup.getRules().stream().filter(r -> !r.isFrozen())
                .filter(r -> isRuleMatch(r, document)).findFirst();

        return rule.orElseThrow(() -> new RecordNotFoundException("Could not find applicable rule for expense"));
    }

    private boolean isRuleMatch(DocumentRule rule, Document document) {
        boolean result = true;

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
            result = rule.getBranchCode().equals(document.getDocumentApprovalContainer().getEmployeeBranch());
            if (!result)
                return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
            result = rule.getDepartmentCode().equals(document.getDocumentApprovalContainer().getEmployeeDepartment());
            if (!result)
                return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
            result = rule.getCostCenterCode().equals(document.getDocumentApprovalContainer().getEmployeeCostCenter());
            if (!result)
                return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
            result = rule.getEmployeeType().equals(document.getDocumentApprovalContainer().getEmployeeType());
            if (!result)
                return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
            result = rule.getEmployeeGrade().equals(document.getDocumentApprovalContainer().getEmployeeGrade());
            if (!result)
                return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
            result = rule.getLocationCategory().equals(document.getLocationCategory());
            if (!result)
                return false;
        }

        return result;
    }

    private ValidationStatusViewModel getValidationStatus(Document document, DocumentRule rule) {
        logger.trace("getValidationStatus: Running validations on the expense");
        boolean status = true;
        StringBuilder stringBuilder = new StringBuilder();

        try {
            logger.trace("getValidationStatus: Validating travel descriptor");
            ValidationStatusViewModel travelDescriptor = validateTravelDescriptor(document);
            logger.trace("getValidationStatus: Valid: {}", travelDescriptor.isValid());
            if (!travelDescriptor.isValid()) {
                status = false;
                stringBuilder.append(travelDescriptor.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating mobility descriptor");
            ValidationStatusViewModel mobilityDescriptor = validateMobilityDescriptor(document);
            logger.trace("getValidationStatus: Valid: {}", mobilityDescriptor.isValid());
            if (!mobilityDescriptor.isValid()) {
                status = false;
                stringBuilder.append(mobilityDescriptor.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating transport descriptor");
            ValidationStatusViewModel transportDescriptor = validateTransportDescriptor(document);
            logger.trace("getValidationStatus: Valid: {}", transportDescriptor.isValid());
            if (!transportDescriptor.isValid()) {
                status = false;
                stringBuilder.append(transportDescriptor.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating claim and applicable amounts");
            ValidationStatusViewModel applicableAmount = validateClaimAndApplicableAmounts(document);
            logger.trace("getValidationStatus: Valid: {}", applicableAmount.isValid());
            if (!applicableAmount.isValid()) {
                status = false;
                stringBuilder.append(applicableAmount.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating invoice and applicable amounts");
            ValidationStatusViewModel invoiceAndApplicableAmount = validateInvoiceAndApplicableAmounts(document);
            logger.trace("getValidationStatus: Valid: {}", invoiceAndApplicableAmount.isValid());
            if (!invoiceAndApplicableAmount.isValid()) {
                status = false;
                stringBuilder.append(invoiceAndApplicableAmount.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating standard deduction computation");
            ValidationStatusViewModel standardDeduction = validateStandardDeduction(document, rule);
            logger.trace("getValidationStatus: Valid: {}", standardDeduction.isValid());
            if (!standardDeduction.isValid()) {
                status = false;
                stringBuilder.append(standardDeduction.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating per diem computation");
            ValidationStatusViewModel perDiem = validatePerDiem(document, rule);
            logger.trace("getValidationStatus: Valid: {}", perDiem.isValid());
            if (!perDiem.isValid()) {
                status = false;
                stringBuilder.append(perDiem.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating merchant details");
            ValidationStatusViewModel merchantDetails = validateMerchantDetails(document);
            logger.trace("getValidationStatus: Valid: {}", merchantDetails.isValid());
            if (!merchantDetails.isValid()) {
                status = false;
                stringBuilder.append(merchantDetails.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating source location");
            ValidationStatusViewModel sourceLocation = validateSourceLocation(document);
            logger.trace("getValidationStatus: Valid: {}", sourceLocation.isValid());
            if (!sourceLocation.isValid()) {
                status = false;
                stringBuilder.append(sourceLocation.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating destination location");
            ValidationStatusViewModel destinationLocation = validateDestinationLocation(document);
            logger.trace("getValidationStatus: Valid: {}", destinationLocation.isValid());
            if (!destinationLocation.isValid()) {
                status = false;
                stringBuilder.append(destinationLocation.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating date range");
            ValidationStatusViewModel dateRange = validateDateRange(document);
            logger.trace("getValidationStatus: Valid: {}", dateRange.isValid());
            if (!dateRange.isValid()) {
                status = false;
                stringBuilder.append(dateRange.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating expense identifier");
            ValidationStatusViewModel expenseIdentifier = validateExpenseIdentifier(document);
            logger.trace("getValidationStatus: Valid: {}", expenseIdentifier.isValid());
            if (!expenseIdentifier.isValid()) {
                status = false;
                stringBuilder.append(expenseIdentifier.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating unit rate computation");
            ValidationStatusViewModel unitRateComputation = validateUnitRateComputation(document, rule);
            logger.trace("getValidationStatus: Valid: {}", unitRateComputation.isValid());
            if (!unitRateComputation.isValid()) {
                status = false;
                stringBuilder.append(unitRateComputation.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating bill amount");
            ValidationStatusViewModel billAmount = validateBillAmount(document, rule);
            logger.trace("getValidationStatus: Valid: {}", billAmount.isValid());
            if (!billAmount.isValid()) {
                status = false;
                stringBuilder.append(billAmount.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating date range overlap");
            ValidationStatusViewModel dateRangeOverlap = validateDateRangeOverlap(document, rule);
            logger.trace("getValidationStatus: Valid: {}", dateRangeOverlap.isValid());
            if (!dateRangeOverlap.isValid()) {
                status = false;
                stringBuilder.append(dateRangeOverlap.getValidationErrors());
            }

            logger.trace("getValidationStatus: Validating Invoice attachment");
            ValidationStatusViewModel invoiceAttachment = validateAttachments(document, rule);
            logger.trace("getValidationStatus: Valid: {}", invoiceAttachment.isValid());
            if (!invoiceAttachment.isValid()) {
                status = false;
                stringBuilder.append(invoiceAttachment.getValidationErrors());
            }
        } catch (NullPointerException nullPointerException) {
            logger.error("getValidationStatus: One of the required fields set to null; exception raised");
            throw new DomainInvariantException(
                    String.format("Error accessing necessary objects: %s", nullPointerException.getMessage()));
        }

        logger.info("getValidationStatus: Returning; Final validity: {}", status);
        return new ValidationStatusViewModel(status, stringBuilder.toString());
    }

    // validation subordinates
    private ValidationStatusViewModel validateMobilityDescriptor(Document document) {
        if (!document.getDocumentSubgroup().isMobilityDescriptorApplicable()) {
            logger.info("ExpenseValidationStatusViewModel: Mobility descriptor is not applicable to this expense");
            return new ValidationStatusViewModel(true, null);
        }

        if (document.getDocumentSubgroup().isMobilityDescriptorApplicable()
                && StaticDataRegistry.isNullOrEmptyOrWhitespace(document.getMobilityDescriptor())) {
            logger.info("ExpenseValidationStatusViewModel: Mobility descriptor is not found in this expense");
            return new ValidationStatusViewModel(false, "Mobility descriptor is missing");
        }

        if (document.getDocumentSubgroup().isMobilityDescriptorApplicable()
                && !StaticDataRegistry.MOBILITY_DESCRIPTOR_VALUE.contains(document.getMobilityDescriptor())) {
            logger.info("ExpenseValidationStatusViewModel: Mobility descriptor {} is not allowed",
                    document.getMobilityDescriptor());
            return new ValidationStatusViewModel(false,
                    "Invalid mobility descriptor: " + document.getMobilityDescriptor());
        }

        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validateTransportDescriptor(Document document) {
        if (!document.getDocumentSubgroup().isTransportDescriptorApplicable()) {
            logger.info("ExpenseValidationStatusViewModel: Transport descriptor is not applicable to this expense");
            return new ValidationStatusViewModel(true, null);
        }

        if (document.getDocumentSubgroup().isTransportDescriptorApplicable()
                && StaticDataRegistry.isNullOrEmptyOrWhitespace(document.getTransportDescriptor())) {
            logger.info("ExpenseValidationStatusViewModel: Transport descriptor is not found in this expense");
            return new ValidationStatusViewModel(false, "Transport descriptor is missing");
        }

        if (document.getDocumentSubgroup().isTransportDescriptorApplicable()
                && !StaticDataRegistry.TRANSPORT_DESCRIPTOR_VALUE.contains(document.getTransportDescriptor())) {
            logger.info("ExpenseValidationStatusViewModel: Transport descriptor {} is not allowed",
                    document.getMobilityDescriptor());
            return new ValidationStatusViewModel(false,
                    "Invalid transport descriptor: " + document.getTransportDescriptor());
        }

        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validateTravelDescriptor(Document document) {
        if (!document.getDocumentSubgroup().isTravelDescriptorApplicable()) {
            logger.info("ExpenseValidationStatusViewModel: Travel descriptor is not applicable to this expense");
            return new ValidationStatusViewModel(true, null);
        }

        if (document.getDocumentSubgroup().isTravelDescriptorApplicable()
                && StaticDataRegistry.isNullOrEmptyOrWhitespace(document.getTravelDescriptor())) {
            logger.info("ExpenseValidationStatusViewModel: Travel descriptor is not found in this expense");
            return new ValidationStatusViewModel(false, "Travel descriptor is missing");
        }

        if (document.getDocumentSubgroup().isTravelDescriptorApplicable()
                && !StaticDataRegistry.TRAVEL_DESCRIPTOR_VALUE.contains(document.getTravelDescriptor())) {
            logger.info("ExpenseValidationStatusViewModel: Travel descriptor {} is not allowed",
                    document.getTravelDescriptor());
            return new ValidationStatusViewModel(false,
                    "Invalid travel descriptor: " + document.getTravelDescriptor());
        }

        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validateClaimAndApplicableAmounts(Document document) {
//		expense.getClaimAmount() > expense.getApplicableAmount()
        if (document.getClaimAmount().compareTo(document.getApplicableAmount()) > 0) {
            logger.info("validateClaimAndApplicableAmounts: Claim amount is more than bill amount");
            return new ValidationStatusViewModel(false, "Claim amount should not exceed the bill amount");
        }

        logger.trace("validateClaimAndApplicableAmounts: Found valid");
        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validateStandardDeduction(Document document, DocumentRule rule) {
        if (!document.getDocumentSubgroup().isStandardDeductionApplicable()) {
            logger.trace(
                    "validateStandardDeduction: Standard deduction is not applicable, clearing related fields if any");
            document.setStandardDeductionRate(null);
            document.setStandardDeductionAmount(null);
            return new ValidationStatusViewModel(true, null);
        }

        if (!document.getStandardDeductionRate().equals(rule.getStandardDeductionRate())) {
            logger.info("validateStandardDeduction: Rate in expense and rule are not matching");
            return new ValidationStatusViewModel(false, "Standard deduction rate on expense is fudged");
        }

        BigDecimal rate = document.getStandardDeductionRate();
        BigDecimal billAmount = document.getApplicableAmount();
        BigDecimal standardDeductionAmount = document.getStandardDeductionAmount();

//		Math.round(rate * billAmount / 100.00) != Math.round(standardDeductionAmount)
        BigDecimal calculatedDeduction = rate.multiply(billAmount).divide(new BigDecimal("100.00"), RoundingMode.HALF_UP);
        BigDecimal providedDeduction = standardDeductionAmount.setScale(0, RoundingMode.HALF_UP);
        if (!(calculatedDeduction.equals(providedDeduction))) {
            logger.info("validateStandardDeduction: Computation of standard deduction amount is wrong {} vs {}",
                    calculatedDeduction, providedDeduction);
            return new ValidationStatusViewModel(false, "Standard deduction amount calculation is wrong");
        }

        BigDecimal target = rule.isCanExceedLimit() ? rule.getMaximumAmount() : rule.getLimitAmount();

//		BigDecimal authorized = Math.ceil(expense.getApplicableAmount() - expense.getStandardDeductionAmount()) < Math
//				.ceil(target) ? expense.getApplicableAmount() - expense.getStandardDeductionAmount() : target;

        BigDecimal authorized = document.getApplicableAmount()
                .subtract(document.getStandardDeductionAmount()).setScale(0, RoundingMode.CEILING)
                .compareTo(target.setScale(0, RoundingMode.CEILING)) < 0 ?
                document.getApplicableAmount().subtract(document.getStandardDeductionAmount()) : target;

        BigDecimal claimAmountRounded = document.getClaimAmount().setScale(0, RoundingMode.CEILING);
        BigDecimal authorizedRounded = authorized.setScale(0, RoundingMode.CEILING);

//		Math.ceil(expense.getClaimAmount()) != Math.ceil(authorized)
        if (claimAmountRounded.compareTo(authorizedRounded) != 0) {
            logger.info("validateStandardDeduction: Claim amount is not the same as computed: {} vs {}",
                    claimAmountRounded, authorizedRounded);
            return new ValidationStatusViewModel(false,
                    "Claim amount calculation (after standard deduction) is wrong");
        }

        logger.trace("validateStandardDeduction: Found valid");
        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validatePerDiem(Document document, DocumentRule rule) {
        if (!rule.isPerDiemAllowed()) {
            logger.trace("validatePerDiem: Per diem doesn't apply to this expense");
            return new ValidationStatusViewModel(true, null);
        }

        if (document.getStartDate() != null && document.getEndDate() != null) {
            Period period = Period.between(document.getStartDate(), document.getEndDate());
            int nDays = period.getDays() + 1;
            logger.trace("validatePerDiem: Per diem should be calculated for {} days at {} / day", nDays,
                    rule.getPerDiemAmount());

//			Math.ceil(expense.getClaimAmount()) == Math.ceil(rule.getPerDiemAmount() * nDays)
            if (document.getClaimAmount().setScale(0, RoundingMode.CEILING)
                    .compareTo(rule.getPerDiemAmount().multiply(new BigDecimal(nDays)).setScale(0, RoundingMode.CEILING)) == 0) {
                logger.trace("validatePerDiem: Per diem for {} days found correct", nDays);
                return new ValidationStatusViewModel(true, null);
            } else {
                return new ValidationStatusViewModel(false,
                        "Claim amount should be calculated according to per diem");
            }
        }

        if (document.getApplicableAmount() != document.getClaimAmount()
                || !rule.getPerDiemAmount().equals(document.getClaimAmount())) {
            logger.info("validatePerDiem: Mismatch in amounts; bill: {}, claim: {}, per-diem: {}",
                    document.getApplicableAmount(), document.getClaimAmount(), rule.getPerDiemAmount());
            return new ValidationStatusViewModel(false, "Claim amount should be the per diem amount");
        }

        logger.trace("validatePerDiem: Found valid");
        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validateMerchantDetails(Document document) {
        if (!document.getDocumentSubgroup().isMerchantRequired()) {
            logger.trace(
                    "validateMerchantDetails: Merchant details doesn't apply to this expense; clearing related fields if any");
            document.setMerchantDetails(null);
            return new ValidationStatusViewModel(true, null);
        }

        if (StaticDataRegistry.isNullOrEmptyOrWhitespace(document.getMerchantDetails())) {
            logger.info("validateSourceLocation: Null, empty, or whitespace provided for merchant details");
            return new ValidationStatusViewModel(false, "Merchant detail is missing or invalid");
        }

        logger.trace("validateSourceLocation: Found valid");
        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validateSourceLocation(Document document) {
        if (!document.getDocumentSubgroup().isSourceLocationApplicable()) {
            logger.trace(
                    "validateSourceLocation: Source location doesn't apply to this expense; clearing related fields if any");
            document.setSourceLocation(null);
            return new ValidationStatusViewModel(true, null);
        }

        if (StaticDataRegistry.isNullOrEmptyOrWhitespace(document.getSourceLocation())) {
            logger.info("validateSourceLocation: Null, empty, or whitespace provided for source location");
            return new ValidationStatusViewModel(false, "Source location is missing or invalid");
        }

        logger.trace("validateSourceLocation: Found valid");
        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validateDestinationLocation(Document document) {
        if (!document.getDocumentSubgroup().isDestinationLocationApplicable()) {
            logger.trace(
                    "validateDestinationLocation: Destination location doesn't apply to this expense; clearing related fields if any");
            document.setDestinationLocation(null);
            return new ValidationStatusViewModel(true, null);
        }

        if (StaticDataRegistry.isNullOrEmptyOrWhitespace(document.getDestinationLocation())) {
            logger.info("validateDestinationLocation: Null, empty, or whitespace provided for destination location");
            return new ValidationStatusViewModel(false, "Destination location is missing or invalid");
        }

        logger.trace("validateDestinationLocation: Found valid");
        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validateDateRange(Document document) {
        if (!document.getDocumentSubgroup().isDateRangeApplicable()) {
            logger.trace(
                    "validateDateRange: Date range doesn't apply to this expense; clearing the related fields if any");
            document.setStartDate(null);
            document.setEndDate(null);
            return new ValidationStatusViewModel(true, null);
        }

        if (document.getStartDate() == null || document.getEndDate() == null) {
            logger.info("validateDateRange: Null value detected; start: {}, end: {}", document.getStartDate(),
                    document.getEndDate());
            return new ValidationStatusViewModel(false, "Start of end date is missing");
        }

        if (document.getEndDate().isBefore(document.getStartDate())) {
            logger.info("validateDateRange: End date falls before the start date");
            return new ValidationStatusViewModel(false, "End date must fall on or after start date");
        }

        if (document.getDocumentDate().isBefore(document.getStartDate())) {
            logger.info(
                    "validateDateRange: Expense date dosn't fit into the provided date range; start: {}, expense: {}, end: {}",
                    document.getStartDate(), document.getDocumentDate(), document.getEndDate());
            return new ValidationStatusViewModel(false,
                    "Expense date must lie in the range of start and end dates");
        }

        logger.trace("validateDateRange: Found valid");
        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validateExpenseIdentifier(Document document) {
        if (!document.getDocumentSubgroup().isDocumentIdentifierApplicable()) {
            logger.trace(
                    "validateExpenseIdentifier: Expense identifier doesn't apply to this expense; clearing related fields if any");
            document.setIdentifier(null);
            return new ValidationStatusViewModel(true, null);
        }

        if (StaticDataRegistry.isNullOrEmptyOrWhitespace(document.getIdentifier())) {
            logger.info("validateExpenseIdentifier: Null, empty, or whitespace provided for expense identifier");
            return new ValidationStatusViewModel(false, "Expense identifier is missing or invalid");
        }

        logger.trace("validateExpenseIdentifier: Found valid");
        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validateInvoiceAndApplicableAmounts(Document document) {
//		(expense.getApplicableAmount().compareTo(expense.getInvoiceAmount()) > 0)
        if (document.isApplicableAmountLesser() && (document.getApplicableAmount().compareTo(document.getInvoiceAmount()) > 0)) {
            return new ValidationStatusViewModel(false, "Applicable amount is not invalid");
        }

        if (!document.isApplicableAmountLesser() && !Objects.equals(document.getApplicableAmount(), document.getInvoiceAmount())) {
            return new ValidationStatusViewModel(false,
                    "Applicable amount should be same as the invoice amount");
        }

        logger.trace("validateInvoiceAndApplicableAmounts: Found valid");
        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validateAttachments(Document document, DocumentRule rule) {
        if (!rule.isInvoiceRequired()) {
            logger.trace("validateAttachments: Found valid");
            return new ValidationStatusViewModel(true, null);
        }

//		expense.getClaimAmount() >= rule.getInvoiceRequiredThreshold()
        if ((document.getClaimAmount().compareTo(rule.getInvoiceRequiredThreshold()) >= 0)
                && StaticDataRegistry.isNullOrEmptyOrWhitespace(document.getDocument1UploadUrl())) {
            return new ValidationStatusViewModel(false, "Invoice attachment is missing for this expense");
        }

        logger.trace("validateAttachments: Found valid");
        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validateUnitRateComputation(Document document, DocumentRule rule) {
        if (!rule.isUnitRateApplicable()) {
            logger.trace(
                    "validateUnitRateComputation: Unit rate doesn't apply to this expense; clearing related fields if any");
            document.setUnitOfMeasure(null);
            document.setUnitRate(null);
            return new ValidationStatusViewModel(true, null);
        }

        if (!rule.getUnitRate().equals(document.getUnitRate())) {
            logger.info("validateUnitRateComputation: Unit rate in expense doesn't match with rule; {} vs {}",
                    document.getUnitRate(), rule.getUnitRate());
            return new ValidationStatusViewModel(false, "Unit rate in the expense is fudged");
        }

        if (!rule.getUnitOfMeasure().equals(document.getUnitOfMeasure())) {
            logger.info("validateUnitRateComputation: Unit of measure in expense doesn't match with rule; {} vs {}",
                    document.getUnitOfMeasure(), rule.getUnitOfMeasure());
            return new ValidationStatusViewModel(false, "Unit of measure in the expense is fudged");
        }

        if (document.getQuantity() == null) {
            logger.info("validateUnitRateComputation: Quantity is missing");
            return new ValidationStatusViewModel(false,
                    "Expense does not have quantity (required for unit rate calculation)");
        }

        // Math.round because of floating point comparison
//		BigDecimal amount = Math.round(expense.getUnitRate() * expense.getQuantity());
        BigDecimal amount = document.getUnitRate().multiply(document.getQuantity()).setScale(0, RoundingMode.CEILING);


//		Math.round(expense.getApplicableAmount()) > amount
        BigDecimal applicableAmountRounded = document.getApplicableAmount().setScale(0, RoundingMode.CEILING);
        if (applicableAmountRounded.compareTo(amount) > 0) {
            logger.info("validateUnitRateComputation: Amount computation is not right; {} vs {}",
                    applicableAmountRounded, amount);
            return new ValidationStatusViewModel(false,
                    "Expense amount doesn't match with computed unit rate value");
        }

        logger.trace("validateUnitRateComputation: Found valid");
        return new ValidationStatusViewModel(true, null);
    }

    private ValidationStatusViewModel validateBillAmount(Document document, DocumentRule rule) {
        if (rule.isPerDiemAllowed()) {
            logger.trace(
                    "validateBillAmount: Falls into the purview of per diem calculation; bypassing limit checking");
            return new ValidationStatusViewModel(true, null);
        }

//		expense.getClaimAmount() <= rule.getLimitAmount()
        if (document.getClaimAmount().compareTo(rule.getLimitAmount()) <= 0) {
            logger.trace("validateBillAmount: Claim amount is within the limit; found valid");
            return new ValidationStatusViewModel(true, null);
        }

        if (rule.isCanExceedLimit() && document.getClaimAmount().compareTo(rule.getMaximumAmount()) <= 0) {
            logger.trace("validateBillAmount: Exceed allowed; claim is within the maximum amount; found valid");
            return new ValidationStatusViewModel(true, null);
        }

        logger.info(
                "validateBillAmount: Bill amount conflicts with limit and maximum; bill: {}, limit: {}, max: {}, exceed allowed: {}",
                document.getApplicableAmount(), rule.getLimitAmount(), rule.getMaximumAmount(), rule.isCanExceedLimit());
        return new ValidationStatusViewModel(false, "Bill amount must abide by the rule (limit and maximum)");
    }

    private ValidationStatusViewModel validateDateRangeOverlap(Document document, DocumentRule rule) {
        logger.trace("validateDateRangeOverlap: Checking if the date range overlaps with earlier expense");
        if (!document.getDocumentSubgroup().isDateRangeApplicable()) {
            logger.trace("validateDateRangeOverlap: Date range doesn't apply; found valid");
            return new ValidationStatusViewModel(true, null);
        }

        Optional<Document> lastExpenseOptional = expenseRepository
                .getByDateRange(document.getCompanyCode(), document.getDocumentSubgroupId(), true,
                        List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK), document.getEmployeeEmail())
                .stream().filter(e -> isDateRangeOverlap(document.getStartDate(), document.getEndDate(), e.getStartDate(),
                        e.getEndDate()))
                .findFirst();

        if (lastExpenseOptional.isEmpty()) {
            logger.trace("validateDateRangeOverlap: Date range doesn't overlap; found valid");
            return new ValidationStatusViewModel(true, null);
        }

        logger.info("validateInvoice: Overlap found; invalid");
        return new ValidationStatusViewModel(false,
                "Similar expense exists with id: " + lastExpenseOptional.get().getId());
    }

    private boolean isBetween(LocalDate date, LocalDate start, LocalDate end) {
        return (date.isAfter(start) && date.isBefore(end));
    }

    private boolean isSameDateRange(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2) {
        return (start1.isEqual(start2) && end1.isEqual(end2));
    }

    public boolean isDateRangeOverlap(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2) {
        boolean areSame = isSameDateRange(start1, end1, start2, end2);
        if (areSame)
            return true;

        return isBetween(start1, start2, end2) || isBetween(end1, start2, end2) || isBetween(start2, start1, end1)
                || isBetween(end2, start1, end1);
    }

    private LocalDate[] getQuarterDelimiters(LocalDate markDate) {
        switch (markDate.getMonth()) {
            case JANUARY:
            case FEBRUARY:
            case MARCH:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.JANUARY, 1),
                        LocalDate.of(markDate.getYear(), Month.MARCH, 31)};
            case APRIL:
            case MAY:
            case JUNE:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.APRIL, 1),
                        LocalDate.of(markDate.getYear(), Month.JUNE, 30)};
            case JULY:
            case AUGUST:
            case SEPTEMBER:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.JULY, 1),
                        LocalDate.of(markDate.getYear(), Month.SEPTEMBER, 30)};
            default:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.OCTOBER, 1),
                        LocalDate.of(markDate.getYear(), Month.DECEMBER, 31)};
        }
    }

    private LocalDate[] getHalfYearDelimiters(LocalDate markDate) {
        switch (markDate.getMonth()) {
            case APRIL:
            case MAY:
            case JUNE:
            case JULY:
            case AUGUST:
            case SEPTEMBER:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.APRIL, 1),
                        LocalDate.of(markDate.getYear(), Month.SEPTEMBER, 30)};
            default:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.OCTOBER, 1),
                        LocalDate.of(markDate.getYear(), Month.MARCH, 31)};
        }
    }

    private LocalDate[] getYearDelimiters(LocalDate markDate) {
        switch (markDate.getMonth()) {
            case JANUARY:
            case FEBRUARY:
            case MARCH:
                return new LocalDate[]{LocalDate.of(markDate.getYear() - 1, Month.APRIL, 1),
                        LocalDate.of(markDate.getYear(), Month.MARCH, 31)};
            default:
                return new LocalDate[]{LocalDate.of(markDate.getYear(), Month.APRIL, 1),
                        LocalDate.of(markDate.getYear() + 1, Month.MARCH, 31)};
        }
    }

    private List<MetadataLimitRule> getApplicableRule(DocumentApprovalContainer report) {
        logger.trace("getApplicableRule: Finding the Metadata Limit Rules for Report with id: {}", report.getId());

        return metadataLimitRuleRepository
                .findByCompanyCodeAndDocumentMetadataId(report.getCompanyCode(), report.getDocumentMetadataId()).stream()
                .filter(r -> isRuleMatch(r, report)).collect(Collectors.toList());
    }

    private boolean isRuleMatch(MetadataLimitRule rule, DocumentApprovalContainer report) {
        boolean result = true;

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
            result = rule.getBranchCode().equals(report.getEmployeeBranch());
            if (!result)
                return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
            result = rule.getDepartmentCode().equals(report.getEmployeeDepartment());
            if (!result)
                return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
            result = rule.getCostCenterCode().equals(report.getEmployeeCostCenter());
            if (!result)
                return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
            result = rule.getEmployeeType().equals(report.getEmployeeType());
            if (!result)
                return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
            result = rule.getEmployeeGrade().equals(report.getEmployeeGrade());
            if (!result)
                return false;
        }

        return result;
    }

    private ReportLimitConsumptionViewModel getDailyLimitConsumption(Document document, MetadataLimitRule rule) {
        ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
        viewModel.setInterval(IntervalMarker.DAILY);
        viewModel.setLimitAmount(rule.getLimitAmount());
        viewModel.setConsumedAmount(
                expenseRepository.getDocumentsOnDay(document.getCompanyCode(), document.getCreatingUserId(),
                        document.getDocumentApprovalContainer().getDocumentMetadataId(), document.getDocumentDate()));

        return viewModel;
    }

    private ReportLimitConsumptionViewModel getMonthlyLimitConsumption(Document document, MetadataLimitRule rule) {
        LocalDate start = document.getDocumentDate().withDayOfMonth(1);
        LocalDate end = document.getDocumentDate().withDayOfMonth(document.getDocumentDate().lengthOfMonth());

        ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
        viewModel.setInterval(IntervalMarker.MONTHLY);
        viewModel.setLimitAmount(rule.getLimitAmount());
        viewModel.setConsumedAmount(expenseRepository.getDocumentsBetween(document.getCompanyCode(),
                document.getCreatingUserId(), document.getDocumentApprovalContainer().getDocumentMetadataId(),
                List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK, ReportStatus.ACCEPTED, ReportStatus.PAID,
                        ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
                start, end));

        return viewModel;
    }

    private ReportLimitConsumptionViewModel getQuarterlyLimitConsumption(Document document, MetadataLimitRule rule) {
        LocalDate[] delimiters = getQuarterDelimiters(document.getDocumentDate());
        ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
        viewModel.setInterval(IntervalMarker.QUARTERLY);
        viewModel.setLimitAmount(rule.getLimitAmount());
        viewModel
                .setConsumedAmount(expenseRepository.getDocumentsBetween(document.getCompanyCode(),
                        document.getCreatingUserId(), document.getDocumentApprovalContainer().getDocumentMetadataId(),
                        List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK, ReportStatus.ACCEPTED,
                                ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
                        delimiters[0], delimiters[1]));

        return viewModel;
    }

    private ReportLimitConsumptionViewModel getHalfYearlyLimitConsumption(Document document, MetadataLimitRule rule) {
        LocalDate[] delimiters = getHalfYearDelimiters(document.getDocumentDate());
        ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
        viewModel.setInterval(IntervalMarker.HALF_YEARLY);
        viewModel.setLimitAmount(rule.getLimitAmount());
        viewModel
                .setConsumedAmount(expenseRepository.getDocumentsBetween(document.getCompanyCode(),
                        document.getCreatingUserId(), document.getDocumentApprovalContainer().getDocumentMetadataId(),
                        List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK, ReportStatus.ACCEPTED,
                                ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
                        delimiters[0], delimiters[1]));

        return viewModel;
    }

    private ReportLimitConsumptionViewModel getYearlyLimitConsumption(Document document, MetadataLimitRule rule) {
        LocalDate[] delimiters = getYearDelimiters(document.getDocumentDate());
        ReportLimitConsumptionViewModel viewModel = new ReportLimitConsumptionViewModel();
        viewModel.setInterval(IntervalMarker.YEARLY);
        viewModel.setLimitAmount(rule.getLimitAmount());
        viewModel
                .setConsumedAmount(expenseRepository.getDocumentsBetween(document.getCompanyCode(),
                        document.getCreatingUserId(), document.getDocumentApprovalContainer().getDocumentMetadataId(),
                        List.of(ReportStatus.SUBMITTED, ReportStatus.SENT_BACK, ReportStatus.ACCEPTED,
                                ReportStatus.PAID, ReportStatus.POSTED_TO_DESTINATION_SYSTEM),
                        delimiters[0], delimiters[1]));

        return viewModel;
    }

    private boolean isParentExpenseReportSubmittable(DocumentApprovalContainer report) {
        return report.getReportStatus() == ReportStatus.DRAFT || report.getReportStatus() == ReportStatus.SENT_BACK;
    }
}
