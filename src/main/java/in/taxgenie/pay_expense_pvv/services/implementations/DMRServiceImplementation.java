package in.taxgenie.pay_expense_pvv.services.implementations;

import com.google.protobuf.ByteString;
import com.google.pubsub.v1.PubsubMessage;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDMRService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEnvironmentDataProvider;
import in.taxgenie.pay_expense_pvv.services.interfaces.IPubSubPublisherService;
import in.taxgenie.pay_expense_pvv.utils.CommonUtility;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice_ocr.UploadInternalViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class DMRServiceImplementation implements IDMRService {

    private final Logger logger;

    private final IPubSubPublisherService pubSubPublisherService;
    private final IEnvironmentDataProvider environmentDataProvider;


    public DMRServiceImplementation(IPubSubPublisherService pubSubPublisherService, IEnvironmentDataProvider environmentDataProvider){
        this.pubSubPublisherService = pubSubPublisherService;
        this.environmentDataProvider = environmentDataProvider;
        logger = LoggerFactory.getLogger(this.getClass());
    }


    @Override
    public String uploadMultipartFileToIPU(String  url, Long invoiceReceivedId, String documentIdentifier, IAuthContextViewModel auth) {
        String invoiceStatus = null;
        UploadInternalViewModel uploadInternalViewModel = new UploadInternalViewModel();

        try {
            uploadInternalViewModel.setCompanyId(Long.toString(auth.getCompanyCode()));
            uploadInternalViewModel.setAccessToken(auth.getToken());
            uploadInternalViewModel.setInvoiceReceivedId(invoiceReceivedId);
            uploadInternalViewModel.setDocumentIdentifier(documentIdentifier);

            uploadInternalViewModel.setUrl(url);

            String uuid = UUID.randomUUID().toString();
            Map<String, String> attributesMap = new HashMap<>();
            attributesMap.put("dataType", "UPLOAD_Invoice_To_ETL");
            attributesMap.put("requestId", uuid);
            attributesMap.put("sourceId", environmentDataProvider.getProductId());

            // Convert the messageJson to ByteString
            String messageJson = CommonUtility.serializeMessage(uploadInternalViewModel, uuid);
            ByteString data = ByteString.copyFromUtf8(messageJson);

            // Build the PubsubMessage with attributes
            PubsubMessage pubsubMessage = PubsubMessage.newBuilder()
                    .setData(data)
                    .putAllAttributes(attributesMap)
                    .build();

            // Log the Pubsub Message for debugging purposes
            logger.info("For UUID: "+ uuid + "Published invoice json data : {}", uuid, messageJson);

            // Publish the message and log the result
            invoiceStatus = pubSubPublisherService.publishMessage(uuid, "UPLOAD_Invoice_To_ETL", pubsubMessage);
            logger.info("For UUID: {} Published invoice status: {}", uuid, invoiceStatus);


        }catch (Exception e){
            logger.info("Exception Message : " + e.getMessage());
            return invoiceStatus;
        }
        return invoiceStatus;
    }
}
