package in.taxgenie.pay_expense_pvv.services.implementations.company;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.company.AdditionalDetails;
import in.taxgenie.pay_expense_pvv.repositories.company.IAdditionalDetailRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.company.IAdditionalDetailService;
import in.taxgenie.pay_expense_pvv.viewmodels.company.AdditionalDetailsViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AdditionalDetailServiceImplementation implements IAdditionalDetailService {
    private final IAdditionalDetailRepository repository;
    private final Logger logger;

    public AdditionalDetailServiceImplementation(IAdditionalDetailRepository repository) {
        this.repository = repository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public List<AdditionalDetailsViewModel> getAllAdditionalDetails(IAuthContextViewModel auth) {
        authSanityCheck("getAllAdditionalDetails", auth);
        logger.info("getAllAdditionalDetails: Entered for company ID {}", auth.getCompanyCode());

        List<AdditionalDetailsViewModel> viewModels = this.repository.findByCompanyCode(auth.getCompanyCode())
                .stream().map(i -> AdditionalDetails.getViewModel(i)).toList();

        logger.info("getAllAdditionalDetails: Returning {} additional details for company code {}", viewModels.size(), auth.getCompanyCode());
        return viewModels;
    }

    private void authSanityCheck(String functionName, IAuthContextViewModel auth) {
        if (auth == null) {
            logger.error("{}}: Auth context", functionName);
            throw new IllegalArgumentException("Auth context cannot be null");
        }
    }

}
