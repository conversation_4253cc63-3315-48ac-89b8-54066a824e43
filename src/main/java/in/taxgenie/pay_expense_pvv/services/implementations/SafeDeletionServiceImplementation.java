package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetFrequencyMapping;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMappingMaster;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetFrequencyMappingRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetMappingMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.ISafeDeletionService;
import jakarta.transaction.Transactional;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SafeDeletionServiceImplementation implements ISafeDeletionService {

    private final IBudgetMappingMasterRepository budgetMappingMasterRepository;
    private final IBudgetRepository budgetRepository;
    private final IBudgetFrequencyMappingRepository budgetFrequencyMappingRepository;

    public SafeDeletionServiceImplementation(IBudgetMappingMasterRepository budgetMappingMasterRepository, IBudgetRepository budgetRepository, IBudgetFrequencyMappingRepository budgetFrequencyMappingRepository) {
        this.budgetMappingMasterRepository = budgetMappingMasterRepository;
        this.budgetRepository = budgetRepository;
        this.budgetFrequencyMappingRepository = budgetFrequencyMappingRepository;
    }

    @Transactional
    @Override
    public List<BudgetMappingMaster> deleteBudgetMappingMasterEntries(List<BudgetMappingMaster> entries) {
        entries.forEach(i -> i.setParent(null));
        budgetMappingMasterRepository.saveAllAndFlush(entries);
        budgetMappingMasterRepository.deleteAll(entries);
        budgetMappingMasterRepository.flush();
        return entries;
    }

    @Transactional
    @Override
    public List<Budget> deleteBudgetEntries(List<Budget> entries) {
        entries.forEach(i -> i.setParent(null));
        budgetRepository.saveAllAndFlush(entries);
        budgetRepository.deleteAll(entries);
        budgetRepository.flush();
        return entries;
    }

    @Override
    public List<BudgetFrequencyMapping> deleteBudgetFrequencyMappingEntries(List<BudgetFrequencyMapping> entries) {
        budgetFrequencyMappingRepository.deleteAll(entries);
        budgetFrequencyMappingRepository.flush();
        return entries;
    }
}
