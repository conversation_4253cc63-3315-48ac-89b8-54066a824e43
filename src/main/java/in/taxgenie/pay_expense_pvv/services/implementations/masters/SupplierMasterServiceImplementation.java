package in.taxgenie.pay_expense_pvv.services.implementations.masters;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Company;
import in.taxgenie.pay_expense_pvv.entities.CompanyMaster;
import in.taxgenie.pay_expense_pvv.entities.StateCodes;
import in.taxgenie.pay_expense_pvv.invoice.repository.IStateCodeRepo;
import in.taxgenie.pay_expense_pvv.repositories.ICompanyMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.ICompanyRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomSupplierMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.masters.ISupplierMasterRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.masters.ISupplierMasterService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.ExcelUtil;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericPageableViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SupplierMasterCombinedViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SupplierMasterSyncRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SupplierMastersGstinViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SupplierMastersViewModel;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.*;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.rmi.RemoteException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static in.taxgenie.pay_expense_pvv.utils.MappersUtil.convertSortOrderToJPASort;

@Service
public class SupplierMasterServiceImplementation implements ISupplierMasterService {
    private final Logger logger;
    private final ISupplierMasterRepository supplierMasterRepository;
    private final CustomSupplierMasterRepository customSupplierMasterRepository;
    private final ICompanyRepository companyRepository;
    private final ICompanyMasterRepository companyMasterRepository;
    private final IStateCodeRepo stateCodeRepository;;
    private final ExcelUtil excelUtil;

    public SupplierMasterServiceImplementation(ISupplierMasterRepository supplierMasterRepository, CustomSupplierMasterRepository customSupplierMasterRepository, ICompanyRepository companyRepository, ICompanyMasterRepository companyMasterRepository, IStateCodeRepo stateCodeRepository, ExcelUtil excelUtil) {
        this.customSupplierMasterRepository = customSupplierMasterRepository;
        this.companyRepository = companyRepository;
        this.companyMasterRepository = companyMasterRepository;
        this.stateCodeRepository = stateCodeRepository;
        this.excelUtil = excelUtil;
        this.logger = LoggerFactory.getLogger(this.getClass());
        this.supplierMasterRepository = supplierMasterRepository;
    }

    @Override
    public GenericPageableViewModel<SupplierMastersViewModel> getSupplierMastersQueue(IAuthContextViewModel auth, QueueFilterViewModel filterValues) {
        GenericPageableViewModel<SupplierMastersViewModel> pagedQueue = new GenericPageableViewModel<>();
        Sort sort = null;

        String sortColumn = filterValues.getSortByColumnName() == null ? "default" : filterValues.getSortByColumnName();
        sort = switch (sortColumn) {
            case "pan" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "pan").and(Sort.by(Sort.Direction.DESC, "id"));

            case "supplierCompanyName" ->
                    Sort.by(convertSortOrderToJPASort(filterValues.getSortOrder()), "supplierCompanyName").and(Sort.by(Sort.Direction.DESC, "id"));
            default -> Sort.by(
                    Sort.Order.desc("companyMasterId")
            );
        };

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize(), sort);

        Page<SupplierMastersViewModel> viewModelPaged = customSupplierMasterRepository.getSupplierMasterQueue(auth.getCompanyCode(), filterValues, pageable);
        pagedQueue.setData(viewModelPaged.getContent());
        pagedQueue.setPages(viewModelPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(viewModelPaged.getTotalElements()));

        return pagedQueue;
    }

    private Page<SupplierMastersViewModel> getDemoResponse() {
        List<SupplierMastersViewModel> supplierList = new ArrayList<>();

        // Create some dummy SupplierMastersViewModel instances
        supplierList.add(new SupplierMastersViewModel(
                1L, "PAN123", 5, true, "2024-01-15", "Net 30",
                "CIN123456", "Active", "TAN123456", "5%",
                "ABC Pvt Ltd", "VEND001", true, "<EMAIL>",
                "John Doe", "1234567890", "123 Street, City",
                "admin", "supervisor", "2024-01-01", "2024-02-01"
        ));

        supplierList.add(new SupplierMastersViewModel(
                2L, "PAN456", 3, false, "2024-01-20", "Net 45",
                "CIN654321", "Inactive", "TAN654321", "10%",
                "XYZ Pvt Ltd", "VEND002", false, "<EMAIL>",
                "Jane Doe", "0987654321", "456 Avenue, City",
                "user", "manager", "2024-01-05", "2024-02-05"
        ));

        // Add more SupplierMastersViewModel instances as needed for testing

        // Convert the list to a Page object
        Pageable pageable = PageRequest.of(0, supplierList.size());
        return new PageImpl<>(supplierList, pageable, supplierList.size());
    }

    @Override
    public List<SupplierMastersGstinViewModel> getAllGSTSuppliersByPan(IAuthContextViewModel auth, String pan) {
        return customSupplierMasterRepository.getAllGSTSuppliersByPan(auth.getCompanyCode(), pan);
    }

    @Override
    public ResponseEntity<Resource> getSupplierMastersDetailsExcelReport(IAuthContextViewModel auth) {
        try {
            List<SupplierMasterCombinedViewModel> allSupplierMasterDetails = customSupplierMasterRepository.getAllSupplierMasterDetails(auth);
            String filePath = getSupplierMastersDetailsExcel(allSupplierMasterDetails);
            Path file = Paths.get(filePath);
            Resource resource = new FileSystemResource(file.toFile());
            ResponseEntity<Resource> response = ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + file.getFileName().toString() + "\"")
                    .body(resource);

            return response;
        } catch (Exception e) {
            logger.error("Error occurred : " + e.getLocalizedMessage());
//            Files.deleteIfExists(file);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public List<SupplierMasterCombinedViewModel> getAllSupplierMastersDetailsForExcel(IAuthContextViewModel auth) {
        return customSupplierMasterRepository.getAllSupplierMasterDetails(auth);
    }

   /* @Override
    @Transactional
    public void saveSupplierMaster(List<SupplierMasterSyncRequestViewModel> requestList, IAuthContextViewModel auth) {
        // 1. Fetch existing CompanyMaster entries by PAN
        Map<String, CompanyMaster> companyMasterMap = companyMasterRepository.findByPanNumberIn(
                requestList.stream().map(SupplierMasterSyncRequestViewModel::getSellerPan).collect(Collectors.toSet())
        ).stream().collect(Collectors.toMap(CompanyMaster::getPanNumber, Function.identity()));

        // 2. Fetch existing Company entries by vendorCode and GST
        // TODO: check if OR can be used in condition in case vendorCode is null or empty OR Gst is "Not--Applicable"
        Map<String, Company> companyMap = companyRepository.findByVendorCodeAndGstCombination(
                requestList.stream().map(req -> (req.getSellerCode() != null ? req.getSellerCode() : "") + "-" + (req.getSellerGstin() != null ? req.getSellerGstin() : ""))
                        .collect(Collectors.toSet())
        ).stream().collect(Collectors.toMap(
                comp -> comp.getVendorCode() + "-" + comp.getGst(),
                Function.identity()
        ));

        for (SupplierMasterSyncRequestViewModel request : requestList) {
            CompanyMaster companyMaster = companyMasterMap.get(request.getSellerPan());
            if (companyMaster == null) {
                companyMaster = new CompanyMaster();
                companyMaster.setPanNumber(request.getSellerPan());
                companyMaster.setCreatedBy(request.getCreatedBy());
                companyMaster.setCreatedAt(DateTimeUtils.getCurrentTimestamp());

            }
            // Update CompanyMaster fields
            if (null == companyMaster.getIsInSyncWithErp()  ||  !companyMaster.getIsInSyncWithErp()) {
                updateCompanyMasterFields(companyMaster, request);
            }

            companyMasterRepository.save(companyMaster);

            // Store in the map
            companyMasterMap.putIfAbsent(request.getSellerPan(), companyMaster);

            String companyKey = request.getSellerCode() + "-" + request.getSellerGstin();
            Company company = companyMap.get(companyKey);
            if (company == null) {
                company = new Company();
                company.setVendorCode(request.getSellerCode());
                company.setGst(request.getSellerGstin());
                company.setCreatedBy(request.getCreatedBy());
                company.setCreatedAt(DateTimeUtils.getCurrentTimestamp());
                company.setCompanyMaster(companyMaster);


                if(!request.getSellerGstin().isEmpty()){
                    String firstTwo = request.getSellerGstin().length() >= 2 ? (request.getSellerGstin().trim()).substring(0, 2) : null;
                    StateCodes stateCode = stateCodeRepository.findByCode(firstTwo);
                    if(stateCode != null){
                        company.setStateCode(stateCode);
                        company.setStateCodeString(stateCode.getCode());
                    }
                }
            }

            // Update Company fields
            updateCompanyFields(company, request, auth);
            companyRepository.save(company);

            companyMaster.getCompanyList().add(company);
            companyMasterRepository.save(companyMaster);

            companyMap.put(companyKey, company);
        }
    }*/

    @Override
    @Transactional
    public void saveSupplierMaster(List<SupplierMasterSyncRequestViewModel> requestList, IAuthContextViewModel auth) {
        // Fetch all state codes once and store them in a Map for quick lookup
        Map<String, StateCodes> stateCodeMap = stateCodeRepository.findAll().stream()
                .collect(Collectors.toMap(StateCodes::getCode, Function.identity()));

        // 1. Fetch existing CompanyMaster entries by PAN
        Map<String, CompanyMaster> companyMasterMap = companyMasterRepository.findByPanNumberIn(
                requestList.stream()
                        .map(SupplierMasterSyncRequestViewModel::getSellerPan)
                        .collect(Collectors.toSet())
        ).stream().collect(Collectors.toMap(CompanyMaster::getPanNumber, Function.identity()));

        // 2. Fetch existing Company entries by vendorCode and GST
        Set<String> companyKeys = requestList.stream()
                .map(req -> (req.getSellerCode() == null ? "" : req.getSellerCode()) + "-" +
                        (req.getSellerGstin() == null ? "" : req.getSellerGstin()))
                .collect(Collectors.toSet());

        Map<String, Company> companyMap = companyRepository.findByVendorCodeAndGstCombination(companyKeys)
                .stream().collect(Collectors.toMap(
                        comp -> comp.getVendorCode() + "-" + comp.getGst(),
                        Function.identity()
                ));

        List<CompanyMaster> companyMastersToSave = new ArrayList<>();
        List<Company> companiesToSave = new ArrayList<>();

        for (SupplierMasterSyncRequestViewModel request : requestList) {
            // Fetch or create CompanyMaster
            CompanyMaster companyMaster = companyMasterMap.computeIfAbsent(request.getSellerPan(), pan -> {
                CompanyMaster newCompanyMaster = new CompanyMaster();
                newCompanyMaster.setPanNumber(pan);
                newCompanyMaster.setCreatedBy(request.getCreatedBy());
                newCompanyMaster.setCreatedAt(DateTimeUtils.getCurrentTimestamp());
                return newCompanyMaster;
            });

            // Update CompanyMaster fields if necessary
            if (companyMaster.getIsInSyncWithErp() == null || !companyMaster.getIsInSyncWithErp()) {
                updateCompanyMasterFields(companyMaster, request);
            }

            // Construct the unique company key
            String companyKey = (request.getSellerCode() == null ? "" : request.getSellerCode()) + "-" +
                    (request.getSellerGstin() == null ? "" : request.getSellerGstin());

            // Fetch or create Company
            Company company = companyMap.computeIfAbsent(companyKey, key -> {
                Company newCompany = new Company();
                newCompany.setVendorCode(request.getSellerCode());
                newCompany.setGst(request.getSellerGstin());
                newCompany.setCreatedBy(request.getCreatedBy());
                newCompany.setCreatedAt(DateTimeUtils.getCurrentTimestamp());
                newCompany.setCompanyMaster(companyMaster);

                // Set state code using preloaded map
                if (request.getSellerGstin() != null && !request.getSellerGstin().isEmpty()) {
                    String firstTwo = request.getSellerGstin().trim().substring(0, 2);
                    StateCodes stateCode = stateCodeMap.get(firstTwo);
                    if (stateCode != null) {
                        newCompany.setStateCode(stateCode);
                        newCompany.setStateCodeString(stateCode.getCode());
                    }
                }
                return newCompany;
            });

            // Update Company fields
            updateCompanyFields(company, request, auth);

            // Add to batch save lists
            companyMastersToSave.add(companyMaster);
            companiesToSave.add(company);
        }

        // Batch save for performance optimization
        companyMasterRepository.saveAll(companyMastersToSave);
        companyRepository.saveAll(companiesToSave);
    }


    private void updateCompanyMasterFields(CompanyMaster companyMaster, SupplierMasterSyncRequestViewModel request) {
        companyMaster.setCinNo(request.getSellerCinNo());
        companyMaster.setCinStatus(request.getSellerCinStatus());
        companyMaster.setContactPerson1(request.getSellerCompanyName()); // no contact name field from erp req
        companyMaster.setEmailId1(request.getSellerEmail1());
        companyMaster.setContactNo1(request.getSellerContact1());
        companyMaster.setCity(request.getSellerCity());
        companyMaster.setCountry(request.getSellerCountry());
        companyMaster.setTan(request.getSellerTanNo());
        companyMaster.setLegalName(request.getSupplierLegalName());
        companyMaster.setVendorCode(request.getSellerCode());
        companyMaster.setZipcode(request.getSellerZipCode());
        companyMaster.setState(request.getSellerState());
        companyMaster.setPanNumber(request.getSellerPan());
        companyMaster.setUpdatedAt(DateTimeUtils.getCurrentTimestamp());
        companyMaster.setUpdatedBy(request.getUpdatedBy());
        companyMaster.setIsInSyncWithErp(true);
    }
    public static Double parseDoubleSafely(String value, Double defaultValue) {
        try {
            return value != null && !value.isEmpty() ? Double.parseDouble(value) : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    private void updateCompanyFields(Company company, SupplierMasterSyncRequestViewModel request, IAuthContextViewModel auth) {
        company.setCamCompanyId(auth.getCompanyCode());
        company.setGstComplianceRating(parseDoubleSafely(request.getComplianceSellerRating(), 0.0));
        company.setItcRiskAmount(request.getItcRisk());
        company.setMsmeStatus(request.getMsmeStatus());
        company.setBankName(request.getSellerAccName());
        company.setBankAccountNumber(request.getSellerAccNo());
        company.setAddr1(request.getSellerAddress1());
        company.setAddr2(request.getSellerAddress2());
        company.setSupplierCompanyName(request.getSellerCompanyName());
        company.setPhoneNo(request.getSellerContact1());
        company.setEmailId(request.getSellerEmail1());

        //Renamed column name from ContactPersonPhone to PhoneNo2 and ContactPersonEmail to EmailId2
        company.setContactPersonPhone(request.getSellerContact2());
        company.setContactPersonEmail(request.getSellerEmail2());

        company.setIfscCode(request.getSellerIfsCode());

        company.setIsSellerBlocked(request.getIsSellerBlocked());
        company.setPin(request.getSellerZipCode());
        company.setUpdatedAt(DateTimeUtils.getCurrentTimestamp());
        company.setUpdatedBy(request.getUpdatedBy());
        company.setEInvoiceApplicable(request.getSellerEinvoiceStatus());
        company.setPaymentTerms(request.getSellerPaymentTerm());
    }

    public String getSupplierMastersDetailsExcel(List<SupplierMasterCombinedViewModel> details) throws IOException {
        // Create a temporary file in the system's temp directory

        File tempFile = null;
        try {
            tempFile = File.createTempFile("supplier-details", ".xlsx");
            tempFile.deleteOnExit();

            return excelUtil.generateSupplierMasterDetailsExcel(details, tempFile.getAbsolutePath());

        } catch (IOException e) {
            logger.error("Error generating the Excel report", e);
            if (tempFile != null && tempFile.exists()) {
                boolean fileDeleted = tempFile.delete();
                if (fileDeleted) {
                    logger.info("Temporary file deleted successfully");
                } else {
                    logger.info("There was an error deleting the tempoary file");
                }
            }
            throw new RemoteException(e.getMessage());
        }
    }
}
