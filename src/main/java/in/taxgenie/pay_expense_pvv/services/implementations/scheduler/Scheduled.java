package in.taxgenie.pay_expense_pvv.services.implementations.scheduler;


import in.taxgenie.pay_expense_pvv.repositories.company.IGstinRepository;
import in.taxgenie.pay_expense_pvv.viewmodels.company.OAuthProxyViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.mailroom.SyncGovInvoiceRequestViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;


@Component
public class Scheduled {

    @Value("${web-client-config.services.base-url-service}")
    private String baseUrlService;

    private final IGstinRepository repository;

    private final Logger logger;

    public Scheduled(IGstinRepository repository) {
        this.repository = repository;
        this.logger = LoggerFactory.getLogger(this.getClass());

    }

    // Cron: 10 AM and 4 PM every day
    @org.springframework.scheduling.annotation.Scheduled(cron = "0 0 10,16 * * ?")
    public void pullInvoiceJson() {
        logger.info("Running scheduled task at {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        try {

            OAuthProxyViewModel oAuthProxyViewModel = new OAuthProxyViewModel();
//            try {
//                oAuthProxyViewModel = gstinDetailService.getAccessTokenWithKeys(auth).block();
//                if (oAuthProxyViewModel == null) {
//                    logger.error("Failed to obtain access token");
//                    return new ResponseViewModel(false, "Something went wrong, please contact Administrator");
//                }
//            } catch (Exception e) {
//                logger.error("Failed to get access token: {}", e.getMessage(), e);
//                return new ResponseViewModel(false, "Something went wrong, please contact Administrator");
//            }

            repository.findAll().forEach( data->{


                String currentMonth = YearMonth.now().format(DateTimeFormatter.ofPattern("MMyyyy"));

                SyncGovInvoiceRequestViewModel syncGovInvoiceRequestViewModel = new SyncGovInvoiceRequestViewModel();

                syncGovInvoiceRequestViewModel.setGstinType("buyer");
                syncGovInvoiceRequestViewModel.setRtnprd(currentMonth);
                syncGovInvoiceRequestViewModel.setSupplyType("B2B");
                syncGovInvoiceRequestViewModel.setGstin(data.getGstinNo());
                syncGovInvoiceRequestViewModel.setApigeeToken(oAuthProxyViewModel.getTokenType() + " " + oAuthProxyViewModel.getAccessToken());
                syncGovInvoiceRequestViewModel.setStatusCallBackSUrl( baseUrlService + "/invoice_mailroom/invoice_sync_history");
                syncGovInvoiceRequestViewModel.setCallBackUrl(baseUrlService + "/invoice_mailroom/buyer_data_upload");
                syncGovInvoiceRequestViewModel.setCallBackAuthToken("Bearer " + "");

            });
            // Your task logic here
            logger.info("Performing invoice sync...");

            // Example: Call service or perform DB operation
            // invoiceSyncService.syncPendingInvoices();

            logger.info("Invoice sync completed successfully.");

        } catch (Exception e) {
            logger.error("Error while performing scheduled task", e);
        }
    }
}
