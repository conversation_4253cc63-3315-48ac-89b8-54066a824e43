package in.taxgenie.pay_expense_pvv.services.implementations;


import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ItemMaster;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import in.taxgenie.pay_expense_pvv.repositories.IItemMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.item_master.CustomItemMasterRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IItemMasterService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.StringUtils;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.item_master.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class ItemMasterServiceImplementation implements IItemMasterService {

    private final IItemMasterRepository itemMasterRepository;
    private final Logger logger;
    private final CustomItemMasterRepository customItemMasterRepository;

    public ItemMasterServiceImplementation(IItemMasterRepository itemMasterRepository, CustomItemMasterRepository customItemMasterRepository) {
        this.itemMasterRepository = itemMasterRepository;
        this.customItemMasterRepository = customItemMasterRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public List<ItemMasterViewModel> getItemsByType(String type, IAuthContextViewModel auth) {
        logger.trace("getItemsByType: Returning view model list");

        List<ItemMaster> items = itemMasterRepository
                .findByTypeAndCompanyCodeAndStatusIsTrue(ItemType.valueOf(type.toUpperCase()), Long.valueOf(auth.getCompanyCode()).intValue());

        return getItemMasterViewModel(items);
    }

    @Override
    public CreateItemMasterViewModel getItemsByItemCode(String itemCode, IAuthContextViewModel auth) {

        CreateItemMasterViewModel response = new CreateItemMasterViewModel();
        try{

            Optional<ItemMaster> itemMasterOptional = itemMasterRepository.findFirstByItemCodeAndCompanyCodeAndStatus(itemCode, auth.getCompanyCode(), true);

            itemMasterOptional.ifPresent(response::getViewModel);

            return response;
        }catch (Exception exception){
            logger.error("Exception in getItemsByItemCode method : " + exception.getMessage());
        }
        return null;
    }

    @Override
    public UniqueViewModel checkItemCodeIsUnique(ItemMasterUniqueCheckRequestViewModel request, IAuthContextViewModel auth) {
        UniqueViewModel response = new UniqueViewModel();
        try {

            String itemCode = request.getItemCode().trim();
            String itemUuis = null != request.getId() ? String.valueOf(request.getId()) : "";

            if(itemCode.isEmpty() || itemCode.isBlank()){
                response.setUnique(false);
                response.setMessage("Please enter valid item code");
                return response;
            }

            Optional<ItemMaster> itemMaster = itemMasterRepository.findFirstByItemCodeAndCompanyCodeAndStatus(itemCode, auth.getCompanyCode(), true);
            if(!itemMaster.isPresent()){
                response.setUnique(true);
                response.setMessage("Item code is unique");
            } else if(itemMaster.isPresent() && !"".equals(itemUuis) && !itemUuis.isEmpty()
                    &&null != itemMaster.get().getItemUuid()
                    && itemMaster.get().getItemUuid().equals(itemUuis)){
                response.setUnique(true);
                response.setMessage("Item code is unique");
            }else {
                response.setUnique(false);
                response.setMessage("Item code is not unique");
            }
        } catch (Exception exception){
            logger.error("Exception in checkItemCodeIsUnique method : " + exception.getMessage());
            response.setUnique(false);
            response.setMessage("Item code is not unique. Exception Message : " + exception.getMessage());
        }

        return response;
    }

    @Override
    public List<SupplierViewModel> getSupplierByItemCode(String itemCode, IAuthContextViewModel auth) {
        List<SupplierViewModel> response = new ArrayList<>();
        try{

            List<ItemMaster> itemMasterList = itemMasterRepository.findByItemCodeAndCompanyCodeAndStatus(itemCode, auth.getCompanyCode(), true);

            if(!itemMasterList.isEmpty()){
                for(ItemMaster itemMaster: itemMasterList){
                    if(null != itemMaster.getVendorId()) {
                        SupplierViewModel supplierViewModel = new SupplierViewModel();
                        supplierViewModel.getViewModel(itemMaster);
                        response.add(supplierViewModel);
                    }
                }
            }

            return response;
        }catch (Exception exception){
            logger.error("Exception in getItemsByItemCode method : " + exception.getMessage());
        }
        return List.of();
    }

    @Override
    public boolean deleteSupplierByItemId(Long id, IAuthContextViewModel auth) {

        try{
            logger.info("deleteSupplierByItemId : " + id);

            Optional<ItemMaster> itemMasterOptional = itemMasterRepository.findByIdAndCompanyCode(id, auth.getCompanyCode());
            if(itemMasterOptional.isPresent()){
                ItemMaster itemMaster = itemMasterOptional.get();

                itemMaster.setStatus(false);

                itemMaster.setUpdatedBy((int) auth.getUserId());
                itemMaster.setUpdatedAt(DateTimeUtils.getCurrentTimestamp());
                itemMasterRepository.save(itemMaster);
            }
            return true;
        }catch (Exception exception){
            logger.error("Exception in deleteSupplierByItemId method : " + exception.getMessage());
            return false;
        }

    }

    @Override
    public List<ItemMasterViewModel> getItemsByTypeAndVendor(String type, Long vendorId, IAuthContextViewModel auth) {
        logger.trace("getItemsByTypeAndVendor: Returning view model list");

        List<ItemMaster> items = itemMasterRepository
                .findByCompanyCodeAndTypeAndVendorIdAndStatus(auth.getCompanyCode(), ItemType.valueOf(type.toUpperCase()), vendorId, true);

       return getItemMasterViewModel(items);
    }

    @Override
    public GenericPageableViewModel<ItemMasterQueueViewModel> getQueue(QueueFilterViewModel filterValues, IAuthContextViewModel auth) {

        logger.info(String.format("BudgetQueueServiceImplementation-getQueue: Called for company id: %s", auth.getCompanyCode()));
        GenericPageableViewModel<ItemMasterQueueViewModel> pagedQueue = new GenericPageableViewModel<>();

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize());

        Page<ItemMasterQueueViewModel> viewModelPaged = customItemMasterRepository.getItemMasterQueue(auth.getCompanyCode(), filterValues, pageable);

        // Set response
        pagedQueue.setData(viewModelPaged.getContent());
        pagedQueue.setPages(viewModelPaged.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(viewModelPaged.getTotalElements()));

       return pagedQueue;

    }

    @Override
    public CreateItemMasterViewModel createUpdateItemMaster(CreateItemMasterViewModel createItemMasterViewModel, IAuthContextViewModel auth) {

        CreateItemMasterViewModel response = new CreateItemMasterViewModel();
        try{

            List<ItemMaster> itemMasterDBList = new ArrayList<>();

            String itemCode = createItemMasterViewModel.getItemCode().trim();
            String itemUuid = createItemMasterViewModel.getUuid();

            if(StringUtils.isNullOrEmpty(itemUuid))
            {
                //Create
                //check for unique item code
                List<ItemMaster> existingItemMaster = itemMasterRepository.findByItemCodeAndCompanyCode(createItemMasterViewModel.getItemCode(), auth.getCompanyCode());
                if(!existingItemMaster.isEmpty()){
                    logger.error("ITEM CODE : " + createItemMasterViewModel.getItemCode() + " already exists for company code : " + auth.getCompanyCode());
                    return null;
                }
                itemMasterDBList.add(createItemMasterViewModel.getCreateEntityFromViewModel(auth));
            } else{
                //Update
                List<ItemMaster> itemMasterList = itemMasterRepository.findByItemUuidAndCompanyCodeAndStatus(
                        itemUuid,
                        auth.getCompanyCode(), true);

                if(!itemMasterList.isEmpty()){
                    for(ItemMaster itemMaster : itemMasterList) {
                        itemMasterDBList.add(createItemMasterViewModel.getUpdateEntityFromViewModel(itemMaster, auth));
                    }
                } else {
                    logger.error("ITEM UUID : " + createItemMasterViewModel.getUuid() + " does not exist for company code : " + auth.getCompanyCode());
                    return null;
                }
            }

            List<ItemMaster> dbItemMasterList = itemMasterRepository.saveAll(itemMasterDBList);

            response.getViewModel(dbItemMasterList.get(0));

            return response;

        } catch (Exception exception){
            logger.error("Exception in createUpdateItemMaster method : " + exception.getMessage());
        }
        return null;
    }

    @Override
    public boolean createUpdateItemSupplier(ListSupplierViewModel supplierViewModelList, IAuthContextViewModel auth) {

        try{

            List<ItemMaster> itemMasterDBList = new ArrayList<>();

            for (SupplierViewModel supplierViewModel : supplierViewModelList.getData()){

                if(null != supplierViewModel.getId()){
                    //Update call
                    Optional<ItemMaster> itemMasterOptional = itemMasterRepository.findByIdAndCompanyCode(supplierViewModel.getId(), auth.getCompanyCode());
                    itemMasterOptional.ifPresent(itemMaster -> itemMasterDBList.add(supplierViewModel.getUpdateEntityFromViewModel(itemMaster, auth)));
                } else{

                    Optional<ItemMaster> itemMasterWithVendorOptional = itemMasterRepository.findByItemCodeAndVendorIdAndCompanyCode(supplierViewModel.getItemCode(), supplierViewModel.getVendorId(), auth.getCompanyCode());

                    if(itemMasterWithVendorOptional.isPresent()){
                        //Update existing
                        itemMasterWithVendorOptional.ifPresent(itemMaster -> itemMasterDBList.add(supplierViewModel.getUpdateEntityFromViewModel(itemMasterWithVendorOptional.get(), auth)));
                    } else {
                        //Create call
                        Optional<ItemMaster> itemMasterOptional = itemMasterRepository.findFirstByItemCodeAndCompanyCodeAndStatus(supplierViewModel.getItemCode(), auth.getCompanyCode(), true);
                        itemMasterOptional.ifPresent(itemMaster -> itemMasterDBList.add(supplierViewModel.getCreateEntityFromViewModel(itemMaster, auth)));
                    }
                }
            }
            itemMasterRepository.saveAll(itemMasterDBList);
            return true;

        } catch (Exception exception){
            logger.error("Exception in createUpdateItemMaster method : " + exception.getMessage());
        }
        return false;
    }

    private List<ItemMasterViewModel> getItemMasterViewModel(List<ItemMaster> items) {
        // Group items by their name
        Map<String, List<ItemMaster>> groupedByName = items.stream()
                .collect(Collectors.groupingBy(ItemMaster::getName));

        return groupedByName.entrySet().stream()
                .map(entry -> {
                    List<VendorItemsViewModel> vendorItemData = entry.getValue().stream()
                            .map(this::getViewModel)
                            .collect(Collectors.toList());
                    return new ItemMasterViewModel(entry.getKey(), vendorItemData);
                })
                .collect(Collectors.toList());
    }

    private VendorItemsViewModel getViewModel(ItemMaster entity) {
        VendorItemsViewModel viewModel = new VendorItemsViewModel();
        BeanUtils.copyProperties(entity, viewModel);
        if (null != entity.getVendor()) {
            viewModel.setVendorId(entity.getVendor().getCompanyId().longValue());
            viewModel.setVendorName(entity.getVendor().getSupplierCompanyName());
            viewModel.setRate(entity.getSupplierRate());
        } else {
            viewModel.setVendorId(0l);
            viewModel.setVendorName("NA");
        }
        viewModel.setHsn(entity.getHsnOrSac());
        return viewModel;
    }
}
