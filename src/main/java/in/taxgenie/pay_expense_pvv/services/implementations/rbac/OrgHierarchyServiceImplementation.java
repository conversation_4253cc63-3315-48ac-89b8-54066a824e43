package in.taxgenie.pay_expense_pvv.services.implementations.rbac;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Users;
import in.taxgenie.pay_expense_pvv.entities.rbac.KeyValueMapping;
import in.taxgenie.pay_expense_pvv.entities.rbac.MappingValuesLabelsHierarchy;
import in.taxgenie.pay_expense_pvv.entities.rbac.OrgHierarchyStructure;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.multitenancy.context.TenantContext;
import in.taxgenie.pay_expense_pvv.repositories.IUsersRepository;
import in.taxgenie.pay_expense_pvv.repositories.rbac.IKeyValueMappingRepository;
import in.taxgenie.pay_expense_pvv.repositories.rbac.IMappingValuesLabelsHierarchyRepository;
import in.taxgenie.pay_expense_pvv.repositories.rbac.IOrgHierarchyRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.rbac.IOrgHierarchyService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.HierarchyMasterTreeDataViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.HierarchyMasterTreeViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.OrgStructureViewWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
public class OrgHierarchyServiceImplementation implements IOrgHierarchyService {
    private final Logger logger;
    private final IOrgHierarchyRepository orgHierarchyRepository;
    private final IKeyValueMappingRepository keyValueMappingRepository;
    private final IMappingValuesLabelsHierarchyRepository mappingValuesLabelsHierarchyRepository;
    private final IUsersRepository usersRepository;

    public OrgHierarchyServiceImplementation(IOrgHierarchyRepository orgHierarchyRepository, IKeyValueMappingRepository keyValueMappingRepository, IMappingValuesLabelsHierarchyRepository mappingValuesLabelsHierarchyRepository, IUsersRepository usersRepository) {
        this.mappingValuesLabelsHierarchyRepository = mappingValuesLabelsHierarchyRepository;
        this.usersRepository = usersRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
        this.orgHierarchyRepository = orgHierarchyRepository;
        this.keyValueMappingRepository = keyValueMappingRepository;
    }

    @Override
    @Transactional
    public HierarchyMasterTreeViewModel create(String key,
                                               HierarchyMasterTreeViewModel hierarchyCreateViewModelWrapper,
                                               IAuthContextViewModel auth) {

        List<OrgHierarchyStructure> existingHierarchy = orgHierarchyRepository.findRootNodeForStructure(auth.getCompanyCode());

        // Sanity Checks
        if (existingHierarchy.size() > 1) {
            throw new DomainInvariantException(String.format("Multiple org trees found for key: %s", key));
        }
        // handle key change scenario - delete existing hierarchy
        if (!existingHierarchy.isEmpty() && !existingHierarchy.get(0).getKey().equals(key)) {
            // Delete the existing hierarchy
            deleteOrgHierarchy(existingHierarchy.get(0));
        }
        OrgHierarchyStructure orgHierarchyStructure = createOrgHierarchyTree(hierarchyCreateViewModelWrapper, null, key, auth, 0, new HashSet<>());

        orgHierarchyStructure = orgHierarchyRepository.saveAndFlush(orgHierarchyStructure);

        // save/update MappingValuesLabelsHierarchy - Async method - commented async
        /*MappingValuesLabelsHierarchy stores -
         * - Labels and values
         *  - Values will be collection of values from parent and children labels
         * */
        createMappingOfValuesAndLabels(key, auth, orgHierarchyStructure);
        return getViewModel(orgHierarchyStructure);

    }

    @Override
    @Transactional
    public void createMappingOfValuesAndLabels(String key, IAuthContextViewModel auth, OrgHierarchyStructure orgHierarchyStructure) {
        // Collect labels and values from the hierarchy
        Map<String, Set<String>> aggregatedLabels = collectLabelsAndValues(orgHierarchyStructure, new HashMap<>());

        // Delete existing mappings for these labels
        deleteExistingMappings(auth.getCompanyCode());

        // Fetch values asynchronously
//        CompletableFuture<Map<String, Set<String>>> labelValueFuture = fetchValuesByLabels(auth.getCompanyCode(), key, orgHierarchyStructure, aggregatedLabels);
        Map<String, Set<String>> labelValueMap = fetchValuesByLabels(auth.getCompanyCode(), key, orgHierarchyStructure, aggregatedLabels);
        // Save label-value pairs asynchronously
        saveMappingValuesLabelsHierarchy(labelValueMap, auth.getCompanyCode());
    }

    private OrgHierarchyStructure createOrgHierarchyTree(HierarchyMasterTreeViewModel node,
                                                         OrgHierarchyStructure parent, String key,
                                                         IAuthContextViewModel auth,
                                                         int level, Set<String> parentMasters) {
        if (node == null) {
            return null;
        }

        HierarchyMasterTreeDataViewModel nodeData = node.getData();
        OrgHierarchyStructure hierarchyStructure = createNewMappingMaster(nodeData, parent, key, auth, level /*keyValueMapping*/);

        // TODO: re-check this - if in unmapped values there is possibility of duplicate label then this will not work - as id will always be different
        if (parentMasters.contains(nodeData.getDisplayName())) {
            logger.info("createOrgHierarchy: The child node {} is already in a parent of the current node", node);
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_DUPLICATE_NODE);
        }

        parentMasters.add(nodeData.getDisplayName());

        // Increment the level for the all children below
        level++;
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (HierarchyMasterTreeViewModel child : node.getChildren()) {
                OrgHierarchyStructure childNode = createOrgHierarchyTree(child, hierarchyStructure, key, auth, level, parentMasters);
                hierarchyStructure.addChildren(childNode);
            }
        }

        return hierarchyStructure;
    }

    private OrgHierarchyStructure createNewMappingMaster(HierarchyMasterTreeDataViewModel viewModel,
                                                         OrgHierarchyStructure parent, String key,
                                                         IAuthContextViewModel auth,
                                                         int level) {
        OrgHierarchyStructure mappingMaster = new OrgHierarchyStructure();


        // Set base attributes
        mappingMaster.setId(viewModel.getId());
        mappingMaster.setCompanyCode(auth.getCompanyCode());
        mappingMaster.setCreatingUserId(auth.getUserId());
        mappingMaster.setActive(true);
        mappingMaster.setUuid(UUID.randomUUID());

        mappingMaster.setKey(key);
        mappingMaster.setDisplayName(viewModel.getDisplayName());
        mappingMaster.setLevel(level);

        // Set parent
        mappingMaster.setParent(parent);

        return mappingMaster;
    }

    private HierarchyMasterTreeViewModel getViewModel(OrgHierarchyStructure node) {
        if (node == null) {
            return null;
        }
        HierarchyMasterTreeViewModel viewModel = setOrgHierarchyDataViewModel(node);

        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (OrgHierarchyStructure child : node.getChildren()) {
                viewModel.addChildren(getViewModel(child));
            }
        }

        return viewModel;
    }

    private HierarchyMasterTreeViewModel setOrgHierarchyDataViewModel(OrgHierarchyStructure node) {
        HierarchyMasterTreeViewModel viewModel = new HierarchyMasterTreeViewModel();
        HierarchyMasterTreeDataViewModel dataViewModel = new HierarchyMasterTreeDataViewModel();

        // Set Data model with currentId and parentId if present;
        dataViewModel.setDisplayName(node.getDisplayName());
//        dataViewModel.setKey(node.getKey());
        dataViewModel.setId(node.getId());

        OrgHierarchyStructure parent = node.getParent();
        dataViewModel.setParentId(parent == null ? null : parent.getId());

        viewModel.setExpanded(true);
        viewModel.setData(dataViewModel);

        return viewModel;

    }

    @Override
    public OrgStructureViewWrapper getTree(IAuthContextViewModel auth, Long keyValueMappingId) {
        List<OrgHierarchyStructure> availableRoots = orgHierarchyRepository.findRootNodeForStructure(auth.getCompanyCode());
        OrgStructureViewWrapper viewWrapperResponse = new OrgStructureViewWrapper();
        // Sanity Checks
        if (availableRoots.size() > 1) {
            throw new DomainInvariantException(String.format("Multiple org trees found for key: %s", availableRoots.get(0).getKey()));
        }

        // If no root found, then not yet created so return empty object.
        if (availableRoots.isEmpty()) {
            logger.info(String.format("getOrgHierarchyStructure: Cannot find the root for the org hierarchy structure for company: %s. This may be a new mapping init", auth.getCompanyCode()));
            return null;
        }

        OrgHierarchyStructure root = availableRoots.get(0);
        HierarchyMasterTreeViewModel viewModel = getViewModel(root);
        viewWrapperResponse.setTree(viewModel);
        viewWrapperResponse.setKey(root.getKey());
        return viewWrapperResponse;
    }

    @Override
    @Transactional
    public void delete(String key, IAuthContextViewModel auth) {
        try {
            String orgKey = orgHierarchyRepository.findDistinctKeyByCompanyCode(auth.getCompanyCode());
            // handle delete non-hierarchy key selection from UI
            if(orgKey == null || !orgKey.equalsIgnoreCase(key))
                return;

            List<OrgHierarchyStructure> existingHierarchy = orgHierarchyRepository.findRootNodeForStructure(auth.getCompanyCode());
            if(existingHierarchy == null || existingHierarchy.isEmpty())
                return;
            // Sanity Checks
            if (existingHierarchy.size() > 1) {
                throw new DomainInvariantException(String.format("Multiple org trees found for key: %s", key));
            }
            // delete mapping_labels_value_hierarchy first
            deleteExistingMappings(auth.getCompanyCode());
            // delete org hierarchy
            deleteOrgHierarchy(existingHierarchy.get(0));
        } catch (Exception ex) {
            logger.error("An Error {} occurred on delete hierarchy for company code {}", ex.getMessage(), auth.getCompanyCode());
            throw new DomainInvariantException("An Error occurred on delete hierarchy for company code" + auth.getCompanyCode());
        }
    }

    private Map<String, Set<String>> collectLabelsAndValues(OrgHierarchyStructure node, Map<String, Set<String>> labelValueMap) {
        if (node == null) {
            return labelValueMap;
        }

        // Initialize the current node's value set
        Set<String> currentValues = collectLabels(node, new HashSet<>());
        labelValueMap.computeIfAbsent(node.getDisplayName(), k -> currentValues);

        // Recursively process children
        if (node.getChildren() != null) {
            for (OrgHierarchyStructure child : node.getChildren()) {
                // Recursively collect child values
                collectLabelsAndValues(child, labelValueMap);
            }
        }

        return labelValueMap;
    }

    private Set<String> collectLabels(OrgHierarchyStructure node, Set<String> labels) {

        if (node == null) return labels;

        if (node.getDisplayName() != null) {
            labels.add(node.getDisplayName());
        }

        if (node.getChildren() != null) {
            for (OrgHierarchyStructure child : node.getChildren()) {
                collectLabels(child, labels);
            }
        }

        return labels;
    }

//    @Async
    public Map<String, Set<String>> fetchValuesByLabels(long companyCode, String key, OrgHierarchyStructure rootNode, Map<String, Set<String>> labelValueMap) {

        // Map to hold final results
//        Long currentTenant = TenantContext.getCurrentTenant();
//        System.out.println("Current Tenant in @Async: " + currentTenant);

        Set<String> allLabels = labelValueMap.keySet();

        // Fetch all matching KeyValueMapping entries
        List<KeyValueMapping> keyValueMappings = keyValueMappingRepository.findByCompanyCodeAndKeyAndLabelIn(companyCode, key, allLabels);

        // Create a lookup map for easier filtering
        Map<String, List<String>> labelToValuesMap = keyValueMappings.stream()
                .collect(Collectors.groupingBy(KeyValueMapping::getLabel,
                        Collectors.mapping(KeyValueMapping::getValue, Collectors.toList())));

        // Enrich the original labelValueMap
        for (Map.Entry<String, Set<String>> entry : labelValueMap.entrySet()) {
            String parentLabel = entry.getKey();
            Set<String> childLabels = entry.getValue();

            // Collect values for parent label
            Set<String> aggregatedValues = new HashSet<>();

            // Include values of all child labels
            for (String childLabel : childLabels) {
                List<String> values = labelToValuesMap.getOrDefault(childLabel, Collections.emptyList());
                aggregatedValues.addAll(values);
            }

            // Update the map with aggregated values
            labelValueMap.put(parentLabel, new HashSet<>(aggregatedValues));
        }

//        return CompletableFuture.completedFuture(labelValueMap);
        return labelValueMap;
    }

//    @Async
    public void saveMappingValuesLabelsHierarchy(Map<String, Set<String>> labelValueMap, long companyCode) {
        if (labelValueMap == null || labelValueMap.isEmpty()) return;

        List<MappingValuesLabelsHierarchy> mappings = new ArrayList<>();

        for (Map.Entry<String, Set<String>> entry : labelValueMap.entrySet()) {
            String label = entry.getKey();
            Set<String> values = entry.getValue();

            for (String value : values) {
                MappingValuesLabelsHierarchy mapping = new MappingValuesLabelsHierarchy();
                mapping.setLabel(label);
                mapping.setValue(value);
                mapping.setCompanyCode(companyCode);
                mappings.add(mapping);
            }
        }

        // Save all mappings in bulk for efficiency
        mappingValuesLabelsHierarchyRepository.saveAll(mappings);
    }

    private void deleteExistingMappings(Long companyCode) {
        mappingValuesLabelsHierarchyRepository.deleteByCompanyCode(companyCode);
        mappingValuesLabelsHierarchyRepository.flush();
    }

    private void deleteOrgHierarchy(OrgHierarchyStructure hierarchy) {
        if (hierarchy == null) {
            return;
        }

        // Recursively delete children
        for (OrgHierarchyStructure child : hierarchy.getChildren()) {
            deleteOrgHierarchy(child);
        }

        // Delete the current node
        orgHierarchyRepository.delete(hierarchy);
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> getValuesForRbac(Long userId, Long companyCode) {
        logger.info("getting values for rbac for userId {} and for companyCode {}", userId, companyCode);
        String key = orgHierarchyRepository.findDistinctKeyByCompanyCode(companyCode);
        if (StaticDataRegistry.isNullOrEmptyOrWhitespace(key)) {
            logger.error("No key found for the provided company code {}", companyCode);
            return Collections.emptyList();
        }

        // Fetch the value from Users table using userId and key
        Users user = usersRepository.findByUserId(userId.intValue())
                .orElseThrow(() -> new RecordNotFoundException("No user found for the provided userId"));

        String value = findValueForKey(user, key);
        if (value == null) {
            logger.error(String.format("No value found for key '%s' in user data", key));
            return Collections.emptyList();
        }

        // get the associated label
        String label = keyValueMappingRepository.findLabelByKeyAndValueAndCompanyCode(key, value, companyCode);
        // if label is present, it is not used in hierarchy then also return original value as list
        if (StaticDataRegistry.isNullOrEmptyOrWhitespace(label) || !orgHierarchyRepository.existsByLabel(label)) {
            // If no label found, return the original value as a list
            // handles case 3 in BRD
            return Collections.singletonList(value);
        }

        // get associated values
        List<String> associatedValues = mappingValuesLabelsHierarchyRepository.findValuesByLabelAndCompanyCode(label, companyCode);
        // handles case 4 in BRD
        List<String> valuesWhereLabelIsNotAssigned = keyValueMappingRepository.findValuesByCompanyCodeAndKeyWhereLabelIsNotAssigned(companyCode, key);
        associatedValues.addAll(valuesWhereLabelIsNotAssigned);

        return associatedValues.isEmpty() ? Collections.singletonList(value) : associatedValues;
    }

    private String findValueForKey(Users user, String key) {
        Map<String, String> keyValueMap = new HashMap<>();
        keyValueMap.put(user.getIdentifierKey01(), user.getIdentifierValue01());
        keyValueMap.put(user.getIdentifierKey02(), user.getIdentifierValue02());
        keyValueMap.put(user.getIdentifierKey03(), user.getIdentifierValue03());
        keyValueMap.put(user.getIdentifierKey04(), user.getIdentifierValue04());
        keyValueMap.put(user.getIdentifierKey05(), user.getIdentifierValue05());
        keyValueMap.put(user.getIdentifierKey06(), user.getIdentifierValue06());
        keyValueMap.put(user.getIdentifierKey07(), user.getIdentifierValue07());
        keyValueMap.put(user.getIdentifierKey08(), user.getIdentifierValue08());
        keyValueMap.put(user.getIdentifierKey09(), user.getIdentifierValue09());
        keyValueMap.put(user.getIdentifierKey10(), user.getIdentifierValue10());

        return keyValueMap.get(key);
    }
}
