package in.taxgenie.pay_expense_pvv.services.implementations.pr;


import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cloud.GcpCSFileIOProvider;
import in.taxgenie.pay_expense_pvv.cloud.interfaces.IGcpCSFileIOProvider;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestHeader;
import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestItem;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.*;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetRepository;
import in.taxgenie.pay_expense_pvv.repositories.documents.IDocumentStorageRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.pr.IPurchaseRequestHeaderRepository;
import in.taxgenie.pay_expense_pvv.repositories.pr.IPurchaseRequestItemRepository;
import in.taxgenie.pay_expense_pvv.services.implementations.budget.BudgetServiceImplementation;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentApprovalContainerUserService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmployeeMasterDataService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IFileIOService;
import in.taxgenie.pay_expense_pvv.services.interfaces.documents.IDocumentManagementService;
import in.taxgenie.pay_expense_pvv.services.interfaces.pr.IPurchaseRequestService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.MultiServiceClientFactory;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.ConsumedItemAvailableCountViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetDetails;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetSelectionViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderItemDetailsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Transactional
public class PurchaseRequestServiceImplementation implements IPurchaseRequestService {

    private final IDocumentApprovalContainerRepository reportRepository;
    private final IDocumentRuleRepository ruleRepository;
    private final IDocumentSubgroupRepository subgroupRepository;
    private final IEmployeeMasterDataService employeeService;
    private final ILocationRepository locationRepository;
    private final IMetadataLimitRuleRepository metadataLimitRuleRepository;
    private final IFileIOService fileIOService;
    private final IDocumentManagementService documentManagementService;
    private final ILookupRepository lookupRepository;
    private final IDocumentRepository documentRepository;
    private final IBudgetRepository budgetRepository;
    private final IDocumentApprovalContainerUserService documentApprovalContainerUserService;
    private final BudgetServiceImplementation budgetServiceImplementation;
    private final ICompanyRepository companyRepository;
    private final GcpCSFileIOProvider gsFileIOProvider;
    private final MultiServiceClientFactory multiServiceClientFactory;
    private final Logger logger;
    private final IPurchaseRequestHeaderRepository purchaseRequestHeaderRepository;
    private final IPurchaseRequestItemRepository purchaseRequestLineItemRepository;
    private final IUsersRepository usersRepository;
    private final IItemMasterRepository itemMasterRepository;

    private final IDocumentStorageRepository documentStorageRepository;
    private final IDocumentApprovalContainerRepository documentApprovalContainerRepository;
    private final CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository;
    private final IGcpCSFileIOProvider gcpCSFileIOProvider;


    public PurchaseRequestServiceImplementation(IDocumentApprovalContainerRepository reportRepository, IDocumentRuleRepository ruleRepository, IDocumentSubgroupRepository subgroupRepository, IEmployeeMasterDataService employeeService, ILocationRepository locationRepository, IMetadataLimitRuleRepository metadataLimitRuleRepository, IFileIOService fileIOService, IDocumentManagementService documentManagementService, ILookupRepository lookupRepository,
                                                IDocumentRepository expenseRepository, IBudgetRepository budgetRepository, IDocumentApprovalContainerUserService documentApprovalContainerUserService, BudgetServiceImplementation budgetServiceImplementation, ICompanyRepository companyRepository, GcpCSFileIOProvider gsFileIOProvider, MultiServiceClientFactory multiServiceClientFactory, IPurchaseRequestHeaderRepository purchaseRequestHeaderRepository, IPurchaseRequestItemRepository purchaseRequestLineItemRepository, IUsersRepository usersRepository, IItemMasterRepository itemMasterRepository, IDocumentStorageRepository documentStorageRepository, IDocumentApprovalContainerRepository documentApprovalContainerRepository, CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository, IGcpCSFileIOProvider gcpCSFileIOProvider) {
        this.reportRepository = reportRepository;
        this.ruleRepository = ruleRepository;
        this.subgroupRepository = subgroupRepository;
        this.employeeService = employeeService;
        this.locationRepository = locationRepository;
        this.metadataLimitRuleRepository = metadataLimitRuleRepository;
        this.fileIOService = fileIOService;
        this.documentManagementService = documentManagementService;
        this.lookupRepository = lookupRepository;
        this.documentRepository = expenseRepository;
        this.budgetRepository = budgetRepository;
        this.documentApprovalContainerUserService = documentApprovalContainerUserService;
        this.budgetServiceImplementation = budgetServiceImplementation;
        this.companyRepository = companyRepository;
        this.gsFileIOProvider = gsFileIOProvider;
        this.multiServiceClientFactory = multiServiceClientFactory;
        this.purchaseRequestLineItemRepository = purchaseRequestLineItemRepository;
        this.usersRepository = usersRepository;
        this.itemMasterRepository = itemMasterRepository;
        this.documentStorageRepository = documentStorageRepository;
        this.documentApprovalContainerRepository = documentApprovalContainerRepository;
        this.customDocumentApprovalContainerRepository = customDocumentApprovalContainerRepository;
        this.gcpCSFileIOProvider = gcpCSFileIOProvider;
        this.logger = LoggerFactory.getLogger(this.getClass());
        this.purchaseRequestHeaderRepository = purchaseRequestHeaderRepository;
    }

    @Transactional
    public DocumentCreateResponse create(Long metadataId, Long subgroupId, IAuthContextViewModel auth) {
        DocumentCreateResponse response = new DocumentCreateResponse();

        Long documentApprovalContainerId = documentApprovalContainerUserService.create(metadataId, auth).getId();


        DocumentApprovalContainer documentApprovalContainer = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), documentApprovalContainerId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find documentApprovalContainer with id: %d", metadataId)));

        if (!isParentExpenseReportSubmittable(documentApprovalContainer)) {
            throw new DomainInvariantException("Parent expense documentApprovalContainer is neither in draft or sent-back state; hence not submittable");
        }
        DocumentSubgroup subgroup = subgroupRepository.findByCompanyCodeAndId(auth.getCompanyCode(), subgroupId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find subgroup with id: %d", subgroupId)));

        logger.info("create: Creating a new Purchase Request entity");

        Document document = new Document();

        createDocument(document, documentApprovalContainer, subgroup, auth);

        logger.info("create: Saving both Document and Document Approval Container");
        documentRepository.saveAndFlush(document);
        reportRepository.saveAndFlush(documentApprovalContainer);

        PurchaseRequestHeader prHeader = new PurchaseRequestHeader();
//        LookupData invoiceType = lookupRepository.findByTypeAndValue(StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_INVOICE);

        prHeader.setCreatingUserId(auth.getUserId());

        createPRHeader(prHeader, document);

        subgroupRepository.saveAndFlush(subgroup);

        logger.info("create: Save successful");
        purchaseRequestHeaderRepository.saveAndFlush(prHeader);
        documentRepository.saveAndFlush(document);

        response.setContainerId(documentApprovalContainer.getId());
        return response;
    }

    @Transactional
    @Override
    public DocumentCreateResponse save(PurchaseRequestCreateViewModel prCreateViewModel, IAuthContextViewModel auth) {
        DocumentCreateResponse response = new DocumentCreateResponse();

        PurchaseRequestHeader prHeader =
                purchaseRequestHeaderRepository.findById(prCreateViewModel.getPrDetails().getId())
                        .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find PR with id: %d", prCreateViewModel.getPrDetails().getId())));

        logger.info(String.format("save: Getting document and document header for PR header id: {%d}", prCreateViewModel.getPrDetails().getId()));
        Document document = prHeader.getDocument();
        DocumentApprovalContainer documentApprovalContainer = document.getDocumentApprovalContainer();

        saveDocument(document, prCreateViewModel, auth);
        logger.info(String.format("save: Preparing the documentApprovalContainer with id: {%d} from the invoice header id: {%d}", document.getDocumentApprovalContainerId(), prCreateViewModel.getPrDetails().getId()));
        saveDocumentApprovalContainer(documentApprovalContainer, prCreateViewModel);
        logger.info("create: Saving both Document and Document Approval Container");
        documentRepository.saveAndFlush(document);
        reportRepository.saveAndFlush(documentApprovalContainer);

        preparePRHeader(prHeader, prCreateViewModel, auth);
        prHeader.setUpdatingUserId(auth.getUserId());

        logger.info("Mapping supporting docs to Document if any");
        documentManagementService.performMappingDocumentToSupportingDocs(prCreateViewModel.getUploadedDocIds(), document.getId());

        logger.info("create: Save successful");
        purchaseRequestHeaderRepository.saveAndFlush(prHeader);

        response.setContainerId(documentApprovalContainer.getId());
        return response;
    }

    private void saveDocumentApprovalContainer(DocumentApprovalContainer entity, PurchaseRequestCreateViewModel saveDocumentApprovalContainer) {
        // Here as invoice doesn't have a start date and end date, we manually set it to the day of entry into the system.
        entity.setApprovalContainerClaimAmount(saveDocumentApprovalContainer.getPrDetails().getTotalPRValue());
// TODO : commented because - if startdate and enddate keeps updating on every save then expenses/docs created may fall before this date
        //        entity.setStartDate(LocalDate.now());
        entity.setEndDate(LocalDate.now());
        entity.setUpdatedTimestamp(DateTimeUtils.getCurrentZonedDateTime());
    }

    private boolean isParentExpenseReportSubmittable(DocumentApprovalContainer report) {
        return report.getReportStatus() == ReportStatus.DRAFT || report.getReportStatus() == ReportStatus.SENT_BACK;
    }

    private void createDocument(Document document, DocumentApprovalContainer documentApprovalContainer, DocumentSubgroup subgroup, IAuthContextViewModel auth) {

        document.setDocumentApprovalContainer(documentApprovalContainer);
        // Bi-directional mapping
        documentApprovalContainer.getDocuments().add(document);
        subgroup.getDocuments().add(document);

        document.setClaimAmount(new BigDecimal("0.0"));
        document.setDocumentApprovalContainerId(documentApprovalContainer.getId());
        document.setDocumentSubgroup(subgroup);
        document.setDocumentSubgroupId(subgroup.getId());
        document.setFrequency(subgroup.getFrequency());

        logger.info("create: Finding the applicable Expense Rule");
        DocumentRule applicableRule = getApplicableRule(document);

        logger.info("create: Attaching the applicable Expense Rule as id");
        document.setDocumentRuleId(applicableRule.getId());

        document.setCreatingUserId(auth.getUserId());
        document.setCompanyCode(auth.getCompanyCode());
        document.setEmployeeEmail(auth.getUserEmail());
        document.setStartDate(LocalDate.now());
        document.setEndDate(LocalDate.now());
        document.setCreatedTimestamp(ZonedDateTime.now());
        document.setDocumentDate(LocalDate.now());
    }

    private void saveDocument(Document document, PurchaseRequestCreateViewModel prRequest, IAuthContextViewModel auth) {
        if (null != prRequest) {
            // Nominal Amount
            document.setTaxableAmount(Optional.ofNullable(prRequest.getPrDetails()).map(PurchaseRequestDetailsView::getTaxableValue).orElse(new BigDecimal("0.0")));
            document.setTotalAmountGSTInclusive(Optional.ofNullable(prRequest.getPrDetails()).map(PurchaseRequestDetailsView::getTotalPRValue).orElse(new BigDecimal("0.0")));
            // Document Dates (Document Date + Start Date + End Date)
            // as per discussion for P2P - 722 for PR DocumentDate should be current date.
            document.setDocumentDate(LocalDate.now());
            // Here as invoice doesn't have a start date and end date, we manually set it to the day of entry into the system.
            document.setUpdatedTimestamp(ZonedDateTime.now());
            document.setRequesterRemark(prRequest.getPrDetails().getRequesterRemark());
        }
    }

    private DocumentRule getApplicableRule(Document document) {
        logger.trace("getApplicableRule: Finding the Expense with id: {}", document.getId());

        DocumentSubgroup subgroup = document.getDocumentSubgroup();

        Optional<DocumentRule> rule = subgroup.getRules().stream().filter(r -> !r.isFrozen()).filter(r -> isRuleMatch(r, document)).findFirst();

        return rule.orElseThrow(() -> new RecordNotFoundException("Could not find applicable rule for expense"));
    }

    private boolean isRuleMatch(DocumentRule r, Document document) {
        return true;
    }

    public void createPRHeader(PurchaseRequestHeader prHeader, Document document) {
        prHeader.setCreatedAt(LocalDateTime.now());
        prHeader.setDocument(document);
        //--- static TODO: change later
        LookupData prType = lookupRepository.findByTypeAndValue(StaticDataRegistry.PR_TYPE, StaticDataRegistry.PR_TYPE_GENERAL)
                .orElseThrow(() -> {
                            logger.error(String.format("createPRHeader: Could not find PR of type: %s and value: %s", StaticDataRegistry.PR_TYPE, StaticDataRegistry.PR_TYPE_GENERAL));
                            return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_PR_NOT_FOUND);
                        }
                );
        prHeader.setPrType(prType);
        // ---
        LookupData docType = lookupRepository.findByTypeAndValue(StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_PR)
                .orElseThrow(() -> {
                            logger.error(String.format("createPRHeader: Could not find doctype: %s and value: %s", StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_PR));
                            return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_PR_NOT_FOUND);
                        }
                );
        prHeader.setDocType(docType);

        // intialise consumed amount
//        prHeader.setConsumedAmount(new BigDecimal(0.0));
        document.setConsumedAmount(BigDecimal.valueOf(0.0)); // Todo Vishal: Check changes

        // create docNo
        Long lasRecordId =
                purchaseRequestHeaderRepository.findByLastPRId();
        document.setDocNo(StringConstants.createDocNo(StaticDataRegistry.LOOKUP_VALUE_PR, lasRecordId));
    }

    public void preparePRHeader(PurchaseRequestHeader prHeader, PurchaseRequestCreateViewModel prCreateViewModel, IAuthContextViewModel auth) {

        Document prDocument = prHeader.getDocument();
        dtoToPr(prHeader, prDocument, prCreateViewModel.getPrDetails());
        handleUpdatePRLineItemDetails(prCreateViewModel.getItemDetails(), prHeader, auth);
        deleteLineItem(prHeader.getId(), prCreateViewModel.getLineItemIds(), auth);
        documentRepository.saveAndFlush(prDocument);
    }

    private void dtoToPr(PurchaseRequestHeader prHeader, Document prDocument, PurchaseRequestDetailsView prDetails) {
        if (null != prDetails.getPrTypeId()) {
            LookupData prType = lookupRepository.findByIdAndType(prDetails.getPrTypeId(), StaticDataRegistry.PR_TYPE)
                    .orElseThrow(() -> {
                                logger.error(String.format("preparePRHeader: Could not find document of type: %s and value: %s", StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_PR));
                                return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_PR_NOT_FOUND);
                            }
                    );
            prHeader.setPrType(prType);
        }
        BeanUtils.copyProperties(prDetails, prHeader);
        prDocument.setTotalAmountGSTInclusive(prDetails.getTotalPRValue());
        prDocument.setClaimAmount(prDetails.getTotalPRValue()); //TODO: confirm ClaimAmount is same as TotalAmountGSTInclusive
        prHeader.setUpdatedAt(ZonedDateTime.now());
        prHeader.setExpectedDeliveryDate(DateTimeUtils.parseDatetoLocalDate(prDetails.getExpectedDeliveryDate()));
        prHeader.setPurchaseReason(prDetails.getDescription());
    }

    private PurchaseRequestDetailsView prToDto(PurchaseRequestHeader prHeader, Document prDocument) {
        PurchaseRequestDetailsView prDetails = new PurchaseRequestDetailsView();
        if (null != prHeader.getPrType()) {
            prDetails.setPrTypeId(prHeader.getPrType().getId());
        }
        BeanUtils.copyProperties(prHeader, prDetails);
        prDetails.setDocNo(prDocument.getDocNo());

        prDetails.setTotalPRValue(prDocument.getTotalAmountGSTInclusive());
        prDetails.setDocumentDate(DateTimeUtils.formatDateJPQL(prDocument.getDocumentDate()));
        prDetails.setTaxableValue(prDocument.getTaxableAmount());

        prDetails.setCreatedAt(DateTimeUtils.formatDateJPQL(prDocument.getCreatedDate()));
        if (prDocument.getDocumentApprovalContainer().getReportStatus() == ReportStatus.DRAFT) {
            prDetails.setDocumentDate(DateTimeUtils.formatTimestampToDate(prDocument.getCreatedTimestamp()));
        } else {
            // TODO : what in case of sent-back
            prDetails.setDocumentDate(DateTimeUtils.formatDateJPQL(prDocument.getDocumentDate()));
        }
        String dateOfDocument = Optional.ofNullable(prDocument.getDocumentDate()).map(DateTimeUtils::formatDate).orElse(null);
        int financialYearStart = DateTimeUtils.getFinancialYearStart(DateTimeUtils.parseFormattedDatetoLocalDate(dateOfDocument));
        prDetails.setFinancialYear(financialYearStart + "-" + (financialYearStart + 1));

        prDetails.setUpdatedAt(DateTimeUtils.formatTimestampToDate(prHeader.getUpdatedAt()));
        prDetails.setExpectedDeliveryDate(DateTimeUtils.formatDateJPQL(prHeader.getExpectedDeliveryDate()));
        prDetails.setDescription(prHeader.getPurchaseReason());
        prDetails.setRequesterRemark(prDocument.getRequesterRemark());
        return prDetails;
    }

    private void handleUpdatePRLineItemDetails(List<PurchaseRequestItemDetailsView> itemDetails, PurchaseRequestHeader prHeader, IAuthContextViewModel auth) {
        if (null == itemDetails) {
            return;
        }

        // validation to check requestedAmount > budget amount
        validateRequestedAmountExceedsBudgetAmount(itemDetails, auth);

        itemDetails.forEach(dto -> {
            if (dto.getId() != null) {
                // If LineItemId is not null, it's an existing LineItem, Find the existing LineItem in the PR
                prHeader.getPrItems()
                        .stream()
                        .filter(lineItem -> lineItem.getId().equals(dto.getId()))
                        .findFirst()
                        .ifPresent(lineItem -> dtoToPrItem(dto, lineItem, prHeader, auth, false));
            } else {
                prHeader.getPrItems()
                        .add(dtoToPrItem(dto, new PurchaseRequestItem(), prHeader, auth, true));
            }
        });
    }

    private void validateRequestedAmountExceedsBudgetAmount(List<PurchaseRequestItemDetailsView> itemDetailsViewModel, IAuthContextViewModel auth) {

        Set<Long> uniqueBudgetNodeIds = itemDetailsViewModel.stream()
                .map(PurchaseRequestItemDetailsView::getBudgetNodeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (uniqueBudgetNodeIds.isEmpty()) {
            return;
        }
        // TODO: revisit for performance cost - can be done only on UI
        BigDecimal budgetNodeAmount = calculateTotalBudgetNodeAmount(uniqueBudgetNodeIds, auth, DocumentType.PURCHASE_REQUEST);
        BigDecimal requestedAmount = calculateRequestedAmount(itemDetailsViewModel);

    }

    public BigDecimal calculateTotalBudgetNodeAmount(Set<Long> uniqueBudgetNodeIds, IAuthContextViewModel auth, DocumentType type) {

        return uniqueBudgetNodeIds.stream()
                .map(budgetNodeId -> budgetServiceImplementation.getBudgetFrequencyTotalAmount(auth, budgetNodeId, type))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal calculateRequestedAmount(List<PurchaseRequestItemDetailsView> itemDetailsViewModel) {
        return itemDetailsViewModel.stream()
                .map(PurchaseRequestItemDetailsView::getAmountWithoutGst)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public List<PurchaseRequestItem> preparePRLineItems(PurchaseRequestCreateViewModel prCreateViewModel, PurchaseRequestHeader prHeader, IAuthContextViewModel auth) {
        if (null == prCreateViewModel || null == prCreateViewModel.getItemDetails())
            return Collections.emptyList();

        return prCreateViewModel.getItemDetails().stream()
                .map(obj -> dtoToPrItem(obj, new PurchaseRequestItem(), prHeader, auth, true))
                .collect(Collectors.toList());
    }

    public PurchaseRequestItem dtoToPrItem(PurchaseRequestItemDetailsView dto, PurchaseRequestItem prItem, PurchaseRequestHeader prHeader, IAuthContextViewModel auth, boolean isNew) {

        BeanUtils.copyProperties(dto, prItem);
        prItem.setTaxPercentage(dto.getTaxPercentage() != null ? dto.getTaxPercentage() : 0.0);

        prItem.setPurchaseRequestHeader(prHeader);
        prItem.setPurchaseRequestHeaderId(prHeader.getId());

        if (dto.getBudgetNodeId() != null) {
            Budget budgetNode = getBudget(auth.getCompanyCode(), dto.getBudgetNodeId());
            prItem.setBudgetNode(budgetNode);
            prItem.setBudgetNodeId(dto.getBudgetNodeId());
            prItem.setBudgetCode(budgetNode.getBudgetCode());
        }

        if (isNew) {
            if (dto.getItemMasterId() != null) {
                ItemMaster item = itemMasterRepository.findByIdAndCompanyCode(dto.getItemMasterId(), (int) auth.getCompanyCode()) // Todo: Convert CompanyId in itemMaster table to long
                        .orElseThrow(() -> {
                            logger.info("addItem: Could not find item with id: {} with vendor id: {} for company id: {}", dto.getItemMasterId(), prHeader.getVendorId(), auth.getCompanyCode());
                            return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
                        });
                prItem.setType(item.getType());
                prItem.setHsn(item.getHsn() == null ? item.getHsnOrSac() : item.getHsn());
                prItem.setVendorId(item.getVendorId());
                prItem.setVendor(item.getVendor());
                prItem.setIsCatalog(true);
                if (null != item.getSupplierRate())
                    prItem.setUnitRate(item.getSupplierRate());
            } else {
                // new case -- if item is not present in item master and considered as new item then set infor and fetch vendor and set to item
                if (dto.getVendorId() != null) {
                    Company vendor = companyRepository.findByCamCompanyIdAndCompanyId(auth.getCompanyCode(), dto.getVendorId()).orElse(null);
                    prItem.setVendor(vendor);
                    prItem.setVendorId(dto.getVendorId().longValue());
                }
                prItem.setType(ItemType.fromString(dto.getType()));
            }

            prItem.setCreatedAt(LocalDateTime.now());
            purchaseRequestLineItemRepository.saveAndFlush(prItem);
        } else {
            // TODO : set vendor code here as well if already saved item edit is allowed?
            prItem.setUpdatedAt(LocalDateTime.now());
        }
        return prItem;
    }

    private Budget getBudget(long companyCode, Long budgetNodeId) {
        return budgetRepository.findByCompanyCodeAndIdAndIsActiveTrue(companyCode, budgetNodeId).orElseThrow(() -> {
            logger.info("add budget to po item: Could not find with id: {} ", budgetNodeId);
            return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
        });
    }

    @Override
    public PurchaseRequestItemDetailsView prItemToDto(PurchaseRequestItem prItem) {
        PurchaseRequestItemDetailsView dto = new PurchaseRequestItemDetailsView();
        BeanUtils.copyProperties(prItem, dto);
        dto.setType(prItem.getType().getFormattedName());
//        itemMasterRepository.findByTypeAndHsnOrSacAndVendorId(prItem.getType(), prItem.getHsn(), prItem.getVendorId()).ifPresent(i -> {
//            dto.setItemMasterId(i.getId());
//        });
        Company vendor = prItem.getVendor();
        dto.setHsn(prItem.getHsn());
        if (vendor != null) {
            dto.setVendorName(vendor.getSupplierCompanyName());
            dto.setVendorId(vendor.getCompanyId());
        } else {
            dto.setVendorName("NA");
        }
        dto.setTaxPercentage(prItem.getTaxPercentage() != null
                ? prItem.getTaxPercentage()
                : 0.0);
        return dto;
    }

    public PurchaseRequestCreateViewModel getByDocumentContainerId(Long documentContainerId, IAuthContextViewModel auth) {

        DocumentApprovalContainer documentApprovalContainer = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), documentContainerId)
                .orElseThrow(() -> {
                    logger.error(String.format("getByDocumentContainerId: DocumentApprovalContainer with id: {%s} could not be found.", documentContainerId));
                    return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_PR_NOT_FOUND);
                });

        // Sanity check in case of any data inconsistency (if unrequired can be removed later)
        performApprovalContainerAndDocumentContainerSantityCheck(documentApprovalContainer, documentContainerId);

        Document document = getDocumentFromDocumentApprovalContainerWithUniquenessChecks(auth.getCompanyCode(), documentContainerId);

        PurchaseRequestHeader prHeader = getPRHeaderFromDocumentWithUniquenessChecks(auth.getCompanyCode(), document);

        BudgetSelectionViewModel budgetDetails = getBudgetNodeForDocument(document, auth);

        return preparePRDetailsResponseData(documentApprovalContainer, prHeader, document, budgetDetails);
    }

    private BudgetSelectionViewModel getBudgetNodeForDocument(Document document, IAuthContextViewModel auth) {
        // Todo This needs to be converted to a api call.
        Long budgetId = document.getDocumentSubgroup().getBudgetId();
        if (null == budgetId) return null;
        return budgetServiceImplementation.getBudgetNode(budgetId, auth);
    }

    @Override
    public void deleteLineItem(Long prId, List<Long> lineItemIds, IAuthContextViewModel auth) {
        Optional.ofNullable(lineItemIds).ifPresent(ids -> ids.forEach(lineItemId -> {
            PurchaseRequestItem item = purchaseRequestLineItemRepository.findById(lineItemId).orElseThrow(() -> new RecordNotFoundException(String.format("Could not find line-item with id: %d", lineItemId)));
            purchaseRequestLineItemRepository.delete(item);
        }));
    }

    @Override
    public GenericPageableViewModel<BudgetAssociatedDocumentDetailViewModel> getSubmittedPRByBudgetId(Long budgetId, QueueFilterViewModel filterValues, IAuthContextViewModel auth) {
        GenericPageableViewModel<BudgetAssociatedDocumentDetailViewModel> pagedQueue = new GenericPageableViewModel<>();

        Pageable pageable = PageRequest.of((filterValues.getPage() - 1), filterValues.getPageSize());

        // Business logic start
        Page<PurchaseRequestHeader> purchaseRequestPage = purchaseRequestHeaderRepository.findByBudgetIdAndReportStatus(budgetId, ReportStatus.SUBMITTED, pageable);

        List<BudgetAssociatedDocumentDetailViewModel> detailViewModels = purchaseRequestPage.getContent().stream()
                .map(this::prepareBudgetAssociatedDocumentDetailViewModel)
                .collect(Collectors.toList());

        pagedQueue.setData(detailViewModels);
        pagedQueue.setPages(purchaseRequestPage.getTotalPages());
        pagedQueue.setTotalElements(Math.toIntExact(purchaseRequestPage.getTotalElements()));

        return pagedQueue;
    }

    @Override
    public List<PurchaseRequestItemDetailsView> getLineItems(Long prId, IAuthContextViewModel auth) {

        PurchaseRequestHeader prHeader =
                purchaseRequestHeaderRepository.findById(prId)
                        .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find PR with id: %d", prId)));

        if (prHeader.getPrItems() == null) {
            return Collections.emptyList();
        }

        return prHeader.getPrItems().stream()
                .map(this::prItemToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<PurchaseRequestItemDetailsView> getLineItemsReadyToConvert(IAuthContextViewModel auth, Integer vendorId) {
        return customDocumentApprovalContainerRepository.getReadyToConvertPrItems(auth.getCompanyCode(), vendorId, null);
    }

    @Override
    public List<ReadyToConvertEntityViewModel> getPurchaseRequestsReadyToConvert(IAuthContextViewModel auth, Integer vendorId, GenericListRequestViewModel<Long> purchaseRequestIds) {
        List<Long> prIds = new ArrayList<>();
        logger.info("getPurchaseRequestsReadyToConvert: Getting available PR's for company {} and vendor {}", auth.getCompanyCode(), vendorId);
        List<ReadyToConvertEntityViewModel> prList = new ArrayList<>();
        if (null == purchaseRequestIds || purchaseRequestIds.getIds().isEmpty()) {
            purchaseRequestHeaderRepository.findByCompanyCodeAndStatusAndVendorId(auth.getCompanyCode(), ReportStatus.ACCEPTED, vendorId)
                    .forEach(i -> {
                        prList.add(i);
                        prIds.add(i.getId());
                    });
        } else {
            // for procurement - get PRs
            purchaseRequestHeaderRepository.findByCompanyCodeAndStatusAndVendorIdAndIdsIn(auth.getCompanyCode(), ReportStatus.ACCEPTED, vendorId, purchaseRequestIds.getIds())
                    .forEach(i -> {
                        prList.add(i);
                        prIds.add(i.getId());
                    });
        }


        logger.info("getPurchaseRequestsReadyToConvert: Getting PR consumptions by header and item");
        HashMap<Long, PurchaseRequestItemDetailsAggViewModel> prHeaderItemCounts = new HashMap<>();
        // The below function get count of the remaining quantity of PrItems that have been either partially or fully converted into POItems
        customDocumentApprovalContainerRepository.getAvailablePRHeaderWithCounts(auth.getCompanyCode(), vendorId).forEach(i -> {
            // As a PR can container multiple items, if the PR id is already found, we add the remaining count of the current item to the total of the previous items belonging to the PR header
            PurchaseRequestItemDetailsAggViewModel aggViewModel;
            if (prHeaderItemCounts.containsKey(i.getId())) {
                aggViewModel = prHeaderItemCounts.get(i.getId());
                aggViewModel.addQuantity(i.getCount());
                aggViewModel.incrementCount();
            } else {
                aggViewModel = new PurchaseRequestItemDetailsAggViewModel(i.getId(), StaticDataRegistry.PR_ITEM_COUNT_INIT, i.getCount());
            }
            prHeaderItemCounts.put(i.getId(), aggViewModel);
        });


        logger.info("getPurchaseRequestsReadyToConvert: Updating and filtering the following PR's according to available item quantity:\t{}", prIds);
        // The below function filters out PR that have no items with available quantities left and updates the total counts of the items that have partially been converted into PO's
        List<ReadyToConvertEntityViewModel> result = new ArrayList<>();
        List<Long> prIdsWithRemainingQty = new ArrayList<>();
        PurchaseRequestItemDetailsAggViewModel prAggDetails;

        for (ReadyToConvertEntityViewModel prViewModel : prList) {
            if (prHeaderItemCounts.containsKey(prViewModel.getId())) {
                prAggDetails = prHeaderItemCounts.get(prViewModel.getId());
                if (prAggDetails.getQuantity() > 0) {
                    prViewModel.setItemCount(prAggDetails.getItemCount());
                    result.add(prViewModel);
                    prIdsWithRemainingQty.add(prViewModel.getId());
                }
            } else {
                result.add(prViewModel);
            }
        }
        logger.info("getPurchaseRequestsReadyToConvert: Result after filtering:\t{}", prIdsWithRemainingQty);
        return result;
    }

    @Override
    public List<PurchaseRequestItemDetailsView> getLineItemsReadyToConvertByPR(IAuthContextViewModel auth, Integer vendorId, GenericListRequestViewModel<Long> prIds) {
        return customDocumentApprovalContainerRepository.getLineItemsReadyToConvertByPR(auth.getCompanyCode(), vendorId, prIds);
    }

    private BudgetAssociatedDocumentDetailViewModel prepareBudgetAssociatedDocumentDetailViewModel(PurchaseRequestHeader pr) {
        BudgetAssociatedDocumentDetailViewModel response = new BudgetAssociatedDocumentDetailViewModel();
        response.setPrNo(pr.getDocument().getDocNo());
        response.setFirstName(pr.getDocument().getDocumentApprovalContainer().getFirstName());
        response.setMiddleName(pr.getDocument().getDocumentApprovalContainer().getMiddleName());
        response.setLastName(pr.getDocument().getDocumentApprovalContainer().getLastName());
        response.setStatus(pr.getDocument().getDocumentApprovalContainer().getReportStatus().name());
        response.setCreatedDate(pr.getDocument().getDocumentDate().toString());
        response.setId(pr.getDocument().getDocumentApprovalContainerId());
        response.setAvailableAmount(BigDecimal.valueOf(1000.00)); // TODO: change dummy value

        List<PurchaseRequestItemDetailsView> itemDetails = pr.getPrItems().stream()
                .map(this::prItemToDto)
                .collect(Collectors.toList());

        response.setItemDetails(itemDetails);

        return response;
    }

    private PurchaseRequestCreateViewModel preparePRDetailsResponseData(DocumentApprovalContainer documentApprovalContainer, PurchaseRequestHeader prHeader, Document document, BudgetSelectionViewModel budgetDetails) {
        PurchaseRequestCreateViewModel response = new PurchaseRequestCreateViewModel();

        PurchaseRequestDetailsView prDetails = prToDto(prHeader, document);
        prDetails.setCreatedBy(documentApprovalContainer.getFirstName() + " " + documentApprovalContainer.getLastName());

        if (prHeader.getUpdatingBy() != null)
            prDetails.setUpdatedBy(StringConstants.validateAndGetName(prHeader.getUpdatingBy().getFirstName()) + " " + StringConstants.validateAndGetName(prHeader.getUpdatingBy().getLastName()));

        List<PurchaseRequestItemDetailsView> prItems = prHeader.getPrItems().stream()
                .sorted(Comparator.comparing(PurchaseRequestItem::getId)).map(this::prItemToDto)
                .collect(Collectors.toList());

        response.setPrDetails(prDetails);
        response.setItemDetails(prItems);
        response.setBudgetDetails(prepareBudgetDetailsData(budgetDetails));
        response.setContainerId(documentApprovalContainer.getId());
        return response;
    }

    public static BudgetDetails prepareBudgetDetailsData(BudgetSelectionViewModel budgetDetails) {
        if (budgetDetails == null) return null;
        return BudgetDetails.builder()
                .id(budgetDetails.getId())
                .code(budgetDetails.getBudgetCode() == null ? StringConstants.EMPTY : budgetDetails.getBudgetCode())
                .description(budgetDetails.getDescription() == null ? StringConstants.EMPTY : budgetDetails.getDescription())
                .build();
    }

    private void performApprovalContainerAndDocumentContainerSantityCheck(DocumentApprovalContainer documentApprovalContainer, Long documentContainerId) {
        // Sanity check in case of any data inconsitency (if unrequired can be removed later)
        if (!documentApprovalContainer.getId().equals(documentContainerId)) {
            logger.error(String.format("getByDocumentContainerId: DocumentApprovalContainer sanity check failed. The provided id in the query : {%s} and the resulting id in the object:  {%s} are mismatched.", documentContainerId, documentApprovalContainer.getId()));
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_PR_NOT_FOUND);
        }
    }

    @Override
    public Document getDocumentFromDocumentApprovalContainerWithUniquenessChecks(long companyCode, Long documentContainerId) {
        List<Document> attachedDocumentsArray = documentRepository.findAllByCompanyCodeAndDocumentApprovalContainerId(companyCode, documentContainerId);

        // TODO: PR may have a more than one document attached to the document container - check later
        if (attachedDocumentsArray.size() != 1) {
            logger.error(String.format("getByDocumentContainerId: DocumentApprovalContainer with id: {%s} has multiple documents attached to it.", documentContainerId));
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_PR_NOT_FOUND);
        }

        return attachedDocumentsArray.get(0);
    }

    @Override
    public PurchaseRequestHeader getPRHeaderFromDocumentWithUniquenessChecks(long companyCode, Document document) {
        return purchaseRequestHeaderRepository.findByDocumentId(document.getId()).orElseThrow(() -> {
            logger.error(String.format("getByDocumentContainerId: Document with id: {%s} has not got an invoice attached to it.", document.getId()));
            return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_PR_NOT_FOUND);
        });
    }

    @Override
    public PRPreviewViewModel getPurchaseRequestPreviewById(IAuthContextViewModel auth, long id, boolean isProcurementView) {
        PRPreviewViewModel prPreviewViewModel = new PRPreviewViewModel();
        List<PurchaseRequestItemDetailsView> prItems = new ArrayList<>();
        DocumentApprovalContainer documentApprovalContainer = documentApprovalContainerRepository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> {
                    logger.error(String.format("getPurchaseRequestPreviewById: DocumentApprovalContainer with id: {%s} could not be found.", id));
                    return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_PR_NOT_FOUND);
                });

        Document document = getDocumentFromDocumentApprovalContainerWithUniquenessChecks(auth.getCompanyCode(), id);

        PurchaseRequestHeader prHeader = getPRHeaderFromDocumentWithUniquenessChecks(auth.getCompanyCode(), document);
        if (isProcurementView) {
            // for procurement view
            List<Long> prItemsIds = prHeader.getPrItems().stream()
                    .map(PurchaseRequestItem::getId)
                    .collect(Collectors.toList());
            List<ConsumedItemAvailableCountViewModel> consumedItemCounts = customDocumentApprovalContainerRepository.getPRItemsConsumedInPOsWithCounts(prItemsIds);

            prItems = prHeader.getPrItems().stream()
                    .filter(prItem -> {
                        /* commented as no more required
                        // Fetch all ReportStatuses for the current PR item -
                        List<ReportStatus> reportStatuses = customDocumentApprovalContainerRepository.getReportStatusForConsumedItem(prItem.getId());

                        // Retain if ReportStatuses contain ReportStatus.SENT_BACK
                        boolean hasSentBack = reportStatuses.contains(ReportStatus.SENT_BACK);
                        */

                        // Try to find the matching consumed item
                        // If a prItem has been fully consumed, it should be excluded from the results
                        // Exclude this prItem if initialQuantity - quantity is 0 i.e. retain none match
                        boolean isFullyConsumed = consumedItemCounts.stream()
                                .filter(consumedItem -> consumedItem.getId().equals(prItem.getId()))
                                .anyMatch(consumedItem -> (consumedItem.getInitialQuantity() - consumedItem.getQuantity()) == 0);
                        // If no match is found in consumedItemCounts, retain the prItem

                        // Retain the item if it has "SENT_BACK" status or is not fully consumed
//                        return hasSentBack || !isFullyConsumed; // removed sent back - 25.11 - check later
                        return !isFullyConsumed;

                    })
                    .map(this::prItemToDto)
                    .collect(Collectors.toList());
        } else {
            prItems = prHeader.getPrItems().stream()
                    .map(this::prItemToDto)
                    .collect(Collectors.toList());
        }


        populatePRPreviewViewModel(prPreviewViewModel, documentApprovalContainer, document, prHeader, prItems);

        return prPreviewViewModel;
    }

    public void populatePRPreviewViewModel(PRPreviewViewModel prPreviewViewModel,
                                           DocumentApprovalContainer documentApprovalContainer,
                                           Document document, PurchaseRequestHeader prHeader,
                                           List<PurchaseRequestItemDetailsView> prItems) {

        // DocumentApprovalContainer
        Optional.ofNullable(documentApprovalContainer).ifPresent(value -> {
            prPreviewViewModel.setId(value.getId());
            prPreviewViewModel.setRequesterName(Optional.ofNullable(value.getFirstName()).orElse("") + " " + Optional.ofNullable(value.getLastName()).orElse(""));
            prPreviewViewModel.setTotalPRAmount(value.getApprovalContainerClaimAmount());
            prPreviewViewModel.setReportStatus(documentApprovalContainer.getReportStatus());
        });

        // Document
        Optional.ofNullable(document).ifPresent(value -> {
            prPreviewViewModel.setDateOfDocument(Optional.ofNullable(value.getDocumentDate()).map(DateTimeUtils::formatDate).orElse(null));

            getBudget(value).ifPresent(budget -> {
                prPreviewViewModel.setBudgetCode(budget.getBudgetCode());
                prPreviewViewModel.setBudgetAmount(budget.getTotal().subtract(budget.getLocked()));
            });

            prPreviewViewModel.setPrNumber(value.getDocNo());
        });

        // PurchaseOrderHeader
        Optional.ofNullable(prHeader).ifPresent(value -> {
            prPreviewViewModel.setPrType(Optional.ofNullable(value.getPrType()).map(LookupData::getValue).orElse(null));
            prPreviewViewModel.setSupplierName(Optional.ofNullable(value.getVendor()).map(Company::getSupplierCompanyName).orElse(null));
//          prPreviewViewModel.setPrAmountWOGst(value.getTotalAmount());
            prPreviewViewModel.setPrDescription(value.getPurchaseReason());
        });

        prPreviewViewModel.setPrItems(prItems);
    }

    private Optional<Budget> getBudget(Document document) {
        return Optional.ofNullable(document)
                .map(Document::getDocumentSubgroup)
                .map(DocumentSubgroup::getBudget);
    }

    @Override
    public List<PurchaseRequestItemDetailsView> getLineItemsByPrIds(GenericListRequestViewModel<Long> prRequest, IAuthContextViewModel auth) {
        List<PurchaseRequestHeader> prHeaders =
                purchaseRequestHeaderRepository.findByIdsIn(prRequest.getIds());

        if (prHeaders == null || prHeaders.isEmpty()) {
            return Collections.emptyList();
        }

        return prHeaders.stream()
                .flatMap(pr -> pr.getPrItems().stream())
                .map(this::prItemToDto)
                .collect(Collectors.toList());
    }
}
