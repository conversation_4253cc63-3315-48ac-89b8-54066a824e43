package in.taxgenie.pay_expense_pvv.services.implementations.budget;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMaster;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetStructureMaster;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetMasterRepository;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetStructureMasterRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.ILookupService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetMappingMasterService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetService;
import in.taxgenie.pay_expense_pvv.services.interfaces.budget.IBudgetStructureMasterService;
import in.taxgenie.pay_expense_pvv.utils.budget.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetMasterTreeViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetCreateDataViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.BudgetCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.mappingmaster.BudgetMappingCreateViewModelWrapper;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.mappingmaster.BudgetMappingMasterCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.structuremaster.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class BudgetStructureMasterService implements IBudgetStructureMasterService {

    private final IBudgetStructureMasterRepository repository;
    private final ILookupService lookupService;
    private final Logger logger;
    private final IBudgetMasterRepository budgetMasterRepository;
    private final IBudgetMappingMasterService budgetMappingMasterService;
    private final IBudgetService budgetService;



    public BudgetStructureMasterService(IBudgetStructureMasterRepository repository, ILookupService lookupService, IBudgetMasterRepository budgetMasterRepository, IBudgetMappingMasterService budgetMappingMasterService, IBudgetService budgetService) {
        this.repository = repository;
        this.lookupService = lookupService;
        this.budgetMasterRepository = budgetMasterRepository;
        this.budgetMappingMasterService = budgetMappingMasterService;
        this.budgetService = budgetService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    @Transactional
    public BudgetStructureMasterViewModel create(BudgetStructureMasterCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.info("createBudgetStructureMaster: Create budget structure master with name: {}", viewModel.getStructureName());
        // Sanity checks
        if (!performBudgetStructureUniqueSanityChecks(viewModel, auth.getCompanyCode())) {
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_SANITY_CHECK_CREATION_ERROR);
        }

        BudgetStructureMaster budgetStructureMaster = new BudgetStructureMaster();
        budgetStructureMaster.setDataForCreate(viewModel, auth);

        BudgetStructureMaster budgetStructureMasterDB = repository.saveAndFlush(budgetStructureMaster);
        logger.info("createBudgetStructureMaster: Created budget structure master with name: {} for company: {} " , viewModel.getStructureName(), auth.getCompanyCode());

        //Creating default organization node.
        createDefaultOrgNode(budgetStructureMasterDB, auth);

        BudgetStructureMasterViewModel returnViewModel = budgetStructureMasterDB.getViewModel();
        returnViewModel.setExpenditureType(viewModel.getExpenditureType());
        return returnViewModel;

    }

    private void createDefaultOrgNode(BudgetStructureMaster budgetStructureMasterDB, IAuthContextViewModel auth) {

        List<BudgetMaster> orgBudgetMastersNodeList = budgetMasterRepository.findByCompanyCodeAndName(auth.getCompanyCode(), "Org");

        if(orgBudgetMastersNodeList.size() == 1){
            BudgetMaster orgBudgetMastersNode = orgBudgetMastersNodeList.get(0);
            BudgetMappingCreateViewModelWrapper budgetMappingCreateViewModelWrapper = new BudgetMappingCreateViewModelWrapper();
            budgetMappingCreateViewModelWrapper.setExpanded(true);

            BudgetMappingMasterCreateViewModel data = new BudgetMappingMasterCreateViewModel(orgBudgetMastersNode.getId(),
                    orgBudgetMastersNode.getDisplayName() );
            budgetMappingCreateViewModelWrapper.setData(data);

            BudgetMasterTreeViewModel budgetMasterTreeViewModel = budgetMappingMasterService.create(budgetStructureMasterDB.getId(), budgetMappingCreateViewModelWrapper, false, auth);

//            BudgetCreateViewModel budgetCreateViewModel = new BudgetCreateViewModel();
//            BudgetCreateDataViewModel budgetCreateDataViewModel = new BudgetCreateDataViewModel();
//            budgetCreateDataViewModel.setBMaI
//            budgetCreateViewModel.setData(budgetCreateDataViewModel);
//            budgetService.createTree(budgetCreateViewModel, budgetStructureMasterDB.getId(), false, auth);
        } else{

        }
    }

    @Override
    public BudgetStructureMasterViewModel get(long structureId, IAuthContextViewModel auth) {
        logger.info("getBudgetStructureMaster: Looking for structure for company: {} with id: {} ", auth.getCompanyCode(), structureId);
        BudgetStructureMaster budgetStructureMaster =  this.repository.findByCompanyCodeAndIdAndIsActive(auth.getCompanyCode(), structureId, true)
                .orElseThrow(() -> {
                    logger.info("getBudgetStructureMaster: Could not find structure for company: {} and id: {}", auth.getCompanyCode(), structureId);
                    return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
                });

        return budgetStructureMaster.getViewModel();
    }

    @Override
    @Transactional
    public BudgetStructureMasterViewModel put(BudgetStructureMasterUpdateViewModel viewModel, IAuthContextViewModel auth) {
        logger.info("updateBudgetStructureMaster: Updating structure id: {}", viewModel.getStructureName());
        // Sanity checks

        if (!performBudgetStructureUniqueSanityChecks(viewModel, auth.getCompanyCode())) {
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_SANITY_CHECK_CREATION_ERROR);
        }
        if (!performBudgetStructureUpdateSanityChecks(viewModel)) {
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_CREATION_ERROR);
        }

        BudgetStructureMaster budgetStructureMasterDB =  this.repository.findByCompanyCodeAndIdAndIsActive(auth.getCompanyCode(), viewModel.getId(), true)
                .orElseThrow(() -> {
                    logger.info("getBudgetStructureMaster: Could not find structure for company: {} and id: {}", auth.getCompanyCode(), viewModel.getId());
                    return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
                });

        budgetStructureMasterDB.setDataForUpdate(viewModel, auth);
        repository.saveAndFlush(budgetStructureMasterDB);
        logger.info("updateBudgetStructureMaster: Updated budget structure master with name: {} for company: {} " , viewModel.getStructureName(), auth.getCompanyCode());
        BudgetStructureMasterViewModel returnViewModel = budgetStructureMasterDB.getViewModel();
        returnViewModel.setExpenditureType(viewModel.getExpenditureType());
        return returnViewModel;
    }

    @Override
    public BudgetStructureMasterViewModel updateLockScreen(LockScreenViewModel lockScreenViewModel, IAuthContextViewModel auth) {

        BudgetStructureMaster budgetStructureMasterDB =  repository.findByCompanyCodeAndIdAndIsActive(auth.getCompanyCode(), lockScreenViewModel.getId(), true)
                .orElseThrow(() -> {
                    logger.info("updateLockScreen: Could not find structure for company: {} and id: {}", auth.getCompanyCode(), lockScreenViewModel.getId());
                    return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
                });

        //Update lock screen
        budgetStructureMasterDB.setDataForLockScreen(lockScreenViewModel, auth);
        repository.saveAndFlush(budgetStructureMasterDB);

       return budgetStructureMasterDB.getViewModel();
    }

    @Override
    public LockScreenViewModel getLockScreen(long structureId, IAuthContextViewModel auth) {

        BudgetStructureMaster budgetStructureMasterDB =  repository.findByCompanyCodeAndIdAndIsActive(auth.getCompanyCode(), structureId, true)
                .orElseThrow(() -> {
                    logger.info("getLockScreen: Could not find structure for company: {} and id: {}", auth.getCompanyCode(), structureId);
                    return new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
                });

        return budgetStructureMasterDB.getLockScreenViewModel();
    }


    // --- HELPER FUNCTIONS START

    private boolean performBudgetStructureUniqueSanityChecks(BudgetStructureMasterViewModelBase budgetStructureMasterCreateViewModel, long companyCode) {
        boolean sanityCheckPassFlag = performBudgetStructureUpdateSanityChecks(budgetStructureMasterCreateViewModel);

        if (repository.findByCompanyCodeAndStructureNameAndFinancialYearAndIsActive(companyCode,
                budgetStructureMasterCreateViewModel.getStructureName(), budgetStructureMasterCreateViewModel.getFinancialYear(), true).isPresent()) {

            if (null != budgetStructureMasterCreateViewModel.getId() &&
            repository.findByIdAndCompanyCodeAndStructureNameAndFinancialYearAndIsActive(budgetStructureMasterCreateViewModel.getId(), companyCode,
                    budgetStructureMasterCreateViewModel.getStructureName(), budgetStructureMasterCreateViewModel.getFinancialYear(), true).isPresent()) {
                sanityCheckPassFlag = true;
            } else {
                logger.info("createBudgetStructureMaster: The provided structure name is already in use: {} for company: {} and financial year: {}",
                        budgetStructureMasterCreateViewModel.getStructureName(), companyCode, budgetStructureMasterCreateViewModel.getFinancialYear());
                sanityCheckPassFlag = false;
            }
        }

        return sanityCheckPassFlag;
    }

    private boolean performBudgetStructureUpdateSanityChecks(BudgetStructureMasterViewModelBase viewModel) {
        boolean sanityCheckPassFlag = true;
        Optional<LookupViewModel> validFinancialYear = lookupService.lookUpByTypeAndValue(StaticDataRegistry.LOOKUP_TYPE_FINANCIAL_YEAR, viewModel.getFinancialYear());
        if (validFinancialYear.isEmpty()) {
            logger.info("createBudgetStructureMaster: The financial year {} that was provided does not exist in the lookup table", viewModel.getFinancialYear());
            sanityCheckPassFlag = false;
        }
        if (viewModel.getStartMonthIndex() == null ||
                (viewModel.getStartMonthIndex() > StaticDataRegistry.START_MONTH_MAXIMUM
                        || viewModel.getStartMonthIndex() < StaticDataRegistry.START_MONTH_MINIMUM)) {
            logger.info("createBudgetStructureMaster: The provided start month index: {} was not valid ", viewModel.getStartMonthIndex());
            sanityCheckPassFlag = false;
        }
        return sanityCheckPassFlag;
    }



    // --- HELPER FUNCTIONS END
}
