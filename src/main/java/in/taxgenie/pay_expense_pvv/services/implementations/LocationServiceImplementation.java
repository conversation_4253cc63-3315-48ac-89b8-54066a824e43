package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Location;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.ILocationRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.ILocationService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.KeyValuePairViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.LocationCreateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.LocationUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.LocationViewModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LocationServiceImplementation implements ILocationService {
    private final ILocationRepository repository;
    private final Logger logger;

    public LocationServiceImplementation(ILocationRepository repository) {
        this.repository = repository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public LocationViewModel create(LocationCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("create: Ensuring category is in the predefined set");
        if (!StaticDataRegistry.locationCategories.contains(viewModel.getCategory())) {
            throw new DomainInvariantException(String.format("Category: %s is not allowed", viewModel.getCategory()));
        }

        logger.trace("create: Ensuring uniqueness");
        if (repository.getCountByParameters(
                auth.getCompanyCode(), viewModel.getCountryCode(), viewModel.getLocation()) > 0) {
            throw new DomainInvariantException(String.format("Location %s/%s for Company: %d already exists",
                    viewModel.getCountryCode(), viewModel.getLocation(), auth.getCompanyCode()));
        }

        logger.trace("create: Creating new entity");
        Location entity = new Location();
        BeanUtils.copyProperties(viewModel, entity);

        logger.trace("create: Setting audit fields");
        entity.setCreatingUserId(auth.getUserId());
        entity.setCreatedTimestamp(ZonedDateTime.now());
        entity.setCompanyCode(auth.getCompanyCode());

        logger.trace("create: Saving entity");
        Location savedEntity = repository.saveAndFlush(entity);
        logger.trace("create: Save successful");

        return getViewModel(savedEntity);
    }

    @Override
    public LocationViewModel update(long id, LocationUpdateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("create: Ensuring category is in the predefined set");
        if (!StaticDataRegistry.locationCategories.contains(viewModel.getCategory())) {
            throw new DomainInvariantException(String.format("Category: %s is not allowed", viewModel.getCategory()));
        }

        logger.trace("update: Matching id with that of the view model");
        if (id != viewModel.getId()) {
            throw new DomainInvariantException("Passed id doesn't match with that of the view model");
        }

        logger.trace("update: Finding the source record");
        Location entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getId())
                .orElseThrow(() -> new RecordNotFoundException(
                        String.format("Could not find %s with id: %d and Company Code: %d", Location.class,
                                viewModel.getId(), auth.getCompanyCode())));

        logger.trace("create: Ensuring uniqueness");
        if (!entity.getCountryCode().equals(viewModel.getCountryCode()) &&
                !entity.getCategory().equals(viewModel.getCategory()) &&
                !entity.getLocation().equals(viewModel.getLocation()) &&
                repository.getCountByParameters(
                        auth.getCompanyCode(), viewModel.getCountryCode(), viewModel.getLocation()) > 0) {
            throw new DomainInvariantException(String.format("Location %s/%s for Company: %d already exists",
                    viewModel.getCountryCode(), viewModel.getLocation(), auth.getCompanyCode()));
        }

        logger.trace("update: Updating the source record");
        BeanUtils.copyProperties(viewModel, entity);

        logger.trace("update: Setting the audit fields");
        entity.setUpdatingUserId(auth.getUserId());
        entity.setUpdatedTimestamp(ZonedDateTime.now());

        logger.trace("update: Saving the update");
        Location savedEntity = repository.saveAndFlush(entity);
        logger.trace("update: Save successful");

        return getViewModel(savedEntity);
    }

    @Override
    public LocationViewModel getById(long id, IAuthContextViewModel auth) {
        logger.trace("getById: Finding the record by identifiers");
        Location entity = repository.findByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new RecordNotFoundException(
                        String.format("Could not find %s with id: %d and Company Code: %d", Location.class, id,
                                auth.getCompanyCode())));

        return getViewModel(entity);
    }

    @Override
    public List<LocationViewModel> getAll(IAuthContextViewModel auth) {
        logger.trace("getAll: Returning view model list");
        return repository
                .findByCompanyCode(auth.getCompanyCode())
                .stream()
                .sorted(Comparator.comparing(Location::getLocation))
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public List<LocationViewModel> getAllByCountryCode(IAuthContextViewModel auth, String query) {
        logger.trace("getAll: Returning view model list");
        String countryCode = "IND";
        if (query.equalsIgnoreCase("DOMESTIC")) {
            return repository
                    .findByCompanyCodeAndCountryCode(auth.getCompanyCode(), countryCode)
                    .stream()
                    .sorted(Comparator.comparing(Location::getLocation))
                    .map(this::getViewModel)
                    .collect(Collectors.toList());
        } else {

            return repository
                    .findByCompanyCodeAndCountryCodeNot(auth.getCompanyCode(), countryCode)
                    .stream()
                    .sorted(Comparator.comparing(Location::getLocation))
                    .map(this::getViewModel)
                    .collect(Collectors.toList());
        }
    }

    @Override
    public List<KeyValuePairViewModel<Long, String>> getLocationKeyValueCollection(IAuthContextViewModel auth) {
        return repository
                .findByCompanyCode(auth.getCompanyCode())
                .stream()
                .filter(e -> !e.isFrozen())
                .map(e -> {
                    return new KeyValuePairViewModel<Long, String>(
                            e.getId(),
                            String.format("%s  |  %s  |  %s", e.getCategory(), e.getCountryCode(), e.getLocation()));
                })
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsByCombination(String countryCode, String location, IAuthContextViewModel auth) {
        return repository.getCountByParameters(auth.getCompanyCode(), countryCode, location) > 0;
    }

    private LocationViewModel getViewModel(Location entity) {
        // logger.trace("create: Preparing view model");
        LocationViewModel returnViewModel = new LocationViewModel();

        // logger.trace("create: Copying fields to view model");
        BeanUtils.copyProperties(entity, returnViewModel);

        // logger.trace("create: Returning the view model");
        return returnViewModel;
    }
}
