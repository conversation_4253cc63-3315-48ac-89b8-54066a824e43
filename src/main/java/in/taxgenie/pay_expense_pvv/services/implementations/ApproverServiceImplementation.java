package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cem.employee.interfaces.IEmployeeViewModel;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.MasterRecordNotFoundException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.repositories.*;
import in.taxgenie.pay_expense_pvv.services.interfaces.IApproverService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IAuxiliaryService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IEmployeeMasterDataService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
public class ApproverServiceImplementation implements IApproverService {
    private final IApproverRepository repository;
    private final IApproverAuditRepository iApproverAuditRepository;
    private final IApprovalDelegationRepository delegationRepository;
    private final IReportStateRepository stateRepository;
    private final IEmployeeMasterDataService employeeService;
    private final IAuxiliaryService auxiliaryService;
    private final IDocumentApprovalContainerRepository reportRepository;
//    private final IReportStateService stateService;
    private final Logger logger;

    public ApproverServiceImplementation(
            IApproverRepository repository,
            IApproverAuditRepository iApproverAuditRepository,
            IApprovalDelegationRepository delegationRepository,
            IReportStateRepository stateRepository,
            IEmployeeMasterDataService employeeService, IAuxiliaryService auxiliaryService,
            IDocumentApprovalContainerRepository reportRepository) {
        this.repository = repository;
        this.iApproverAuditRepository = iApproverAuditRepository;
        this.delegationRepository = delegationRepository;
        this.stateRepository = stateRepository;
        this.employeeService = employeeService;
        this.auxiliaryService = auxiliaryService;
        this.reportRepository = reportRepository;
//        this.stateService = stateService;
        this.logger = LoggerFactory.getLogger(ApproverServiceImplementation.class);
    }

    @Override
    public ApproverViewModel create(ApproverCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("create: Ensuring whether the approver with these critical details already exists");
        if (repository.getCountByCriteria(auth.getCompanyCode(), viewModel.getApprovalMatcher(), viewModel.getApprovalMatchValue(), viewModel.getApprovalTitle(), StaticDataRegistry.DEFAULT_ENTITY_ID) > 0) {
            throw new DomainInvariantException(String.format("Approver with title: %s for %s -> %s for company: %d already exists", viewModel.getApprovalTitle(), viewModel.getApprovalMatcher(), viewModel.getApprovalMatchValue(), auth.getCompanyCode()));
        }

        logger.trace("create: Creating the new entity and copying the fields");
        Approver approver = new Approver();
        BeanUtils.copyProperties(viewModel, approver);

        logger.trace("create: Setting the audit fields");
        approver.setCompanyCode(auth.getCompanyCode());
        approver.setCreatingUserId(auth.getUserId());
        approver.setCreatedTimestamp(ZonedDateTime.now());

        logger.trace("create: Saving the entity");
        Approver savedEntity = repository.saveAndFlush(approver);

        logger.trace("create: Returning the view-model");
        return getViewModel(savedEntity);
    }

    @Transactional
    @Override
    public ApproverViewModel update(long id, ApproverUpdateViewModel viewModel, IAuthContextViewModel auth) {
        // as per P2P 3296
        logger.trace("validate email: Ensuring the approver email exists in employee master");
        if(!auxiliaryService.isApproverExist(viewModel.getApprover(), false, auth)){
            throw new DomainInvariantException(String.format("Email: %s for company: %d does not exists", viewModel.getApprover(),  auth.getCompanyCode()));
        }


        logger.trace("update: Ensuring the approver record exists");
        Approver entity = repository.findApproverByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getId())
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find Approver with id: %d for company: %d", viewModel.getId(), auth.getCompanyCode())));

        logger.trace("update: Ensuring the critical fields are not duplicated");
        if (repository.getCountByCriteria(
                auth.getCompanyCode(), viewModel.getApprovalMatcher(), viewModel.getApprovalMatchValue(), viewModel.getApprovalTitle(), viewModel.getId()
        ) > 0) {
            throw new DomainInvariantException(String.format("Approver with title: %s for %s -> %s for company: %d already exists", viewModel.getApprovalTitle(), viewModel.getApprovalMatcher(), viewModel.getApprovalMatchValue(), auth.getCompanyCode()));
        }

        logger.trace("update: Updating the fields in the entity");
        BeanUtils.copyProperties(viewModel, entity);
        entity.setUpdatingUserId(auth.getUserId());
        entity.setUpdatedTimestamp(ZonedDateTime.now());

        logger.trace("update: Saving the entity");
        Approver savedEntity = repository.saveAndFlush(entity);
        auditApprover(viewModel, auth);
        logger.trace("update: Returning the view-model");
        return getViewModel(savedEntity);
    }

    private void auditApprover(ApproverUpdateViewModel viewModel, IAuthContextViewModel auth){
        ApproverAudit approverAudit = new ApproverAudit();
        BeanUtils.copyProperties(viewModel, approverAudit);
        approverAudit.setUpdatingUserId(auth.getUserId());
        approverAudit.setUpdatedTimestamp(ZonedDateTime.now());
        iApproverAuditRepository.saveAndFlush(approverAudit);
        logger.trace("audit: Auditing approver updates into audit table for history");
    }

    @Transactional
    @Override
    public void startDelegation(DelegationCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("startDelegation: Conducting preliminary screening before the start of delegation");
        String approver = processScreeingForStartingDelegation(viewModel, auth);

        logger.trace("startDelegation: Creating new ApprovalDelegation entity");
        ApprovalDelegation delegation = new ApprovalDelegation();

        logger.trace("startDelegation: Copying from source");
        BeanUtils.copyProperties(viewModel, delegation);

        logger.trace("startDelegation: Setting audit details");
        delegation.setCreatingUserId(auth.getUserId());
        delegation.setCreatedTimestamp(ZonedDateTime.now());
        delegation.setCompanyCode(auth.getCompanyCode());

        logger.trace("startDelegation: Setting bi-directional mapping");
        delegation.setOriginator(approver);
        delegation.setAssignedTo(viewModel.getToApprover());

        logger.trace("startDelegation: Saving the delegation");
        delegationRepository.saveAndFlush(delegation);

        logger.trace("startDelegation: Processing the swapping of original approver with the delegated approver");
        processExpenseApproverSwapping(auth, approver, delegation);
    }

    private String processScreeingForStartingDelegation(DelegationCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("processScreeingForStartingDelegation: Ensuring the approver has not delegated already");
        List<ApprovalDelegation> currentDelegations = delegationRepository.getAllDelegationByApprover(auth.getCompanyCode(), auth.getUserEmail(), LocalDate.now());

        if (currentDelegations.size() > 0) {
            throw new DomainInvariantException("You have an active delegation already");
        }

        IEmployeeViewModel toApprover
                = employeeService.getEmployeeMasterData(viewModel.getToApprover(), auth);

        if (!toApprover.isCanBeDelegated()) {
            throw new DomainInvariantException(String.format("%s cannot be delegated to", viewModel.getToApprover()));
        }

        logger.trace("processScreeingForStartingDelegation: Ensuring this is not self-delegation");
        if (auth.getUserEmail().equalsIgnoreCase(viewModel.getToApprover())) {
            throw new DomainInvariantException("Self-delegation detected for approver: " + viewModel.getToApprover());
        }

        logger.trace("processScreeingForStartingDelegation: Ensuring the start date is not back-dated");
        if (viewModel.getStartDate() == null || viewModel.getStartDate().isBefore(LocalDate.now())) {
            logger.trace("processScreeingForStartingDelegation: Start date is back-dated");
            throw new DomainInvariantException("Start date of the delegation cannot be back-dated");
        }

        logger.trace("startDelegation: Ensuring the end date is after start date");
        if (viewModel.getEndDate() == null || viewModel.getEndDate().isBefore(viewModel.getStartDate())) {
            logger.trace("startDelegation: End date is either null or before start date");
            throw new DomainInvariantException("End date of the delegation must be defined and should be after the start date");
        }

        return auth.getUserEmail().toLowerCase();
    }

    private String processScreeingForStartingDelegationByAdmin(DelegationCreateViewModel viewModel, IAuthContextViewModel auth) {

        IEmployeeViewModel fromApprover
                = employeeService.getEmployeeMasterData(viewModel.getFromApprover(), auth);

        IEmployeeViewModel toApprover
                = employeeService.getEmployeeMasterData(viewModel.getToApprover(), auth);

        if (!toApprover.isCanBeDelegated()) {
            throw new DomainInvariantException(String.format("%s cannot be delegated to", viewModel.getToApprover()));
        }

        logger.trace("processScreeingForStartingDelegationByAdmin: Ensuring this is not self-delegation");
        if (viewModel.getFromApprover().equalsIgnoreCase(viewModel.getToApprover())) {
            throw new DomainInvariantException("Self-delegation detected for approver: " + viewModel.getToApprover());
        }

        logger.trace("processScreeingForStartingDelegationByAdmin: Ensuring the original approver has not delegated already");
        List<ApprovalDelegation> currentDelegations = delegationRepository.getAllDelegationByApprover(auth.getCompanyCode(), viewModel.getFromApprover(), LocalDate.now());

        if (currentDelegations.size() > 0) {
            throw new DomainInvariantException("The source approver has an active delegation already");
        }

        logger.trace("processScreeingForStartingDelegationByAdmin: Ensuring the start date is not back-dated");
        if (viewModel.getStartDate() == null || viewModel.getStartDate().isBefore(LocalDate.now())) {
            logger.trace("processScreeingForStartingDelegation: Start date is back-dated");
            throw new DomainInvariantException("Start date of the delegation cannot be back-dated");
        }

        logger.trace("processScreeingForStartingDelegationByAdmin: Ensuring the end date is after start date");
        if (viewModel.getEndDate() == null || viewModel.getEndDate().isBefore(viewModel.getStartDate())) {
            logger.trace("processScreeingForStartingDelegationByAdmin: End date is either null or before start date");
            throw new DomainInvariantException("End date of the delegation must be defined and should be after the start date");
        }

        return viewModel.getFromApprover();
    }

    private void processExpenseApproverSwapping(IAuthContextViewModel auth, String approver, ApprovalDelegation delegation) {


        List<ReportState> stateList = stateRepository.findStatesByOriginalApproverActionStatus(auth.getCompanyCode(), approver, ExpenseActionStatus.UNACTIONED, List.of(ReportStatus.DRAFT, ReportStatus.SUBMITTED, ReportStatus.SENT_BACK));
        // Above query has been replaced with below function
//        List<ReportState> stateList = stateService.findStatesByOriginalApproverActionStatus(auth.getCompanyCode(), approver, ExpenseActionStatus.UNACTIONED, List.of(ReportStatus.DRAFT, ReportStatus.SUBMITTED, ReportStatus.SENT_BACK));


        for (ReportState state : stateList) {
//            // Todo TEMPORARY code for now : -> need to remove repository call from loop
//            ExpenseReport expenseReport = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), state.getExpenseReportId())
//                    .orElseThrow(() -> new DomainInvariantException(String.format("processExpenseApproverSwapping: Multiple Expense Reports for found for the same id %s", state.getExpenseReportId())));
//
//
//            if (expenseReport.getEmployeeEmail().equals(delegation.getAssignedTo())) {
//                continue;
//            }
            if (state.getDocumentApprovalContainer().getEmployeeEmail().equals(delegation.getAssignedTo())) {
                continue;
            }

            if (delegation.isAssignUnapproved()) {
                logger.trace("processExpenseApproverSwapping: Swapping the approver for all unactioned expense state items as assign unapproved flag is set");
                swapApprover(state, delegation, StaticDataRegistry.getApprovalDelegationMarker(approver, delegation.getAssignedTo(), state.getLevel(), delegation.getId()), auth);
                continue;
            }

            //  Required as the approvers are set before submission
//            if (expenseReport.getReportStatus() == ReportStatus.DRAFT) {
            if (state.getDocumentApprovalContainer().getReportStatus() == ReportStatus.DRAFT) {
                logger.trace("processExpenseApproverSwapping: Swapping the approver for unactioned expense states of expenses in draft mode as the assign unapproved flag is not set");
                swapApprover(state, delegation, StaticDataRegistry.getApprovalDelegationMarker(approver, delegation.getAssignedTo(), state.getLevel(), delegation.getId()), auth);
            }
        }

        logger.trace("processExpenseApproverSwapping: Saving all the modified expense state entities");
        stateRepository.saveAllAndFlush(stateList);
    }

    private void swapApprover(ReportState state, ApprovalDelegation delegation, String delegationMarker, IAuthContextViewModel auth) {
        logger.trace("swapApprover: Swapping the approver by changing the expense state fields");

        IEmployeeViewModel employeeDetails = employeeService.getEmployeeMasterData(delegation.getAssignedTo(), auth);

        state.setDelegationTimestamp(ZonedDateTime.now());
        state.setApprover(delegation.getAssignedTo());

        state.setApproverFirstName(employeeDetails.getFirstName());
        state.setApproverLastName(employeeDetails.getLastName());
        state.setApproverEmployeeCode(employeeDetails.getEmployeeCode());

        state.setDelegationRemarks(delegationMarker);
        state.setDelegated(true);
    }

    private void resetApprover(ReportState state, String approver, String remarks, IAuthContextViewModel auth) {
        logger.trace("resetApprover: Resetting the expense states (for draft expenses) as the delegation is ended");

        IEmployeeViewModel employeeDetails = employeeService.getEmployeeMasterData(approver, auth);

        state.setDelegationTimestamp(null);
        state.setApprover(approver);

        state.setApproverFirstName(employeeDetails.getFirstName());
        state.setApproverLastName(employeeDetails.getLastName());
        state.setApproverEmployeeCode(employeeDetails.getEmployeeCode());

        state.setDelegationRemarks(remarks);
        state.setRemarks(remarks);
        state.setDelegated(false);
    }

    @Transactional
    @Override
    public void endDelegation(long id, IAuthContextViewModel auth) {
        logger.trace("endDelegation: Ensuring the delegation exists and valid");
        ApprovalDelegation delegation = delegationRepository.findTheDelegation(auth.getCompanyCode(), id, LocalDate.now())
                .orElseThrow(() -> new RecordNotFoundException("Could not find the valid delegation record"));

        logger.trace("endDelegation: Freezing the delegation and setting audit fields");
        delegation.setFrozen(true);
        delegation.setUpdatingUserId(auth.getUserId());
        delegation.setUpdatedTimestamp(ZonedDateTime.now());

        logger.trace("endDelegation: Saving the updated delegation");
        delegationRepository.saveAndFlush(delegation);

        logger.trace("endDelegation: Getting the list of all expense states delegated to {} that are in draft mode", delegation.getAssignedTo());

        List<ReportState> stateList = stateRepository.findStatesByDelegatedApproverReportState(auth.getCompanyCode(), delegation.getAssignedTo(), ReportStatus.DRAFT);
        // Replaced above query with below function.
//        List<ReportState> stateList = stateService.findStatesByDelegatedApproverReportState(auth.getCompanyCode(), delegation.getAssignedTo(), ReportStatus.DRAFT);

        for (ReportState state : stateList) {
            resetApprover(state, delegation.getOriginator(), String.format("Previous delegation: %d is stopped on %s", delegation.getId(), delegation.getUpdatedTimestamp()), auth);
        }

        logger.trace("endDelegation: Saving the expense states");
        stateRepository.saveAllAndFlush(stateList);
    }


    @Transactional
    @Override
    public void startDelegationByAdmin(DelegationCreateViewModel viewModel, IAuthContextViewModel auth) {
        logger.trace("startDelegationByAdmin: Conducting preliminary screening before the start of delegation");
        String approver = processScreeingForStartingDelegationByAdmin(viewModel, auth);

        logger.trace("startDelegationByAdmin: Creating new ApprovalDelegation entity");
        ApprovalDelegation delegation = new ApprovalDelegation();

        logger.trace("startDelegationByAdmin: Copying from source");
        BeanUtils.copyProperties(viewModel, delegation);

        delegation.setAssignedTo(viewModel.getToApprover());

        logger.trace("startDelegationByAdmin: Setting audit details");
        delegation.setCreatingUserId(auth.getUserId());
        delegation.setCreatedTimestamp(ZonedDateTime.now());
        delegation.setCompanyCode(auth.getCompanyCode());

        logger.trace("startDelegationByAdmin: Setting bi-directional mapping");
        delegation.setOriginator(approver);

        logger.trace("startDelegationByAdmin: Saving the delegation");
        delegationRepository.saveAndFlush(delegation);

        logger.trace("startDelegationByAdmin: Processing the swapping of original approver with the delegated approver");
        processExpenseApproverSwapping(auth, approver, delegation);
    }

    @Transactional
    @Override
    public void endDelegationByAdmin(long id, IAuthContextViewModel auth) {
        logger.trace("endDelegationByAdmin: Ensuring the delegation exists and valid");
        ApprovalDelegation delegation = delegationRepository.findTheDelegation(auth.getCompanyCode(), id, LocalDate.now())
                .orElseThrow(() -> new RecordNotFoundException("Could not find the valid delegation record"));

        logger.trace("endDelegationByAdmin: Freezing the delegation and setting audit fields");
        delegation.setFrozen(true);
        delegation.setUpdatingUserId(auth.getUserId());
        delegation.setUpdatedTimestamp(ZonedDateTime.now());

        logger.trace("endDelegationByAdmin: Saving the updated delegation");
        delegationRepository.saveAndFlush(delegation);

        List<ReportState> stateList = stateRepository.findStatesByDelegatedApproverReportState(auth.getCompanyCode(), delegation.getAssignedTo(), ReportStatus.DRAFT);
        // Replaced above query with below function.
//        List<ReportState> stateList = stateService.findStatesByDelegatedApproverReportState(auth.getCompanyCode(), delegation.getAssignedTo(), ReportStatus.DRAFT);

        for (ReportState state : stateList) {
            resetApprover(state, delegation.getOriginator(), String.format("Previous delegation: %d is stopped on %s", delegation.getId(), delegation.getUpdatedTimestamp()), auth);
        }

        logger.trace("endDelegationByAdmin: Saving the expense states");
        stateRepository.saveAllAndFlush(stateList);
    }

    @Override
    public List<ApprovalDelegationViewModel> getAllDelegations(IAuthContextViewModel auth) {
        return delegationRepository
                .getAllByCompanyCode(auth.getCompanyCode())
                .stream()
                .map(this::getDelegationViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public List<ApprovalDelegationViewModel> getUserDelegations(IAuthContextViewModel auth) {
        return delegationRepository
                .getAllByCompanyCodeAndCreatingUserId(auth.getCompanyCode(), auth.getUserId())
                .stream()
                .map(this::getDelegationViewModel)
                .collect(Collectors.toList());
    }

    private ApprovalDelegationViewModel getDelegationViewModel(ApprovalDelegation entity) {
        ApprovalDelegationViewModel viewModel = new ApprovalDelegationViewModel();
        BeanUtils.copyProperties(entity, viewModel);
        viewModel.setToApprover(entity.getAssignedTo());
        viewModel.setFromApprover(entity.getOriginator());
        //Added this check for the Delegation shows active in excel even it is set as expired
//        viewModel.setFrozen(validateDates(entity.getEndDate()));
        if (!entity.isFrozen()) {
            viewModel.setFrozen(validateDates(entity.getEndDate()));
        }
        return viewModel;
    }

    private boolean validateDates(LocalDate endDate) {
        LocalDate today = LocalDate.now(ZoneId.of("UTC"));
        return today.isAfter(endDate);
    }

    @Override
    public List<ApproverViewModel> getAll(IAuthContextViewModel auth) {
        logger.trace("getAll: Returning view-model list");
        return repository
                .findAllByCompanyCode(auth.getCompanyCode())
                .stream()
                .map(this::getViewModel)
                .collect(Collectors.toList());
    }

    @Override
    public ApproverViewModel getById(long id, IAuthContextViewModel auth) {
        Approver entity = repository.findApproverByCompanyCodeAndId(auth.getCompanyCode(), id)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find Approver with id: %d for company: %d", id, auth.getCompanyCode())));

        logger.trace("getById: Returning the view-model");
        return getViewModel(entity);
    }

    @Override
    public List<ApprovalMatcherTitleViewModel> getApprovalMatcherTitleList(IAuthContextViewModel auth) {
        logger.trace("getApprovalMatcherTitleList: Returning list of approver matcher and related titles");
        List<Approver> all = repository
                .findAllByCompanyCode(auth.getCompanyCode());

        List<ApprovalMatcherTitleViewModel> list =
                all
                        .stream()
                        .map(a -> new ApprovalMatcherTitleViewModel(a.getApprovalMatcher(), a.getChannel()))
                        .filter(distinctByKey(ApprovalMatcherTitleViewModel::getMatcher))
                        .sorted((v1, v2) -> v1.getMatcher().compareTo(v2.getMatcher()))
                        .collect(Collectors.toList());

        list.forEach(e -> e.setTitles(
                all
                        .stream()
                        .filter(a -> a.getApprovalMatcher().equals(e.getMatcher()))
                        .map(Approver::getApprovalTitle)
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList())
        ));

        return list;
    }

    @Override
    public boolean existsByCriteria(String approvalMatcher, String approvalMatchValue, String approvalTitle, IAuthContextViewModel auth) {
        logger.trace("existsByCriteria: Returning whether {}/{}/{} exists for Company: {}", approvalMatcher, approvalMatchValue, approvalTitle, auth.getCompanyCode());
        return repository.getCountByCriteria(auth.getCompanyCode(), approvalMatcher, approvalMatchValue, approvalTitle, StaticDataRegistry.DEFAULT_ENTITY_ID) > 0;
    }

    @Transactional
    @Override
    public void changeDesk(long reportId, String toApproverMail, IAuthContextViewModel auth) {

        logger.trace("changeDesk: Ensuring the existence of Expense record for this request");
        DocumentApprovalContainer report =
                reportRepository
                        .findByCompanyCodeAndId(auth.getCompanyCode(), reportId)
                        .orElseThrow(() -> new MasterRecordNotFoundException(String.format("Could not find %s with id: %d and Company Code: %d", Document.class, reportId, auth.getCompanyCode())));

        logger.trace("changeDesk: Ensuring that report is in submitted state");
        if (!report.getReportStatus().equals(ReportStatus.SUBMITTED)) {
            throw new DomainInvariantException(String.format("Report %s is not in submitted state", reportId));
        }

        logger.trace("changeDesk: Ensuring that the toApprover can approve requests");
        IEmployeeViewModel toApprover
                = employeeService.getEmployeeMasterData(toApproverMail, auth);
        if (!toApprover.isCanBeDelegated()) {
            throw new DomainInvariantException(String.format("%s cannot be delegated to", toApproverMail));
        }

        logger.trace("changeDesk: Ensuring this is not self-assignment");
        if (report.getEmployeeEmail().equalsIgnoreCase(toApproverMail)) {
            throw new DomainInvariantException("changeDesk: Self-Assignment detected for approver: " + toApproverMail);
        }

        logger.trace("changeDesk: Ensuring that state of the current approver is unactioned");
        Optional<ReportState> reportState = stateRepository.findTopByCompanyCodeAndDocumentApprovalContainerIdAndLevelAndStatus(auth.getCompanyCode(), report.getId(), report.getActionLevel(), ExpenseActionStatus.UNACTIONED);
        if (!reportState.isPresent()) {
            throw new DomainInvariantException(String.format("changeDesk: The report %s is not in appropriate state for changedesk: ", reportId));
        }

        logger.trace("changeDesk: Swapping Approver in Report State for Current Approver");
        swapApproverInReportState(reportState.get(), toApprover, StaticDataRegistry.getChangeDeskMarker(reportState.get().getApprover(), toApproverMail));

        logger.trace("changeDesk: Changing Current Approver in Report");
        report.setCurrentApproverFirstName(toApprover.getFirstName());
        report.setCurrentApproverLastName(toApprover.getLastName());
        report.setCurrentApproverEmployeeCode(toApprover.getEmployeeCode());
        //report.setCurrentApproverSystemIdCode(toApprover.getSystemIdCode());
        reportRepository.saveAndFlush(report);
    }

    private void swapApproverInReportState(ReportState state, IEmployeeViewModel toApprover, String changeDeskRemarks) {
        logger.trace("swapApprover: Swapping the approver by changing the expense state fields");
        state.setApprover(toApprover.getEmail());
        state.setApproverFirstName(toApprover.getFirstName());
        state.setApproverLastName(toApprover.getLastName());
        state.setApproverEmployeeCode(toApprover.getEmployeeCode());
        state.setChangeDeskTimestamp(ZonedDateTime.now());
        state.setUpdatedTimestamp(ZonedDateTime.now());
        state.setChangeDeskRemarks(changeDeskRemarks);
        state.setChangeDesk(true);
        stateRepository.saveAndFlush(state);
    }

    private ApproverViewModel getViewModel(Approver entity) {
        logger.trace("getViewModel: Preparing view model");
        ApproverViewModel returnViewModel = new ApproverViewModel();

        logger.trace("getViewModel: Copying fields to view model");
        BeanUtils.copyProperties(entity, returnViewModel);

        logger.trace("getViewModel: Returning the view model");
        return returnViewModel;
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }
}
