package in.taxgenie.pay_expense_pvv.api;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextFactory;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.govrndto.SignedDataDTO;
import in.taxgenie.pay_expense_pvv.response.interfaces.factory.IServerResponseFactory;
import in.taxgenie.pay_expense_pvv.response.interfaces.infra.IServerResponseWithBody;
import in.taxgenie.pay_expense_pvv.response.interfaces.infra.IServerResponseWithoutBody;
import in.taxgenie.pay_expense_pvv.services.interfaces.IInvoiceMailroomService;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.company.ResponseViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.TATResponseViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.mailroom.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("api/v1/invoice_mailroom")
@Api(value = "Invoice Actions", tags = {"Invoice API"})
public class InvoiceMailroomApi {
    public static final String INVOICE = "Invoice";
    private final IInvoiceMailroomService invoiceMailroomService;

    private final IAuthContextFactory authContextFactory;
    private final IServerResponseFactory serverResponseFactory;
    private final Logger logger;

    public InvoiceMailroomApi(

            IAuthContextFactory authContextFactory,
            IServerResponseFactory serverResponseFactory,
            IInvoiceMailroomService invoiceMailroomService) {
        this.serverResponseFactory = serverResponseFactory;

        this.invoiceMailroomService = invoiceMailroomService;
        this.authContextFactory = authContextFactory;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Get mail room display queue")
    @PostMapping("/queue")
    public ResponseEntity<IServerResponseWithBody<GenericPageableViewModel<InvoiceMailroomQueueViewModel>>> getItemQueue(
            @RequestBody QueueFilterViewModel filterValues) {

        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        logger.info(" Auth in User queue " + auth.getCompanyCode() + "  " + auth.getUserId() + "  "
                + auth.getAuthorities());

        return ResponseEntity.status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(serverResponseFactory.getServerResponseWithBody(StaticDataRegistry.HTTP_RESPONSE_OK,
                        StaticDataRegistry.HTTP_MESSAGE_OK, true, invoiceMailroomService.getQueue(filterValues, auth)));
    }

    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Get mail room record data by id")
    @GetMapping("/queue/{id}")
    public ResponseEntity<IServerResponseWithBody<InvoiceMailroomDataViewModel>> getItemQueue(@PathVariable long id) {

        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        logger.info(" Auth in User queue " + auth.getCompanyCode() + "  " + auth.getUserId() + "  "
                + auth.getAuthorities());

        return ResponseEntity.status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(serverResponseFactory.getServerResponseWithBody(StaticDataRegistry.HTTP_RESPONSE_OK,
                        StaticDataRegistry.HTTP_MESSAGE_OK, true, invoiceMailroomService.getQueueDataById(id, auth)));
    }

    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Get mail room tat data")
    @GetMapping("/queue/tat_data")
    public ResponseEntity<IServerResponseWithBody<TATResponseViewModel>> getQueueTatData() {

        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        logger.info(" Auth in User queue " + auth.getCompanyCode() + "  " + auth.getUserId() + "  "
                + auth.getAuthorities());

        return ResponseEntity.status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(serverResponseFactory.getServerResponseWithBody(StaticDataRegistry.HTTP_RESPONSE_OK,
                        StaticDataRegistry.HTTP_MESSAGE_OK, true, invoiceMailroomService.getQueueTatData(auth)));
    }

    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Get sync report display queue")
    @PostMapping("/queue/report")
    public ResponseEntity<IServerResponseWithBody<GenericPageableViewModel<InvoiceSyncReportQueueViewModel>>> getMailroomSyncReportQueue(
            @RequestBody QueueFilterViewModel filterValues) {

        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        logger.info(" Auth in User queue " + auth.getCompanyCode() + "  " + auth.getUserId() + "  "
                + auth.getAuthorities());

        return ResponseEntity.status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(serverResponseFactory.getServerResponseWithBody(StaticDataRegistry.HTTP_RESPONSE_OK,
                        StaticDataRegistry.HTTP_MESSAGE_OK, true, invoiceMailroomService.getMailroomSyncReportQueue(filterValues, auth)));

    }


    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Sync now api to sync specific month invoices.")
    @PostMapping("/sync_now")
    public ResponseEntity<IServerResponseWithBody<ResponseViewModel>> getItemQueue(
            @RequestBody InvoiceSyncRequestViewModel requestViewModel) {

        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());

        return ResponseEntity.status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(serverResponseFactory.getServerResponseWithBody(StaticDataRegistry.HTTP_RESPONSE_OK,
                        StaticDataRegistry.HTTP_MESSAGE_OK, true, invoiceMailroomService.syncInvoice(requestViewModel, auth)));
    }


    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Get monthly lookup data current-6 to current")
    @GetMapping("/month_lookup")
    public ResponseEntity<IServerResponseWithBody<List<MonthObjectViewModel>>> get() {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        return ResponseEntity.status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(serverResponseFactory.getServerResponseWithBody(StaticDataRegistry.HTTP_RESPONSE_OK,
                        StaticDataRegistry.HTTP_MESSAGE_OK, true, invoiceMailroomService.getMonthLookupData( auth)));
    }

    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Download invoice json bi id")
    @GetMapping("/download_json/{id}")
    public ResponseEntity<IServerResponseWithBody<String>> downloadJson(@PathVariable Long id) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        return ResponseEntity.status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(serverResponseFactory.getServerResponseWithBody(StaticDataRegistry.HTTP_RESPONSE_OK,
                        StaticDataRegistry.HTTP_MESSAGE_OK, true, invoiceMailroomService.downloadJson( id, auth)));
    }

    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Update or create invoice sync history")
    @PostMapping("/invoice_sync_history")
    public ResponseEntity<IServerResponseWithoutBody>  createOrUpdateInvoiceSyncHistory(
            @RequestBody InvoiceSyncReportRequestViewModel requestViewModel) {

        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());

        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                invoiceMailroomService.createOrUpdateInvoiceSyncHistory(requestViewModel, auth)
                        )
                );
    }


    @ApiOperation(value = "callBackUrl url to upload json invoice")
    @PostMapping("/buyer_data_upload")
    public ResponseEntity<IServerResponseWithoutBody>  buyerDataUpload(
            @RequestBody SignedDataDTO requestViewModel,
            @RequestHeader(value = "requestId", required = true) String requestId) {

        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());

        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                invoiceMailroomService.buyerDataUpload(requestViewModel, requestId, auth)
                        )
                );
    }
}