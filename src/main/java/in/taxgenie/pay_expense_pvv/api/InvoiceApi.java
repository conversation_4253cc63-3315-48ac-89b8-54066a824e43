package in.taxgenie.pay_expense_pvv.api;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextFactory;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.govrndto.InvoiceDeatilsByIrnDTO;
import in.taxgenie.pay_expense_pvv.invoice.request.InvoiceDetailsReq;
import in.taxgenie.pay_expense_pvv.response.interfaces.factory.IServerResponseFactory;
import in.taxgenie.pay_expense_pvv.response.interfaces.infra.IServerResponseWithBody;
import in.taxgenie.pay_expense_pvv.response.interfaces.infra.IServerResponseWithoutBody;
import in.taxgenie.pay_expense_pvv.services.InvoiceOCRService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentApprovalContainerUserService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IFileIOService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IInvoiceService;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.ExistenceResultViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.InvoiceApprovalContainerFromIRNViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.document_matcher.DocumentMatcherResponse;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice.*;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice_ocr.BuyerDocIdRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentCreateResponse;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice_ocr.StatusUpdateViewModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Enumeration;
import java.util.List;


@RestController
@RequestMapping("api/v1/invoice")
@Api(value = "Invoice Actions", tags = {"Invoice API"})
public class InvoiceApi {
    public static final String INVOICE = "Invoice";
    private final IInvoiceService invoiceService;
    private final InvoiceOCRService invoiceOCRService;
    private final IAuthContextFactory authContextFactory;
    private final IServerResponseFactory serverResponseFactory;
    private final IFileIOService fileIOService;
    private final Logger logger;

    public InvoiceApi(
            IInvoiceService service,
            IAuthContextFactory authContextFactory,
            IServerResponseFactory serverResponseFactory,
            IFileIOService fileIOService, IDocumentApprovalContainerUserService documentApprovalContainerUserService, InvoiceOCRService invoiceOCRService) {
        this.invoiceService = service;
        this.authContextFactory = authContextFactory;
        this.serverResponseFactory = serverResponseFactory;
        this.fileIOService = fileIOService;
        this.invoiceOCRService = invoiceOCRService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @PreAuthorize("hasAnyAuthority('user', 'approver', 'checker')")
    @ApiOperation(value = "Get an existing Invoice")
    @GetMapping("/{invoiceId}")
    public ResponseEntity<IServerResponseWithBody<InvoiceDetailsReq>> getById(@PathVariable Long invoiceId) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .contentType(MediaType.APPLICATION_JSON)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                invoiceService.getById(invoiceId, auth)
                        )
                );
    }

    @PreAuthorize("hasAnyAuthority('user', 'approver', 'checker')")
    @ApiOperation(value = "Get an existing Invoice")
    @GetMapping("by-document-container-id/{documentContainerId}")
    public ResponseEntity<IServerResponseWithBody<InvoiceDetailsReq>> getByDocumentContainerId(@PathVariable Long documentContainerId) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                invoiceService.getByDocumentContainerId(documentContainerId, auth)
                        )
                );
    }


    @PreAuthorize("hasAuthority('user')")
    @ApiOperation(value = "Create a new Invoice by creation request")
    @PostMapping("create/{metadataId}/{subgroupId}")
    public ResponseEntity<IServerResponseWithBody<DocumentCreateResponse>> createInvoice(
            @PathVariable Long metadataId, @PathVariable Long subgroupId,
            @RequestPart(name = "invoiceDetails", required = false) String invoiceDetailsRequest,
            @RequestPart(name = "supportingDocuments", required = false) List<MultipartFile> supportingDocuments,
            @RequestPart(name = "invoice", required = false) List<MultipartFile> invoice
    ) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        DocumentCreateResponse response = invoiceService.create(metadataId, subgroupId, invoiceDetailsRequest, invoice, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                response.getMessage() == null ? StaticDataRegistry.HTTP_MESSAGE_OK : response.getMessage(),
                                true,
                                response

                        )
                );
    }
    @PreAuthorize("hasAuthority('user')")
    @ApiOperation(value = "Create a new Invoice by creation request")
    @PostMapping("create/post-upload")
    public ResponseEntity<IServerResponseWithBody<DocumentCreateResponse>> createInvoicePostUpload(
            @RequestBody QueueBulkInvoiceUploadDataViewModel invoiceDetailsRequest
    ) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        DocumentCreateResponse response = invoiceService.createInvoicePostUpload(invoiceDetailsRequest, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                response.getMessage() == null ? StaticDataRegistry.HTTP_MESSAGE_OK : response.getMessage(),
                                true,
                                response

                        )
                );
    }
    @PreAuthorize("hasAuthority('user')")
    @ApiOperation(value = "Check if Invoice already exists by IRN")
    @PostMapping("irn/already-exists")
    public ResponseEntity<IServerResponseWithBody<InvoiceApprovalContainerFromIRNViewModel>> getByIRN(
            @RequestParam(name = "invoice") MultipartFile invoice
    ) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());

        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                invoiceService.getByIRN(invoice, auth)
                        )
                );
    }

    @PreAuthorize("hasAuthority('user')")
    @ApiOperation(value = "Save a new Invoice by update request")
    @PutMapping("save")
    public ResponseEntity<IServerResponseWithoutBody> saveInvoice(
            @RequestBody InvoiceDetailsReq invoiceDetailsRequest
    ) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        boolean isAdded = invoiceService.save(invoiceDetailsRequest, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                isAdded
                        )
                );
    }

    @PreAuthorize("hasAuthority('user')")
    @ApiOperation(value = "Save a Invoice json from dmr")
    @PostMapping("save/ocr_json")
    public ResponseEntity<IServerResponseWithoutBody> saveInvoiceOCRJson(
            @RequestBody InvoiceDeatilsByIrnDTO invoiceDeatilsByIrnDTO,
            HttpServletRequest request
    ) {
        logger.info("Inside saveInvoiceOCRJson" + DateTimeUtils.getCurrentZonedDateTime());
        // Logging the incoming request details
        System.out.println("=== Incoming Request Details ===");
        System.out.println("Request URI: " + request.getRequestURI());
        System.out.println("Method: " + request.getMethod());

        // Logging all headers
        System.out.println("Headers:");
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            System.out.println(headerName + ": " + request.getHeader(headerName));
        }

        // Logging the body
        System.out.println("Request Body: " + invoiceDeatilsByIrnDTO);
        System.out.println("===============================");

        // Existing logic
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        boolean isAdded = invoiceService.saveInvoiceOCRJson(invoiceDeatilsByIrnDTO, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                isAdded
                        )
                );
    }

    @PreAuthorize("hasAuthority('user')")
    @ApiOperation(value = "Save a Invoice buyerDocId from dmr")
    @PutMapping("save/buyer_doc_id")
    public ResponseEntity<IServerResponseWithoutBody> saveInvoiceBuyerDocId(
            @RequestBody BuyerDocIdRequestViewModel buyerDocIdRequestViewModel
    ) {

        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        boolean isAdded = invoiceService.saveInvoiceBuyerDocId(buyerDocIdRequestViewModel, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                isAdded
                        )
                );
    }

    @PreAuthorize("hasAuthority('user')")
    @ApiOperation(value = "Save status of buyerDocId")
    @PutMapping("save/buyer_doc_id/status_update")
    public ResponseEntity<IServerResponseWithoutBody> saveInvoiceBuyerDocIdStatusUpdate(
            @RequestBody StatusUpdateViewModel statusUpdateViewModel
    ) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        boolean isAdded = invoiceService.saveInvoiceBuyerDocIdStatusUpdate(statusUpdateViewModel, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                isAdded
                        )
                );
    }

    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Delete an existing Invoice")
    @DeleteMapping("/{invoiceId}")
    public ResponseEntity<IServerResponseWithoutBody> deleteById(@PathVariable Long invoiceId) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        invoiceService.delete(invoiceId, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true
                        )
                );
    }

    @PreAuthorize("hasAnyAuthority('user', 'approver', 'checker')")
    @ApiOperation(value = "Get existing line items")
    @GetMapping("/{invoiceId}/lineItem")
    public ResponseEntity<IServerResponseWithBody<InvoiceLineItemViewModel>> getByInvoiceId(@PathVariable Long invoiceId) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                invoiceService.getByInvoiceId(invoiceId, auth)
                        )
                );
    }

    @PreAuthorize("hasAnyAuthority('user')")
    @ApiOperation(value = "Delete an existing LineItem")
    @DeleteMapping("/{invoiceId}/lineItem")
    public ResponseEntity<IServerResponseWithoutBody> deleteLineItemById(@PathVariable long invoiceId, @RequestParam("lineItemId") List<Long> lineItemIds) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        invoiceService.deleteLineItem(invoiceId, lineItemIds, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithoutBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true
                        )
                );
    }

    @PreAuthorize("hasAnyAuthority('admin', 'user', 'approver', 'checker')")
    @ApiOperation(value = "Check whether invoice number exists")
    @GetMapping("exists/{supplierId}/invoice/{invoiceNo}")
    public ResponseEntity<IServerResponseWithBody<ExistenceResultViewModel>> checkExistenceByInvoiceNo(@PathVariable String invoiceNo, @PathVariable Long supplierId, @RequestParam(value = "id", required = false) Long id) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());

        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                new ExistenceResultViewModel(
                                        invoiceService.checkExistenceByInvoiceNo(supplierId, invoiceNo, id, auth),
                                        INVOICE)

                        )
                );
    }


    @PreAuthorize("hasAnyAuthority('user', 'approver', 'checker')")
    @ApiOperation(value = "Perform OCR on Invoice")
    @GetMapping("/ocr")
    public ResponseEntity<IServerResponseWithBody<InvoiceDetailsReq>> performOcr(@RequestParam("file") MultipartFile file,
                                                                                 @RequestParam("buyer_gstin") String buyerGstin) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                invoiceOCRService.performOcrAndFetchDetails(file, buyerGstin, auth)
                        )
                );
    }


    @PreAuthorize("hasAnyAuthority('user', 'approver', 'checker')")
    @ApiOperation(value = "Get an existing Invoice PDF base 64 for SAP")
    @GetMapping("pdf/by-document-id/{documentId}")
    public ResponseEntity<IServerResponseWithBody<String>> getInvoicePDFBase64ByDocumentIdentifier(@PathVariable String documentId) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                invoiceService.getInvoicePDFBase64ByDocumentIdentifier(documentId, auth)
                        )
                );
    }

    @GetMapping("testCompanyToken")
    public ResponseEntity<String> testAPI(){
        IAuthContextViewModel auth = authContextFactory.getCompanyAuthContext(SecurityContextHolder.getContext());
        System.out.println("PAN : "+auth.getCompanyPan());
        System.out.println("companyId : "+auth.getCompanyCode());
        System.out.println("senderProductId : "+auth.getSenderProductId());
        System.out.println("receiverProductId : "+auth.getReceiverProductId());

        return ResponseEntity.ok("Success");
    }

    @PreAuthorize("hasAuthority('user')")
    @ApiOperation(value = "Create a new signed url")
    @PostMapping("get/signed-url")
    public ResponseEntity<IServerResponseWithBody<SignedUrlResponseViewModel>> getSignedUrl(
            @RequestBody GetSignedUrlRequestViewModel getSignedUrlRequestViewModel
    ) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        SignedUrlResponseViewModel response = invoiceService.createSignedUrl(getSignedUrlRequestViewModel, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                response
                        )
                );
    }


    @PreAuthorize("hasAuthority('user')")
    @ApiOperation(value = "Bulk upload of invoice")
    @PostMapping("bulk_upload")
    public ResponseEntity<IServerResponseWithBody<BulkInvoiceUploadResponseViewModel>> invoiceBulkUpload(
            @RequestBody BulkInvoiceUploadRequestViewModel requestViewModel
    ) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());
        BulkInvoiceUploadResponseViewModel response = invoiceService.invoiceBulkUpload(requestViewModel, auth);
        return ResponseEntity
                .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                .body(
                        serverResponseFactory.getServerResponseWithBody(
                                StaticDataRegistry.HTTP_RESPONSE_OK,
                                StaticDataRegistry.HTTP_MESSAGE_OK,
                                true,
                                response
                        )
                );
    }

    @PreAuthorize("hasAuthority('user')")
    @ApiOperation(value = "Match invoice with multiple purchase orders")
    @PostMapping("match-with-purchase-orders")
    public ResponseEntity<IServerResponseWithBody<DocumentMatcherResponse>> matchInvoiceWithPurchaseOrders(
            @RequestBody InvoicePoMatchRequest request
    ) {
        IAuthContextViewModel auth = authContextFactory.getAuthContext(SecurityContextHolder.getContext());

        DocumentMatcherResponse response = invoiceService.matchInvoiceWithPurchaseOrders(request, auth);
            return ResponseEntity
                    .status(StaticDataRegistry.HTTP_RESPONSE_OK)
                    .body(
                            serverResponseFactory.getServerResponseWithBody(
                                    StaticDataRegistry.HTTP_RESPONSE_OK,
                                    StaticDataRegistry.HTTP_MESSAGE_OK,
                                    true,
                                    response
                            )
                    );
    }
}
