package in.taxgenie.pay_expense_pvv.multitenancy.context;

public class TenantContext {

    private static ThreadLocal<Long> currentTenant = new ThreadLocal<>();
    private static final ThreadLocal<String> currentSchema = new ThreadLocal<>();

    public static Long getCurrentTenant() {
        return currentTenant.get();
    }

    public static void setCurrentTenant(Long tenant) {
        currentTenant.set(tenant);
    }

    public static void setCurrentSchema(String schema) {
        currentSchema.set(schema);
    }

    public static String getCurrentSchema() {
        return currentSchema.get();
    }

    public static void clear() {
        currentTenant.remove();
        currentSchema.remove();
    }

}