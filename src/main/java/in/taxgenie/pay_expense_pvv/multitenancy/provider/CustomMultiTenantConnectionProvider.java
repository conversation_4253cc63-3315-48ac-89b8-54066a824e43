package in.taxgenie.pay_expense_pvv.multitenancy.provider;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import in.taxgenie.pay_expense_pvv.multitenancy.context.TenantContext;
import in.taxgenie.pay_expense_pvv.multitenancy.entities.TenantConnectionDetails;
import in.taxgenie.pay_expense_pvv.multitenancy.service.TenantService;
import org.hibernate.engine.jdbc.connections.spi.MultiTenantConnectionProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.SQLException;
import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Component
public class CustomMultiTenantConnectionProvider implements MultiTenantConnectionProvider {
    private DataSource defaultDataSource;

    @Autowired
    private TenantService tenantService;

    private static final Logger logger = LoggerFactory.getLogger(CustomMultiTenantConnectionProvider.class);


    // Cache of DataSources per tenant
    private Map<Long, DataSource> dataSources = new ConcurrentHashMap<>();

    // Constructor for dependency injection, used by Spring
    @Autowired
    public CustomMultiTenantConnectionProvider(DataSource defaultDataSource, TenantService tenantService) {
        this.defaultDataSource = defaultDataSource;
        this.tenantService = tenantService;
        logger.info("CustomMultiTenantConnectionProvider initialized with default data source.");
    }
    @Override
    public Connection getAnyConnection() throws SQLException {
        if (defaultDataSource == null) {
            throw new IllegalStateException("Default data source is not initialized");
        }
        logger.debug("Getting any connection from default data source.");
        return defaultDataSource.getConnection();
    }


    @Override
    public void releaseAnyConnection(Connection connection) throws SQLException {
        logger.debug("Releasing any connection.");
        connection.close();
    }

    @Override
    public Connection getConnection(Object tenantIdentifier) throws SQLException {
        Long tenantId = (Long) tenantIdentifier;
        logger.info("Getting connection for tenant ID: {}", tenantId);
        DataSource dataSource = getDataSourceForTenant(tenantId);
        logger.debug("Acquired data source for tenant ID: {}", tenantId);
        Connection connection = dataSource.getConnection();
        TenantContext.setCurrentSchema(connection.getSchema());
        return connection;
    }

    @Override
    public void releaseConnection(Object o, Connection connection) throws SQLException {
        logger.debug("Releasing connection for tenant ID: {}", o);
        connection.close();
    }

    @Override
    public boolean supportsAggressiveRelease() {
        return false;
    }

    @Override
    public boolean isUnwrappableAs(Class unwrapType) {
        return MultiTenantConnectionProvider.class.isAssignableFrom(unwrapType);
    }

    @Override
    public <T> T unwrap(Class<T> unwrapType) {
        if (isUnwrappableAs(unwrapType)) {
            return (T) this;
        } else {
            return null;
        }
    }


    // HELPER METHODS
    private DataSource getDataSourceForTenant(Long tenantIdentifier) {
        logger.debug("Fetching data source for tenant ID: {}", tenantIdentifier);
        return dataSources.computeIfAbsent(tenantIdentifier, id -> {
            TenantConnectionDetails details = fetchTenantConnectionDetails(id);
            if (details.isSchemaBased()) {
                return new SchemaAwareDataSource(defaultDataSource, details.getSchema());
            } else {
                return createDataSource(details);
            }
        });
    }

    private DataSource createDataSourceForTenant(Long tenantIdentifier) {
        logger.info("Creating data source for tenant ID: {}", tenantIdentifier);
        // Fetch tenant connection details from your service or database
        TenantConnectionDetails details = fetchTenantConnectionDetails(tenantIdentifier);

        // Determine if it's schema-based or database-based
        if (details.isSchemaBased()) {
            logger.info("Tenant ID: {} is schema-based.", tenantIdentifier);
            // Use the default DataSource and set the schema at the connection level
            return new SchemaAwareDataSource(defaultDataSource, details.getSchema());
        } else {
            logger.info("Tenant ID: {} is database-based.", tenantIdentifier);
            // Create a new DataSource for the tenant's database
            return createDataSource(details);
        }
    }

    private DataSource createDataSource(TenantConnectionDetails details) {
        logger.info("Creating new HikariDataSource for tenant ID: {}", details.getTenantIdentifier());
        // Create and return a DataSource based on the tenant's connection details
        // Example using HikariCP
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(details.getJdbcUrl());
        config.setUsername(details.getUsername());
        config.setPassword(details.getPassword());
        return new HikariDataSource(config);
    }

    private TenantConnectionDetails fetchTenantConnectionDetails(Long tenantIdentifier) {
        // Implement your logic to fetch tenant connection details
        // For example, query a table or call a service
        return tenantService.getTenantConnectionDetails(tenantIdentifier);
    }

}
