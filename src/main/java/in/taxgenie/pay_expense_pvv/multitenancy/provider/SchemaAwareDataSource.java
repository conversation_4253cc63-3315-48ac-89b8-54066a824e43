package in.taxgenie.pay_expense_pvv.multitenancy.provider;

import org.springframework.jdbc.datasource.DelegatingDataSource;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

public class SchemaAwareDataSource extends DelegatingDataSource {

    private String schema;

    public SchemaAwareDataSource(DataSource targetDataSource, String schema) {
        super(targetDataSource);
        this.schema = schema;
    }

    @Override
    public Connection getConnection() throws SQLException {
        Connection connection = super.getConnection();
        connection.setSchema(schema);
        return connection;
    }

    @Override
    public Connection getConnection(String username, String password) throws SQLException {
        Connection connection = super.getConnection(username, password);
        connection.setSchema(schema);
        return connection;
    }
}