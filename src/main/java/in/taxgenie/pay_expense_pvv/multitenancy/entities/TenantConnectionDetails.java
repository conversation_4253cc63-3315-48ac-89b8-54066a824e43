package in.taxgenie.pay_expense_pvv.multitenancy.entities;

public class TenantConnectionDetails {
    private Long tenantIdentifier;
    private String jdbcUrl;
    private String username;
    private String password;
    private String schema;
    private boolean schemaBased = false;

    public Long getTenantIdentifier() {
        return tenantIdentifier;
    }

    public void setTenantIdentifier(Long tenantIdentifier) {
        this.tenantIdentifier = tenantIdentifier;
    }

    public String getJdbcUrl() {
        return jdbcUrl;
    }

    public void setJdbcUrl(String jdbcUrl) {
        this.jdbcUrl = jdbcUrl;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSchema() {
        return schema;
    }

    public void setSchema(String schema) {
        this.schema = schema;
    }

    public boolean isSchemaBased() {
        return schemaBased;
    }

    public void setSchemaBased(boolean schemaBased) {
        this.schemaBased = schemaBased;
    }
}
