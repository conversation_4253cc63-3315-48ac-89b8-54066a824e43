package in.taxgenie.pay_expense_pvv.multitenancy.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.boot.jdbc.DataSourceBuilder;
import javax.sql.DataSource;

@Configuration
public class DataSourceConfig {

    @Value("${multi-tenancy-config.db-url}")
    private String url;
    @Value("${multi-tenancy-config.db-username}")
    private String username;
    @Value("${multi-tenancy-config.db-password}")
    private String password;

    @Bean
    public DataSource defaultDataSource() {
        return DataSourceBuilder.create()
                .url(url)
                .username(username)
                .password(password)
                .driverClassName("org.postgresql.Driver")
                .build();
    }
}