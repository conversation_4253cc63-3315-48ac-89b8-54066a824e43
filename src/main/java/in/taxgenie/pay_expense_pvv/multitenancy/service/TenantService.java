package in.taxgenie.pay_expense_pvv.multitenancy.service;

import in.taxgenie.pay_expense_pvv.multitenancy.entities.TenantConnectionDetails;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.ResultSet;
import java.sql.SQLException;

@Service
public class TenantService {

    private final JdbcTemplate jdbcTemplate;
    private final Logger logger;

    @Autowired
    public TenantService(DataSource defaultDataSource) {
        // Use the default DataSource to create a JdbcTemplate for querying tenant details
        this.jdbcTemplate = new JdbcTemplate(defaultDataSource);
        this.logger = LoggerFactory.getLogger(TenantService.class);
    }

    public TenantConnectionDetails getTenantConnectionDetails(long tenantIdentifier) {
        // Query to get tenant details by tenant ID from the specified schema
        String sql = "SELECT tenant_id, jdbc_url, username, password, schema, schema_based " +
                "FROM sch_payinvoice_p2p.tenant_details " +
                "WHERE tenant_id = ?";

        try {
            // Attempt to fetch tenant connection details with the provided tenant ID
            return jdbcTemplate.queryForObject(sql, new Object[]{tenantIdentifier}, new TenantConnectionDetailsRowMapper());
        } catch (EmptyResultDataAccessException e) {
            logger.info("getTenantConnectionDetails: The provided tenant: {} using default database", tenantIdentifier);
            // If no result is found, retry with the default tenant ID (0)
            return jdbcTemplate.queryForObject(sql, new Object[]{StaticDataRegistry.DEFAULT_TENANT_IDENTIFIER}, new TenantConnectionDetailsRowMapper());
        }
    }

    // RowMapper to map the result set to TenantConnectionDetails object
    private static class TenantConnectionDetailsRowMapper implements RowMapper<TenantConnectionDetails> {
        @Override
        public TenantConnectionDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
            TenantConnectionDetails tenantConnectionDetails = new TenantConnectionDetails();
            tenantConnectionDetails.setTenantIdentifier(rs.getLong("tenant_id"));
            tenantConnectionDetails.setJdbcUrl(rs.getString("jdbc_url"));
            tenantConnectionDetails.setUsername(rs.getString("username"));
            tenantConnectionDetails.setPassword(rs.getString("password"));
            tenantConnectionDetails.setSchema(rs.getString("schema"));
            tenantConnectionDetails.setSchemaBased(rs.getBoolean("schema_based"));
            return tenantConnectionDetails;
        }
    }
}