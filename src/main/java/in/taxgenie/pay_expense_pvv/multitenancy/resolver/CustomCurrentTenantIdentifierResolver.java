package in.taxgenie.pay_expense_pvv.multitenancy.resolver;

import in.taxgenie.pay_expense_pvv.multitenancy.context.TenantContext;
import org.checkerframework.checker.initialization.qual.Initialized;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.checkerframework.checker.nullness.qual.UnknownKeyFor;
import org.hibernate.context.spi.CurrentTenantIdentifierResolver;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class CustomCurrentTenantIdentifierResolver implements CurrentTenantIdentifierResolver {
    @Override
    public @UnknownKeyFor @Nullable @Initialized Object resolveCurrentTenantIdentifier() {
        Long tenant = TenantContext.getCurrentTenant();
        return Objects.requireNonNullElse(tenant, 0L);
    }

    @Override
    public @UnknownKeyFor @NonNull @Initialized boolean validateExistingCurrentSessions() {
        return true;
    }
}
