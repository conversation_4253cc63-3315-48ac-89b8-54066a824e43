package in.taxgenie.pay_expense_pvv.invoice.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.erp.ERPGrn;
import in.taxgenie.pay_expense_pvv.entities.erp.POGrnMapping;
import in.taxgenie.pay_expense_pvv.entities.gl_master.GlMaster;
import in.taxgenie.pay_expense_pvv.entities.invoice.InvoiceItem;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderItem;
import in.taxgenie.pay_expense_pvv.govrndto.*;
import in.taxgenie.pay_expense_pvv.invoice.message.CustomDetails;
import in.taxgenie.pay_expense_pvv.invoice.message.*;
import in.taxgenie.pay_expense_pvv.invoice.request.InvoiceDetailsReq;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetDetails;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetSelectionViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.erp.ERPGrnViewModel;
import org.eclipse.jdt.core.compiler.InvalidInputException;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
public class InvoiceHeaderMapper {

    public static void prepareInvoiceHeaderOld(InvoiceHeader invoiceHeader, InvoiceDetailsReq request, Document document) {


        //        invoiceHeader.setInvoiceReceived(Optional.ofNullable(request.getInvoiceReceivedId()).map(value -> InvoiceReceived.builder().invoiceReceivedId(value).build()).orElse(null));
        document.setDocNo(Optional.ofNullable(request.getDocumentDetails()).map(InvoiceDocumentDetails::getDocNo).orElse(null));
        invoiceHeader.setDocument(document);

        invoiceHeader.setInvoiceDocumentTypeId(Optional.ofNullable(request.getDocumentDetails()).map(InvoiceDocumentDetails::getInvoiceDocumentType).orElse(null));
        invoiceHeader.setInvoiceTypeId(Optional.ofNullable(request.getDocumentDetails()).map(InvoiceDocumentDetails::getInvoiceType).orElse(null));
        invoiceHeader.setInvoiceSubTypeId(Optional.ofNullable(request.getDocumentDetails()).map(InvoiceDocumentDetails::getInvoiceSubType).orElse(null));
        invoiceHeader.setIsPrecedingDoc(Optional.ofNullable(request.getDocumentDetails()).map(InvoiceDocumentDetails::getIsPrecedingDoc).orElse(null));

        invoiceHeader.setDispatchName(Optional.ofNullable(request.getDispatchDetails()).map(DispatchDetails::getName).orElse(null));
        invoiceHeader.setDispatchAddress1(Optional.ofNullable(request.getDispatchDetails()).map(DispatchDetails::getAddress1).orElse(null));
        invoiceHeader.setDispatchAddress2(Optional.ofNullable(request.getDispatchDetails()).map(DispatchDetails::getAddress2).orElse(null));
        invoiceHeader.setDispatchLocation(Optional.ofNullable(request.getDispatchDetails()).map(DispatchDetails::getLocation).orElse(null));
        invoiceHeader.setDispatchPin(Optional.ofNullable(request.getDispatchDetails()).map(DispatchDetails::getPin).orElse(null));
        invoiceHeader.setDispatchStateCode(Optional.ofNullable(request.getDispatchDetails())
                .map(DispatchDetails::getStateCodeId)
                .map(gstStateCodeId -> StateCodes.builder().id(gstStateCodeId).build())
                .orElse(null));
        invoiceHeader.setIsDispatchAddressSame(Optional.ofNullable(request.getDispatchDetails()).map(DispatchDetails::getIsDispatchAddressSame).orElse(null));
        invoiceHeader.setDispatchPhoneNumber(Optional.ofNullable(request.getDispatchDetails()).map(DispatchDetails::getPhoneNumber).orElse(null));
        invoiceHeader.setDispatchEmail(Optional.ofNullable(request.getDispatchDetails()).map(DispatchDetails::getEmail).orElse(null));

        invoiceHeader.setShipGstin(Optional.ofNullable(request.getShipToDetails()).map(ShipToDetails::getGstin).orElse(null));
        invoiceHeader.setShipLegalName(Optional.ofNullable(request.getShipToDetails()).map(ShipToDetails::getLegalName).orElse(null));
        invoiceHeader.setShipTradename(Optional.ofNullable(request.getShipToDetails()).map(ShipToDetails::getTradeName).orElse(null));
        invoiceHeader.setShipAddress1(Optional.ofNullable(request.getShipToDetails()).map(ShipToDetails::getAddress1).orElse(null));
        invoiceHeader.setShipAddress2(Optional.ofNullable(request.getShipToDetails()).map(ShipToDetails::getAddress2).orElse(null));
        invoiceHeader.setShipLocation(Optional.ofNullable(request.getShipToDetails()).map(ShipToDetails::getLocation).orElse(null));
        invoiceHeader.setShipPin(Optional.ofNullable(request.getShipToDetails()).map(ShipToDetails::getPin).orElse(null));
        invoiceHeader.setShipStateCode1(Optional.ofNullable(request.getShipToDetails())
                .map(ShipToDetails::getStateCodeId)
                .map(gstStateCodeId -> StateCodes.builder().id(gstStateCodeId).build())
                .orElse(null));
        invoiceHeader.setIsDeliveryAddressSame(Optional.ofNullable(request.getShipToDetails()).map(ShipToDetails::getIsDeliveryAddressSame).orElse(null));
        invoiceHeader.setShipPhoneNumber(Optional.ofNullable(request.getShipToDetails()).map(ShipToDetails::getPhoneNumber).orElse(null));
        invoiceHeader.setShipEmail(Optional.ofNullable(request.getShipToDetails()).map(ShipToDetails::getEmail).orElse(null));

        // Todo Vishal: Move below values to Document Discount amounts : Start
//        invoiceHeader.setTotalCessValue(Optional.ofNullable(request.getAmountDetails()).map(AmountDetails::getTotalCessValue).orElse(null));
//        invoiceHeader.setTotalStateCessValue(Optional.ofNullable(request.getAmountDetails()).map(AmountDetails::getTotalStateCessValue).orElse(null));
//        invoiceHeader.setInvoiceDiscount(Optional.ofNullable(request.getAmountDetails()).map(AmountDetails::getInvoiceDiscount).orElse(null));
//        invoiceHeader.setInvoiceOtherCharges(Optional.ofNullable(request.getAmountDetails()).map(AmountDetails::getInvoiceOtherCharges).orElse(null));
        invoiceHeader.setRoundoffAmount(Optional.ofNullable(request.getAmountDetails()).map(AmountDetails::getRoundoffAmount).orElse(null));
//                .totalInvoiceValue(Optional.ofNullable(request.getAmountDetails()).map(AmountDetails::getTotalInvoiceValue).orElse(null))
//                .finalInvoiceValue(Optional.ofNullable(request.getAmountDetails()).map(AmountDetails::getFinalInvoiceValue).orElse(null))
        // Todo Vishal: Move below values to Document Discount amounts : End


        invoiceHeader.setPayeeName(Optional.ofNullable(request.getPaymentDetails()).map(PaymentDetails::getPayeeName).orElse(null));
        invoiceHeader.setModeOfPayment(Optional.ofNullable(request.getPaymentDetails()).map(PaymentDetails::getModeOfPayment).orElse(null));
        invoiceHeader.setFinInsBr(Optional.ofNullable(request.getPaymentDetails()).map(PaymentDetails::getIfscCode).orElse(null));
        invoiceHeader.setPaymentTerm(Optional.ofNullable(request.getPaymentDetails()).map(PaymentDetails::getPaymentTerm).orElse(null));
        invoiceHeader.setPaymentInstruction(Optional.ofNullable(request.getPaymentDetails()).map(PaymentDetails::getPaymentInstruction).orElse(null));
        invoiceHeader.setCreditTransfer(Optional.ofNullable(request.getPaymentDetails()).map(PaymentDetails::getCreditTransfer).orElse(null));
        invoiceHeader.setDirectDebit(Optional.ofNullable(request.getPaymentDetails()).map(PaymentDetails::getDirectDebit).orElse(null));
        invoiceHeader.setCreditDays(Optional.ofNullable(request.getPaymentDetails()).map(PaymentDetails::getCreditDays).orElse(null));


//        invoiceHeader.setPaidAmount(Optional.ofNullable(request.getPaymentDetails()).map(PaymentDetails::getPaidAmount).orElse(null));
//        invoiceHeader.setPaymentDue(Optional.ofNullable(request.getPaymentDetails()).map(PaymentDetails::getPaymentDue).orElse(null));
        document.setConsumedAmount(Optional.ofNullable(request.getPaymentDetails()).map(PaymentDetails::getPaidAmount).orElse(null)); // Todo Vishal: Check changes
        document.setRemainingAmount(Optional.ofNullable(request.getPaymentDetails()).map(PaymentDetails::getPaymentDue).orElse(null)); // Todo Vishal: Check if can auto-derive this instead

        invoiceHeader.setAccountDetails(Optional.ofNullable(request.getPaymentDetails()).map(PaymentDetails::getAccountDetails).orElse(null));

        invoiceHeader.setInvRm(Optional.ofNullable(request.getReferenceDetails()).map(ReferenceDetails::getInvoiceRemarks).orElse(null));

        invoiceHeader.setInvoiceStartDate(Optional.ofNullable(request.getDocumentDetails()).flatMap(value -> Optional.ofNullable(value.getInvoiceStartDate())
                        .map(DateTimeUtils::parseDatetoLocalDate))
                .orElse(null));

        invoiceHeader.setInvoiceEndDate(Optional.ofNullable(request.getDocumentDetails()).flatMap(value -> Optional.ofNullable(value.getInvoiceEndDate()).map(DateTimeUtils::parseDatetoLocalDate))
                .orElse(null));

        invoiceHeader.setPrecedingInvoiceNo(Optional.ofNullable(request.getDocumentDetails()).map(InvoiceDocumentDetails::getPrecedingInvoiceNo).orElse(null));
        invoiceHeader.setPrecedingInvoiceDate(Optional.ofNullable(request.getDocumentDetails()).flatMap(value -> Optional.ofNullable(value.getPrecedingInvoiceDate())
                        .map(DateTimeUtils::parseDatetoLocalDate))
                .orElse(null));
        invoiceHeader.setOtherRef(Optional.ofNullable(request.getDocumentDetails()).map(InvoiceDocumentDetails::getOtherRef).orElse(null));

        invoiceHeader.setReceiptAdviceNo(Optional.ofNullable(request.getContractReferenceDetails()).map(ContractReferenceDetails::getReceiptAdviceNo).orElse(null));
        invoiceHeader.setDateOfReceiptAdvice(Optional.ofNullable(request.getContractReferenceDetails()).flatMap(value -> Optional.ofNullable(value.getDateOfReceiptAdvice())
                        .map(DateTimeUtils::parseDatetoLocalDate))
                .orElse(null));
        invoiceHeader.setTendRef(Optional.ofNullable(request.getContractReferenceDetails()).map(ContractReferenceDetails::getLotRefNo).orElse(null));
        invoiceHeader.setContractRefNo(Optional.ofNullable(request.getContractReferenceDetails()).map(ContractReferenceDetails::getContractRefNo).orElse(null));
        invoiceHeader.setExtRef(Optional.ofNullable(request.getContractReferenceDetails()).map(ContractReferenceDetails::getAnyOtherRef).orElse(null));
        invoiceHeader.setProjectRef(Optional.ofNullable(request.getContractReferenceDetails()).map(ContractReferenceDetails::getProjectRef).orElse(null));
        invoiceHeader.setPoRefNo(Optional.ofNullable(request.getContractReferenceDetails()).map(ContractReferenceDetails::getPoRefNo).orElse(null));
        invoiceHeader.setPoRefDate(Optional.ofNullable(request.getContractReferenceDetails()).flatMap(value -> Optional.ofNullable(value.getPoRefDate()).map(DateTimeUtils::parseDatetoLocalDate))
                .orElse(null));

        invoiceHeader.setShipBNo(Optional.ofNullable(request.getExportDetails()).map(ExportDetails::getShippingBillNo).orElse(null));
        invoiceHeader.setShipBDt(Optional.ofNullable(request.getExportDetails()).flatMap(value -> Optional.ofNullable(value.getShippingBillDate()).map(DateTimeUtils::parseDatetoLocalDate))
                .orElse(null));
        invoiceHeader.setPort(Optional.ofNullable(request.getExportDetails())
                .map(ExportDetails::getPortCode)
                .map(portCode -> Ports.builder().id(portCode).build())
                .orElse(null));
        invoiceHeader.setRefClm(Optional.ofNullable(request.getExportDetails()).map(ExportDetails::getRefundClaim).orElse(null));
        invoiceHeader.setForCur(Optional.ofNullable(request.getExportDetails())
                .map(ExportDetails::getForeignCurrency)
                .map(foreignCurrency -> LookupData.builder().id(foreignCurrency).build())
                .orElse(null));
        invoiceHeader.setCntCode(Optional.ofNullable(request.getExportDetails())
                .map(ExportDetails::getCountryCode)
                .map(countryCode -> Countries.builder().id(countryCode).build())
                .orElse(null));

//        invoiceHeader.setExpDuty(Optional.ofNullable(request.getExportDetails()).map(ExportDetails::getExportDuty).orElse(null));
        document.setExportDutyAmount(Optional.ofNullable(request.getExportDetails()).map(ExportDetails::getExportDuty).orElse(null)); // Todo Vishal: Check Changes

        invoiceHeader.setTransId(Optional.ofNullable(request.getEwayBillDetails()).map(EwayBillDetails::getTransporter).orElse(null));
        invoiceHeader.setTransName(Optional.ofNullable(request.getEwayBillDetails()).map(EwayBillDetails::getTransporterName).orElse(null));
        invoiceHeader.setTransMode(Optional.ofNullable(request.getEwayBillDetails()).map(EwayBillDetails::getModeOfTransportation).orElse(null));
        invoiceHeader.setDistance(Optional.ofNullable(request.getEwayBillDetails()).map(EwayBillDetails::getDistOfTransportation).orElse(null));
        invoiceHeader.setTransDocNo(Optional.ofNullable(request.getEwayBillDetails()).map(EwayBillDetails::getTransporterDocNo).orElse(null));
        invoiceHeader.setTransDocDt(Optional.ofNullable(request.getEwayBillDetails()).flatMap(value -> Optional.ofNullable(value.getTransporterDocDate()).map(DateTimeUtils::parseDatetoLocalDate))
                .orElse(null));
        invoiceHeader.setVehNo(Optional.ofNullable(request.getEwayBillDetails()).map(EwayBillDetails::getVehicleNo).orElse(null));
        invoiceHeader.setOdcOrRegular(Optional.ofNullable(request.getEwayBillDetails()).map(EwayBillDetails::getOdcOrRegular).orElse(null));

        invoiceHeader.setAdditionalParameters(request.getAdditionalParameter());
//                .invoiceStatus(Optional.ofNullable(request.getInvoiceStatusId()).map(value-> LookupData.builder().id(value).build()).orElseGet(null))

        // new fields add - TODO : get and set values
        invoiceHeader.setPos(Optional.ofNullable(invoiceHeader.getDispatchStateCode()).map(StateCodes::getCode).orElse(null));
//        invoiceHeader.setPos(""); // set from statecode id
//        invoiceHeader.setTdsValue(BigDecimal.ZERO);
//        invoiceHeader.setRegRev(false);
//        invoiceHeader.setItcType("");

        // --
        if (null != invoiceHeader.getInvoiceHeaderId()) {
            // remove - not items on invoice create - should be on save
//            LineItemsMapper.handleUpdateLineItemDetails(request.getItemList(), invoiceHeader);
            invoiceHeader.setUpdatedAt(DateTimeUtils.getCurrentTimestamp());
        } else {
            //TODO: commented - check later - handled in service as per invoice item
            invoiceHeader.setInvoiceItems(LineItemsMapper.lineItemDetailsMapper(request.getItemList(), invoiceHeader));
            invoiceHeader.setCreatedAt(DateTimeUtils.getCurrentTimestamp());
        }

    }

    public static void prepareInvoiceHeader(InvoiceHeader invoiceHeader, InvoiceDetailsReq request, Document document) {

        // Document details
        InvoiceDocumentDetails docDetails = request.getDocumentDetails();
        if (docDetails != null) {
            document.setDocNo(docDetails.getDocNo());
            invoiceHeader.setInvoiceDocumentTypeId(docDetails.getInvoiceDocumentType());
            invoiceHeader.setInvoiceTypeId(docDetails.getInvoiceType());
            invoiceHeader.setInvoiceSubTypeId(docDetails.getInvoiceSubType());
            invoiceHeader.setItcType(docDetails.getItcEligibility());
            invoiceHeader.setIsPrecedingDoc(docDetails.getIsPrecedingDoc());
            invoiceHeader.setInvoiceStartDate(parseLocalDate(docDetails.getInvoiceStartDate()));
            invoiceHeader.setInvoiceEndDate(parseLocalDate(docDetails.getInvoiceEndDate()));
            invoiceHeader.setPrecedingInvoiceNo(docDetails.getPrecedingInvoiceNo());
            invoiceHeader.setPrecedingInvoiceDate(parseLocalDate(docDetails.getPrecedingInvoiceDate()));
            invoiceHeader.setOtherRef(docDetails.getOtherRef());
            invoiceHeader.setRegRev(docDetails.getRegRev());
        }
        invoiceHeader.setDocument(document);

        // Dispatch details
        DispatchDetails dispatch = request.getDispatchDetails();
        if (dispatch != null) {
            invoiceHeader.setDispatchName(dispatch.getName());
            invoiceHeader.setDispatchAddress1(dispatch.getAddress1());
            invoiceHeader.setDispatchAddress2(dispatch.getAddress2());
            invoiceHeader.setDispatchLocation(dispatch.getLocation());
            invoiceHeader.setDispatchPin(dispatch.getPin());
            invoiceHeader.setDispatchStateCode(buildStateCode(dispatch.getStateCodeId()));
            invoiceHeader.setIsDispatchAddressSame(dispatch.getIsDispatchAddressSame());
            invoiceHeader.setDispatchPhoneNumber(dispatch.getPhoneNumber());
            invoiceHeader.setDispatchEmail(dispatch.getEmail());
        }
        // Shipping details
        ShipToDetails shipTo = request.getShipToDetails();
        if (shipTo != null) {
            invoiceHeader.setShipGstin(shipTo.getGstin());
            invoiceHeader.setShipLegalName(shipTo.getLegalName());
            invoiceHeader.setShipTradename(shipTo.getTradeName());
            invoiceHeader.setShipAddress1(shipTo.getAddress1());
            invoiceHeader.setShipAddress2(shipTo.getAddress2());
            invoiceHeader.setShipLocation(shipTo.getLocation());
            invoiceHeader.setShipPin(shipTo.getPin());
            invoiceHeader.setShipStateCode1(buildStateCode(shipTo.getStateCodeId()));
            invoiceHeader.setIsDeliveryAddressSame(shipTo.getIsDeliveryAddressSame());
            invoiceHeader.setShipPhoneNumber(shipTo.getPhoneNumber());
            invoiceHeader.setShipEmail(shipTo.getEmail());
        }
        AmountDetails amountDetails = request.getAmountDetails();
        if (amountDetails != null) {
            invoiceHeader.setRoundoffAmount(amountDetails.getRoundoffAmount());
        }
        // Payment details
        PaymentDetails payment = request.getPaymentDetails();
        if (payment != null) {
            invoiceHeader.setPayeeName(payment.getPayeeName());
            invoiceHeader.setModeOfPayment(payment.getModeOfPayment());
            invoiceHeader.setFinInsBr(payment.getIfscCode());
            invoiceHeader.setPaymentTerm(payment.getPaymentTerm());
            invoiceHeader.setPaymentInstruction(payment.getPaymentInstruction());
            invoiceHeader.setCreditTransfer(payment.getCreditTransfer());
            invoiceHeader.setDirectDebit(payment.getDirectDebit());
            invoiceHeader.setCreditDays(payment.getCreditDays());
            invoiceHeader.setAccountDetails(payment.getAccountDetails());
            document.setConsumedAmount(payment.getPaidAmount());
            document.setRemainingAmount(payment.getPaymentDue());
        }
        // Reference details
        ReferenceDetails referenceDetails = request.getReferenceDetails();
        if (referenceDetails != null) {
            invoiceHeader.setInvRm(referenceDetails.getInvoiceRemarks());
        }
        // Contract details
        ContractReferenceDetails contract = request.getContractReferenceDetails();
        if (contract != null) {
            invoiceHeader.setReceiptAdviceNo(contract.getReceiptAdviceNo());
            invoiceHeader.setDateOfReceiptAdvice(parseLocalDate(contract.getDateOfReceiptAdvice()));
            invoiceHeader.setTendRef(contract.getLotRefNo());
            invoiceHeader.setContractRefNo(contract.getContractRefNo());
            invoiceHeader.setExtRef(contract.getAnyOtherRef());
            invoiceHeader.setProjectRef(contract.getProjectRef());
            invoiceHeader.setPoRefNo(contract.getPoRefNo());
            invoiceHeader.setPoRefDate(parseLocalDate(contract.getPoRefDate()));
        }
        // Export details
        ExportDetails export = request.getExportDetails();
        if (export != null) {
            invoiceHeader.setShipBNo(export.getShippingBillNo());
            invoiceHeader.setShipBDt(parseLocalDate(export.getShippingBillDate()));
            invoiceHeader.setPort(buildPort(export.getPortCode()));
            invoiceHeader.setRefClm(export.getRefundClaim());
            invoiceHeader.setForCur(buildLookupData(export.getForeignCurrency()));
            invoiceHeader.setCntCode(buildCountry(export.getCountryCode()));
            document.setExportDutyAmount(export.getExportDuty());
        }

        EwayBillDetails ewbDtls = request.getEwayBillDetails();
        if (ewbDtls != null) {
            invoiceHeader.setTransId(ewbDtls.getTransporter());
            invoiceHeader.setTransName(ewbDtls.getTransporterName());
            invoiceHeader.setTransMode(ewbDtls.getModeOfTransportation());
            invoiceHeader.setDistance(ewbDtls.getDistOfTransportation());
            invoiceHeader.setTransDocNo(ewbDtls.getTransporterDocNo());
            invoiceHeader.setTransDocDt(parseLocalDate(ewbDtls.getTransporterDocDate()));
            invoiceHeader.setVehNo(ewbDtls.getVehicleNo());
            invoiceHeader.setOdcOrRegular(ewbDtls.getOdcOrRegular());
        }
        invoiceHeader.setAdditionalParameters(request.getAdditionalParameter());
        StateCodes dispatchStateCode = invoiceHeader.getDispatchStateCode();
        if (dispatchStateCode != null) {
            invoiceHeader.setPos(dispatchStateCode.getCode());
        }
        if (null != invoiceHeader.getInvoiceHeaderId()) {
            invoiceHeader.setUpdatedAt(DateTimeUtils.getCurrentTimestamp());
        } else {
            // check if setInvoiceItems is needed here
            invoiceHeader.setInvoiceItems(LineItemsMapper.lineItemDetailsMapper(request.getItemList(), invoiceHeader));
            invoiceHeader.setCreatedAt(DateTimeUtils.getCurrentTimestamp());
        }
    }

    private static LocalDate parseLocalDate(String date) {
        return Optional.ofNullable(date).map(DateTimeUtils::parseDatetoLocalDate).orElse(null);
    }

    private static StateCodes buildStateCode(Integer stateCodeId) {
        return Optional.ofNullable(stateCodeId).map(id -> StateCodes.builder().id(id).build()).orElse(null);
    }

    private static Ports buildPort(Integer portCode) {
        return Optional.ofNullable(portCode).map(id -> Ports.builder().id(id).build()).orElse(null);
    }

    private static LookupData buildLookupData(Integer code) {
        return Optional.ofNullable(code).map(id -> LookupData.builder().id(id).build()).orElse(null);
    }

    private static Countries buildCountry(Integer countryCode) {
        return Optional.ofNullable(countryCode).map(id -> Countries.builder().id(id).build()).orElse(null);
    }

    public static void prepareInvoiceHeaderFromJsonOld(InvoiceHeader invoiceHeader, InvoiceDTO request, Document document) throws JsonProcessingException {
        document.setDocNo(Optional.ofNullable(request.getDocDtls()).map(DocDtlsDTO::getNo).orElse(null));
        invoiceHeader.setDocument(document);

        invoiceHeader.setDispatchName(Optional.ofNullable(request.getShipDtls()).map(ShipDtlsDTO::getShipLegalName).orElse(null));
        invoiceHeader.setDispatchAddress1(Optional.ofNullable(request.getShipDtls()).map(ShipDtlsDTO::getShipAddress1).orElse(null));
        invoiceHeader.setDispatchAddress2(Optional.ofNullable(request.getShipDtls()).map(ShipDtlsDTO::getShipAddress2).orElse(null));
        invoiceHeader.setDispatchLocation(Optional.ofNullable(request.getShipDtls()).map(ShipDtlsDTO::getShipLocation).orElse(null));
        invoiceHeader.setDispatchPin(Optional.ofNullable(request.getShipDtls()).map(v -> String.valueOf(v.getShipPin())).orElse(null));
//        invoiceHeader.setDispatchStateCode(Optional.ofNullable(request.getShipDtls())
//                .map(value -> value.getShipStateCode1())
//                .map(gstStateCodeId -> StateCodes.builder().id(Integer.valueOf(gstStateCodeId)).build())
//                .orElse(null));

        invoiceHeader.setDispatchStateCode(Optional.ofNullable(request.getShipDtls())
                .map(ShipDtlsDTO::getShipStateCode1)
                .filter(gstStateCodeId -> gstStateCodeId != null && !gstStateCodeId.trim().isEmpty()) // Handle empty string
                .map(gstStateCodeId -> StateCodes.builder().id(Integer.valueOf(gstStateCodeId)).build())
                .orElse(null));
        invoiceHeader.setShipGstin(Optional.ofNullable(request.getShipDtls()).map(ShipDtlsDTO::getShipGstin).orElse(null));
        invoiceHeader.setShipLegalName(Optional.ofNullable(request.getShipDtls()).map(ShipDtlsDTO::getShipLegalName).orElse(null));
        invoiceHeader.setShipTradename(Optional.ofNullable(request.getShipDtls()).map(ShipDtlsDTO::getShipTradename).orElse(null));
        invoiceHeader.setShipAddress1(Optional.ofNullable(request.getShipDtls()).map(ShipDtlsDTO::getShipAddress1).orElse(null));
        invoiceHeader.setShipAddress2(Optional.ofNullable(request.getShipDtls()).map(ShipDtlsDTO::getShipAddress2).orElse(null));
        invoiceHeader.setShipLocation(Optional.ofNullable(request.getShipDtls()).map(ShipDtlsDTO::getShipLocation).orElse(null));
        invoiceHeader.setShipPin(Optional.ofNullable(request.getShipDtls()).map(v -> String.valueOf(v.getShipPin())).orElse(null));
//        invoiceHeader.setShipStateCode1(Optional.ofNullable(request.getShipDtls())
//                .map(value -> value.getShipStateCode1())
//                .map(gstStateCodeId -> StateCodes.builder().id(Integer.valueOf(gstStateCodeId)).build())
//                .orElse(null));
        invoiceHeader.setShipStateCode1(Optional.ofNullable(request.getShipDtls())
                .map(ShipDtlsDTO::getShipStateCode1)
                .filter(gstStateCodeId -> gstStateCodeId != null && !gstStateCodeId.trim().isEmpty()) // Handle empty string
                .map(gstStateCodeId -> StateCodes.builder().id(Integer.valueOf(gstStateCodeId)).build())
                .orElse(null));

        // Todo Vishal: Move below values to Document Discount amounts : Start
//        invoiceHeader.setTotalCessValue(Optional.ofNullable(request.getValDtls()).map(ValDtlsDTO::getTotalCessValue).map(BigDecimal::new).orElse(null));
//        invoiceHeader.setTotalStateCessValue(Optional.ofNullable(request.getValDtls()).map(ValDtlsDTO::getTotalStateCessValue).map(BigDecimal::new).orElse(null));
//        invoiceHeader.setInvoiceDiscount(Optional.ofNullable(request.getValDtls()).map(ValDtlsDTO::getInvoiceDiscount).map(BigDecimal::new).orElse(null));
//        invoiceHeader.setInvoiceOtherCharges(Optional.ofNullable(request.getValDtls()).map(ValDtlsDTO::getInvoiceOtherCharges).map(BigDecimal::new).orElse(null));
        invoiceHeader.setRoundoffAmount(Optional.ofNullable(request.getValDtls()).map(ValDtlsDTO::getRoundoffAmount).map(BigDecimal::new).orElse(null));
////                .totalInvoiceValue(Optional.ofNullable(request.getAmountDetails()).map(AmountDetails::getTotalInvoiceValue).orElse(null))
////                .finalInvoiceValue(Optional.ofNullable(request.getAmountDetails()).map(AmountDetails::getFinalInvoiceValue).orElse(null))
        // Todo Vishal: Move below values to Document Discount amounts : End

        invoiceHeader.setPayeeName(Optional.ofNullable(request.getPayDtls()).map(PayDtlsDTO::getPayeeName).orElse(null));
        invoiceHeader.setModeOfPayment(Optional.ofNullable(request.getPayDtls()).map(PayDtlsDTO::getModeOfPayment).orElse(null));
//        invoiceHeader.setIfscCode(Optional.ofNullable(request.getPayDtls()).map(PayDtlsDTO::getIfscCode).orElse(null)); -- not in json
        invoiceHeader.setPaymentTerm(Optional.ofNullable(request.getPayDtls()).map(PayDtlsDTO::getPaymentTerm).orElse(null));
        invoiceHeader.setPaymentInstruction(Optional.ofNullable(request.getPayDtls()).map(PayDtlsDTO::getPaymentInstruction).orElse(null));
        invoiceHeader.setCreditTransfer(Optional.ofNullable(request.getPayDtls()).map(PayDtlsDTO::getCreditTransfer).orElse(null));
        invoiceHeader.setDirectDebit(Optional.ofNullable(request.getPayDtls()).map(PayDtlsDTO::getDirectDebit).orElse(null));
        invoiceHeader.setCreditDays(Optional.ofNullable(request.getPayDtls()).map(PayDtlsDTO::getCreditDays).orElse(null));

//        invoiceHeader.setPaidAmount(Optional.ofNullable(request.getPayDtls()).map(PayDtlsDTO::getPaidAmount).map(BigDecimal::new).orElse(null));
//        invoiceHeader.setPaymentDue(Optional.ofNullable(request.getPayDtls()).map(v -> v.getPaymentDue()).map(BigDecimal::new).orElse(null));

        document.setConsumedAmount(Optional.ofNullable(request.getPayDtls()).map(PayDtlsDTO::getPaidAmount).map(BigDecimal::new).orElse(null)); // Todo Vishal: Check changes
        document.setRemainingAmount(Optional.ofNullable(request.getPayDtls()).map(PayDtlsDTO::getPaymentDue).map(BigDecimal::new).orElse(null));


        invoiceHeader.setAccountDetails(Optional.ofNullable(request.getPayDtls()).map(PayDtlsDTO::getAccountDetails).orElse(null));

        invoiceHeader.setInvRm(Optional.ofNullable(request.getRefDtls()).map(RefDtlsDTO::getInvRm).orElse(null));

        //TODO : HRUSHI Undo this changes
//        invoiceHeader.setInvoiceStartDate(
//                Optional.ofNullable(request.getRefDtls())
//                        .map(RefDtlsDTO::getDocPerdDtls)
//                        .map(v -> {
//                            String invoiceStartDateString = String.valueOf(Optional.ofNullable(v.getInvoiceStartDate()).orElse(null));
//                            return invoiceStartDateString != null ? DateTimeUtils.parseDatetoLocalDate(invoiceStartDateString) : null;
//                        })
//                        .orElse(null)
//        );

        Optional.ofNullable(request.getRefDtls()).ifPresent(refDtls -> {
            // Setting invoiceEndDate
            Optional.ofNullable(refDtls.getDocPerdDtls())
                    .map(DocPerdDtlsDTO::getInvoiceEndDate)
                    .map(DateTimeUtils::parseDatetoLocalDate)
                    .ifPresent(invoiceHeader::setInvoiceEndDate);

            // Working with PrecDocDtls list
            Optional.ofNullable(refDtls.getPrecDocDtls())
                    .filter(list -> !list.isEmpty())
                    .map(list -> list.get(0))
                    .ifPresent(precDoc -> {
                        invoiceHeader.setPrecedingInvoiceNo(precDoc.getPrecedingInvoiceNo());
                        Optional.ofNullable(precDoc.getPrecedingInvoiceDate())
                                .map(DateTimeUtils::parseDatetoLocalDate)
                                .ifPresent(invoiceHeader::setPrecedingInvoiceDate);
                        invoiceHeader.setOtherRef(precDoc.getOtherRef());
                    });

            // Working with ContrDtls list
            Optional.ofNullable(refDtls.getContrDtls())
                    .filter(list -> !list.isEmpty())
                    .map(list -> list.get(0))
                    .ifPresent(contrDtls -> {
                        invoiceHeader.setReceiptAdviceNo(contrDtls.getReceiptAdviceNo());
                        Optional.ofNullable(contrDtls.getReceiptAdviceNo())
                                .map(DateTimeUtils::parseDatetoLocalDate)
                                .ifPresent(invoiceHeader::setDateOfReceiptAdvice);
                        invoiceHeader.setContractRefNo(contrDtls.getContractRefNo());
                        invoiceHeader.setExtRef(contrDtls.getExtRef());
                        invoiceHeader.setProjectRef(contrDtls.getProjectRef());
                        invoiceHeader.setPoRefNo(contrDtls.getPoRefNo());
                        Optional.ofNullable(contrDtls.getPoRefDate())
                                .map(DateTimeUtils::parseDatetoLocalDate)
                                .ifPresent(invoiceHeader::setPoRefDate);
                    });
        });


        invoiceHeader.setShipBNo(Optional.ofNullable(request.getExpDtls()).map(ExpDtlsDTO::getShipBNo).orElse(null));
        invoiceHeader.setShipBDt(Optional.ofNullable(request.getExpDtls()).flatMap(value -> Optional.ofNullable(value.getShipBDt()).map(DateTimeUtils::parseDatetoLocalDate))
                .orElse(null));
        invoiceHeader.setPort(Optional.ofNullable(request.getExpDtls())
                .map(ExpDtlsDTO::getPort)
                .map(portCode -> Ports.builder().id(Integer.valueOf(portCode)).build())
                .orElse(null)); //-- test
        invoiceHeader.setRefClm(Optional.ofNullable(request.getExpDtls()).map(v -> Boolean.valueOf(v.getRefClm())).orElse(null)); // -- verify boolean or string
        invoiceHeader.setForCur(Optional.ofNullable(request.getExpDtls())
                .map(ExpDtlsDTO::getForCur)
                .map(foreignCurrency -> LookupData.builder().type(foreignCurrency).build())
                .orElse(null)); //-- test
        invoiceHeader.setCntCode(Optional.ofNullable(request.getExpDtls())
                .map(ExpDtlsDTO::getCntCode)
                .map(countryCode -> Countries.builder().id(Integer.valueOf(countryCode)).build())
                .orElse(null)); //-- test

//        invoiceHeader.setExpDuty(Optional.ofNullable(request.getExpDtls()).map(ExpDtlsDTO::getExpDuty).map(BigDecimal::new).orElse(null));
        document.setExportDutyAmount(Optional.ofNullable(request.getExpDtls()).map(ExpDtlsDTO::getExpDuty).map(BigDecimal::new).orElse(null)); // Todo Vishal: Check Changes

        invoiceHeader.setTransId(Optional.ofNullable(request.getEwbDtls()).map(EwbDtlsDTO::getTransId).orElse(null));
        invoiceHeader.setTransName(Optional.ofNullable(request.getEwbDtls()).map(EwbDtlsDTO::getTransName).orElse(null));
        invoiceHeader.setTransMode(Optional.ofNullable(request.getEwbDtls()).map(EwbDtlsDTO::getTransMode).orElse(null));
        invoiceHeader.setDistance(Optional.ofNullable(request.getEwbDtls()).map(EwbDtlsDTO::getDistance).orElse(null));
        invoiceHeader.setTransDocNo(Optional.ofNullable(request.getEwbDtls()).map(EwbDtlsDTO::getTransDocNo).orElse(null));
        invoiceHeader.setTransDocDt(Optional.ofNullable(request.getEwbDtls()).flatMap(value -> Optional.ofNullable(value.getTransDocDt()).map(DateTimeUtils::parseDatetoLocalDate))
                .orElse(null));
        invoiceHeader.setVehNo(Optional.ofNullable(request.getEwbDtls()).map(EwbDtlsDTO::getVehNo).orElse(null));
        invoiceHeader.setOdcOrRegular(Optional.ofNullable(request.getEwbDtls()).map(v -> Boolean.valueOf(v.getVehType())).orElse(null));

        invoiceHeader.setAdditionalParameters(new ObjectMapper().writeValueAsString(Optional.ofNullable(request.getAddlDocDtls())));
//                .invoiceStatus(Optional.ofNullable(request.getInvoiceStatusId()).map(value-> LookupData.builder().id(value).build()).orElseGet(null))
        invoiceHeader.setInvoiceItems(LineItemsMapper.lineItemDetailsMapperFromJson(request.getItemList(), invoiceHeader));
        invoiceHeader.setCreatedAt(DateTimeUtils.getCurrentTimestamp());
    }

    public static void prepareInvoiceHeaderFromJson(InvoiceHeader invoiceHeader, InvoiceDTO request, Document document) throws JsonProcessingException, InvalidInputException {
        // Set Document details
        DocDtlsDTO docDtls = request.getDocDtls();
        if (docDtls != null) {
            document.setDocNo(docDtls.getNo());
        }
        invoiceHeader.setDocument(document);

        // Set Shipping details
        ShipDtlsDTO shipDtls = request.getShipDtls();
        if (shipDtls != null) {
            invoiceHeader.setDispatchName(shipDtls.getShipLegalName());
            invoiceHeader.setDispatchAddress1(shipDtls.getShipAddress1());
            invoiceHeader.setDispatchAddress2(shipDtls.getShipAddress2());
            invoiceHeader.setDispatchLocation(shipDtls.getShipLocation());
            invoiceHeader.setDispatchPin(shipDtls.getShipPin() != null ? String.valueOf(shipDtls.getShipPin()) : null);
            invoiceHeader.setShipGstin(shipDtls.getShipGstin());
            invoiceHeader.setShipLegalName(shipDtls.getShipLegalName());
            invoiceHeader.setShipTradename(shipDtls.getShipTradename());
            invoiceHeader.setShipAddress1(shipDtls.getShipAddress1());
            invoiceHeader.setShipAddress2(shipDtls.getShipAddress2());
            invoiceHeader.setShipLocation(shipDtls.getShipLocation());
            invoiceHeader.setShipPin(shipDtls.getShipPin() != null ? String.valueOf(shipDtls.getShipPin()) : null);

            // Handle State Code conversion
            if (shipDtls.getShipStateCode1() != null && !shipDtls.getShipStateCode1().trim().isEmpty()) {
                StateCodes stateCode = StateCodes.builder().id(Integer.valueOf(shipDtls.getShipStateCode1())).build();
                invoiceHeader.setDispatchStateCode(stateCode);
                invoiceHeader.setShipStateCode1(stateCode);
            }
        }
        ValDtlsDTO valDtls = request.getValDtls();
        if (valDtls != null) {
            invoiceHeader.setRoundoffAmount(valDtls.getRoundoffAmount() != null ? BigDecimal.valueOf(valDtls.getRoundoffAmount()) : null);
        }
        // Set Payment details
        PayDtlsDTO payDtls = request.getPayDtls();
        if (payDtls != null) {
            invoiceHeader.setPayeeName(payDtls.getPayeeName());
            invoiceHeader.setModeOfPayment(payDtls.getModeOfPayment());
            invoiceHeader.setPaymentTerm(payDtls.getPaymentTerm());
            invoiceHeader.setPaymentInstruction(payDtls.getPaymentInstruction());
            invoiceHeader.setCreditTransfer(payDtls.getCreditTransfer());
            invoiceHeader.setDirectDebit(payDtls.getDirectDebit());
            invoiceHeader.setCreditDays(payDtls.getCreditDays());

            document.setConsumedAmount(payDtls.getPaidAmount() != null ? BigDecimal.valueOf(payDtls.getPaidAmount()) : null);
            document.setRemainingAmount(payDtls.getPaymentDue() != null ? BigDecimal.valueOf(payDtls.getPaymentDue()) : null);

            invoiceHeader.setAccountDetails(payDtls.getAccountDetails());
        }

        // Set Reference details
        RefDtlsDTO refDtls = request.getRefDtls();
        if (refDtls != null) {
            invoiceHeader.setInvRm(refDtls.getInvRm());

            DocPerdDtlsDTO docPerdDtls = refDtls.getDocPerdDtls();
            if (docPerdDtls != null && docPerdDtls.getInvoiceEndDate() != null) {
                invoiceHeader.setInvoiceEndDate(DateTimeUtils.parsetoLocalDateV2(docPerdDtls.getInvoiceEndDate()));
            }

            List<PrecDocDtlsDTO> precDocDtlsList = refDtls.getPrecDocDtls();
            if (precDocDtlsList != null && !precDocDtlsList.isEmpty()) {
                PrecDocDtlsDTO precDoc = precDocDtlsList.get(0);
                invoiceHeader.setPrecedingInvoiceNo(precDoc.getPrecedingInvoiceNo());
                if (precDoc.getPrecedingInvoiceDate() != null) {
                    invoiceHeader.setPrecedingInvoiceDate(DateTimeUtils.parseDatetoLocalDate(precDoc.getPrecedingInvoiceDate()));
                }
                invoiceHeader.setOtherRef(precDoc.getOtherRef());
            }

            List<ContrDtlsDTO> contrDtlsList = refDtls.getContrDtls();
            if (contrDtlsList != null && !contrDtlsList.isEmpty()) {
                ContrDtlsDTO contrDtls = contrDtlsList.get(0);
                invoiceHeader.setReceiptAdviceNo(contrDtls.getReceiptAdviceNo());
                if (contrDtls.getReceiptAdviceNo() != null) {
                    invoiceHeader.setDateOfReceiptAdvice(DateTimeUtils.parseDatetoLocalDate(contrDtls.getReceiptAdviceNo()));
                }
                invoiceHeader.setContractRefNo(contrDtls.getContractRefNo());
                invoiceHeader.setExtRef(contrDtls.getExtRef());
                invoiceHeader.setProjectRef(contrDtls.getProjectRef());
                invoiceHeader.setPoRefNo(contrDtls.getPoRefNo());
                if (contrDtls.getPoRefDate() != null) {
                    invoiceHeader.setPoRefDate(
                            DateTimeUtils.parseETLDateToLocalDate(contrDtls.getPoRefDate()));
                }
            }
        }

        // Set Export details
        ExpDtlsDTO expDtls = request.getExpDtls();
        if (expDtls != null) {
            invoiceHeader.setShipBNo(expDtls.getShipBNo());
            if (expDtls.getShipBDt() != null) {
                invoiceHeader.setShipBDt(DateTimeUtils.parseETLDateToLocalDate(expDtls.getShipBDt()));
            }
            if (expDtls.getPort() != null) {
                invoiceHeader.setPort(Ports.builder().id(Integer.valueOf(expDtls.getPort())).build());
            }
            if (expDtls.getRefClm() != null) {
                invoiceHeader.setRefClm(Boolean.valueOf(expDtls.getRefClm()));
            }

            document.setExportDutyAmount(expDtls.getExpDuty() != null ? BigDecimal.valueOf(expDtls.getExpDuty()) : null);
        }

        EwbDtlsDTO ewbDtls = request.getEwbDtls();
        if (ewbDtls != null) {
            invoiceHeader.setTransId(ewbDtls.getTransId());
            invoiceHeader.setTransName(ewbDtls.getTransName());
            invoiceHeader.setTransMode(ewbDtls.getTransMode());
            invoiceHeader.setDistance(ewbDtls.getDistance());
            invoiceHeader.setTransDocNo(ewbDtls.getTransDocNo());
            invoiceHeader.setTransDocDt(Optional.of(ewbDtls).flatMap(value -> Optional.ofNullable(value.getTransDocDt()).map(DateTimeUtils::parseDatetoLocalDate))
                    .orElse(null));
            invoiceHeader.setVehNo(ewbDtls.getVehNo());
            invoiceHeader.setOdcOrRegular(Optional.of(ewbDtls).map(v -> Boolean.valueOf(v.getVehType())).orElse(null));
        }
        invoiceHeader.setAdditionalParameters(new ObjectMapper().writeValueAsString(Optional.ofNullable(request.getAddlDocDtls())));
//                .invoiceStatus(Optional.ofNullable(request.getInvoiceStatusId()).map(value-> LookupData.builder().id(value).build()).orElseGet(null))
        boolean hasPrecedingDocument = document.getDocumentSubgroup().getHasPrecedingDocument() != null && document.getDocumentSubgroup().getHasPrecedingDocument();
        if (request.getItemList() == null) {
            throw new InvalidInputException("There are no line items in this JSON object");
        }
        invoiceHeader.setInvoiceItems(LineItemsMapper.lineItemDetailsMapperFromJson(request.getItemList(), invoiceHeader));

        invoiceHeader.setCreatedAt(DateTimeUtils.getCurrentTimestamp());
    }

    public static InvoiceDetailsReq prepareInvoiceDetailsResponseData(InvoiceHeader invoiceHeader, Document document, DocumentApprovalContainer documentApprovalContainer, BudgetSelectionViewModel budgetDetails) {

        return InvoiceDetailsReq.builder()
                .documentDetails(prepareInvoiceDocumentDetailsData(invoiceHeader, document, documentApprovalContainer))
                .budgetDetails(prepareBudgetDetailsData(budgetDetails))
                .buyerDetails(prepareCompanyDetailsDataFromInvoice(invoiceHeader))
                .sellerDetails(prepareCompanyDetailsData(invoiceHeader.getSupplier()))
                .dispatchDetails(prepareDispatchDetailsData(invoiceHeader))
                .shipToDetails(prepareShipToDetailsData(invoiceHeader))
                .amountDetails(prepareAmountDetailsData(invoiceHeader, document))
                .paymentDetails(preparePaymentDetailsData(invoiceHeader, document))
                .referenceDetails(prepareReferenceDetailsData(invoiceHeader))
                .contractReferenceDetails(prepareContractReferenceDetailsData(invoiceHeader))
                .exportDetails(prepareExportDetailsData(invoiceHeader, document))
                .ewayBillDetails(prepareEwayBillDetailsData(invoiceHeader))
                .additionalParameter(null)
                .invoiceReceivedId(Optional.ofNullable(invoiceHeader.getInvoiceReceived()).map(InvoiceReceived::getInvoiceReceivedId).orElse(null))
                .invoiceHeaderId(invoiceHeader.getInvoiceHeaderId())
//                .itemList(LineItemsMapper.prepareInvoiceLineItemsData(invoiceHeader.getInvoiceItems()))
                .invoiceStatusId(Optional.ofNullable(invoiceHeader.getInvoiceStatus()).map(LookupData::getId).orElse(null))

                .build();
    }

    public static InvoiceDocumentDetails prepareInvoiceDocumentDetailsData(InvoiceHeader invoiceHeader, Document document, DocumentApprovalContainer documentApprovalContainer) {
        return InvoiceDocumentDetails.builder()
                .docTypeId(invoiceHeader.getDocTypeId())
                .docDate(Optional.ofNullable(document.getDocumentDate())
                        .map(DateTimeUtils::formatDateJPQL).orElse(null))
                .invoiceDocumentType(Optional.ofNullable(invoiceHeader.getInvoiceDocumentType()).map(LookupData::getId).orElse(null))
                .invoiceType(Optional.ofNullable(invoiceHeader.getInvoiceType()).map(LookupData::getId).orElse(null))
                .invoiceSubType(Optional.ofNullable(invoiceHeader.getInvoiceSubType()).map(LookupData::getId).orElse(null))
                .itcEligibility(invoiceHeader.getItcType())
                .regRev(invoiceHeader.getRegRev())
                .isOnBehalf(documentApprovalContainer.getIsOnBehalf())
                .docNo(document.getDocNo())
                .documentIdentifier(documentApprovalContainer.getDocumentIdentifier())
                .requesterName(Optional.ofNullable(invoiceHeader.getCreatingUser()).map((value ->
                                invoiceHeader.getCreatingUser().getFirstName() + " " + invoiceHeader.getCreatingUser().getLastName()))
                        .orElse(null))
                //invoiceHeader.getCreatingUser().getFirstName() + " " + invoiceHeader.getCreatingUser().getLastName())

                .invoiceStartDate(Optional.ofNullable(invoiceHeader.getInvoiceStartDate()).flatMap(value -> Optional.of(value).map(DateTimeUtils::formatDateJPQL)).orElse(null))
                .invoiceEndDate(Optional.ofNullable(invoiceHeader.getInvoiceEndDate()).flatMap(value -> Optional.of(value).map(DateTimeUtils::formatDateJPQL)).orElse(null))
                .isPrecedingDoc(invoiceHeader.getIsPrecedingDoc())
                .precedingInvoiceNo(invoiceHeader.getPrecedingInvoiceNo())
                .precedingInvoiceDate(Optional.ofNullable(invoiceHeader.getPrecedingInvoiceDate()).flatMap(value -> Optional.of(value).map(DateTimeUtils::formatDateJPQL)).orElse(null))
                .otherRef(invoiceHeader.getOtherRef())
                .build();

    }

    public static BudgetDetails prepareBudgetDetailsData(BudgetSelectionViewModel budgetDetails) {
        if (budgetDetails == null) return null;
        return BudgetDetails.builder()
                .id(budgetDetails.getId())
                .code(budgetDetails.getBudgetCode() == null ? StringConstants.EMPTY : budgetDetails.getBudgetCode())
                .description(budgetDetails.getDescription() == null ? StringConstants.EMPTY : budgetDetails.getDescription())
                .build();
    }

    public static CompanyDetails prepareCompanyDetailsData(Company company) {
        if (company != null) {

            StateCodes stateCode = company.getStateCode();
            Integer stateCodeId = stateCode != null ? stateCode.getId() : null;
            String stateCodeValue = stateCode != null ? stateCode.getCode() : null;

            return CompanyDetails.builder()
                    .id(company.getCompanyId())
                    .gstin(company.getGst())
                    .legalName(company.getLegalName())
                    .tradeName(null) // ToDo: Currently null until we confirm business logic
                    .address1(company.getAddr1())
                    .address2(company.getAddr2())
                    .location(company.getLoc())
                    .pin(company.getPin())
                    .stateCodeId(stateCodeId)
                    .stateCode(stateCodeValue)
                    .city(company.getLoc()) // City not present for the company
                    .phoneNumber(company.getPhoneNo())
                    .email(company.getEmailId())
                    .vendorCode(company.getVendorCode())
                    .supplierCompanyName(company.getSupplierCompanyName())
                    .build();
        } else {
            return null;
        }
    }

    // TODO: temp code for demo - remove
    public static CompanyDetails prepareCompanyDetailsDataFromPO(PurchaseOrderHeader po) {
        if (Optional.ofNullable(po).orElse(null) != null) {
            StateCodes stateCode = po.getStateCode();
            Integer stateCodeId = stateCode != null ? stateCode.getId() : null;
            String stateCodeValue = stateCode != null ? stateCode.getCode() : null;

            return CompanyDetails.builder()
                    .organisationType("Company")
                    .buyerContactPerson(po.getBuyerContactPerson())
                    .buyerBranch(po.getBranch())
                    .gstin(po.getGstin())
                    .legalName(po.getPurchasingOrganization())
                    .tradeName(null) // ToDo: Currently null until we confirm business logic
                    .address1(po.getAddress1())
                    .address2(po.getAddress2())
                    .deliveryAddress(po.getAddress2())
                    .location(po.getLoc())
                    .city(po.getLoc()) // City not present on the PO
                    .pincode(po.getPin())
                    .district(po.getDistrict())
                    .streetName(po.getStreetName())
                    .stateCodeId(stateCodeId)
                    .stateCode(stateCodeValue)
                    .phoneNumber(po.getPhoneNo())
                    .email(po.getEmail())
                    .build();
        } else {
            return null;
        }
    }

    // TODO: temp code for demo - remove
    public static CompanyDetails prepareCompanyDetailsDataFromInvoice(InvoiceHeader invoiceHeader) {
        if (Optional.ofNullable(invoiceHeader).orElse(null) != null) {
            StateCodes state = invoiceHeader.getStateCode();
            return CompanyDetails.builder()
                    .organisationType("Company")
                    .buyerContactPerson(invoiceHeader.getBuyerContactPerson())
                    .gstin(invoiceHeader.getGstin())
                    .legalName(invoiceHeader.getBuyerLegalName())
                    .tradeName(invoiceHeader.getBuyerTradeName())
                    .address1(invoiceHeader.getAddress1())
                    .address2(invoiceHeader.getAddress2())
                    .deliveryAddress(invoiceHeader.getAddress2())
                    .location(invoiceHeader.getLoc())
                    .pincode(invoiceHeader.getPin())
                    .district(invoiceHeader.getDistrict())
                    .streetName(invoiceHeader.getStreetName())
                    .stateCodeId(state != null ? state.getId() : null)
                    .stateCode(state != null ? state.getCode() : null)
                    .phoneNumber(invoiceHeader.getPhoneNo())
                    .email(invoiceHeader.getEmail())
                    .build();
        } else {
            return null;
        }
    }

    public static DispatchDetails prepareDispatchDetailsData(InvoiceHeader invoiceHeader) {
        return DispatchDetails.builder()
                .name(invoiceHeader.getDispatchName())
                .address1(invoiceHeader.getDispatchAddress1())
                .address2(invoiceHeader.getDispatchAddress2())
                .location(invoiceHeader.getDispatchLocation())
                .pin(invoiceHeader.getDispatchPin())
                .stateCodeId(Optional.ofNullable(invoiceHeader.getDispatchStateCode())
                        .map(value -> invoiceHeader.getDispatchStateCode().getId()).orElse(null))
                .phoneNumber(invoiceHeader.getDispatchPhoneNumber())
                .email(invoiceHeader.getDispatchEmail())
                .isDispatchAddressSame(invoiceHeader.getIsDispatchAddressSame())
                .build();
    }

    public static ShipToDetails prepareShipToDetailsData(InvoiceHeader invoiceHeader) {
        return ShipToDetails.builder()
                .gstin(invoiceHeader.getShipGstin())
                .address1(invoiceHeader.getShipAddress1())
                .address2(invoiceHeader.getShipAddress2())
                .legalName(invoiceHeader.getShipLegalName())
                .tradeName(invoiceHeader.getShipTradename())
                .location(invoiceHeader.getShipLocation())
                .pin(invoiceHeader.getShipPin())
                .stateCodeId(Optional.ofNullable(invoiceHeader.getShipStateCode1())
                        .map(value -> invoiceHeader.getShipStateCode1().getId()).orElse(null))
                .phoneNumber(invoiceHeader.getShipPhoneNumber())
                .email(invoiceHeader.getShipEmail())
                .isDeliveryAddressSame(invoiceHeader.getIsDeliveryAddressSame())
                .build();
    }

    public static ShipToDetails prepareShipToDetailsDataForPO(PurchaseOrderHeader purchaseOrderHeader) {
        return ShipToDetails.builder()
                .gstin(purchaseOrderHeader.getShipGstin())
                .address1(purchaseOrderHeader.getShipAddress1())
                .address2(purchaseOrderHeader.getShipAddress2())
                .legalName(purchaseOrderHeader.getShipLegalName())
                .tradeName(purchaseOrderHeader.getShipTradename())
                .location(purchaseOrderHeader.getShipLocation())
                .pin(purchaseOrderHeader.getShipPin())
                .stateCodeId(Optional.ofNullable(purchaseOrderHeader.getShipStateCode1())
                        .map(value -> purchaseOrderHeader.getShipStateCode1().getId()).orElse(null))
                .phoneNumber(purchaseOrderHeader.getShipPhoneNumber())
                .email(purchaseOrderHeader.getShipEmail())
                .isDeliveryAddressSame(purchaseOrderHeader.getIsDeliveryAddressSame())
                .build();
    }

    public static AmountDetails prepareAmountDetailsData(InvoiceHeader invoiceHeader, Document document) {
        return AmountDetails.builder()
                .totalAssValue(document.getTaxableAmount())
                .totalCgstValue(document.getCgstAmount())
                .totalSgstValue(document.getSgstAmount())
                .totalIgstValue(document.getIgstAmount())
                // Todo Vishal: Move below values to Document Discount amounts : Start
//                .totalCessValue(invoiceHeader.getTotalCessValue())
//                .totalStateCessValue(invoiceHeader.getTotalStateCessValue())
//                .invoiceDiscount(invoiceHeader.getInvoiceDiscount())
//                .invoiceOtherCharges(invoiceHeader.getInvoiceOtherCharges())
                .roundoffAmount(invoiceHeader.getRoundoffAmount())
                // Todo Vishal: Move below values to Document Discount amounts : End
                .totalInvoiceValue(document.getClaimAmount())
                .finalInvoiceValue(document.getClaimAmountInNativeCurrency())
                .build();
    }

    public static PaymentDetails preparePaymentDetailsData(InvoiceHeader invoiceHeader, Document document) {
        return PaymentDetails.builder()
                .payeeName(invoiceHeader.getPayeeName())
                .modeOfPayment(invoiceHeader.getModeOfPayment())
                .ifscCode(invoiceHeader.getFinInsBr())
                .paymentTerm(invoiceHeader.getPaymentTerm())
                .paymentInstruction(invoiceHeader.getPaymentInstruction())
                .creditTransfer(invoiceHeader.getCreditTransfer())
                .directDebit(invoiceHeader.getDirectDebit())
                .creditDays(invoiceHeader.getCreditDays())
//                .paidAmount(invoiceHeader.getPaidAmount())
//                .paymentDue(invoiceHeader.getPaymentDue())
                .paidAmount(document.getConsumedAmount()) // Todo Vishal: Check changes
                .paymentDue(document.getRemainingAmount())
                .accountDetails(invoiceHeader.getAccountDetails())
                .build();
    }

    public static ReferenceDetails prepareReferenceDetailsData(InvoiceHeader invoiceHeader) {
        return ReferenceDetails.builder()
                .invoiceRemarks(invoiceHeader.getInvRm())
                .build();
    }


    public static ContractReferenceDetails prepareContractReferenceDetailsData(InvoiceHeader invoiceHeader) {
        return ContractReferenceDetails.builder()
                .receiptAdviceNo(invoiceHeader.getReceiptAdviceNo())
                .dateOfReceiptAdvice(Optional.ofNullable(invoiceHeader.getDateOfReceiptAdvice()).flatMap(value -> Optional.of(value).map(DateTimeUtils::formatDateJPQL)).orElse(null))
                .lotRefNo(invoiceHeader.getTendRef())
                .contractRefNo(invoiceHeader.getContractRefNo())
                .anyOtherRef(invoiceHeader.getExtRef())
                .projectRef(invoiceHeader.getProjectRef())
                .poRefNo(invoiceHeader.getPoRefNo())
                .poRefDate(Optional.ofNullable(invoiceHeader.getPoRefDate()).flatMap(value -> Optional.of(value).map(DateTimeUtils::formatDateJPQL)).orElse(null))
                .build();
    }

    public static ExportDetails prepareExportDetailsData(InvoiceHeader invoiceHeader, Document document) {
        return ExportDetails.builder()
                .shippingBillNo(invoiceHeader.getShipBNo())
                .shippingBillDate(Optional.ofNullable(invoiceHeader.getShipBDt()).flatMap(value -> Optional.of(value).map(DateTimeUtils::formatDateJPQL)).orElse(null))
                .portCode(Optional.ofNullable(invoiceHeader.getPort()).map(value -> invoiceHeader.getPort().getId()).orElse(null))
                .refundClaim(invoiceHeader.getRefClm())
                .foreignCurrency(Optional.ofNullable(invoiceHeader.getForCur()).map(value -> invoiceHeader.getForCur().getId()).orElse(null))
                .countryCode(Optional.ofNullable(invoiceHeader.getCntCode()).map(value -> invoiceHeader.getCntCode().getId()).orElse(null))
//                .exportDuty(invoiceHeader.getExpDuty())
                .exportDuty(document.getExportDutyAmount()) // Todo Vishal: Check changes
                .build();
    }

    public static EwayBillDetails prepareEwayBillDetailsData(InvoiceHeader invoiceHeader) {
        return EwayBillDetails.builder()
                .transporter(invoiceHeader.getTransId())
                .transporterName(invoiceHeader.getTransName())
                .modeOfTransportation(invoiceHeader.getTransMode())
                .distOfTransportation(invoiceHeader.getDistance())
                .transporterDocNo(invoiceHeader.getTransDocNo())
                .transporterDocDate(Optional.ofNullable(invoiceHeader.getTransDocDt()).flatMap(value -> Optional.of(value).map(DateTimeUtils::formatDateJPQL)).orElse(null))
                .vehicleNo(invoiceHeader.getVehNo())
                .odcOrRegular(invoiceHeader.getOdcOrRegular())
                .build();
    }

    public static InvoiceReceived prepareInvoiceReceived(InvoiceDetailsReq invoiceDetailsReq,
                                                         long companyId,
                                                         InvoiceHeader invoiceHeader,
                                                         String filename,
                                                         String irn) {
        return InvoiceReceived.builder()
                .companyId(companyId)
                .invoiceHeader(invoiceHeader)
                .purchaseOrderId(Optional.ofNullable(invoiceDetailsReq.getDocumentDetails()).map(InvoiceDocumentDetails::getPoId).orElse(null)).documentUrl(null)
                .isPoInvoice(Optional.ofNullable(invoiceDetailsReq.getDocumentDetails()).map(InvoiceDocumentDetails::getIsPoBased).orElse(null)).documentType(StaticDataRegistry.LOOKUP_VALUE_INVOICE) // doctype
                .documentUrl(filename)
                .createdAt(DateTimeUtils.getCurrentTimestamp())
                .irn(irn).build();
        //setJsonPdfMatchedStatus
        //setInvoiceSource
    }

    public static InvoiceReceived prepareInvoiceReceivedFromJson(InvoiceDTO dto,
                                                                 long companyId,
                                                                 InvoiceHeader invoiceHeader) {
        boolean isPoInvoice = Optional.ofNullable(dto.getRefDtls()).map(RefDtlsDTO::getContrDtls)
                .flatMap(list -> list.isEmpty() ? Optional.empty() : Optional.of(list.get(0)))
                .map(ContrDtlsDTO::getPoRefNo).isPresent();
        return InvoiceReceived.builder()
                .companyId(companyId)
                .invoiceHeader(invoiceHeader)
                .isPoInvoice(isPoInvoice)
                .documentType(StaticDataRegistry.LOOKUP_VALUE_INVOICE) // doctype
                .irn(dto.getIrn())
                .createdAt(DateTimeUtils.getCurrentTimestamp()).build();
        //setJsonPdfMatchedStatus
        //setInvoiceSource
    }

    public static InvoiceReceived prepareInvoiceReceivedFromJson(InvoiceReceived invoiceReceived, InvoiceDTO dto,
                                                                 long companyId,
                                                                 InvoiceHeader invoiceHeader) {
        boolean isPoInvoice = Optional.ofNullable(dto.getRefDtls()).map(RefDtlsDTO::getContrDtls)
                .flatMap(list -> list.isEmpty() ? Optional.empty() : Optional.of(list.get(0)))
                .map(ContrDtlsDTO::getPoRefNo).isPresent();

        invoiceReceived.setInvoiceHeader(invoiceHeader);
        invoiceReceived.setIsPoInvoice(isPoInvoice);
        invoiceReceived.setIrn(dto.getIrn());

        return invoiceReceived;
    }


    // prepare invoice json from the database
    public static InvoiceDTO mapToInvoiceDTO(InvoiceHeader invoiceHeader) {
        InvoiceDTO invoiceDTO = new InvoiceDTO();

        // Set Document Details (DocDtlsDTO)
        DocDtlsDTO docDtlsDTO = new DocDtlsDTO();
        Document document = invoiceHeader.getDocument();
        DocumentApprovalContainer dac = document.getDocumentApprovalContainer();
        docDtlsDTO.setNo(document.getDocNo());
        docDtlsDTO.setDt(DateTimeUtils.formatDate(document.getDocumentDate()));
        docDtlsDTO.setTyp(Optional.ofNullable(invoiceHeader.getInvoiceType()).map(LookupData::getAttribute).orElse(null));
        invoiceDTO.setDocDtls(docDtlsDTO); // tranDtlsDTO.setSupTyp(Optional.ofNullable(invoiceHeader.getInvoiceType()).map(LookupData::getAttribute).orElse(null));

        // Set Value Details (ValDtlsDTO)
        ValDtlsDTO valDtlsDTO = new ValDtlsDTO();
        valDtlsDTO.setTotalAssValue(parseDoubleValue(document.getTaxableAmount()));
        valDtlsDTO.setTotalCgstValue(parseDoubleValue(document.getCgstAmount()));
        valDtlsDTO.setTotalIgstValue(parseDoubleValue(document.getIgstAmount()));
        valDtlsDTO.setTotalSgstValue(parseDoubleValue(document.getSgstAmount()));
        valDtlsDTO.setTotalInvoiceValue(parseDoubleValue(document.getClaimAmount()));
        valDtlsDTO.setExchangeRate(invoiceHeader.getExchangeRate());
        valDtlsDTO.setCurrency(invoiceHeader.getCurrency());
        valDtlsDTO.setTdsValue(invoiceHeader.getTdsValue());
        valDtlsDTO.setRoundoffAmount(parseDoubleValue(invoiceHeader.getRoundoffAmount()));
        invoiceDTO.setValDtls(valDtlsDTO);

        // Set Ship Details (ShipDtlsDTO)
        ShipDtlsDTO shipDtlsDTO = new ShipDtlsDTO();
        shipDtlsDTO.setShipLegalName(invoiceHeader.getDispatchName());
        shipDtlsDTO.setShipAddress1(invoiceHeader.getDispatchAddress1());
        shipDtlsDTO.setShipAddress2(invoiceHeader.getDispatchAddress2());
        shipDtlsDTO.setShipLocation(invoiceHeader.getDispatchLocation());
        shipDtlsDTO.setShipPin(parseInteger(invoiceHeader.getDispatchPin()));
        shipDtlsDTO.setShipStateCode1(invoiceHeader.getDispatchStateCode() != null
                ? String.valueOf(invoiceHeader.getDispatchStateCode().getId())
                : null);
        invoiceDTO.setShipDtls(shipDtlsDTO);

        //DispDtls
        DispDtls dispDtls = new DispDtls();
        dispDtls.setName(invoiceHeader.getDispatchName());
        dispDtls.setAddress1(invoiceHeader.getDispatchAddress1());
        dispDtls.setAddress2(invoiceHeader.getDispatchAddress2());
        dispDtls.setLocation(invoiceHeader.getDispatchLocation());
        dispDtls.setPin(invoiceHeader.getDispatchPin());
        dispDtls.setStateCode(invoiceHeader.getDispatchStateCode() != null
                ? String.valueOf(invoiceHeader.getDispatchStateCode().getId())
                : null);
        invoiceDTO.setDispatchDetails(dispDtls);

        // Set Payment Details (PayDtlsDTO)
        PayDtlsDTO payDtlsDTO = new PayDtlsDTO();
        payDtlsDTO.setPayeeName(invoiceHeader.getPayeeName());
        payDtlsDTO.setModeOfPayment(invoiceHeader.getModeOfPayment());
        payDtlsDTO.setPaymentTerm(invoiceHeader.getPaymentTerm());
        payDtlsDTO.setPaymentInstruction(invoiceHeader.getPaymentInstruction());
        payDtlsDTO.setCreditTransfer(invoiceHeader.getCreditTransfer());
        payDtlsDTO.setDirectDebit(invoiceHeader.getDirectDebit());
        payDtlsDTO.setCreditDays(invoiceHeader.getCreditDays());
        payDtlsDTO.setPaidAmount(parseDoubleValue(document.getConsumedAmount()));
        payDtlsDTO.setPaymentDue(parseDoubleValue(document.getRemainingAmount()));
        payDtlsDTO.setAccountDetails(invoiceHeader.getAccountDetails());
        invoiceDTO.setPayDtls(payDtlsDTO);

        // Set Reference Details (RefDtlsDTO)
        RefDtlsDTO refDtlsDTO = new RefDtlsDTO();
        refDtlsDTO.setInvRm(invoiceHeader.getInvRm());

        PrecDocDtlsDTO precDocDtlsDTO = new PrecDocDtlsDTO();
        precDocDtlsDTO.setPrecedingInvoiceNo(invoiceHeader.getPrecedingInvoiceNo());
        precDocDtlsDTO.setPrecedingInvoiceDate(invoiceHeader.getPrecedingInvoiceDate() != null
                ? DateTimeUtils.formatDate(invoiceHeader.getPrecedingInvoiceDate())
                : null);
        precDocDtlsDTO.setOtherRef(invoiceHeader.getOtherRef());
        refDtlsDTO.setPrecDocDtls(List.of(precDocDtlsDTO));

        // Contract Details
        ContrDtlsDTO contrDtlsDTO = new ContrDtlsDTO();
        contrDtlsDTO.setReceiptAdviceNo(invoiceHeader.getReceiptAdviceNo());
        contrDtlsDTO.setDateOfReceiptAdvice(invoiceHeader.getDateOfReceiptAdvice() != null
                ? invoiceHeader.getDateOfReceiptAdvice().toString()
                : null);
        contrDtlsDTO.setContractRefNo(invoiceHeader.getContractRefNo());
        contrDtlsDTO.setExtRef(invoiceHeader.getExtRef());
        contrDtlsDTO.setProjectRef(invoiceHeader.getProjectRef());
        contrDtlsDTO.setPoRefNo(invoiceHeader.getPoRefNo());
        refDtlsDTO.setContrDtls(List.of(contrDtlsDTO));
        invoiceDTO.setRefDtls(refDtlsDTO);

        // Handle invoice period details if required
        if (invoiceHeader.getInvoiceEndDate() != null) {
            DocPerdDtlsDTO docPerdDtlsDTO = new DocPerdDtlsDTO();
            docPerdDtlsDTO.setInvoiceEndDate(DateTimeUtils.formatDate(invoiceHeader.getInvoiceEndDate()));
            refDtlsDTO.setDocPerdDtls(docPerdDtlsDTO);
        }
        // TranDtlsDTO
        TranDtlsDTO tranDtlsDTO = new TranDtlsDTO();
        tranDtlsDTO.setSupTyp(Optional.ofNullable(invoiceHeader.getInvoiceSubType()).map(LookupData::getAttribute).orElse(null));
        tranDtlsDTO.setRegRev(invoiceHeader.getRegRev() != null && invoiceHeader.getRegRev());
        invoiceDTO.setTranDtls(tranDtlsDTO);

        // Buyer details
        invoiceDTO.setBuyerDtls(prepareBuyerDetailsDataFromInvoiceForSync(invoiceHeader));
        // Seller details
        invoiceDTO.setSellerDtls(prepareSellerDetailsDataFromInvoiceForSync(invoiceHeader));
        // CustomDetails
        CustomDetails customDetails = new CustomDetails();
        customDetails.setItcType(invoiceHeader.getItcType());

        // Get vendor directly from invoice.
        String vendorCode = Optional.ofNullable(invoiceHeader.getSupplier()).map(Company::getVendorCode).orElse(null);
        customDetails.setDocumentApprovalCode(dac.getDocumentApprovalCode() == null ? null : dac.getDocumentApprovalCode().toString());
        customDetails.setDocumentIdentifier(dac.getDocumentIdentifier()); // AVOID NULL: Request of Saurabh G. (Bridge Engineer)
        customDetails.setVendorCode(vendorCode);
//      customDetails.setGlCode(""); // TODO: GlCode is on item level - ?
        // customDetails.setGrnList("") // :TODO: grn list dev is pending

        if (invoiceHeader.getHasPrecedingDocument()) {

            Set<PurchaseOrderHeader> poHeaders = new HashSet<>();

            for (InvoiceItem invoiceItem : invoiceHeader.getInvoiceItems()) {
                // Updated to use many-to-many relationship
                if (invoiceItem.getPurchaseOrderItems() != null && !invoiceItem.getPurchaseOrderItems().isEmpty()) {
                    for (PurchaseOrderItem poItem : invoiceItem.getPurchaseOrderItems()) {
                        if (poItem != null) {
                            PurchaseOrderHeader poHeader = poItem.getPurchaseOrderHeader();
                            if (poHeader != null) {
                                poHeaders.add(poHeader);
                            }
                        }
                    }
                }
            }

            List<PODetails> poDetailsList = poHeaders.stream().map(poHeader -> {
                PODetails poDetails = new PODetails();

                poDetails.setPoNo(poHeader.getDocument().getDocNo());
                poDetails.setPoDate(DateTimeUtils.formatDateJPQL(poHeader.getPoDate()));
                poDetails.setPodoc(poHeader.getPurchasingDocTypeFromErp());
                return poDetails;
            }).toList();

            customDetails.setPoList(poDetailsList);
        }

        invoiceDTO.setCustomDetails(customDetails);

        // set items
        if (invoiceHeader.getInvoiceItems() != null)
            invoiceDTO.setItemList(lineItemDetailsMapperToJson(invoiceHeader.getInvoiceItems()));

        return invoiceDTO;
    }

    // used in erp-sync
    public static CompanyDtlsDTO prepareBuyerDetailsDataFromInvoiceForSync(InvoiceHeader invoiceHeader) {
        if (invoiceHeader != null) {
            // TODO : change setting details from invoice to Buyer (Company) once buyer details is impmented in invoice
            return CompanyDtlsDTO.builder()
                    .gst(invoiceHeader.getGstin())
                    .legalName(invoiceHeader.getBuyerLegalName())
                    .name(invoiceHeader.getBuyerTradeName())
                    .addr1(invoiceHeader.getAddress1())
                    .addr2(invoiceHeader.getAddress2())
                    .loc(invoiceHeader.getLoc())
                    .pin(invoiceHeader.getPin())
                    .stcd(invoiceHeader.getStateCode().getCode())
                    .phoneNo(invoiceHeader.getPhoneNo())
                    .Pos(Optional.ofNullable(invoiceHeader.getDispatchStateCode()).map(StateCodes::getCode).orElse(null)) // Assuming Pos corresponds to the state code
                    .emailId(invoiceHeader.getEmail())
                    .build();
        } else {
            return null;
        }
    }

    public static CompanyDtlsDTO prepareSellerDetailsDataFromInvoiceForSync(InvoiceHeader invoiceHeader) {
        if (invoiceHeader.getSupplier() != null) {
            Company seller = invoiceHeader.getSupplier();
            return CompanyDtlsDTO.builder()
                    .gst(seller.getGst())
                    .legalName(seller.getLegalName())
                    .name(seller.getSupplierCompanyName())
                    .addr1(seller.getAddr1())
                    .addr2(seller.getAddr2())
                    .loc(seller.getLoc())
                    .pin(seller.getPin())
                    .stcd(Optional.ofNullable(seller.getStateCode()).map(StateCodes::getCode).orElse(null))
                    .phoneNo(seller.getPhoneNo())
                    .Pos(Optional.ofNullable(seller.getStateCode()).map(StateCodes::getCode).orElse(null)) // Assuming Pos corresponds to the state code
                    .emailId(seller.getEmailId())
                    .build();
        } else {
            return null;
        }
    }

    public static List<ItemDTO> lineItemDetailsMapperToJson(List<InvoiceItem> invoiceItems) {

        AtomicInteger index = new AtomicInteger(1);
        return invoiceItems.stream()
                .sorted(Comparator.comparing(InvoiceItem::getId)) // Sort by 'id'
                .map(invoiceItem -> {
                    int currentIndex = index.getAndIncrement();

                    List<ERPGrnViewModel> grnList = new ArrayList<>();
                    // Handle many-to-many ERP GRN relationship
                    if (invoiceItem.getErpGrns() != null && !invoiceItem.getErpGrns().isEmpty()) {
                        for (ERPGrn erpGrn : invoiceItem.getErpGrns()) {
                            ERPGrnViewModel grnViewModel = POGrnMapping.getViewModel(erpGrn);
                            if (grnViewModel != null) {
                                grnList.add(grnViewModel);
                            }
                        }
                    }

                    return ItemDTO.builder()
                            .slNo(currentIndex)
                            .serialNo(currentIndex)
                            .productDescription(invoiceItem.getDescription())
                            .isService(invoiceItem.getIsService())
                            .hsnCode(invoiceItem.getHsn())
                            .glCode(Optional.ofNullable(invoiceItem.getGlMaster()).map(GlMaster::getGl).orElse(null))
                            .quantity(invoiceItem.getQuantity() != null ? invoiceItem.getQuantity() : 0)
                            .unitPrice(Optional.ofNullable(invoiceItem.getUnitRate()).map(BigDecimal::doubleValue).orElse(0.0))
                            .totalAmount(Optional.ofNullable(invoiceItem.getTotal()).map(BigDecimal::doubleValue).orElse(0.0))
                            .discount(Optional.ofNullable(invoiceItem.getDiscount()).map(BigDecimal::doubleValue).orElse(0.0))
                            .assessableAmount(Optional.ofNullable(invoiceItem.getAssessableAmount()).map(BigDecimal::doubleValue).orElse(0.0))
                            .gstRate(invoiceItem.getTaxPercentage())
                            .igstAmount(Optional.ofNullable(invoiceItem.getIgst()).map(BigDecimal::doubleValue).orElse(0.0))
                            .cgstAmount(Optional.ofNullable(invoiceItem.getCgst()).map(BigDecimal::doubleValue).orElse(0.0))
                            .sgstAmount(Optional.ofNullable(invoiceItem.getSgst()).map(BigDecimal::doubleValue).orElse(0.0))
                            .cessRate(invoiceItem.getCessPercentage())
                            .cessAmount(Optional.ofNullable(invoiceItem.getCessAmt()).map(BigDecimal::doubleValue).orElse(0.0))
                            .cessNonAdvolAmount(Optional.ofNullable(invoiceItem.getCessNonAdvolAmount()).map(BigDecimal::doubleValue).orElse(0.0))
                            .stateCessRate(invoiceItem.getStateCessPercentage())
                            .stateCessAmount(Optional.ofNullable(invoiceItem.getStateCessAmt()).map(BigDecimal::doubleValue).orElse(0.0))
                            .stateCessNonAdvolAmount(Optional.ofNullable(invoiceItem.getStateCessNonAdvolAmount()).map(BigDecimal::doubleValue).orElse(0.0))
                            .otherCharges(Optional.ofNullable(invoiceItem.getOtherCharges()).map(BigDecimal::doubleValue).orElse(0.0))
                            .totalItemValue(Optional.ofNullable(invoiceItem.getTotalWithTax()).map(BigDecimal::doubleValue).orElse(0.0))
                            .grnList(grnList)
                            .poList(invoiceItem.getPoListForJson())
                            .build();
                })
                .collect(Collectors.toList());
    }


    private static Double parseDoubleValue(BigDecimal value) {
        return value != null ? value.doubleValue() : null;
    }

    private static Integer parseInteger(String value) {
        try {
            return value != null ? Integer.parseInt(value) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }


}
