package in.taxgenie.pay_expense_pvv.repositories;

import java.util.List;

public interface QueueV2Repository {

	public List<Object[]> getAdminQueueData(String queryString, long companyCode, int pageNumber, int numberOfRecords, String sortBy,
			String sort);

	public Integer getTotalElements(String queryString, long companyCode);
	public Integer getTotalElementsForUser(String queryString, long companyCode, long userId);

	public String whereQuery(long companyCode);

	public String fromQueryForAdmin();

	public String selectCountQueryForAdmin(long companyCode);

	public String selectAllQueryForAdmin(long companyCode);

	public String selectCountQueryForChecker(long companyCode, int checker, int expenseActionStatus, String string);

	public String selectAllQueryForChecker(long companyCode, int checker, int expenseActionStatus, String userEmail);

	List<Object[]> getCheckerQueueData(String queryString, List<Integer> reportStatus, int pageNumber,
			int numberOfRecords, String sortBy, String sort);

	Integer getCheckerTotalElements(String query, List<Integer> reportStatus);

	List<Object[]> getUserQueueData(String queryString, int pageNumber, int numberOfRecords, String sortBy, String sort);

	String selectAllQueryForUser(long companyCode, long userId);

	String selectCountQueryForUser(long companyCode, long userId);

}
