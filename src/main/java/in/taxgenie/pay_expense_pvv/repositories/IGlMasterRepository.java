package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.gl_master.GlMaster;
import in.taxgenie.pay_expense_pvv.viewmodels.gl_master.GlMasterSummary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface IGlMasterRepository extends JpaRepository<GlMaster, Long> {
    Optional<GlMaster> findByGlAndCompanyCode(String gl, long companyCode);

    Optional<GlMaster> findByIdAndCompanyCode(Long id, long companyCode);

    List<GlMasterSummary> findSummariesByGlNameStartingWithIgnoreCaseAndCompanyCodeOrderByGlNameAsc(String key, long companyCode);

    List<GlMasterSummary> findByGlIgnoreCaseContainingOrGlNameIgnoreCaseContainingAndCompanyCodeOrderByGlNameAsc(
            String gl, String glName, Long companyCode);

    List<GlMasterSummary> findSummariesByGlStartingWithIgnoreCaseAndGlNameStartingWithIgnoreCaseAndCompanyCodeOrderByGlNameAsc(
            String gl, String glName, Long companyCode);


    List<GlMaster> findByGlNameStartingWithIgnoreCaseAndCompanyCodeOrderByGlNameAsc(String key, long companyCode);

    List<GlMasterSummary> findTop30ByCompanyCodeOrderByCreatedTimestampDesc(long companyCode);

}