package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.Company;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel;
import in.taxgenie.pay_expense_pvv.viewmodels.company.SellerTemplateViewModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface ICompanyRepository extends JpaRepository<Company, Integer> {
    Optional<Company> findByCamCompanyIdAndCompanyId(long companyId, Integer vendorId);
    @Query("select distinct new in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel(co.companyId, co.gst) " +
            "from Company as co " +
            "where co.camCompanyId = ?1 and co.gst != ?2 and co.gstinStatus = 'Active'")
    List<LookupDataModel> getVendorGstinDetailsByCamCompanyId(long camCompanyId, String notApplicableKeyWord);

    @Query("select distinct new in.taxgenie.pay_expense_pvv.viewmodels.company.SellerTemplateViewModel(co.companyId, co.gst, null, concat(co.vendorCode, ' - ', co.supplierCompanyName )) " +
            "from Company as co " +
            "where co.camCompanyId = ?1 and co.gst != ?2 and co.gstinStatus = 'Active'")
    List<SellerTemplateViewModel> getFullVendorGstinDetailsByCamCompanyId(long camCompanyId, String notApplicableKeyWord);

    @Query("select distinct new in.taxgenie.pay_expense_pvv.viewmodels.company.SellerTemplateViewModel(co.companyId, co.gst, null, concat(co.vendorCode, ' - ', co.supplierCompanyName )) " +
            "from Company as co " +
            "where co.camCompanyId = ?1 and co.gstinStatus = 'Active'")
    List<SellerTemplateViewModel> getFullVendorGstinDetailsByCamCompanyIdWithNonApplicable(long camCompanyId);

    @Query("SELECT DISTINCT new in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel(co.companyId, co.supplierCompanyName) " +
            "FROM Company AS co " +
            "WHERE co.camCompanyId = ?1 " +
            "  AND co.gst <> ?2 " +
            "  AND co.gstinStatus = 'Active' " +
            "  AND co.stateCode IS NOT NULL")
    List<LookupDataModel> getVendorNameDetailsByCamCompanyId(long camCompanyId, String notApplicableKeyWord);

    @Query("SELECT DISTINCT new in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel(co.companyId, co.supplierCompanyName) " +
            "FROM Company AS co " +
            "WHERE co.camCompanyId = ?1 " +
            " AND co.isSellerBlocked = FALSE " +
            " AND co.stateCode IS NOT NULL")
    List<LookupDataModel> getVendorNameDetailsIncludingNotApplicableGstByCamCompanyId(long camCompanyId);

    List<Company> findByCamCompanyIdAndSupplierCompanyNameAndGstinStatusAndGstNot(long companyCode, String supplierCompanyName, String activeStatus, String notApplicableKeyWord);
    Optional<Company> findByGstAndCamCompanyId(String gst,Long camCompanyId);

    @Query("select distinct new in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel(co.companyId, co.gst) " +
            "from Company as co " +
            "where co.companyId = ?1 and co.camCompanyId = ?2 and co.gst != ?3 and co.gstinStatus = 'Active' and co.stateCode IS NOT null")
    List<LookupDataModel> getVendorGstinDetailsByCamCompanyIdAndCompanyId(Long supplierId, long companyCode, String vendorGstinNotApplicable);

    @Query("select distinct new in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel(co.companyId, co.gst) " +
            "from Company as co " +
            "where co.companyId = ?1 and co.camCompanyId = ?2 and co.gstinStatus = 'Active' ")
    List<LookupDataModel> getVendorGstinDetailsIncludingNotApplicableGstByCamCompanyIdAndCompanyId(Long supplierId, long companyCode);

    List<Company> findByCamCompanyIdAndGst(long companyCode, String gst);

    List<Company> findByGst(String gst);

    @Query("SELECT c FROM Company c WHERE CONCAT(c.vendorCode, '-', c.gst) IN :vendorCodeGstList")
    List<Company> findByVendorCodeAndGstCombination(@Param("vendorCodeGstList") Set<String> vendorCodeGstList);

    List<Company> findFirstByCompanyIdAndCamCompanyIdAndGst(Long supplierId, long companyCode, String gstin);
}
