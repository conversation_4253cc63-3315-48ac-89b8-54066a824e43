package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.TatReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.util.List;

public interface ITATReportRepository extends JpaRepository<TatReport, Long> {

    @Query("select tr from TatReport tr where tr.companyCode = ?1  " +
            " and tr.createdDate  between ?2 and ?3  and tr.reportStatus = ?4 " +
            " and UPPER(TRIM(tr.entity)) like ?5 order by tr.expenseReportId desc ")
    List<TatReport> getByExpenseReportByTATSelection(long companyCode, LocalDate fromDate, LocalDate toDate,ReportStatus voucherStatus, String entity);

    @Query("select tr from TatReport tr where tr.companyCode = ?1  " +
            " and tr.createdDate  between ?2 and ?3  and tr.reportStatus not in (?4 , ?5) " +
            " and UPPER(TRIM(tr.entity)) like ?6 order by tr.expenseReportId desc ")
    List<TatReport> getByExpenseReportByTATSelectionForAll(long companyCode, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatus, String entity);
    
    
}
