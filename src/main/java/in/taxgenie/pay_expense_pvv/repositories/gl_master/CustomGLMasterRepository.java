package in.taxgenie.pay_expense_pvv.repositories.gl_master;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.core.types.ConstructorExpression;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import in.taxgenie.pay_expense_pvv.entities.gl_master.GLStatus;
import in.taxgenie.pay_expense_pvv.entities.gl_master.QGlMaster;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.gl_master.GLMasterStatusViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.gl_master.GLMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.item_master.ItemMasterQueueViewModel;
import jakarta.persistence.EntityManager;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Component
public class CustomGLMasterRepository {
    private static final QGlMaster glMaster = QGlMaster.glMaster;
    private final JPAQueryFactory queryFactory;
    private final Logger logger;

    public CustomGLMasterRepository(EntityManager em) {
        this.queryFactory = new JPAQueryFactory(em);;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public Page<GLMasterViewModel> getGLMasterQueue(long companyCode, QueueFilterViewModel filterValues, Pageable pageable) {

        BooleanBuilder builder = new BooleanBuilder().and(glMaster.companyCode.eq(companyCode));

        ConstructorExpression<GLMasterViewModel> queueConstructorExpression =
                Projections.constructor(GLMasterViewModel.class,
                        glMaster.id, glMaster.companyCode, glMaster.plGroup, glMaster.parent,
                        glMaster.glName, glMaster.unary,
                        glMaster.gl, glMaster.hsnCode, glMaster.validFrom, glMaster.validTo, glMaster.status);

        // With requested filters
        createGlobalFilterQuery(filterValues, builder);

        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);

        QueryResults<GLMasterViewModel> results = queryFactory.selectDistinct(queueConstructorExpression)
                .from(glMaster)
                .where(builder)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    private List<OrderSpecifier<?>> createStandardOrderSpecifier(Pageable pageable) {
        List<OrderSpecifier<?>> defaultOrderSpecifier = pageable.getSort().stream()
                .map(order -> {
                    return glMaster.glName.asc();

                }).collect(Collectors.toList());
        defaultOrderSpecifier.add(glMaster.glName.asc());
        return defaultOrderSpecifier;
    }


    private void createFilterQuery(QueueFilterViewModel filterValues, BooleanBuilder builder) {
        Map<String, List<String>> filters = filterValues.getSearchCriteria() == null ? new HashMap<>() : filterValues.getSearchCriteria();
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            BooleanBuilder innerBuilder = new BooleanBuilder();
            // Todo populate filter query later.
            switch (searchCriteria) {
            }
            builder.and(innerBuilder);
        }
    }

    private void createGlobalFilterQuery(QueueFilterViewModel filterValues, BooleanBuilder builder) {
        String searchString = filterValues.getSearchString();
        BooleanBuilder innerBuilder = new BooleanBuilder();

        if (!isNullOrEmpty(searchString)) {
            String searchPattern = "%" + searchString.trim().toLowerCase() + "%";
            innerBuilder.or(glMaster.glName.lower().like(searchPattern))
                    .or(glMaster.unary.lower().like(searchPattern))
                    .or(glMaster.gl.like(searchPattern))
                    .or(glMaster.hsnCode.lower().like(searchPattern));
        }

        builder.and(innerBuilder);
    }

    private static boolean isNullOrEmpty(String value) {
        return value == null || value.isEmpty() || value.trim().isEmpty();
    }
}
