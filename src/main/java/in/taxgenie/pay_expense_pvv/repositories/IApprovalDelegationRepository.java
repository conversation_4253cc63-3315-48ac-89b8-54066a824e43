package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.ApprovalDelegation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface IApprovalDelegationRepository extends JpaRepository<ApprovalDelegation, Long> {
    @Query("select a from ApprovalDelegation a where a.companyCode = ?1 and lower(a.originator) = lower(?2) and a.endDate >= ?3 and a.isFrozen = false")
    Optional<ApprovalDelegation> findTheDelegateFor(long companyCode, String approver, LocalDate endDate);

    @Query("select a from ApprovalDelegation a where a.companyCode = ?1 and lower(a.originator) = lower(?2) and a.endDate >= ?3 and a.isFrozen = false")
    List<ApprovalDelegation> getAllDelegationByApprover(long companyCode, String approver, LocalDate endDate);

    @Query("select count(a) from ApprovalDelegation a where a.companyCode = ?1 and a.assignedTo = ?2 and a.endDate >= ?3 and a.isFrozen = false")
    long getTheDelegateCountFor(long companyCode, String assignedTo, LocalDate endDate);

    @Query("select a from ApprovalDelegation a where a.companyCode = ?1 and a.id = ?2 and a.endDate >= ?3 and a.isFrozen = false")
    Optional<ApprovalDelegation> getTheDelegateForEdit(long companyCode, long id, LocalDate endDate);

    @Query("select count(a) from ApprovalDelegation a where a.companyCode = ?1 and a.originator = ?2 and a.endDate >= ?3 and a.isFrozen = false")
    long getDelegateCountBy(long companyCode, String approver, LocalDate endDate);

    @Query("select a from ApprovalDelegation a where a.companyCode = ?1 and a.id = ?2 and a.endDate >= ?3 and a.isFrozen = false")
    Optional<ApprovalDelegation> findTheDelegation(long companyCode, long id, LocalDate endDate);

    List<ApprovalDelegation> getAllByCompanyCode(long companyCode);
    List<ApprovalDelegation> getAllByCompanyCodeAndCreatingUserId(long companyCode, long creatingUserId);
}
