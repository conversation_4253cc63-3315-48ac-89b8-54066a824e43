package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.IntervalMarker;
import in.taxgenie.pay_expense_pvv.entities.MetadataLimitRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IMetadataLimitRuleRepository extends JpaRepository<MetadataLimitRule, Long> {
    Optional<MetadataLimitRule> findByCompanyCodeAndId(long companyCode, long id);
    List<MetadataLimitRule> findByCompanyCodeAndDocumentMetadataId(long companyCode, long metadataId);

    @Query("select count(e) from MetadataLimitRule e where e.companyCode = ?1 and e.intervalMarker =?2 and e.id <> ?3 and e.isFrozen = false")
    long getCountByInterval(long companyCode, IntervalMarker marker, long id);
}
