package in.taxgenie.pay_expense_pvv.repositories.budget;

import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetSelectionViewModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface IBudgetRepository extends JpaRepository<Budget, Long> {
    Optional<Budget> findByCompanyCodeAndBudgetCodeAndBudgetStructureMasterIdAndIsActiveTrue(long companyCode,
                                                                                             String budgetCode,
                                                                                             Long budgetStructureMasterId);

    Optional<Budget> findByCompanyCodeAndIdAndIsActiveTrue(long companyCode, Long id);
    List<Budget> findByCompanyCodeAndIdInAndIsActiveTrue(long companyCode, List<Long> ids);
    List<Budget> findByCompanyCodeAndBudgetStructureMasterIdAndParentIsNullAndIsActiveTrue(long companyCode, long budgetStructureMasterId);
    List<Budget> findByCompanyCodeAndBudgetStructureMasterIdAndIsActiveTrue(long companyCode, long budgetStructureMasterId);
    @Query("select b.id from Budget b where b.companyCode = ?1 and b.budgetStructureMasterId = ?2")
    List<Long> getBudgetByCompanyCodeAndBudgetStructureMasterId(long companyCode, long budgetStructureMasterId);

    @Query("select distinct new in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetSelectionViewModel(" +
            "b.id, " +
            "b.budgetCode," +
            "bSync.description, b.budgetStructureMasterId) " +
            "from Budget b " +
            "inner join b.budgetSyncMaster bSync " +
            "where b.companyCode = ?1 and b.id = ?2")
    Optional<BudgetSelectionViewModel> getBudgetNodeByStructure(long companyCode, long budgetId);

    @Query(
            "select distinct new in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetSelectionViewModel(b.id, bSync.code, " +
            "bSync.description, b.budgetStructureMasterId, concat(b.budgetCode, ' - ', bSync.description)) " +
            "from Budget b " +
            "inner join b.budgetSyncMaster bSync " +
            "where b.companyCode = ?1 and b.isActive=true"
    )
    List<BudgetSelectionViewModel> getBudgetNodesByCompanyCode(long companyCode);

    @Query(
            "select distinct new in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetSelectionViewModel(b.id, b.budgetStructureMasterId, b.budgetCode, " +
                    "bSync.description, b.isBudgetLocked, concat(b.budgetCode, ' - ', bSync.description)) " +
                    "from Budget b " +
                    "inner join b.budgetSyncMaster bSync " +
                    "where b.companyCode = ?1 and b.budgetStructureMasterId = ?2 and b.isActive=true"
    )
    List<BudgetSelectionViewModel> getBudgetNodesByCompanyCodeAndStructureId(long companyCode, long structureId);

    @Query("SELECT SUM(b.total) FROM Budget b WHERE  b.companyCode = ?1 AND b.budgetStructureMasterId = ?2")
    Optional<BigDecimal> findTotalByCompanyCodeAndBudgetStructureMasterId(long companyCode, long budgetStructureMasterId);

    Optional<Budget> findByCompanyCodeAndIdAndIsActive(Long budgetId, long companyCode, boolean isActive);

    @Query("select b.updatedTimestamp from Budget b where b.companyCode = ?1 and b.budgetStructureMasterId = ?2 and b.isActive = ?3")
    ZonedDateTime findZonedDateTimeByCompanyCodeAndIdAndIsActive(Long budgetId, long companyCode, boolean isActive);
}
