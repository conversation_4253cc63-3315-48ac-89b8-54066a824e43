package in.taxgenie.pay_expense_pvv.repositories.budget;

import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMappingMaster;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IBudgetMappingMasterRepository extends JpaRepository<BudgetMappingMaster, Long> {
    List<BudgetMappingMaster> findByCompanyCodeAndBudgetStructureMasterId(long companyCode, long structureId);
    @Query("select b from BudgetMappingMaster b where b.companyCode = ?1 and b.budgetStructureMasterId = ?2 and b.parent is null and b.isActive = true")
    List<BudgetMappingMaster> findRootNodeForStructure(long companyCode, long structureId);
    @Query("select b from BudgetMappingMaster b where b.companyCode = ?1 and b.budgetStructureMasterId = ?2 and b.parent.id = ?3 and b.isActive = true")
    List<BudgetMappingMaster> findActiveBudgetMappingChildNodesForStructure(long companyCode, long structureId, long parentId);
    @Query("select b from BudgetMappingMaster b where b.companyCode = ?1 and b.budgetStructureMasterId = ?2 and b.isActive = true")
    List<BudgetMappingMaster> findActiveBudgetMappingsForStructure(long companyCode, long structureId);

    boolean existsByBudgetMasterIdAndCompanyCodeAndIsActive(Long budgetMasterId, Long companyCode, boolean isActive);

}
