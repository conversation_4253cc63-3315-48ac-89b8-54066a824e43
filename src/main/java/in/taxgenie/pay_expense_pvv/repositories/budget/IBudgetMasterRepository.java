package in.taxgenie.pay_expense_pvv.repositories.budget;

import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMaster;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IBudgetMasterRepository extends JpaRepository<BudgetMaster, Long> {

    List<BudgetMaster> findByCompanyCode(long companyId, Sort sort);
    List<BudgetMaster> findByCompanyCodeAndIsActive(long companyId, boolean isActive, Sort sort);
    List<BudgetMaster> findByCompanyCode(long companyId);

    List<BudgetMaster> findByCompanyCodeAndName(long companyId, String name);
}
