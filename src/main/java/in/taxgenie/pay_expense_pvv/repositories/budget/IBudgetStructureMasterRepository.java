package in.taxgenie.pay_expense_pvv.repositories.budget;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetStructureMaster;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.queue.BudgetLookupViewModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IBudgetStructureMasterRepository extends JpaRepository<BudgetStructureMaster, Long> {

    Optional<BudgetStructureMaster> findByCompanyCodeAndStructureNameAndFinancialYearAndIsActive(long companyCode, String structureName, String financialYear, boolean isActive);

    Optional<BudgetStructureMaster> findByIdAndCompanyCodeAndStructureNameAndFinancialYearAndIsActive(long id, long companyCode, String structureName, String financialYear, boolean isActive);

    Optional<BudgetStructureMaster> findByCompanyCodeAndIdAndIsActive(long companyCode, long id, boolean isActive);

    List<BudgetStructureMaster> findByCompanyCodeAndIsApproved(long companyCode, boolean isApproved);
    List<BudgetStructureMaster> findByCompanyCodeAndStatus(long companyCode,ReportStatus status);

    @Query("SELECT New in.taxgenie.pay_expense_pvv.viewmodels.budget.queue.BudgetLookupViewModel(bsm.id, bsm.structureName) " +
            "FROM BudgetStructureMaster bsm INNER JOIN DocumentMetadata dm ON bsm.documentMetadataId = dm.id " +
            "WHERE bsm.companyCode = :companyCode AND bsm.status = :status AND dm.applicableExpenseType = :applicableExpenseType ")
    List<BudgetLookupViewModel> findByCompanyCodeAndStatusAndApplicableExpenseType(long companyCode, ReportStatus status, ExpenseType applicableExpenseType);
}
