package in.taxgenie.pay_expense_pvv.repositories.budget;

import in.taxgenie.pay_expense_pvv.entities.budget.BudgetFrequencyMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IBudgetFrequencyMappingRepository extends JpaRepository<BudgetFrequencyMapping, Long> {

    List<BudgetFrequencyMapping> findByCompanyCodeAndBudgetStructureMasterIdAndBudgetIdIn(long companyCode, long budgetStructureMasterId, List<Long> budgetId);

    List<BudgetFrequencyMapping> findByCompanyCodeAndBudgetStructureMasterIdAndIsActiveTrue(long companyCode, long structureId);

    List<BudgetFrequencyMapping> findByCompanyCodeAndBudgetIdAndIsActiveTrue(long companyCode, long structureId);

    List<BudgetFrequencyMapping> findByCompanyCodeAndIdInAndIsActiveTrue(long companyCode, List<Long> budgetFrequencyMappingIds);

    List<BudgetFrequencyMapping> findByCompanyCodeAndBudgetIdAndIsActiveTrue(long companyCode, Long budgetId);

    @Query("select count(b) from BudgetFrequencyMapping b where b.companyCode = ?1 and b.budgetStructureMasterId = ?2 and b.budgetId = ?3")
    Integer getIntervalCount(long companyCode, long structureId, long budgetId);
}