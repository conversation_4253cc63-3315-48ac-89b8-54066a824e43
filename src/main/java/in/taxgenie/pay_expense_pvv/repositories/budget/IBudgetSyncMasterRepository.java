package in.taxgenie.pay_expense_pvv.repositories.budget;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.budget.BudgetSyncMaster;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderHeader;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.Optional;

@Repository
public interface IBudgetSyncMasterRepository extends JpaRepository<BudgetSyncMaster, Long> {

    List<BudgetSyncMaster> findByCompanyCodeAndBudgetMasterId(long companyCode,
    		long budgetMasterId,
    		Sort sort);

    List<BudgetSyncMaster> findByCompanyCodeAndBudgetMasterIdAndIsActive(long companyCode,
                                                              long budgetMasterId,
                                                              boolean isActive,
                                                              Sort sort);

    Page<BudgetSyncMaster> findByCompanyCodeAndBudgetMasterIdAndIsActive(long companyCode,
                                                                         long budgetMasterId,
                                                                         boolean isActive,
                                                                         Pageable pageable);


    Page<BudgetSyncMaster> findByCompanyCodeAndBudgetMasterId(long companyCode,
                                                              long budgetMasterId,
                                                              Pageable pageable);

    List<BudgetSyncMaster> findByCompanyCode(long companyId);
    List<BudgetSyncMaster> findByCompanyCodeAndBudgetMasterId(long companyId, long budgetMasterId);
    List<BudgetSyncMaster> findByCompanyCodeAndBudgetMasterIdAndIsActiveTrue(long companyId, long budgetMasterId);

    List<BudgetSyncMaster> findByCompanyCodeAndBudgetMasterIdAndCode(long companyId, long budgetMasterId, String code);

    Optional<BudgetSyncMaster> findByCompanyCodeAndBudgetMasterIdAndIdAndIsActive(long companyCode, long budgetMasterId, long id, boolean b, Sort description);
}
