package in.taxgenie.pay_expense_pvv.repositories.budget;

import in.taxgenie.pay_expense_pvv.entities.budget.BudgetDocumentAction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface IBudgetDocumentActionRepository extends JpaRepository<BudgetDocumentAction, Long> {
    @Query("SELECT bd.id FROM BudgetDocumentAction bd ORDER BY bd.id DESC Limit 1")
    Long findByLastBudgetDocumentActionId();
}
