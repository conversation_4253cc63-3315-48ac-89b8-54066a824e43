package in.taxgenie.pay_expense_pvv.repositories.pr;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestHeader;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.ReadyToConvertEntityViewModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface IPurchaseRequestHeaderRepository extends JpaRepository<PurchaseRequestHeader, Long> {
    Optional<PurchaseRequestHeader> findByDocumentId(Long documentId);

    @Query("SELECT pr FROM PurchaseRequestHeader pr " +
            "INNER JOIN pr.document doc " +
            "INNER JOIN doc.documentApprovalContainer dac " +
            "INNER JOIN doc.documentSubgroup docSub " +
            "WHERE dac.reportStatus = :status and docSub.budgetId = :budgetId")
    Page<PurchaseRequestHeader> findByBudgetIdAndReportStatus(@Param("budgetId") Long budgetId, @Param("status") ReportStatus status, Pageable pageable);

    @Query("SELECT pr.id FROM PurchaseRequestHeader pr ORDER BY pr.id DESC Limit 1")
    Long findByLastPRId();

    @Query("SELECT pr FROM PurchaseRequestHeader pr " +
            "INNER JOIN pr.document doc " +
            "INNER JOIN doc.documentApprovalContainer dac " +
            "WHERE dac.id = :entityId")
    Optional<PurchaseRequestHeader> findByDocumentApprovalContainerId(Long entityId);

    @Query("SELECT pr FROM PurchaseRequestHeader pr WHERE pr.id IN ?1")
    List<PurchaseRequestHeader> findByIdsIn(List<Long> ids);

    @Query("select new in.taxgenie.pay_expense_pvv.viewmodels.pr.ReadyToConvertEntityViewModel(pr.id, dac.id, CONCAT('PR - ',dm.documentGroup) as policyName, " +
            "(doc.claimAmount - COALESCE(doc.consumedAmount, 0)) as prAmount , COUNT(pri.id), doc.docNo, docSub.subgroupFieldsJson, CONCAT(u.firstName, ' ', u.lastName) as createdBy, doc.createdTimestamp, docSub.id, docSub.hasPrecedingDocument) FROM " +
            "PurchaseRequestHeader pr " +
            "INNER JOIN pr.prItems pri "+
            "INNER JOIN pr.document doc " +
            "INNER JOIN doc.documentApprovalContainer dac " +
            "INNER JOIN dac.documentMetadata dm "+
            "INNER JOIN doc.documentSubgroup docSub " +
            "INNER JOIN pr.createdBy u "+
            "WHERE doc.companyCode= :companyCode AND dac.reportStatus = :status AND (pri.vendorId = :vendorId OR pri.vendorId IS NULL)  " +
            "GROUP BY pr.id, dac.id, dm.documentGroup, doc.claimAmount, doc.consumedAmount, doc.docNo, u.firstName, u.lastName, doc.createdTimestamp, docSub.id, docSub.subgroupFieldsJson, docSub.hasPrecedingDocument")
    List<ReadyToConvertEntityViewModel> findByCompanyCodeAndStatusAndVendorId(long companyCode, ReportStatus status, Integer vendorId);

    @Query("select new in.taxgenie.pay_expense_pvv.viewmodels.pr.ReadyToConvertEntityViewModel(pr.id, dac.id, CONCAT('PR - ',dm.documentGroup) as policyName, " +
            "(doc.claimAmount - COALESCE(doc.consumedAmount, 0)) as prAmount , COUNT(pri.id), doc.docNo, docSub.subgroupFieldsJson, CONCAT(u.firstName, ' ', u.lastName) as createdBy, doc.createdTimestamp, docSub.id, docSub.hasPrecedingDocument) FROM " +
            "PurchaseRequestHeader pr " +
            "INNER JOIN pr.prItems pri "+
            "INNER JOIN pr.document doc " +
            "INNER JOIN doc.documentApprovalContainer dac " +
            "INNER JOIN dac.documentMetadata dm "+
            "INNER JOIN doc.documentSubgroup docSub " +
            "INNER JOIN pr.createdBy u "+
            "WHERE doc.companyCode= :companyCode AND dac.reportStatus = :status AND pri.vendorId = :vendorId AND pr.id in (:ids)" +
            "GROUP BY pr.id, dac.id, dm.documentGroup, doc.claimAmount, doc.consumedAmount, doc.docNo, u.firstName, u.lastName, doc.createdTimestamp, docSub.id, docSub.subgroupFieldsJson, docSub.hasPrecedingDocument")
    List<ReadyToConvertEntityViewModel> findByCompanyCodeAndStatusAndVendorIdAndIdsIn(long companyCode, ReportStatus status, Integer vendorId, List<Long> ids);
}
