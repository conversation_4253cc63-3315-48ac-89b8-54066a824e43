package in.taxgenie.pay_expense_pvv.repositories.pr;

import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface IPurchaseRequestItemRepository extends JpaRepository<PurchaseRequestItem, Long> {
    @Query("select prItem from PurchaseRequestItem prItem " +
            "inner join prItem.purchaseRequestHeader pr " +
            "inner join pr.document doc " +
            "where prItem.id = ?1 and doc.companyCode = ?2")
    Optional<PurchaseRequestItem> findByIdAndCompanyCode(long id, long companyCode);

    @Query("select prItem from PurchaseRequestItem prItem where prItem.id = ?1 ")
    Optional<PurchaseRequestItem> findById(long id);

    @Query("SELECT COALESCE(SUM(d.claimAmount), 0) " +
            "FROM PurchaseRequestItem pri " +
            "JOIN pri.purchaseRequestHeader pr " +
            "JOIN pr.document d " +
            "WHERE pri.id IN :prItemIds")
    BigDecimal getClaimAmountSumByPrItemIds(@Param("prItemIds") List<Long> prItemIds);
}
