package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.DocumentIdentifier;
import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface IDocumentIdentifierRepository extends JpaRepository<DocumentIdentifier, Long> {

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    Optional<DocumentIdentifier> findFirstByCompanyCodeAndDocumentTypeAndYearAndMonth(
            long companyCode, String documentType, int year, int month);
}