package in.taxgenie.pay_expense_pvv.repositories.erp;

import in.taxgenie.pay_expense_pvv.entities.erp.ERPGrn;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMaster;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface IERPGrnRepository extends JpaRepository<ERPGrn, Long> {

    Optional<ERPGrn> findByGrnNumberAndCompanyCode(String grnNumber, long companyCode);

    Optional<ERPGrn> findByCompanyCodeAndId(long companyCode, Long id);

}
