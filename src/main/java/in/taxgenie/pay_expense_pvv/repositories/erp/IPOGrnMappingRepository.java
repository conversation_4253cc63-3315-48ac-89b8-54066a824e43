package in.taxgenie.pay_expense_pvv.repositories.erp;

import in.taxgenie.pay_expense_pvv.entities.InvoiceHeader;
import in.taxgenie.pay_expense_pvv.entities.erp.POGrnMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IPOGrnMappingRepository extends JpaRepository<POGrnMapping, Long> {

    List<POGrnMapping> findBypurchaseOrderHeaderId(Long id);
}
