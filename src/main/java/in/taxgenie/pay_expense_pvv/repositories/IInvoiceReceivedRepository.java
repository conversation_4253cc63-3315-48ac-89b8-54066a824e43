package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.InvoiceReceived;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface IInvoiceReceivedRepository extends JpaRepository<InvoiceReceived, Long> {
    Optional<InvoiceReceived> findByCompanyIdAndIrn(long companyCode, String irn);

    Optional<InvoiceReceived> findByDmrReferenceId(String dmrReferenceId);

    @Query("SELECT ir FROM InvoiceReceived ir " +
            "JOIN ir.invoiceHeader ih " +
            "JOIN ih.document d " +
            "JOIN d.documentApprovalContainer dac " +
            "WHERE dac.documentIdentifier = :documentIdentifier ")
    Optional<InvoiceReceived> findByDocumentIdentifierId(
            @Param("documentIdentifier") String documentIdentifier);

    Optional<InvoiceReceived> findByDmrReferenceIdAndCompanyId(String dmrReferenceId, Long companyId);

    @Query("SELECT ir FROM InvoiceReceived ir " +
            "JOIN ir.invoiceHeader ih " +
            "JOIN ih.document d " +
            "JOIN d.documentApprovalContainer dac " +
            "WHERE dac.documentIdentifier = :documentIdentifier " +
            "AND ir.companyId = :companyId")
    Optional<InvoiceReceived> findByDocumentIdentifierIdAndCompanyId(
            @Param("documentIdentifier") String documentIdentifier,
            @Param("companyId") Long companyId);

    Optional<InvoiceReceived> findByIrnAndInvoiceHeader_Document_DocumentApprovalContainer_ReportStatusNotInAndInvoiceHeader_Document_CompanyCode(String irn, List<ReportStatus> excludedStatuses, long companyCode);
}
