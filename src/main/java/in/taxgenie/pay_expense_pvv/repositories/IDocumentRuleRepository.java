package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.DocumentRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IDocumentRuleRepository extends JpaRepository<DocumentRule, Long> {
    Optional<DocumentRule> findByCompanyCodeAndId(long companyCode, long id);
    List<DocumentRule> findByCompanyCodeAndDocumentSubgroupId(long companyCode, long subgroupId);
}
