package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.DocumentSubgroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IDocumentSubgroupRepository extends JpaRepository<DocumentSubgroup, Long> {
    @Query("select count(e) from DocumentSubgroup e where e.companyCode = ?1 and e.documentCode = ?2 and e.documentMetadataId = ?3 and e.id <> ?4 and e.isFrozen = false")
    long getCountByCode(long companyCode, String documentCode, long metadataId, long id);

    @Query("select count(e) from DocumentSubgroup e where e.companyCode = ?1 and e.documentSubgroup = ?2 and e.documentMetadataId = ?3 and e.id <> ?4 and e.isFrozen = false")
    long getCountBySubgroup(long companyCode, String subgroup, long metadataId, long id);

    @Query("select count(e) from DocumentSubgroup e where e.companyCode = ?1 and e.documentSubgroupPrefix = ?2 and e.documentMetadataId = ?3 and e.id <> ?4 and e.isFrozen = false")
    long getCountByPrefix(long companyCode, String prefix, long metadataId, long id);

    Optional<DocumentSubgroup> findByCompanyCodeAndId(long companyCode, long id);

    @Query("select e from DocumentSubgroup  e where e.companyCode = ?1 and e.isFrozen = false")
    List<DocumentSubgroup> findAllByCompanyCode(long companyCode);

    List<DocumentSubgroup> findAllByCompanyCodeAndDocumentMetadataId(long companyCode, long metadataId);

    @Query("SELECT DISTINCT ds FROM DocumentSubgroup ds "
            + "LEFT JOIN DocumentRule dr ON ds.id = dr.documentSubgroupId "
            + "WHERE ds.companyCode = :companyCode AND ds.documentMetadataId = :metadataId "
            + "AND ds.rulesCount > 0 "
            + "AND dr.isFrozen = false")
    List<DocumentSubgroup> findDocumentSubgroupForInvoiceCreation(long companyCode, long metadataId);

    @Query("SELECT ds FROM DocumentSubgroup ds "
            + "JOIN Document d ON ds.id = d.documentSubgroupId "
            + "JOIN DocumentApprovalContainer dac ON d.documentApprovalContainerId = dac.id "
            + "WHERE dac.id = :documentApprovalContainerId AND ds.companyCode = :companyId ")
    Optional<DocumentSubgroup> findSubgroupByDocumentApprovalContainerIdAndCompanyId(@Param("documentApprovalContainerId") Long documentApprovalContainerId, @Param("companyId") Long companyId);

}
