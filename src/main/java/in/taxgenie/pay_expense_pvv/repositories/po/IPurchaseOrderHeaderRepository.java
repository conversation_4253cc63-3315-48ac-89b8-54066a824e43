package in.taxgenie.pay_expense_pvv.repositories.po;

import in.taxgenie.pay_expense_pvv.entities.InvoiceHeader;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.ReadyToConvertEntityViewModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IPurchaseOrderHeaderRepository extends JpaRepository<PurchaseOrderHeader, Long> {
    Optional<PurchaseOrderHeader> findByDocumentId(Long documentId);

    @Query("select po from PurchaseOrderHeader po " +
            "inner join po.document doc " +
            "where po.id = ?1 and doc.companyCode = ?2")
    Optional<PurchaseOrderHeader> findByIdAndCompanyCode(long id, long companyCode);

    @Query("SELECT po FROM PurchaseOrderHeader po " +
            "INNER JOIN po.document doc " +
            "INNER JOIN doc.documentApprovalContainer dac " +
            "INNER JOIN doc.documentSubgroup docSub " +
            "WHERE dac.reportStatus = :status and docSub.budgetId = :budgetId")
    Page<PurchaseOrderHeader> findByBudgetIdAndReportStatus(@Param("budgetId") Long budgetId, @Param("status") ReportStatus status, Pageable pageable);

    @Query("SELECT po.id FROM PurchaseOrderHeader po ORDER BY po.id DESC Limit 1")
    Long findByLastPOId();

    @Query("SELECT po FROM PurchaseOrderHeader po " +
            "INNER JOIN po.document doc " +
            "INNER JOIN doc.documentApprovalContainer dac " +
            "WHERE dac.id = :entityId")
    Optional<PurchaseOrderHeader> findByDocumentApprovalContainerId(Long entityId);

    @Query("select new in.taxgenie.pay_expense_pvv.viewmodels.pr.ReadyToConvertEntityViewModel(po.id, dac.id, CONCAT('PO - ',dm.documentGroup) as policyName, " +
            "(doc.claimAmount - COALESCE(doc.consumedAmount, 0)) as poAmount , COUNT(po.id), doc.docNo, docSub.subgroupFieldsJson, CONCAT(u.firstName, ' ', u.lastName) as createdBy, doc.createdTimestamp, docSub.id, docSub.hasPrecedingDocument) FROM " +
            "PurchaseOrderHeader po " +
            "INNER JOIN po.purchaseOrderItems poi "+
            "INNER JOIN po.document doc " +
            "INNER JOIN doc.documentApprovalContainer dac " +
            "INNER JOIN dac.documentMetadata dm "+
            "INNER JOIN doc.documentSubgroup docSub " +
            "INNER JOIN po.createdBy u "+
            "WHERE doc.companyCode= :companyCode AND dac.reportStatus = :reportStatus AND poi.vendorId = :vendorId " +
            "GROUP BY po.id, dac.id, dm.documentGroup, doc.claimAmount, doc.consumedAmount, doc.docNo, u.firstName, u.lastName, doc.createdTimestamp, docSub.id, docSub.subgroupFieldsJson, docSub.hasPrecedingDocument")
    List<ReadyToConvertEntityViewModel> findByCompanyCodeAndStatusAndVendorId(long companyCode, ReportStatus reportStatus, Integer vendorId);

    @Query("SELECT purchaseOrderHeader FROM PurchaseOrderHeader purchaseOrderHeader " +
            "JOIN purchaseOrderHeader.document doc " +
            "JOIN doc.documentApprovalContainer dac " +
            "WHERE (dac.acknowledgedByErp IS NULL OR dac.acknowledgedByErp = false) " +
            "AND (purchaseOrderHeader.syncAttempt < 3 OR purchaseOrderHeader.syncAttempt IS NULL)" +
            "AND dac.reportStatus = 3 " +
            "ORDER BY purchaseOrderHeader.priority DESC, dac.approvedDate ASC, purchaseOrderHeader.syncAttempt ASC")
    List<PurchaseOrderHeader> findPOPendingForSyncToERP(Pageable pageable);
}
