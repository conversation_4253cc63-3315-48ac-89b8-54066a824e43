package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.DocumentApprovalContainer;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.viewmodels.AdminQueueViewModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface IDocumentApprovalContainerRepository extends JpaRepository<DocumentApprovalContainer, Long> {
    Optional<DocumentApprovalContainer> findByCompanyCodeAndId(long companyCode, long id);
    Optional<DocumentApprovalContainer> findByCompanyCodeAndIdAndCreatingUserId(long companyCode, long id, long creatingUserId);

    Optional<DocumentApprovalContainer> findByCompanyCodeAndIdAndEmployeeEmail(long companyCode, long id, String email);
    Optional<DocumentApprovalContainer> findByCompanyCodeAndIdAndReportStatusNot(long companyCode, long id, ReportStatus status);
    List<DocumentApprovalContainer> findByCompanyCodeAndReportStatus(long companyCode, ReportStatus status);
    List<DocumentApprovalContainer> findByCompanyCodeAndReportStatusIn(long companyCode, List<ReportStatus> statusList);

//    @Query("select distinct new in.taxgenie.pay_expense_pvv.viewmodels.pr.PurchaseRequestContainerViewModel(dac.id, doc.totalAmountGSTInclusive, (doc.taxableAmount-pr.consumedAmount), " +
//            "dac.documentIdentifier, dac.createdDate, doc.documentDate, dac.reportStatus, dac.submitDate, dac.currentApproverFirstName, dac.currentApproverLastName, " +
//            "dac.currentApproverSystemIdCode, dac.currentApproverEmployeeCode, " +
//            "dac.firstName, dac.middleName, dac.lastName, dac.employeeCode, dac.employeeGrade, " +
//            "dac.updatedTimestamp, " +
//            "doc.originatorDocumentIdentifier, doc.documentSubgroupId, dac.employeeDepartment, pr.expectedDeliveryDate, pr.purchaseReason, doc.docNo, pr.prType.value, NULL, NULL), " + // TODO : here change dac.lastName to departmentName later
//            "dac.id as id, dac.lastActionedAt " +
//            "from DocumentApprovalContainer dac " +
//            "left join dac.documents doc " +
//            "inner join doc.documentSubgroup docSub "+
//            "left join doc.prHeader pr " +
//            "left join Budget bg ON bg.id = docSub.budgetId "+
//            "where dac.companyCode = ?1 and dac.creatingUserId = ?2 and dac.documentMetadataId in ?3 ")
//    Page<PurchaseRequestContainerViewModel> getPRQueue(long companyCode, long userId, List<Long> documentMetadataArray, Pageable pageable);

    long countByCompanyCodeAndReportStatusAndCreatingUserId(long companyCode, ReportStatus status, long creatingUserId);
    @Query("select e from DocumentApprovalContainer e where e.companyCode = ?1 and e.creatingUserId = ?2 order by e.id DESC")
    List<DocumentApprovalContainer> getUserQueue(long companyCode, long creatingUserId);

    @Query("select e from DocumentApprovalContainer e where e.companyCode = ?1 order by e.id DESC")
    List<DocumentApprovalContainer> getAdminQueue(long companyCode);

    @Query(value="select rs.approver_employee_code as currentApproverEmployeeCode,rs.approver_first_name as currentApproverFirstName,rs.level as level,rs.approver_last_name as currentApproverLastName,er1.id,er1.document_identifier as documentIdentifier,er1.created_date as createdDate,er1.submit_date as submitDate,er1.start_date as startDate,er1.end_date as endDate,er1.approval_container_title as approvalContainerTitle,er1.description as description,er1.purpose as purpose,er1.approval_container_claim_amount as approvalContainerClaimAmount,er1.report_status as reportStatus,er1.action_level as actionLevel,er1.contains_deviation as containsDeviation,er1.deviation_remarks as deviationRemarks,er1.approval_container_sgst_amount as approvalContainerSgstAmount,er1.approval_container_cgst_amount as approvalContainerCgstAmount,er1.approval_container_igst_amount as approvalContainerIgstAmount,er1.approval_container_taxable_amount as approvalContainerTaxableAmount,er1.first_name as firstName,er1.middle_name as middleName,er1.last_name as lastName,er1.employee_email as employeeEmail,er1.employee_code as employeeCode,er1.employee_grade as employeeGrade,er1.employee_system_id as employeeSystemId,er1.expense_type as gender,er1.document_metadata_id as documentMetadataId,em.document_group as documentGroup,em.document_type as documentType,er1.send_back_remarks as sendBackRemarks,er1.reject_remarks as rejectRemarks,er1.delegation_remarks as delegationRemarks,er1.default_approver_remarks as defaultApproverRemarks,er1.contains_sent_back as containsSentBack,er1.gl_posting_date as glPostingDate,er1.payment_date as paymentDate from sch_payinvoice_p2p.document_approval_container as er1 inner join sch_payinvoice_p2p.document_metadata em on er1.document_metadata_id = em.id left join sch_payinvoice_p2p.report_state rs on rs.id = (select rs2.id from sch_payinvoice_p2p.report_state as rs2 where rs2.document_approval_container_id = er1.id and rs2.status=0 order by rs2.level limit 1) where er1.company_code = ?1 and er1.document_metadata_id = em.id order by id desc",nativeQuery = true)
    List<AdminQueueViewModel> getAdminQueueV2(long companyCode);

    @Query("select e from DocumentApprovalContainer e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.documentMetadataId = ?3 and e.reportStatus in ?4 order by e.id DESC")
    List<DocumentApprovalContainer> getReportsByMetadataUserStatus(long companyCode, long creatingUserId, long metadataId, List<ReportStatus> statusList);

    @Query("select e from DocumentApprovalContainer e where e.companyCode = ?1 and e.reportStatus = ?2 and e.isPostedToGl = false and e.acknowledgedByErp = false and e.employeeGlMainAccountCode is not null order by e.id DESC")
    List<DocumentApprovalContainer> getReportsForErpProcessing(long companyCode, ReportStatus status);

    List<DocumentApprovalContainer> getByCompanyCode(long companyCode);

    @Query("select e from DocumentApprovalContainer e where e.companyCode = ?1 and e.id = ?2 and e.reportStatus not in ?3 ")
    Optional<DocumentApprovalContainer> getReportsForErpPosting(long companyCode, long id, List<ReportStatus> status);

    @Query("select sum(e.approvalContainerClaimAmount) from DocumentApprovalContainer e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.reportStatus in ?3 and e.submitDate >= ?4 and e.submitDate <= ?5")
    Optional<BigDecimal> getTotalClaimForPeriod(long companyCode, long userId, List<ReportStatus> status, LocalDate start, LocalDate end);

    @Query("select sum(e.totalPaidAmount) from DocumentApprovalContainer e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.submitDate >= ?3 and e.submitDate <= ?4")
    Optional<BigDecimal> getTotalPaidForPeriod(long companyCode, long userId, LocalDate start, LocalDate end);

    @Query("select sum(e.totalTdsAmount) from DocumentApprovalContainer e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.submitDate >= ?3 and e.submitDate <= ?4")
    Optional<BigDecimal> getTotalTdsForPeriod(long companyCode, long userId, LocalDate start, LocalDate end);

    @Query("select count(e) from DocumentApprovalContainer e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.reportStatus = ?3 and e.submitDate >= ?4 and e.submitDate <= ?5")
    Optional<Long> getReportCountByCriteria(long companyCode, long userId, ReportStatus status, LocalDate start, LocalDate end);

    @Query("select count(e) from DocumentApprovalContainer e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.reportStatus = ?3 and e.createdDate >= ?4 and e.createdDate <= ?5")
    Optional<Long> getReportCountByCriteriaAndCreation(long companyCode, long userId, ReportStatus status, LocalDate start, LocalDate end);

    @Query("select e from DocumentApprovalContainer e  where e.companyCode = ?1 and e.id in ?2")
    List<DocumentApprovalContainer> findByCompanyCodeAndIdsIn(long companyCode, List<Long> documentApprovalContainerIds);

    @Query("select e from DocumentApprovalContainer e  where e.companyCode = ?1 and e.id in ?2")
    Page<DocumentApprovalContainer> findByCompanyCodeAndIdsIn(long companyCode, List<Long> documentApprovalContainerIds, Pageable pageable);

    @Query("select e from DocumentApprovalContainer e  where e.companyCode = ?1 and e.creatingUserId = ?2 and e.documentMetadataId in ?3")
    Page<DocumentApprovalContainer> findByCompanyCodeAndCreatingUserIdAndDocumentMetadataIdsIn(long companyCode, long userId, List<Long> documentMetadataIds, Pageable pageable);

    List<DocumentApprovalContainer> findAllByReportStatus(ReportStatus status);

    @Query("select er from DocumentApprovalContainer er where er.companyCode = ?1  " +
            " and er.createdDate  between ?2 and ?3  and er.reportStatus = ?4 " +
            " and UPPER(TRIM(er.value03)) like ?5 order by er.id desc ")
    List<DocumentApprovalContainer> getDocumentApprovalContainerByTATSelection(long companyCode, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, String entity);

    @Query("select er from DocumentApprovalContainer er where er.companyCode = ?1  " +
            " and er.createdDate  between ?2 and ?3  and er.reportStatus not in (?4 , ?5) " +
            " and UPPER(TRIM(er.value03)) like ?6 order by er.id desc ")
    List<DocumentApprovalContainer> getDocumentApprovalContainerByTATSelectionForAll(long companyCode, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatus, String entity);
        @Query("select er from DocumentApprovalContainer er where er.companyCode = ?1 and er.documentMetadata.documentType like ?2 and er.createdDate  between ?3 and ?4 " +
            " and er.reportStatus = ?5 and UPPER(TRIM(er.value03))  like ?6 order by er.employeeCode ")
    List<DocumentApprovalContainer> getByDocumentType(long companyCode, String documentType, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, String entity);

    @Query("select er from DocumentApprovalContainer er where er.companyCode = ?1 and er.documentMetadata.documentType like ?2 and  " +
            " er.createdDate  between ?3 and ?4 and er.reportStatus not in (?5 , ?6) and UPPER(TRIM(er.value03)) like ?7  order by er.employeeCode ")
    List<DocumentApprovalContainer> getDocumentApprovalContainerTypeForAll(long companyCode, String documentType, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatus, String entity);

    @Modifying
    @Query("UPDATE DocumentApprovalContainer dac SET dac.updatedTimestamp = :updatedTimestamp " +
            "WHERE dac.id = (" +
            "    SELECT doc.documentApprovalContainerId FROM Document doc " +
            "    JOIN doc.budgetDocumentAction bda " +
            "    WHERE (bda.budgetStructureMasterId = :structureMasterId)" +
            ")")
    void updateUpdatedTimestampByStructureMasterId(@Param("updatedTimestamp") ZonedDateTime updatedTimestamp,
                                                   @Param("structureMasterId") Long structureMasterId);

    Optional<DocumentApprovalContainer> findByCompanyCodeAndDocumentApprovalCode(long companyCode, UUID docApprovalCode);
    List<DocumentApprovalContainer> findByCompanyCodeAndDocumentIdentifier(long companyCode, String documentIdentifier);

}
