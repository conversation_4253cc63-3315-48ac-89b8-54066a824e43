package in.taxgenie.pay_expense_pvv.repositories.mailroom;

import in.taxgenie.pay_expense_pvv.entities.invoice.Mailroom;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface IMailRoomReposiitory extends JpaRepository<Mailroom, Long> {

    Optional<Mailroom> findByCompanyCodeAndType(long companyCode, String type);

    Optional<Mailroom> findByCompanyCodeAndIrn(long companyCode, String type);

    boolean existsByCompanyCodeAndIrn(long companyCode, String irn);
}
