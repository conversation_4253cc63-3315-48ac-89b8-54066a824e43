package in.taxgenie.pay_expense_pvv.repositories.mailroom;

import in.taxgenie.pay_expense_pvv.entities.mailroom.InvoiceSyncReport;
import in.taxgenie.pay_expense_pvv.viewmodels.mailroom.SummaryViewModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface IInvoiceSyncReportReposiitory extends JpaRepository<InvoiceSyncReport, Long> {

    @Query("SELECT new in.taxgenie.pay_expense_pvv.viewmodels.mailroom.SummaryViewModel(COUNT(m), SUM(m.totalAmount)) " +
            "FROM Mailroom m WHERE m.companyCode = :companyCode")
    SummaryViewModel getMailroomSummary(@Param("companyCode") Long companyCode);

    Optional<InvoiceSyncReport> findByCompanyCodeAndRequestId(long companyCode, String requestId);
}
