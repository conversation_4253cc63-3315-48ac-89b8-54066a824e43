package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.ApprovalDefinition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Repository
public interface IApprovalDefinitionRepository extends JpaRepository<ApprovalDefinition, Long> {
    Optional<ApprovalDefinition> findByCompanyCodeAndId(long companyCode, long id);

    List<ApprovalDefinition> findByCompanyCodeAndDocumentMetadataId(long companyCode, long documentMetadataId);

    long countByDocumentMetadataIdAndLevelAndCompanyCodeAndIsFrozenIsFalse(long documentMetadataId, int level, long companyCode);

    long countByDocumentMetadataIdAndApprovalMatcherAndApprovalTitleAndCompanyCodeAndIsFrozenIsFalse(long documentMetadataId, String approvalMatcher, String approvalTitle, long companyCode);

    @Query("select a from ApprovalDefinition a where a.companyCode = ?1 and a.id = ?2")
    Optional<ApprovalDefinition> findByCompanyIdAndDefinitionId(long companyCode, long id);

    @Query("select a from ApprovalDefinition a where a.companyCode = ?1 and a.documentMetadataId = ?2 and a.limitAmount < ?3 and a.isFrozen = false order by a.level")
    List<ApprovalDefinition> findApplicableDefinitions(long companyCode, long documentMetadataId, BigDecimal claimAmount);

    @Query("select a from ApprovalDefinition a where a.companyCode = ?1 and a.documentMetadataId = ?2 and a.limitAmount = ?3 and a.isFrozen = false order by a.level")
    List<ApprovalDefinition> findApplicableDefinitionsEqualAmount(long companyCode, long documentMetadataId, BigDecimal claimAmount);

    Optional<ApprovalDefinition> findFirstByCompanyCodeAndDocumentMetadataIdAndLimitAmountGreaterThanAndIsFrozenIsFalseOrderByLevel(long companyCode, long documentMetadataId, BigDecimal claimAmount);

    @Query("select a from ApprovalDefinition a where a.companyCode = ?1 and a.documentMetadataId = ?2 and a.limitAmount = ?3 and a.isFrozen = false order by a.level")
    List<ApprovalDefinition> getEqualLimitAmountDefinitionsInLastBand(long companyCode, long documentMetadataId, BigDecimal limitAmount);

    @Query("select a from ApprovalDefinition a where a.companyCode = ?1 and a.documentMetadataId = ?2 and a.limitAmount < ?3 " +
            " order by a.level")
    List<ApprovalDefinition> findAllApplicableDefinitions(long companyCode, long documentMetadataId, BigDecimal claimAmount);

    @Query("select a from ApprovalDefinition a where a.companyCode = ?1 and a.documentMetadataId = ?2 and a.limitAmount = ?3 " +
            " order by a.level")
    List<ApprovalDefinition> findAllApplicableDefinitionsEqualAmount(long companyCode, long documentMetadataId, BigDecimal claimAmount);

    @Query("SELECT a FROM ApprovalDefinition a WHERE a.companyCode = ?1 AND a.documentMetadataId = ?2 " +
            "AND a.limitAmount > ?3 ORDER BY a.level")
    Optional<ApprovalDefinition> findFirstByCompanyCodeAndDocumentMetadataIdAndLimitAmountGreaterThanOrderByLevel(
            long companyCode, long documentMetadataId, BigDecimal claimAmount);

    @Query("select a from ApprovalDefinition a where a.companyCode = ?1 and a.documentMetadataId = ?2 and a.limitAmount = ?3 " +
            " order by a.level")
    List<ApprovalDefinition> getAllEqualLimitAmountDefinitionsInLastBand(long companyCode, long documentMetadataId, BigDecimal limitAmount);

    @Query("select a from ApprovalDefinition a where a.companyCode = ?1 and a.isFrozen = false")
    List<ApprovalDefinition> findAllActiveDefinitions(long companyCode);

    @Query("select count(a) from ApprovalDefinition a where a.companyCode = ?1 and a.documentMetadataId = ?2 and a.level = ?3 and a.id <> ?4 and a.isFrozen = false")
    long getCountByCriteria(long companyCode, long documentMetadataId, int level, long definitionId);

    @Query("SELECT DISTINCT a.approvalMatcher FROM ApprovalDefinition a WHERE a.documentMetadataId = :documentMetadataId")
    List<String> findDistinctApprovalMatchersByDocumentMetadataId(Long documentMetadataId);

}
