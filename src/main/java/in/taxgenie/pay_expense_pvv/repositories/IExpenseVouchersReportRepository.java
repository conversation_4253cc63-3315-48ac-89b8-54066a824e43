package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.ExpenseVouchersReport;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface IExpenseVouchersReportRepository extends JpaRepository<ExpenseVouchersReport, Long> {

    @Query("select evr from ExpenseVouchersReport evr where evr.companyCode = ?1 and evr.expenseType like ?2 and  " +
            " evr.createdDate  between ?3 and ?4 and evr.reportStatus not in (?5 , ?6) and UPPER(TRIM(evr.value03)) like ?7  order by evr.expenseReportId")
    List<ExpenseVouchersReport> getByExpenseTypeForAll(long companyId, String expenseType, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatus, String entity);

    @Query("select evr from ExpenseVouchersReport evr where evr.companyCode = ?1 and evr.expenseType like ?2 and evr.createdDate between ?3 and ?4 " +
            " and evr.reportStatus = ?5 and UPPER(TRIM(evr.value03))  like ?6 order by evr.expenseReportId ")
    List<ExpenseVouchersReport> getByExpenseType(long companyCode, String expenseType, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, String entity);

}
