package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.multitenancy.context.TenantContext;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository("QueueV2Repository")
public class QueueV2RepoImpl implements QueueV2Repository {

    @PersistenceContext
    EntityManager em;

//    @Value("${spring.jpa.properties.hibernate.default_schema}")
//    private String schema;

    @SuppressWarnings("unchecked")
    @Override
    public List<Object[]> getAdminQueueData(String searchWhereQuery, long companyCode, int pageNumber, int numberOfRecords, String sortBy,
                                            String sort) {
        StringBuilder queryBuilder = new StringBuilder(selectAllQueryForAdmin(companyCode))
                .append(searchWhereQuery);

        if (sortBy != null && !sortBy.isBlank()) {
            queryBuilder.append(" ORDER BY ").append(sortBy).append(" ").append(sort).append(", er1.id DESC ");
        } else {
            queryBuilder.append(" ORDER BY er1.id DESC ");
        }

        Query query = em.createNativeQuery(queryBuilder.toString());
        query.setParameter("companyCode", companyCode);
        query.setFirstResult(pageNumber * numberOfRecords);
        query.setMaxResults(numberOfRecords);

        return query.getResultList();
    }

    @Override
    public Integer getTotalElements(String searchWhereQuery, long companyCode) {
        // TODO : temp fix - to get the connection details - schema in the context before DB call
        em.createNativeQuery("select count(er1.id) from sch_payinvoice_p2p.document_approval_container er1 ").getSingleResult();
        // temp fix end
        StringBuilder queryBuilder = new StringBuilder(selectCountQueryForAdmin(companyCode))
                .append(searchWhereQuery);

        Query query = em.createNativeQuery(queryBuilder.toString());
        query.setParameter("companyCode", companyCode);
        return ((Number) query.getSingleResult()).intValue();

    }
    public Integer getTotalElementsForUser(String searchWhereQuery, long companyCode, long userId) {
        StringBuilder queryBuilder = new StringBuilder(selectCountQueryForUser(companyCode, userId))
                .append(searchWhereQuery);
        return ((Long) em.createNativeQuery(queryBuilder.toString()).getSingleResult()).intValue();

    }

    @Override
    public String selectAllQueryForAdmin(long companyCode) {

        StringBuilder queryString = new StringBuilder();
        queryString.append(selectQueryQueue()).append(fromQueryForAdmin()).append(whereQuery(companyCode));
        return queryString.toString();
    }

    public String selectQueryQueue() {
        StringBuilder sb = new StringBuilder();
        return sb.append(
                        "select  rs.approver_employee_code, rs.approver_first_name, rs.level,rs.approver_last_name, er1.id, er1.document_identifier, ")
                .append("er1.created_date, er1.submit_date, er1.start_date, er1.end_date, er1.approval_container_title, er1.description, er1.purpose, ")
                .append("er1.approval_container_claim_amount, er1.report_status, er1.action_level, er1.contains_deviation, er1.deviation_remarks, ")
                .append("er1.approval_container_sgst_amount, er1.approval_container_cgst_amount, er1.approval_container_igst_amount, er1.approval_container_taxable_amount, er1.first_name, ")
                .append("er1.middle_name, er1.last_name , er1.employee_email, er1.employee_code, er1.employee_grade, er1.employee_system_id, ")
                .append("er1.expense_type, er1.document_metadata_id, em.document_group, em.document_type, er1.send_back_remarks, er1.reject_remarks, ")
                .append("er1.delegation_remarks, er1.default_approver_remarks, er1.contains_sent_back, er1.gl_posting_date, er1.payment_date, em.document_category, sg.id, pm.payment_reference, er1.updated_timestamp ")
                .toString();
    }

    @Override
    public String selectCountQueryForAdmin(long companyCode) {

        return new StringBuilder("select count(er1.id)").append(fromQueryForAdmin()).append(whereQuery(companyCode))
                .toString();

    }

    @Override
    public String fromQueryForAdmin() {
        String schema = TenantContext.getCurrentSchema();
        return new StringBuilder("FROM "+schema+".document_approval_container er1 ")
                .append("INNER JOIN "+schema+".document d on er1.id = d.document_approval_container_id ")
                .append("INNER JOIN "+schema+".document_subgroup sg on sg.id = d.document_subgroup_id ")
                .append("INNER JOIN "+schema+".document_metadata em on er1.document_metadata_id = em.id ")
                .append("LEFT JOIN "+schema+".payment pm on er1.id = pm.document_approval_container_id ")
                .append("LEFT JOIN "+schema+".report_state rs on rs.id =  (select rs2.id from "+schema+".report_state as rs2 where rs2.document_approval_container_id = er1.id and rs2.status = 0 ")
                .append("ORDER BY rs2.level limit 1) ").toString();
    }

    @Override
    public String whereQuery(long companyCode) {
        return new StringBuilder("WHERE er1.company_code = :companyCode ").toString();

    }

    @Override
    public String selectCountQueryForChecker(long companyCode, int checker, int expenseActionStatus, String userEmail) {
        return new StringBuilder("select count(er1.id) ").append(fromQueryForChecker())
                .append(whereQueryChecker(companyCode, checker, expenseActionStatus, userEmail)).toString();
    }

    @Override
    public String selectAllQueryForChecker(long companyCode, int checker, int expenseActionStatus, String userEmail) {
        StringBuilder queryString = new StringBuilder();
        return queryString.append(selectQueryQueue()).append(fromQueryForChecker())
                .append(whereQueryChecker(companyCode, checker, expenseActionStatus, userEmail)).toString();
    }

    public String fromQueryForChecker() {
        String schema = TenantContext.getCurrentSchema();
        return new StringBuilder(
                " from "+schema+".report_state rs inner join "+schema+".document_approval_container er1 ON (rs.document_approval_container_id = er1.id and rs.level = er1.action_level) ")
                .append("INNER JOIN "+schema+".document_metadata em on er1.document_metadata_id = em.id ")
                .append("INNER JOIN "+schema+".document doc on er1.id = doc.document_approval_container_id ")
                .append("INNER JOIN "+schema+".document_subgroup sg on sg.id = doc.document_subgroup_id ")
                .append("LEFT JOIN "+schema+".payment pm on er1.id = pm.document_approval_container_id ")
                .toString();
    }

    public String whereQueryChecker(long companyCode, int checker, int expenseActionStatus, String userEmail) {
        return new StringBuilder("WHERE rs.company_code = \'" + companyCode + "\' ")
                .append("and er1.report_status IN (:reportStatus) ").append("and rs.channel = \'" + checker + "\' ")
                .append("and rs.status = \'" + expenseActionStatus + "\' ")
                .append("and rs.approver = \'" + userEmail + "\' ").toString();

    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Object[]> getCheckerQueueData(String queryString, List<Integer> reportStatus, int pageNumber,
                                              int numberOfRecords, String sortBy, String sort) {
        List<Object[]> list = new ArrayList<>();

        if (null != sortBy) {
            queryString += " ORDER BY " + sortBy + " " + sort;
            queryString += ",  er1.id ASC ";
        } else {
            queryString += " ORDER BY er1.id ASC ";
        }
        int startPosition = pageNumber * numberOfRecords;

        list = em.createNativeQuery(queryString).setParameter("reportStatus", reportStatus)
                .setFirstResult(startPosition).setMaxResults(numberOfRecords).getResultList();
        return list;
    }

    @Override
    public Integer getCheckerTotalElements(String query, List<Integer> reportStatus) {

        return ((Long) em.createNativeQuery(query).setParameter("reportStatus", reportStatus).getSingleResult())
                .intValue();

    }

    @Override
    public String selectCountQueryForUser(long companyCode, long userId) {
        return new StringBuilder("select count(er1.id) ").append(fromQueryForUser())
                .append(whereQueryUser(companyCode, userId)).toString();
    }

    @Override
    public String selectAllQueryForUser(long companyCode, long userId) {
        StringBuilder queryString = new StringBuilder();
        return queryString.append(
                        "select case when report_status=1 then rs.approver_employee_code end as approver_employee_code, ")
                .append("case when report_status=1 then rs.approver_first_name end as approver_first_name, ")
                .append("rs.level, case when report_status=1 then rs.approver_last_name end as approver_last_name, ")
                .append("er1.id, er1.document_identifier, ")
                .append("er1.created_date, er1.submit_date, er1.start_date, er1.end_date, er1.approval_container_title, er1.description, er1.purpose, ")
                .append("er1.approval_container_claim_amount, er1.report_status, er1.action_level, er1.contains_deviation, er1.deviation_remarks, ")
                .append("er1.approval_container_sgst_amount, er1.approval_container_cgst_amount, er1.approval_container_igst_amount, er1.approval_container_taxable_amount, er1.first_name, ")
                .append("er1.middle_name, er1.last_name , er1.employee_email, er1.employee_code, er1.employee_grade, er1.employee_system_id, ")
                .append("er1.expense_type, er1.document_metadata_id, em.document_group, em.document_type, er1.send_back_remarks, er1.reject_remarks, ")
                .append("er1.delegation_remarks, er1.default_approver_remarks, er1.contains_sent_back, er1.gl_posting_date, er1.payment_date ")
                .append(fromQueryForUser()).append(whereQueryUser(companyCode, userId)).toString();
    }

    public String fromQueryForUser() {
        String schema = TenantContext.getCurrentSchema();
        return new StringBuilder("FROM "+schema+".document_approval_container er1 ")
                .append("INNER JOIN "+schema+".document_metadata em on er1.document_metadata_id = em.id ")
                .append("LEFT JOIN "+schema+".report_state rs on rs.id =  (select rs2.id from "+schema+".report_state as rs2 where rs2.document_approval_container_id = er1.id and rs2.status = 0 ")
                .append("ORDER BY rs2.level limit 1) ").toString();
    }

    public String whereQueryUser(long companyCode, long userId) {
        return new StringBuilder("WHERE er1.company_code = \'" + companyCode + "\' ")
                .append("and er1.creating_user_id = \'" + userId + "\' ").toString();

    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Object[]> getUserQueueData(String queryString, int pageNumber, int numberOfRecords, String sortBy,
                                           String sort) {
        List<Object[]> list = new ArrayList<>();
        if (null != sortBy) {
            queryString += " ORDER BY " + sortBy + " " + sort;
            queryString += ", er1.id DESC ";
        } else {
            queryString += " ORDER BY er1.id DESC ";
        }
        int startPosition = pageNumber * numberOfRecords;

        list = em.createNativeQuery(queryString).setFirstResult(startPosition).setMaxResults(numberOfRecords)
                .getResultList();
        return list;
    }
}