package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.MatchConfigurationData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IMatchConfigurationRepository extends JpaRepository<MatchConfigurationData, Long> {
    
    // Optimized query with fetch join to reduce N+1 problem
    @Query("SELECT m FROM MatchConfigurationData m " +
           "LEFT JOIN FETCH m.lookupPerformMatchAt " +
           "LEFT JOIN FETCH m.lookupDefaultOperation " +
           "WHERE m.companyCode = :companyCode")
    List<MatchConfigurationData> findByCompanyCode(@Param("companyCode") Long companyCode);
    
    // Optimized query for finding by ID with eager loading
    @Query("SELECT m FROM MatchConfigurationData m " +
           "LEFT JOIN FETCH m.lookupPerformMatchAt " +
           "LEFT JOIN FETCH m.lookupDefaultOperation " +
           "WHERE m.id = :id")
    Optional<MatchConfigurationData> findByIdWithDetails(@Param("id") Long id);
    
    // Query to find by criteria name for a specific company
    @Query("SELECT m FROM MatchConfigurationData m " +
           "WHERE m.companyCode = :companyCode " +
           "AND m.criteriaName = :criteriaName")
    List<MatchConfigurationData> findByCompanyCodeAndCriteriaName(
            @Param("companyCode") Long companyCode,
            @Param("criteriaName") String criteriaName);

    // Query to find by is2WayMatch for a specific company
    @Query("SELECT m FROM MatchConfigurationData m " +
           "LEFT JOIN FETCH m.lookupPerformMatchAt " +
           "LEFT JOIN FETCH m.lookupDefaultOperation " +
           "WHERE m.companyCode = :companyCode " +
           "AND m.is2WayMatch = :is2WayMatch")
    List<MatchConfigurationData> findByCompanyCodeAndIs2WayMatch(
            @Param("companyCode") Long companyCode,
            @Param("is2WayMatch") Boolean is2WayMatch);
}