package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.Company;
import in.taxgenie.pay_expense_pvv.entities.CompanyMaster;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

public interface ICompanyMasterRepository extends JpaRepository<CompanyMaster, Integer> {
    List<CompanyMaster> findByPanNumberIn(Set<String> panNumbers);
}
