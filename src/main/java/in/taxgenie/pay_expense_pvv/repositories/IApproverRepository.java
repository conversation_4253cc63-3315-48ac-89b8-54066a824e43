package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.Approver;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IApproverRepository extends JpaRepository<Approver, Long> {
    @Query("select count(a) from Approver a where a.companyCode = ?1 and a.approvalMatcher = ?2 and a.approvalMatchValue = ?3 and a.approvalTitle = ?4 and a.isFrozen = false and a.id <> ?5")
    long getCountByCriteria(long companyCode, String approvalMatcher, String approvalMatchValue, String approvalTitle, long id);

    Optional<Approver> findApproverByCompanyCodeAndId(long companyCode, long id);

    Optional<Approver> findByCompanyCodeAndApprover(long companyCode, String approverEmail);

    @Query("select a from Approver a where a.companyCode = ?1 and a.approvalMatcher = ?2 and a.approvalMatchValue = ?3 and a.approvalTitle = ?4 and a.isFrozen = false")
    Optional<Approver> findApproverByCriteria(long companyCode, String approvalMatcher, String approvalMatchValue, String approvalTitle);

    @Query("select a from Approver a where a.companyCode = ?1 and a.approvalMatcher = ?2 and a.approvalTitle = ?3 and a.isFrozen = false")
    Optional<Approver> findSingularApproverByCriteria(long companyCode, String approvalMatcher, String approvalTitle);


    @Query("select a from Approver a where a.companyCode = ?1 and upper(a.approver) = upper(?2)")
    Optional<Approver> getByEmail(long companyCode, String approver);



    List<Approver> findAllByCompanyCode(long companyCode);
}
