package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.ExpenseActionStatus;
import in.taxgenie.pay_expense_pvv.entities.ReportState;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.entities.StateChannel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IReportStateRepository extends JpaRepository<ReportState, Long> {
    Optional<ReportState> findByCompanyCodeAndIdAndSaveTime(long companyCode, long id, long saveTime);

    @Query("select e from ReportState e where e.companyCode = ?1 and upper(e.approver) = upper(?2) and e.status = ?3 and e.isDelegated = false and e.documentApprovalContainer.reportStatus in ?4")
    List<ReportState> findStatesByOriginalApproverActionStatus(long companyCode, String approver, ExpenseActionStatus status, List<ReportStatus> statusList);

    @Query("select e from ReportState e where e.companyCode = ?1 and upper(e.approver) = upper(?2) and e.documentApprovalContainer.reportStatus = ?3 and e.isDelegated = true")
    List<ReportState> findStatesByDelegatedApproverReportState(long companyCode, String delegatedApprover, ReportStatus status);

    @Query("select e from ReportState e where e.companyCode = ?1 and e.documentApprovalContainerId = ?2 and e.documentApprovalContainer.reportStatus in ?3 order by e.updatedTimestamp")
    List<ReportState> findByDocumentApprovalContainerIdSorted(long companyCode, long documentApprovalContainerId, List<ReportStatus> statusList);

    @Query("select e from ReportState e where e.companyCode = ?1 and e.documentApprovalContainer.reportStatus in ?2 and e.channel = ?3 and e.status = ?4 and e.approver = ?5 and e.level = e.documentApprovalContainer.actionLevel order by e.documentApprovalContainer.submitDate")
    List<ReportState> getApproverQueue(long companyCode, List<ReportStatus> statusList, StateChannel channel, ExpenseActionStatus status, String approverEmail);

    Optional<ReportState> findFirstByCompanyCodeAndDocumentApprovalContainerIdAndStatusOrderByLevel(long companyCode, long documentId, ExpenseActionStatus status);

    Optional<ReportState> findTopByCompanyCodeAndDocumentApprovalContainerIdAndLevelAndStatus(long companyCode, long documentId, int level, ExpenseActionStatus status);

    @Query("select e from ReportState e where e.companyCode = ?1 and e.documentApprovalContainerId = ?2 order by e.level")
    List<ReportState> getStatesByDocumentApprovalContainer(long companyCode, long documentApprovalContainerId);

    @Query("select e from ReportState e where e.companyCode = ?1 and e.documentApprovalContainerId = ?2 and e.status in ?3 order by e.level")
    List<ReportState> getActionedStatesByDocumentApprovalContainer(long companyCode, long documentApprovalContainerId, List<ExpenseActionStatus> statusList);

    Optional<ReportState> findFirstByCompanyCodeAndDocumentApprovalContainerIdAndStatusOrderByLevelDesc(long companyCode, long documentApprovalContainerId, ExpenseActionStatus status);

    @Query("select rs from ReportState rs where rs.companyCode = ?1 and rs.documentApprovalContainerId = ?2 and rs.status = ?3 order by rs.updatedTimestamp desc")
    Optional<List<ReportState>> getStateDetailsByupdatedTimeStampDesc(long companyCode, long documentApprovalContainerId, ExpenseActionStatus status);

    @Query("select rs from ReportState rs where rs.companyCode = ?1 and rs.documentApprovalContainerId = ?2 and rs.status = ?3 order by rs.updatedTimestamp asc")
    Optional<List<ReportState>> getStateDetailsByupdatedTimeStampAsc(long companyCode, long documentApprovalContainerId, ExpenseActionStatus status);
}
