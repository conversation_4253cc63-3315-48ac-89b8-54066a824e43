package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.LookupData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ILookupRepository extends JpaRepository<LookupData, Integer> {
    List<LookupData> findByType(String type);

    Optional<LookupData>  findFirstByValue(String value);

    @Query("select ld2 from LookupData ld INNER JOIN LookupData ld2 on ld2.value = ld.type where ld.value = ?1 ")
    List<LookupData> findByValue(String value);

    Optional<LookupData> findByTypeAndValue(String type, String value);

    LookupData findByAttribute(String value);

    LookupData findByLookupDataCode(UUID value);

    Optional<LookupData> findByIdAndType(Integer typeId, String type);
}
