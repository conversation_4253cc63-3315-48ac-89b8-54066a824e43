package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.LineItemDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ILineItemDetailsRepository extends JpaRepository<LineItemDetails, Integer> {

    @Query("SELECT ii FROM LineItemDetails ii WHERE ii.invoiceHeader.invoiceHeaderId = :invoiceHeaderId")
    List<LineItemDetails> findByInvoiceHeaderInvoiceHeaderId(long invoiceHeaderId);
}
