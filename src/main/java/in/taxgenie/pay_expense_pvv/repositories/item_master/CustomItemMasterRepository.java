package in.taxgenie.pay_expense_pvv.repositories.item_master;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.core.types.ConstructorExpression;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Wildcard;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQueryFactory;
import in.taxgenie.pay_expense_pvv.entities.QItemMaster;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.item_master.ItemMasterQueueViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import jakarta.persistence.EntityManager;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@Component
public class CustomItemMasterRepository {

    private static final QItemMaster itemMaster = QItemMaster.itemMaster;
    private final JPAQueryFactory queryFactory;
    private final Logger logger;


    public CustomItemMasterRepository(EntityManager em) {
        this.queryFactory = new JPAQueryFactory(em);
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public Page<ItemMasterQueueViewModel> getItemMasterQueue(long companyCode, QueueFilterViewModel filterValues, Pageable pageable) {

        BooleanBuilder builder = new BooleanBuilder().and(itemMaster.companyCode.eq(companyCode).and(itemMaster.status.eq(true)));

        ConstructorExpression<ItemMasterQueueViewModel> queueConstructorExpression =
                Projections.constructor(ItemMasterQueueViewModel.class,
                        itemMaster.itemCode, itemMaster.name, itemMaster.type,
                        itemMaster.unitOfMeasure, itemMaster.description,
                        itemMaster.hsn, itemMaster.hsnOrSac, itemMaster.gstRate, itemMaster.rate, itemMaster.quantity, itemMaster.itemUuid, itemMaster.status);

        // With requested filters
        createFilterQuery(filterValues, builder);

        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);

        QueryResults<ItemMasterQueueViewModel> results = queryFactory.selectDistinct(queueConstructorExpression)
                .from(itemMaster)
                .where(builder)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();

        // 2. Count query with DISTINCT fields // distinct should work on itemCode
        // and queryDSL getTotal should work but the data with fields in select is not returning exact count when pnly distinct item code is used
        List<ItemMasterQueueViewModel> resultsList = queryFactory.selectDistinct(queueConstructorExpression)
                .from(itemMaster)
                .where(builder)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .fetchResults().getResults();

        return new PageImpl<>(results.getResults(), pageable, resultsList.size());
    }


    private List<OrderSpecifier<?>> createStandardOrderSpecifier(Pageable pageable) {
        List<OrderSpecifier<?>> defaultOrderSpecifier = pageable.getSort().stream()
                .map(order -> {
                    return itemMaster.itemCode.asc();

                }).collect(Collectors.toList());
        defaultOrderSpecifier.add(itemMaster.itemCode.asc());
        return defaultOrderSpecifier;
    }

    private void createFilterQuery(QueueFilterViewModel filterValues, BooleanBuilder builder) {
        Map<String, List<String>> filters = filterValues.getSearchCriteria() == null ? new HashMap<>() : filterValues.getSearchCriteria();
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            BooleanBuilder innerBuilder = new BooleanBuilder();
            switch (searchCriteria) {
                case StringConstants.ITEM_TYPE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(itemMaster.type.eq(ItemType.fromString(caseInsensitive)));
                        }
                    });
                    break;
                case StringConstants.ITEM_HSN_OR_SAC:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(itemMaster.hsnOrSac.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;

                case StringConstants.ITEM_DETAILS:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(itemMaster.name.toLowerCase().contains(value.toLowerCase()))
                                    .or(itemMaster.type.eq(ItemType.fromString(value.toLowerCase())));
                        }
                    });
                    break;

                case StringConstants.ITEM_NAME:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(itemMaster.name.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;

                case StringConstants.ITEM_UNIT_OF_MEASURE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(itemMaster.unitOfMeasure.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;

                case StringConstants.ITEM_DESCRIPTION:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(itemMaster.description.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;

                case StringConstants.ITEM_CODE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(itemMaster.itemCode.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;
                case StringConstants.ITEM_ALL:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(itemMaster.itemCode.toLowerCase().contains(value.toLowerCase()))
                                    .or(itemMaster.hsnOrSac.toLowerCase().contains(value.toLowerCase()))
                                    .or(itemMaster.name.toLowerCase().contains(value.toLowerCase()))
                                    .or(itemMaster.type.eq(ItemType.fromString(value.toLowerCase())))
                                    .or(itemMaster.unitOfMeasure.toLowerCase().contains(value.toLowerCase()))
                                    .or(itemMaster.description.toLowerCase().contains(value.toLowerCase()))
                            ;
                        }
                    });
                    break;
                default:
            }
            builder.and(innerBuilder);
        }
    }
    private static boolean isNullOrEmpty(String value) {
        return value == null || value.isEmpty();
    }
}
