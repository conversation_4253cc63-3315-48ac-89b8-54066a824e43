package in.taxgenie.pay_expense_pvv.repositories.documents;

import in.taxgenie.pay_expense_pvv.entities.DocumentStorage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IDocumentStorageRepository extends JpaRepository<DocumentStorage, Long> {
    @Modifying
    @Query("UPDATE DocumentStorage ds SET ds.documentId = :documentId WHERE ds.id IN :ids")
    void updateDocumentIds(@Param("documentId") Long documentId, @Param("ids") List<Long> ids);

    List<DocumentStorage> findByEntityIdAndCategoryId(Long entityId, Integer categoryId);

    Optional<DocumentStorage> findByCategoryIdAndEntityIdAndId(Integer categoryId, Long entityId, Long id);

    Optional<DocumentStorage> findByFileHash(String fileHash);

    Optional<DocumentStorage> findByFileHashAndCategoryIdAndEntityId(String fileHash, Integer categoryId, Long entityId);


//    @Query("SELECT COUNT(d) FROM DocumentStorage d WHERE d.entityId = :entityId AND d.categoryId =: categoryId")
//    Long countDocumentsByEntityIdAndCategoryId(@Param("entityId") Long entityId, @Param("categoryId") Integer categoryId);

}
