package in.taxgenie.pay_expense_pvv.repositories.invoice;

import in.taxgenie.pay_expense_pvv.entities.invoice.InvoiceItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface IInvoiceItemRepository extends JpaRepository<InvoiceItem, Long> {
    List<InvoiceItem> findByInvoiceHeaderId(long invoiceHeaderId);

    @Query("SELECT ii FROM InvoiceItem ii WHERE ii.invoiceHeader.invoiceHeaderId = :invoiceHeaderId")
    List<InvoiceItem> findByInvoiceHeaderInvoiceHeaderId(Long invoiceHeaderId);
}
