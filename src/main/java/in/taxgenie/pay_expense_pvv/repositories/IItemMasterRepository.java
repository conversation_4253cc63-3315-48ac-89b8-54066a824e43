package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.ItemMaster;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IItemMasterRepository extends JpaRepository<ItemMaster, Long> {


    Optional<ItemMaster> findByIdAndCompanyIdAndVendorId(long id, int companyId, long vendorId);
    List<ItemMaster> findByType(ItemType type);

    Optional<ItemMaster> findByIdAndCompanyId(long id, long companyId);

    Optional<ItemMaster> findByIdAndCompanyCode(long id, long companyCode);

    Optional<ItemMaster> findByIdAndCompanyIdAndStatus(long id, long companyId, boolean status);

    List<ItemMaster> findByCompanyIdAndTypeAndVendorId(long companyCode, ItemType type, long vendorId);

    List<ItemMaster> findByCompanyCodeAndTypeAndVendorIdAndStatus(long companyCode, ItemType type, long vendorId, boolean status);

    List<ItemMaster> findByTypeAndCompanyIdAndStatus(ItemType itemType, Integer companyCode, boolean status);

    List<ItemMaster> findByTypeAndCompanyCodeAndStatusIsTrue(ItemType itemType, Integer companyCode);

    List<ItemMaster> findByItemCodeAndCompanyCodeAndStatus(String itemCode, long companyCode,boolean status);

    List<ItemMaster> findByItemUuidAndCompanyCodeAndStatus(String itemUuid, long companyCode,boolean status);

    List<ItemMaster> findByItemCodeAndCompanyCode(String itemCode, long companyCode);

    Optional<ItemMaster> findFirstByItemCodeAndCompanyCodeAndStatus(String itemCode, long companyCode, boolean status);

    Optional<ItemMaster> findByItemCodeAndVendorIdAndCompanyCode(String itemCode, Long vendorId, long companyCode);

    @Query("SELECT i FROM ItemMaster i WHERE i.type = :type AND (i.hsn = :hsn OR i.hsnOrSac = :hsn) AND i.vendorId = :vendorId")
    Optional<ItemMaster> findByTypeAndHsnOrSacAndVendorId(@Param("type") ItemType type, @Param("hsn") String hsn, @Param("vendorId") Long vendorId);


}
