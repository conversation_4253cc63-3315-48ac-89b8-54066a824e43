package in.taxgenie.pay_expense_pvv.repositories.rbac;

import in.taxgenie.pay_expense_pvv.entities.rbac.KeyValueMapping;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupViewModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface IKeyValueMappingRepository extends JpaRepository<KeyValueMapping, Long> {

    @Query("SELECT DISTINCT k.label FROM KeyValueMapping k WHERE k.companyCode = :companyCode AND k.key = :key")
    List<String> findDistinctLabelsByCompanyCodeAndKey(@Param("companyCode") long companyCode, @Param("key") String key);

    @Query("SELECT DISTINCT k.value FROM KeyValueMapping k WHERE k.companyCode = :companyCode AND k.key = :key AND k.value IS NOT NULL AND k.value != ''")
    List<String> findDistinctValuesByCompanyCodeAndKey(@Param("companyCode") long companyCode, @Param("key") String key);

    @Query("SELECT DISTINCT k.key FROM KeyValueMapping k WHERE k.companyCode = :companyCode")
    List<String> findDistinctKeysByCompanyCode(@Param("companyCode") long companyCode);

    @Query("SELECT k FROM KeyValueMapping k WHERE k.companyCode = :companyCode AND k.key = :key")
    List<KeyValueMapping> findByCompanyCodeAndKey(@Param("companyCode") long companyCode, @Param("key") String key);

    Optional<KeyValueMapping> findByCompanyCodeAndId(long companyCode, long id);

    List<KeyValueMapping> findByCompanyCodeAndKeyAndLabelIn(@Param("companyCode") long companyCode, @Param("key") String key, @Param("labels") Set<String> labels);

    Optional<KeyValueMapping> findByKeyAndValueAndCompanyCode(String key, String value, Long companyCode);

    @Query("SELECT COUNT(kvm) FROM KeyValueMapping kvm WHERE kvm.key = :key AND kvm.value = :value AND kvm.label = :label")
    long countByKeyAndValueAndLabel(@Param("key") String key, @Param("value") String value, @Param("label") String label);

    @Query("SELECT k.label FROM KeyValueMapping k WHERE k.key = :key AND k.value = :value and k.companyCode = :companyCode ")
    String findLabelByKeyAndValueAndCompanyCode(@Param("key") String key, @Param("value") String value, @Param("companyCode") long companyCode);

    @Query("SELECT kvm.value FROM KeyValueMapping kvm WHERE kvm.companyCode = :companyCode AND kvm.label IS NULL OR kvm.label = '' AND kvm.key = :key")
    List<String> findValuesByCompanyCodeAndKeyWhereLabelIsNotAssigned(@Param("companyCode") long companyCode, @Param("key") String key);

    @Query("SELECT k.key, k.value FROM KeyValueMapping k WHERE k.companyCode = :companyCode")
    List<Object[]> findAllKeysAndValuesByCompanyCode(long companyCode);

    @Query("SELECT k.key, k.value FROM KeyValueMapping k WHERE k.companyCode = :companyCode AND k.key IN :keys")
    List<Object[]> findValuesByCompanyCodeAndKeys(long companyCode, List<String> keys);

}
