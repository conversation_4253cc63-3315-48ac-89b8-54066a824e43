package in.taxgenie.pay_expense_pvv.repositories.rbac;

import in.taxgenie.pay_expense_pvv.entities.budget.BudgetMappingMaster;
import in.taxgenie.pay_expense_pvv.entities.rbac.OrgHierarchyStructure;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IOrgHierarchyRepository extends JpaRepository<OrgHierarchyStructure, Long> {
    @Query("select o from OrgHierarchyStructure o where o.companyCode = ?1 and o.parent is null and o.isActive = true")
    List<OrgHierarchyStructure> findRootNodeForStructure(long companyCode);

    @Query("SELECT CASE WHEN COUNT(ohs) > 0 THEN TRUE ELSE FALSE END FROM OrgHierarchyStructure ohs WHERE ohs.displayName = :label")
    boolean existsByLabel(@Param("label") String label);

    @Query("select o from OrgHierarchyStructure o where o.companyCode = ?1 and o.key = ?2 and o.parent is null and o.isActive = true")
    Optional<OrgHierarchyStructure> findRootNodeForStructureByKey(long companyCode, String key);

    @Query("SELECT DISTINCT o.key FROM OrgHierarchyStructure o WHERE o.companyCode = :companyCode")
    String findDistinctKeyByCompanyCode(@Param("companyCode") Long companyCode);
}
