package in.taxgenie.pay_expense_pvv.repositories.rbac;

import in.taxgenie.pay_expense_pvv.entities.rbac.MappingValuesLabelsHierarchy;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface IMappingValuesLabelsHierarchyRepository extends JpaRepository<MappingValuesLabelsHierarchy, Long> {
    @Modifying
    @Query("DELETE FROM MappingValuesLabelsHierarchy WHERE label IN :labels")
    void deleteByLabels(@Param("labels") Set<String> labels);

    @Query("SELECT m.value FROM MappingValuesLabelsHierarchy m WHERE m.label = :label AND m.companyCode = :companyCode")
    List<String> findValuesByLabelAndCompanyCode(@Param("label") String label, @Param("companyCode") Long companyCode);

    @Modifying
    @Query("DELETE FROM MappingValuesLabelsHierarchy WHERE companyCode = :companyCode")
    void deleteByCompanyCode(Long companyCode);
}
