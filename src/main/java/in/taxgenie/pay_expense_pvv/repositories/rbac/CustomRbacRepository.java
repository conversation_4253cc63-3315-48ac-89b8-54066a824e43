package in.taxgenie.pay_expense_pvv.repositories.rbac;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.core.types.ConstructorExpression;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQueryFactory;
import in.taxgenie.pay_expense_pvv.entities.QCompany;
import in.taxgenie.pay_expense_pvv.entities.QUsers;
import in.taxgenie.pay_expense_pvv.entities.rbac.QKeyValueMapping;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.KeyValueMappingViewModel;
import jakarta.persistence.EntityManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class CustomRbacRepository {
    private final JPAQueryFactory queryFactory;
    private final Logger logger;
    private static final QCompany company = QCompany.company;
    private static final QKeyValueMapping kvm = QKeyValueMapping.keyValueMapping;
    QUsers creatingUser = new QUsers("creatingUser");
    QUsers updatingUser = new QUsers("updatingUser");

    public CustomRbacRepository(EntityManager em) {
        this.queryFactory = new JPAQueryFactory(em);
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public Page<KeyValueMappingViewModel> getKeyValueMappingQueue(long companyCode, QueueFilterViewModel filterValues, Pageable pageable) {
        BooleanBuilder builder = initBuilder(companyCode);
        createFilterQuery(filterValues, builder);
        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);

        ConstructorExpression<KeyValueMappingViewModel> ce =
                Projections.constructor(KeyValueMappingViewModel.class, kvm.id, kvm.key, kvm.value, kvm.label,
                        kvm.createdTimestamp, kvm.updatedTimestamp, kvm.source);

        QueryResults<KeyValueMappingViewModel> results = queryFactory.select(ce)
                .from(kvm)
                .where(builder)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    private BooleanBuilder initBuilder(long companyCode) {
        return new BooleanBuilder();
    }

    private void createFilterQuery(QueueFilterViewModel filterValues, BooleanBuilder builder) {
        Map<String, List<String>> filters = filterValues.getSearchCriteria() == null ? new HashMap<>() : filterValues.getSearchCriteria();
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            List<String> values = entry.getValue();

            BooleanBuilder innerBuilder = new BooleanBuilder();

            switch (searchCriteria) {

                case StringConstants.SupplierMaster.VENDOR_CODE:
//                    applyOrCondition(innerBuilder, values, companyMaster.vendorCode::containsIgnoreCase);
                    break;


                default:
                    logger.warn("Unhandled search criteria: " + searchCriteria);
                    break;
            }

            builder.and(innerBuilder);
        }
    }

    private void applyOrCondition(BooleanBuilder innerBuilder, List<String> values, Function<String, BooleanExpression> expressionFunction) {
        for (String value : values) {
            if (!isNullOrEmpty(value)) {
                BooleanExpression expression = expressionFunction.apply(value);
                if (expression != null) {
                    innerBuilder.or(expression);
                }
            }
        }
    }

    private List<OrderSpecifier<?>> createStandardOrderSpecifier(org.springframework.data.domain.Pageable pageable) {
        List<OrderSpecifier<?>> defaultOrderSpecifier = pageable.getSort().stream()
                .map(order -> switch (order.getProperty()) {
                    case "key" ->
                            order.getDirection().isAscending() ? kvm.key.asc() : kvm.key.desc();
                    case "value" ->
                            order.getDirection().isAscending() ? kvm.value.asc() : kvm.value.desc();
                    case "label" ->
                            order.getDirection().isAscending() ? kvm.label.asc() : kvm.label.desc();
                    default -> kvm.id.desc();
                }).collect(Collectors.toList());
        return defaultOrderSpecifier;
    }

    private static boolean isNullOrEmpty(String value) {
        return value == null || value.isEmpty();
    }
}
