package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.EntitlementDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IEntitlementDetailsRepository extends JpaRepository<EntitlementDetails, Long> {
    List<EntitlementDetails> findByCompanyCodeAndJobBand(long companyCode, String band);
    Optional<EntitlementDetails> findByCompanyCodeAndJobBandAndEntitlementHeader(long companyCode, String band, String header);
}
