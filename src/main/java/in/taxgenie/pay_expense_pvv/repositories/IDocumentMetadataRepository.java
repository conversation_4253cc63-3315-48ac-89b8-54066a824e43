package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.DocumentMetadata;
import in.taxgenie.pay_expense_pvv.entities.ActionType;
import in.taxgenie.pay_expense_pvv.entities.budget.ExpenseType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface IDocumentMetadataRepository extends JpaRepository<DocumentMetadata, Long> {
    Optional<DocumentMetadata> findByCompanyCodeAndId(long companyCode, long id);
    List<DocumentMetadata> findByCompanyCodeOrderByIdDesc(long companyCode);

    List<DocumentMetadata> findByCompanyCodeAndDocumentCategoryIdOrderByIdDesc(long companyCode, long categoryId);

    List<DocumentMetadata> findByCompanyCodeAndDocumentCategoryIdAndDocumentTypeContainingOrderByIdDesc(long companyCode, long categoryId, String action);
    List<DocumentMetadata> findByCompanyCodeAndDocumentCategoryIdAndDocumentTypeContainingAndApplicableExpenseTypeAndIsFrozenFalseOrderByIdDesc(long companyCode, long categoryId, String action, ExpenseType expenseType);
    List<DocumentMetadata> findByCompanyCodeAndDocumentCategoryIdAndActionTypeAndApplicableExpenseTypeAndIsFrozenFalseOrderByIdDesc(long companyCode, long categoryId, ActionType actionType, ExpenseType expenseType);

    List<DocumentMetadata> findByCompanyCodeAndUuidAndActionTypeIn(long companyCode, String uuid, List<ActionType> actionTypes);

    @Query("select count(e) from DocumentMetadata e where e.companyCode = ?1 and upper(e.documentTypePrefix) = upper(?2) and upper(e.documentGroupPrefix) = upper(?3) ")
    long getCountByPrefixes(long companyCode, String documentTypePrefix, String documentGroupPrefix);

    @Query("select count(e) from DocumentMetadata e where e.companyCode = ?1 and upper(e.documentType) = upper(?2) and upper(e.documentGroup) = upper(?3)  and e.isFrozen = false")
    long getCountOfTypeAndGroup(long companyCode, String documentType, String documentGroup);

    Collection<DocumentMetadata> findByCompanyCodeAndDocumentCategoryIdAndApplicableExpenseTypeAndDocumentType(long companyCode, Integer budgetCategoryId, String expenseType, String documentType);

    @Query("SELECT m FROM DocumentMetadata m WHERE m.companyCode = :companyCode AND m.documentTypePrefix LIKE %:documentTypePrefix% AND m.documentGroupPrefix LIKE %:documentGroupPrefix%")
    List<DocumentMetadata> findByCompanyCodeAndDocumentTypePrefixAndDocumentGroupPrefix(
            @Param("companyCode") Long companyCode,
            @Param("documentTypePrefix") String documentTypePrefix,
            @Param("documentGroupPrefix") String documentGroupPrefix);
}
