package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.Users;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IUsersRepository extends JpaRepository<Users,Long> {
    Optional<Users> findByUserId(long userId);

    List<Users> findByCompanyCodeAndIdIn(Long companyCode, List<Integer> idList);
}
