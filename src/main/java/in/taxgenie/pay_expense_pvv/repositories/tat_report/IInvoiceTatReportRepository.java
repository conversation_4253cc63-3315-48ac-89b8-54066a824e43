package in.taxgenie.pay_expense_pvv.repositories.tat_report;

import in.taxgenie.pay_expense_pvv.entities.ApproverType;
import in.taxgenie.pay_expense_pvv.entities.InvoiceTatReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IInvoiceTatReportRepository extends JpaRepository<InvoiceTatReport, Long> {

    Optional<InvoiceTatReport> findTopByDocIdAndApproverTypeAndCompanyCodeOrderByIdDesc(
            Long docId,
            ApproverType approverType,
            Long companyCode
    );

    // Method for Uploader (Already provided)
    @Query(value = """
                WITH stat AS (
                                                           SELECT 
                                                               itr.approver_type,
                                                               itr.doc_id,
                                                               SUM(itr.tat_time_days) AS totaldays
                                                           FROM invoice_tat_report itr
                                                           JOIN document_approval_container dac ON dac.id = itr.doc_id
                                                           WHERE
                                                               itr.approver_type = :approverType
                                                               AND itr.company_code = :companyCode
                                                               AND (
                                                                               CAST(:metadataFilters AS jsonb) = '{}'::jsonb
                                                                               OR itr.metadata @> CAST(:metadataFilters AS jsonb)
                                                                   )
                                                           GROUP BY itr.approver_type, itr.doc_id
                                                       ),
                                                       agg AS (
                                                           SELECT
                                                               pa.ageing,
                                                               pa.indicator,
                                                               pa.lower_bound,
                                                               pa.upper_bound,
                                                               SUM(dac.approval_container_claim_amount) AS total_claim_amount,
                                                               COUNT(stat.doc_id) AS doc_count                                                  
                                                           FROM predefined_ageing pa
                                                           LEFT JOIN stat
                                                               ON stat.totaldays BETWEEN pa.lower_bound AND pa.upper_bound
                                                               AND pa.approver_type::varchar = :approverType
                                                           LEFT JOIN document_approval_container dac
                                                               ON dac.id = stat.doc_id
                                                           WHERE pa.approver_type::varchar = :approverType
                                                           GROUP BY pa.ageing, pa.lower_bound, pa.upper_bound,  pa.indicator
                                                       )
                                                       SELECT
                                                           ageing,
                                                           indicator,
                                                           doc_count,
                                                           total_claim_amount,
                                                           ROUND(100.0 * total_claim_amount / SUM(total_claim_amount) OVER (), 2) AS percentage_of_total
                                                       FROM agg
                                                       ORDER BY lower_bound;
            """, nativeQuery = true)
    List<Object[]> findInvoiceAgeingData(
            @Param("approverType") String approverType,
            @Param("metadataFilters") String metadataFilters,
            @Param("companyCode") long companyCode);


    @Query(value = """
                WITH docss AS (
                            SELECT doc_id
                            FROM invoice_tat_report
                            WHERE approver_type = 'PAYER'
                            and company_code=:companyCode  and
                            case when coalesce(cast(:metadataFilters as jsonb),'{}') <>'{}' then
                            metadata @> coalesce(cast(:metadataFilters as jsonb),
                          '{}') else 1=1 end
                        ),
                        doccount AS (
                            SELECT
                                doc_id,
                                SUM(tat_time_days) AS totaltat
                            FROM invoice_tat_report
                            WHERE doc_id IN (SELECT doc_id FROM docss)
                            GROUP BY doc_id
                        ),
                        agg_data AS (
                            SELECT
                                CASE
                                    WHEN dc.totaltat BETWEEN 0 AND 2 THEN '0-2 Days'
                                    WHEN dc.totaltat BETWEEN 3 AND 5 THEN '3-5 Days'
                                    WHEN dc.totaltat BETWEEN 6 AND 8 THEN '6-8 Days'
                                    WHEN dc.totaltat BETWEEN 9 AND 10 THEN '9-10 Days'
                                    ELSE '>10 Days'
                                END AS Aging,
                                CASE
                                    WHEN dc.totaltat BETWEEN 0 AND 2 THEN '#4Fcc92'
                                    WHEN dc.totaltat BETWEEN 3 AND 5 THEN '#FFC300'
                                    WHEN dc.totaltat BETWEEN 6 AND 8 THEN '#FGE5AE'
                                    WHEN dc.totaltat BETWEEN 9 AND 10 THEN '#FB5858'
                                    ELSE '#FF0000'
                                END AS Indicator,
                                COUNT(dc.doc_id) AS doc_count,
                                SUM(dac.approval_container_claim_amount) AS total_amount
                            FROM doccount dc
                            JOIN document_approval_container dac ON dac.id = dc.doc_id
                            GROUP BY Aging, Indicator
                        ),
                        buckets AS (
                            SELECT '0-2 Days' AS Aging, '#4Fcc92' AS Indicator UNION ALL
                            SELECT '3-5 Days', '#FFC300' UNION ALL
                            SELECT '6-8 Days', '#FGE5AE' UNION ALL
                            SELECT '9-10 Days', '#FB5858' UNION ALL
                            SELECT '>10 Days', '#FF0000'
                        )                  
                        SELECT
                            b.Aging,
                            b.Indicator,
                            COALESCE(a.doc_count, 0) AS doc_count,
                            COALESCE(a.total_amount, 0) AS "Total Invoice Amount",
                            ROUND(
                                COALESCE(a.total_amount, 0) / NULLIF(SUM(COALESCE(a.total_amount, 0)) OVER (), 0) * 100,
                                2
                            ) AS Mix
                        FROM buckets b
                        LEFT JOIN agg_data a
                            ON a.Aging = b.Aging AND a.Indicator = b.Indicator
                        ORDER BY
                            CASE
                                WHEN b.Aging = '0-2 Days' THEN 1
                                WHEN b.Aging = '3-5 Days' THEN 2
                                WHEN b.Aging = '6-8 Days' THEN 3
                                WHEN b.Aging = '9-10 Days' THEN 4
                                ELSE 5
                        END;
            """, nativeQuery = true)
    List<Object[]> findInvoiceAgeingDataForAll(
            @Param("metadataFilters") String metadataFilters,
            @Param("companyCode") long companyCode);


    @Query(value = """
                with tatmailroom as (select id, document_date, total_amount, company_code , NOW()::date - document_date::date as tatdays
                            from mailroom)
                            select
                            cfg.ageing ,cfg."indicator", count(pag.id),COALESCE(sum(total_amount),0) from  predefined_ageing cfg
                            left join tatmailroom pag on pag.tatdays between cfg.lower_bound and cfg.upper_bound
                            and pag.company_code=:companyCode
                            where cfg.approver_type ='MAILROOM'
                            group by cfg.ageing,cfg."indicator"
            """, nativeQuery = true)
    List<Object[]> findMailroomInvoiceAgeingData(
            @Param("companyCode") long companyCode);

    @Query(value = """
    WITH docss AS (
        SELECT doc_id
        FROM invoice_tat_report
        WHERE approver_type = :approverType
          AND company_code = :companyCode
            and
                       case when coalesce(cast(:metadataFiltersJson as jsonb),'{}') <>'{}' then
                       metadata @> coalesce(cast(:metadataFiltersJson as jsonb),
                     '{}') else 1=1 end
    )
    SELECT dac.document_identifier
    FROM invoice_tat_report itr
    JOIN document_approval_container dac ON dac.id = itr.doc_id
    WHERE itr.doc_id IN (
        SELECT doc_id FROM docss
    )
    GROUP BY dac.document_identifier
    HAVING SUM(tat_time_days) BETWEEN :minTat AND :maxTat
    """, nativeQuery = true)
    List<Object[]> getDocumentIdentifiersForIntervalAndApprovalTypeForAll(
            @Param("minTat") Integer minTat,
            @Param("maxTat") Integer maxTat,
            @Param("approverType") String approverType,
            @Param("metadataFiltersJson") String metadataFiltersJson,
            @Param("companyCode") Long companyCode
    );


}
