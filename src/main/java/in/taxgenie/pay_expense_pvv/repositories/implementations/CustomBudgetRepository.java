package in.taxgenie.pay_expense_pvv.repositories.implementations;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.core.types.ConstructorExpression;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQueryFactory;
import in.taxgenie.pay_expense_pvv.entities.QUsers;
import in.taxgenie.pay_expense_pvv.entities.budget.*;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.utils.budget.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels.BudgetSyncToBudgetMappingMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels.BudgetToBudgetMappingMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.budget.internalmodels.BudgetTotalsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.queue.BudgetRowViewModel;
import jakarta.persistence.EntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class CustomBudgetRepository {
    private final JPAQueryFactory queryFactory;
    private static final QBudgetStructureMaster structureMaster = QBudgetStructureMaster.budgetStructureMaster;
    private static final QBudgetMappingMaster bMap = QBudgetMappingMaster.budgetMappingMaster;
    private static final QBudget budget = QBudget.budget;
    private static final QBudgetFrequencyMapping frequencyMap = QBudgetFrequencyMapping.budgetFrequencyMapping;

    private static final QBudgetSyncMaster bSync = QBudgetSyncMaster.budgetSyncMaster;
    private static final QBudgetMaster bMaster = QBudgetMaster.budgetMaster;

    public CustomBudgetRepository(EntityManager em) {
        this.queryFactory = new JPAQueryFactory(em);
    }

    public List<BudgetToBudgetMappingMasterViewModel> findBudgetMappingMastersForBudget(long companyCode, long structureId) {
        ConstructorExpression<BudgetToBudgetMappingMasterViewModel> constructorExpression = Projections
                .constructor(BudgetToBudgetMappingMasterViewModel.class, budget.id, bMap.id);
        QueryResults<BudgetToBudgetMappingMasterViewModel> results = queryFactory.select(constructorExpression)
                .from(budget)
                .innerJoin(budget.budgetSyncMaster, bSync)
                .innerJoin(bSync.budgetMaster, bMaster)
                .innerJoin(bMap)
                .on(bMap.budgetMasterId.eq(bMaster.id))
                .where(
                        budget.companyCode.eq(companyCode)
                                .and(bMap.isActive.isTrue())
                                .and(bMap.budgetStructureMasterId.eq(structureId))
                )
                .orderBy(budget.id.asc())
                .fetchResults();
        return results.getResults();
    }

    private void addBudgetFilters(Map<String, List<String>> filters, BooleanBuilder builder) {
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            BooleanBuilder innerBuilder = new BooleanBuilder();

            switch (searchCriteria) {
                case StringConstants.BUDGET_STRUCTURE_NAME:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(structureMaster.structureName.toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.BUDGET_EXPENDITURE_TYPE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(structureMaster.documentMetadata.applicableExpenseType.stringValue().toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                default:
            }
            builder.and(innerBuilder);
        }
    }

    private void builderHaving(Map<String, List<String>> filters, BooleanBuilder builder) {
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            BooleanBuilder innerBuilder = new BooleanBuilder();

            switch (searchCriteria) {

                case StringConstants.BUDGET_AMOUNT:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            try {
                                innerBuilder.or(frequencyMap.total.sum().eq(new BigDecimal(value)));
                            } catch (Exception e){
                                throw new DomainInvariantException("Invalid characters in search.");
                            }
                        }
                    });
                    break;
                case StringConstants.BUDGET_BALANCE_AMOUNT:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            try {
                                innerBuilder.or(frequencyMap.treasury.sum().eq(new BigDecimal(value)));
                            } catch (Exception e){
                                throw new DomainInvariantException("Invalid characters in search.");
                            }

                        }
                    });
                    break;
                default:
            }
            builder.and(innerBuilder);
        }
    }


    private static boolean isNullOrEmpty(String value) {
        return value == null || value.isEmpty();
    }

    public Page<BudgetRowViewModel> getBudgetRowModelsForCompany(long companyCode, QueueFilterViewModel filterValues,  Pageable pageable) {
        Map<String, List<String>> filters = filterValues.getSearchCriteria() == null ? new HashMap<>() : filterValues.getSearchCriteria();

        QUsers createdByUser = new QUsers("createdByUser");
        QUsers updatedByUser = new QUsers("updatedByUser");

        BooleanBuilder builder = new BooleanBuilder()
                .and(structureMaster.companyCode.eq(companyCode))
                .and(structureMaster.isActive.isTrue());
        BooleanBuilder builderHaving = new BooleanBuilder();

        addBudgetFilters(filters, builder);
        builderHaving(filters, builderHaving);


        ConstructorExpression<BudgetRowViewModel> constructorExpression = Projections
                .constructor(BudgetRowViewModel.class,
                        structureMaster.id,
                        structureMaster.structureName,
                        frequencyMap.total.sum(),
                        frequencyMap.treasury.sum(),
                        frequencyMap.total.subtract(frequencyMap.treasury).sum(),
                        Expressions.stringTemplate("({0} || ' ' || {1})", createdByUser.firstName, createdByUser.lastName),
                        Expressions.stringTemplate("TO_CHAR({0}, 'YYYY-MM-DD')", structureMaster.createdTimestamp),
                        Expressions.stringTemplate("TO_CHAR({0}, 'HH24:MI:SS')", structureMaster.createdTimestamp),
                        Expressions.stringTemplate("({0} || ' ' || {1})", updatedByUser.firstName, updatedByUser.lastName),
                        Expressions.stringTemplate("TO_CHAR({0}, 'YYYY-MM-DD')", structureMaster.updatedTimestamp),
                        Expressions.stringTemplate("TO_CHAR({0}, 'HH24:MI:SS')", structureMaster.updatedTimestamp),
                        structureMaster.isApproved,
                        structureMaster.status,
                        structureMaster.documentMetadata.applicableExpenseType
                );

        QueryResults<BudgetRowViewModel> results = queryFactory.select(constructorExpression)
                .from(structureMaster)
                .leftJoin(budget)
                .on(
                        budget.budgetStructureMasterId.eq(structureMaster.id)
                                .and(budget.parent.isNull())
                                .and(budget.isActive.isTrue())
                )
                .leftJoin(budget.budgetFrequencyMappings, frequencyMap) // left join because user can save on any stage.
                .leftJoin(structureMaster.creatingUser, createdByUser)
                .leftJoin(structureMaster.updatingUser, updatedByUser)
                .where(builder)
                .having(builderHaving)
                .groupBy(
                        structureMaster.id,
                        structureMaster.structureName,
                        createdByUser.firstName,
                        createdByUser.lastName,
                        structureMaster.createdTimestamp,
                        updatedByUser.firstName,
                        updatedByUser.lastName,
                        structureMaster.updatedTimestamp,
                        structureMaster.documentMetadata.applicableExpenseType
                )
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .orderBy(List.of(structureMaster.id.desc()).toArray(new OrderSpecifier[0]))
                .fetchResults(); // Todo : convert from fetch results.

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    public BudgetTotalsViewModel getBudgetTotals(long companyCode, long structureId, long budgetId) {
            ConstructorExpression<BudgetTotalsViewModel> constructorExpression = Projections.constructor(BudgetTotalsViewModel.class,
                frequencyMap.total.sum(), frequencyMap.treasury.sum(), frequencyMap.softLocked.sum(), frequencyMap.locked.sum(),
                    frequencyMap.consumed.sum()
                );

        BooleanBuilder builder =  new BooleanBuilder();
        builder.and(frequencyMap.companyCode.eq(companyCode))
                .and(frequencyMap.budgetStructureMasterId.eq(structureId))
                .and(frequencyMap.budgetId.eq(budgetId))
                .and(budget.parent.isNull());

        List<BudgetTotalsViewModel> resultSet =  queryFactory.select(constructorExpression)
                .from(budget)
                .innerJoin(budget.budgetFrequencyMappings, frequencyMap)
                .where(builder)
                .groupBy(frequencyMap.budgetId)
                .fetch();

        if (resultSet.isEmpty()) {
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        } else if (resultSet.size() > 1) {
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        }

        return resultSet.get(0);
    }

    public BudgetTotalsViewModel getBudgetTotalsForBudgetNode(long companyCode, long budgetId) {
        ConstructorExpression<BudgetTotalsViewModel> constructorExpression = Projections.constructor(BudgetTotalsViewModel.class,
                frequencyMap.total.sum(), frequencyMap.treasury.sum(), frequencyMap.softLocked.sum(), frequencyMap.locked.sum(),
                frequencyMap.consumed.sum());

        BooleanBuilder builder =  new BooleanBuilder();
        builder.and(frequencyMap.companyCode.eq(companyCode))
                .and(frequencyMap.budgetId.eq(budgetId));

        List<BudgetTotalsViewModel> resultSet =  queryFactory.select(constructorExpression)
                .from(budget)
                .innerJoin(budget.budgetFrequencyMappings, frequencyMap)
                .where(builder)
                .groupBy(frequencyMap.budgetId)
                .fetch();

        if (resultSet.isEmpty()) {
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        } else if (resultSet.size() > 1) {
            throw new DomainInvariantException(StaticDataRegistry.BUDGET_GENERIC_ERROR);
        }

        return resultSet.get(0);
    }

    public List<BudgetSyncToBudgetMappingMasterViewModel> getBudgetSyncToMappingMasterMap(long companyId, List<Long> budgetSyncMasterIds) {
        ConstructorExpression<BudgetSyncToBudgetMappingMasterViewModel> constructorExpression = Projections.constructor(BudgetSyncToBudgetMappingMasterViewModel.class,
                bSync.id, bSync.description, bMaster.id, bMaster.name);

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(bSync.companyCode.eq(companyId))
                .and(bSync.id.in(budgetSyncMasterIds))
                .and(bSync.isActive.isTrue());

        return queryFactory.select(constructorExpression)
                .from(bSync)
                .innerJoin(bSync.budgetMaster, bMaster)
                .where(builder)
                .fetch();
    }

}
