package in.taxgenie.pay_expense_pvv.repositories.implementations;

import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.services.interfaces.ExpenseRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Repository("ExpenseRepository")
public class ExpenseRepositoryImpl implements ExpenseRepository {
	@Autowired
	EntityManager em;

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getByExpenseTypeForAllV2(long companyCode, String expenseTypeForSeletion, LocalDate fromDate,
			LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatus, String entity) {
		List<Object[]> list = new ArrayList<>();

//		String query = "select res1.*, res2.* from ( " + getSelectQuery() + " from expense_report er "
//				+ "inner join expense e on  (e.expense_report_id = er.id and e.company_code = \'" + companyCode + "\') "
//				+ "inner join expense_metadata em on em.id = er.expense_metadata_id "
//				+ "left join payment pt on pt.expense_report_id = er.id "
//				+ "left join report_state rs on rs.expense_report_id = (select rs2.expense_report_id from report_state rs2 "
//				+ "where rs2.expense_report_id = er.id and rs2.company_code = \'" + companyCode
//				+ "\' and ((rs2.status = 1 and er.report_status = 3) or rs2.status = 3) order by rs2.updated_timestamp desc limit 1) "
//				+ getWhereClauseAll(companyCode, expenseTypeForSeletion, fromDate, toDate, voucherStatus, revokeStatus,
//						entity)
//				+ " group by er.id,  er.employee_code order by er.employee_code)  res1 "
//				+ "INNER JOIN (select sum(e.claim_amount), e.expense_report_id "
//				+ "from expense_report er inner join expense e on (e.expense_report_id = er.id and e.company_code = \'"
//				+ companyCode + "\') " + "inner join expense_metadata em on em.id = er.expense_metadata_id "
//				+ getWhereClauseAll(companyCode, expenseTypeForSeletion, fromDate, toDate, voucherStatus, revokeStatus,
//						entity)
//				+ " group by er.id order by er.employee_code)  res2 " + "ON res1.id = res2.expense_report_id";

//		StoredProcedureQuery querySP = em.createStoredProcedureQuery("expense_report_all")
//				.registerStoredProcedureParameter(1, Long.class, ParameterMode.IN).setParameter(1, companyCode)
//				.registerStoredProcedureParameter(2, String.class, ParameterMode.IN)
//				.setParameter(2, expenseTypeForSeletion)
//				.registerStoredProcedureParameter(3, LocalDate.class, ParameterMode.IN).setParameter(3, fromDate)
//				.registerStoredProcedureParameter(4, LocalDate.class, ParameterMode.IN).setParameter(4, toDate)
//				.registerStoredProcedureParameter(5, Integer.class, ParameterMode.IN)
//				.setParameter(5, ReportStatus.valueOf(voucherStatus.name()).ordinal())
//				.registerStoredProcedureParameter(6, Integer.class, ParameterMode.IN)
//				.setParameter(6, ReportStatus.valueOf(revokeStatus.name()).ordinal())
//				.registerStoredProcedureParameter(7, String.class, ParameterMode.IN).setParameter(7, entity);

		// querySP.execute();

		// way -2
		Query queryS = em.createNativeQuery("{call expense_report_all(?,?,?,?,?,?,?)}").setParameter(1, companyCode)
				.setParameter(2, expenseTypeForSeletion).setParameter(3, fromDate).setParameter(4, toDate)
				.setParameter(5, ReportStatus.valueOf(voucherStatus.name()).ordinal())
				.setParameter(6, ReportStatus.valueOf(revokeStatus.name()).ordinal()).setParameter(7, entity);

		// list = em.createNativeQuery(query).getResultList();
		return list = queryS.getResultList();
	}

	private String getWhereClauseAll(long companyCode, String expenseTypeForSeletion, LocalDate fromDate,
			LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatus, String entity) {
		String query = " where er.company_code = \'" + companyCode + "\' " + "and em.expense_type like \'"
				+ expenseTypeForSeletion + "\'  " + "and (er.created_date between \'" + fromDate + "\'  and \'" + toDate
				+ "\' ) and er.report_status not in (\'" + ReportStatus.valueOf(voucherStatus.name()).ordinal()
				+ "\', \'" + ReportStatus.valueOf(revokeStatus.name()).ordinal()
				+ "\') and UPPER(TRIM(er.value03)) like \'" + entity + "\' ";
		return query;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getByExpenseTypeV2(long companyCode, String expenseTypeForSeletion, LocalDate fromDate,
			LocalDate toDate, ReportStatus voucherStatus, String entity) {
		List<Object[]> list = new ArrayList<>();
//		String query = getSelectQuery() + "from expense_report er "
//				+ "inner join expense e on  (e.expense_report_id = er.id and e.company_code = \'" + companyCode + "\') "
//				+ "inner join expense_metadata em on em.id = er.expense_metadata_id "
//				+ "left join payment pt on pt.expense_report_id = er.id "
//				+ "left join report_state rs on rs.expense_report_id = (select rs2.expense_report_id from report_state rs2 "
//				+ "where rs2.expense_report_id = er.id and rs2.company_code = \'" + companyCode
//				+ "\' and ((rs2.status = 1 and er.report_status = 3) or rs2.status = 3) order by rs2.updated_timestamp desc limit 1) "
//				+ getWhereClause(companyCode, expenseTypeForSeletion, fromDate, toDate, voucherStatus, entity);
		// way -2 - pass only one status and set another as null, it will fetch rows for
		// specific report type
		Query query = em.createNativeQuery("{call expense_report_all(?,?,?,?,?,?,?)}").setParameter(1, companyCode)
				.setParameter(2, expenseTypeForSeletion).setParameter(3, fromDate).setParameter(4, toDate)
				.setParameter(5, ReportStatus.valueOf(voucherStatus.name()).ordinal()).setParameter(6, null)
				.setParameter(7, entity);

		return list = query.getResultList();
	}

	private String getWhereClause(long companyCode, String expenseTypeForSeletion, LocalDate fromDate, LocalDate toDate,
			ReportStatus voucherStatus, String entity) {
		String query = "where er.company_code = \'" + companyCode + "\' " + "and em.expense_type like \'"
				+ expenseTypeForSeletion + "\'  " + "and (er.created_date between \'" + fromDate + "\'  and \'" + toDate
				+ "\' ) and er.report_status not in = \'" + ReportStatus.valueOf(voucherStatus.name()).ordinal()
				+ "\' and UPPER(TRIM(er.value03)) like \'" + entity + "\'";
		return query;
	}

	String getSelectQuery() {
		String query = "select er.company_code as companyCode, er.created_date as createdDate, er.report_status as reportStatus , er.document_identifier as documentIdentifier ,er.report_claim_amount as reportClaimAmount,er.paid_status as paidStatus, er.total_paid_amount as totalPaidAmount , er.gl_posting_date as glPostingDate, er.id as id,"
				+ "er.employee_branch as employeeBranch, er.start_date as startDate , er.end_date as endDate, er.value03 as value03 ,er.employee_grade as employeeGrade, er.submit_date as submitDate, pt.payment_date as paymentDate, pt.payment_reference as paymentReference,"
				+ "em.expense_type as expenseType, er.report_title as reportTitle, er.first_name as firstName, er.middle_name as middleName, er.last_name as lastName, er.employee_cost_center as employeeCostCenter, er.employee_department as employeeDepartment, "
				+ "count(rs.id) as sentBackTimes,  CAST(null as char), "
				+ "case when rs.status = 3 then rs.updated_timestamp end as sentBackOnDate ,"
				+ "case when rs.status = 3 then rs.remarks end as remarks,"
				+ "case when rs.status = 3 then rs.approver_employee_code end as sentBackByCode,"
				+ "case when rs.status = 3 then CONCAT(rs.approver_first_name, ' ', rs.approver_last_name) end as sentBackBy,"
				+ "case when rs.status = 1 and er.report_status = 3 then rs.approver_employee_code end as currentApproverEmployeeCode,"
				+ "case when rs.status = 1 and er.report_status = 3 then CONCAT(rs.approver_first_name, ' ', rs.approver_last_name) end as currentApproverName,"
				+ "case when rs.status = 1 and er.report_status = 3 then rs.action_date end  as approvedDate, er.employee_code as employeeCode, "
				+ "CONCAT(er.current_approver_first_name, ' ', er.current_approver_last_name)  as pendingAt,  er.current_approver_employee_code,   er.employee_code ";
		return query;
	}
}
