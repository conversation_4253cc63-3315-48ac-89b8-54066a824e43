package in.taxgenie.pay_expense_pvv.repositories.implementations;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.ConstructorExpression;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.jpa.impl.JPAQueryFactory;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.budget.QBudget;
import in.taxgenie.pay_expense_pvv.entities.budget.QBudgetDocumentAction;
import in.taxgenie.pay_expense_pvv.entities.budget.QBudgetStructureMaster;
import in.taxgenie.pay_expense_pvv.entities.invoice.QInvoiceItem;
import in.taxgenie.pay_expense_pvv.entities.po.QPurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.entities.po.QPurchaseOrderItem;
import in.taxgenie.pay_expense_pvv.entities.pr.QPurchaseRequestHeader;
import in.taxgenie.pay_expense_pvv.entities.pr.QPurchaseRequestItem;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.BudgetDetailsWidgetViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.InvoicePayableStatisticsViewModel;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static in.taxgenie.pay_expense_pvv.entities.QDocumentMetadata.documentMetadata;
import static in.taxgenie.pay_expense_pvv.entities.budget.QBudgetDocumentAction.budgetDocumentAction;
import static in.taxgenie.pay_expense_pvv.entities.budget.QBudgetStructureMaster.budgetStructureMaster;

@Component
public class CustomWidgetDashbosrdRepository {
    private final JPAQueryFactory queryFactory;
    private static final QDocumentApprovalContainer dac = QDocumentApprovalContainer.documentApprovalContainer;
    private static final QDocument doc = QDocument.document;
    private static final QDocumentSubgroup docSub = QDocumentSubgroup.documentSubgroup1;
    private static final QInvoiceHeader invoiceHeader = QInvoiceHeader.invoiceHeader;
    private static final QCompany company = QCompany.company;
    private static final QUsers user = QUsers.users;
    private static final QBudget budget = QBudget.budget;
    private static final QBudgetStructureMaster bsm = budgetStructureMaster;
    private static final QPurchaseRequestHeader pr = QPurchaseRequestHeader.purchaseRequestHeader;
    private static final QPurchaseOrderHeader po = QPurchaseOrderHeader.purchaseOrderHeader;
    private static final QPurchaseRequestItem prItem = QPurchaseRequestItem.purchaseRequestItem;
    private static final QPurchaseOrderItem poItem = QPurchaseOrderItem.purchaseOrderItem;
    private static final QInvoiceItem invoiceItem = QInvoiceItem.invoiceItem;
    private static final QBudgetDocumentAction bda = QBudgetDocumentAction.budgetDocumentAction;
    private static final QItemMaster itemMaster = QItemMaster.itemMaster;
    private final Logger logger;

    public CustomWidgetDashbosrdRepository(EntityManager em) {
        this.queryFactory = new JPAQueryFactory(em);
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public Page<BudgetDetailsWidgetViewModel> fetchBudgetDetails(long companyCode, long userId, QueueFilterViewModel filterValues, Pageable pageable, String orgKey, List<String> rbacValues) {
        BooleanBuilder builder = new BooleanBuilder().and(documentMetadata.companyCode.eq(companyCode));
        // Filter DocumentApprovalContainer (dac) by matching keys and values
        if (!StaticDataRegistry.isNullOrEmptyOrWhitespace(orgKey) && !rbacValues.isEmpty()) {
            builder.and(applyRbac(orgKey, rbacValues));
        } else {
            // handle scenario - when user does not have value against the key for which hierarchy is created - TODO: re-check this.
            builder.and(dac.creatingUserId.eq(userId));
        }
        ConstructorExpression<BudgetDetailsWidgetViewModel> queueConstructorExpression = Projections.constructor(BudgetDetailsWidgetViewModel.class,
                budget.id, budgetStructureMaster.id, budget.total, budget.treasury.subtract(budget.consumed.add(budget.locked)), budget.budgetCode,
                documentMetadata.applicableExpenseType);

        createFilterQuery(filterValues, builder);

        QueryResults<BudgetDetailsWidgetViewModel> results = queryFactory.selectDistinct(queueConstructorExpression)
                .from(budget)
                .innerJoin(bsm).on(budget.budgetStructureMasterId.eq(bsm.id))
                .innerJoin(documentMetadata).on(documentMetadata.id.eq(bsm.documentMetadataId))
                .innerJoin(bda).on(bsm.id.eq(bda.budgetStructureMasterId))
                .innerJoin(bda.document, doc)
                .innerJoin(doc.documentApprovalContainer, dac)
                .where(builder)
                .orderBy(new OrderSpecifier<>(Order.DESC, budget.id))
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize()).fetchResults();
        List<BudgetDetailsWidgetViewModel> budgetDetails = results.getResults();

        // get counts
        List<Long> budgetIds = budgetDetails.stream()
                .map(BudgetDetailsWidgetViewModel::getBudgetId)
                .collect(Collectors.toList());

        Map<Long, Long> poCounts = fetchPurchaseOrderCount(companyCode, userId, budgetIds, orgKey, rbacValues);
        Map<Long, Long> invoiceCounts = fetchInvoiceCount(companyCode, userId, budgetIds, orgKey, rbacValues);

        // Combine
        List<BudgetDetailsWidgetViewModel> finalResults = budgetDetails.stream()
                .map(budget -> {
                    Long poCount = poCounts.getOrDefault(budget.getBudgetId(), 0L);
                    Long invoiceCount = invoiceCounts.getOrDefault(budget.getBudgetId(), 0L);
                    budget.setPurchaseOrderCount(poCount);
                    budget.setInvoiceCount(invoiceCount);
                    return budget;
                })
                .collect(Collectors.toList());

        return PageableExecutionUtils.getPage(finalResults, pageable, results::getTotal);
    }

    public Map<Long, Long> fetchInvoiceCount(long companyCode, Long userId, List<Long> budgetIds, String orgKey, List<String> rbacValues) {

        BooleanBuilder builder = new BooleanBuilder();

        // Filter DocumentApprovalContainer (dac) by company code
        builder.and(dac.companyCode.eq(companyCode));

        // Apply additional filtering based on orgKey and rbacValues
//        if (!StaticDataRegistry.isNullOrEmptyOrWhitespace(orgKey) && !rbacValues.isEmpty()) {
//            builder.and(applyRbac(orgKey, rbacValues));
//        } else {
//            // Handle scenario when user does not have value against the key for which hierarchy is created
//            builder.and(dac.creatingUserId.eq(userId));
//        }

        return queryFactory.select(
                        invoiceItem.budgetNode.id.as("budgetNodeId"),
                        invoiceHeader.invoiceHeaderId.countDistinct().as("count")  // Apply the condition and count distinct invoice headers
                )
                .from(invoiceItem)
                .leftJoin(invoiceItem.invoiceHeader, invoiceHeader)
                .leftJoin(invoiceHeader.document, doc)
                .leftJoin(doc.documentApprovalContainer, dac)
                .where(invoiceItem.budgetNode.id.in(budgetIds).and(dac.reportStatus.in(StaticDataRegistry.BUDGET_CONSUMED_STATES)).and(builder))
                .groupBy(invoiceItem.budgetNode.id)  // Group by budgetNode ID
                .fetch()
                .stream()
                .collect(Collectors.toMap(
                        entry -> entry.get(Expressions.numberPath(Long.class, "budgetNodeId")),
                        entry -> Optional.ofNullable(entry.get(Expressions.numberPath(Long.class, "count"))).orElse(0L)
                ));
    }

    public Map<Long, Long> fetchPurchaseOrderCount(long companyCode, Long userId, List<Long> budgetIds, String orgKey, List<String> rbacValues) {

        BooleanBuilder builder = new BooleanBuilder();

        // Filter DocumentApprovalContainer (dac) by company code
        builder.and(dac.companyCode.eq(companyCode));

        // Apply additional filtering based on orgKey and rbacValues
//        if (!StaticDataRegistry.isNullOrEmptyOrWhitespace(orgKey) && !rbacValues.isEmpty()) {
//            builder.and(applyRbac(orgKey, rbacValues));
//        } else {
//            // Handle scenario when user does not have value against the key for which hierarchy is created
//            builder.and(dac.creatingUserId.eq(userId));
//        }

        return queryFactory.select(
                        poItem.budgetNode.id.as("budgetNodeId"),
                        po.id.countDistinct().as("count")
                )
                .from(poItem)
                .leftJoin(poItem.purchaseOrderHeader, po)
                .leftJoin(po.document, doc)
                .leftJoin(doc.documentApprovalContainer, dac)
                .where(poItem.budgetNode.id.in(budgetIds).and(dac.reportStatus.in(StaticDataRegistry.BUDGET_CONSUMED_STATES))
                        .and(builder))
                .groupBy(poItem.budgetNode.id)
                .fetch()
                .stream()
                .collect(Collectors.toMap(
                        entry -> entry.get(Expressions.numberPath(Long.class, "budgetNodeId")),
                        entry -> Optional.ofNullable(entry.get(Expressions.numberPath(Long.class, "count"))).orElse(0L)
                ));
    }

    private BooleanBuilder initBuilderWithoutUser(long companyCode, List<Long> metadataIds) {
        return new BooleanBuilder().and(dac.companyCode.eq(companyCode)).and(dac.documentMetadataId.in(metadataIds));
    }
    private BooleanBuilder applyRbac(String rbacKey, List<String> rbacValues) {
        BooleanBuilder dacKeyValueFilter = new BooleanBuilder();
        if (!rbacValues.isEmpty() && rbacKey != null) {
            dacKeyValueFilter.or(
                    Expressions.anyOf(
                            dac.key01.eq(rbacKey).and(dac.value01.in(rbacValues)),
                            dac.key02.eq(rbacKey).and(dac.value02.in(rbacValues)),
                            dac.key03.eq(rbacKey).and(dac.value03.in(rbacValues)),
                            dac.key04.eq(rbacKey).and(dac.value04.in(rbacValues)),
                            dac.key05.eq(rbacKey).and(dac.value05.in(rbacValues)),
                            dac.key06.eq(rbacKey).and(dac.value06.in(rbacValues)),
                            dac.key07.eq(rbacKey).and(dac.value07.in(rbacValues)),
                            dac.key08.eq(rbacKey).and(dac.value08.in(rbacValues)),
                            dac.key09.eq(rbacKey).and(dac.value09.in(rbacValues)),
                            dac.key10.eq(rbacKey).and(dac.value10.in(rbacValues))
                    )
            );
        }
        return dacKeyValueFilter;
    }
    private void createFilterQuery(QueueFilterViewModel filterValues, BooleanBuilder builder) {
        Map<String, List<String>> filters = filterValues.getSearchCriteria() == null ? new HashMap<>() : filterValues.getSearchCriteria();
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            BooleanBuilder innerBuilder = new BooleanBuilder();
            switch (searchCriteria) {
                case StringConstants.BUDGET_CODE:
                    entry.getValue().forEach(value -> {
                        if (!StaticDataRegistry.isNullOrEmptyOrWhitespace(value)) {
                            innerBuilder.or(budget.budgetCode.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;
                default:
            }
            builder.and(innerBuilder);
        }
    }

    private BooleanBuilder initBuilder(long companyCode, List<Long> metadataIds) {
        return new BooleanBuilder().and(dac.companyCode.eq(companyCode)).and(dac.documentMetadataId.in(metadataIds));
    }

    @Transactional
    public InvoicePayableStatisticsViewModel getInvoiceStatistics(IAuthContextViewModel auth, List<Long> metadataIds,
                                                                  String financialYear, Integer month,
                                                                  String requestId) {
        logger.info("START getInvoiceStatistics | RequestId: {} | FinancialYear: {} | Month: {}", requestId, financialYear, month);

        YearMonth targetMonthYear = null;
        Integer year = null;
        boolean validFinancialYearAndMonth = StaticDataRegistry.isNotNullOrEmptyOrWhitespace(financialYear) && null != month;

        try {
            if (validFinancialYearAndMonth) {
                int startYear = Integer.parseInt(financialYear.split("-")[0]);
                year = (month >= 4) ? startYear : startYear + 1;
                targetMonthYear = YearMonth.of(year, month);
                logger.info("Parsed TargetMonthYear: {} | RequestId: {}", targetMonthYear, requestId);
            }
        } catch (DateTimeException | NumberFormatException e) {
            logger.error("Error parsing financial year/month | RequestId: {} | Error: {}", requestId, e.getMessage(), e);
            throw new RuntimeException("Error parsing financial year or month for Dashboard getInvoiceStatistics", e);
        }

        InvoicePayableStatisticsViewModel stats = getInvoicePayableStatisticsViewModel(auth, metadataIds, targetMonthYear, year);
        if (validFinancialYearAndMonth) {
            // TODO: this may not fall in current year
            YearMonth previousMonth = targetMonthYear.minusMonths(1);
            YearMonth previousToPreviousMonth = targetMonthYear.minusMonths(2);

            logger.info("Calculating insights for RequestId: {} | PreviousMonth: {} | PreviousToPreviousMonth: {}", requestId, previousMonth, previousToPreviousMonth);

//            InvoicePayableStatisticsViewModel previousMonthStats = getInvoicePayableStatisticsViewModel(auth, metadataIds, previousMonth, year);
//            InvoicePayableStatisticsViewModel previousToPreviousMonthStats = getInvoicePayableStatisticsViewModel(auth, metadataIds, previousToPreviousMonth, year);

//            stats.setTotalVendorInsight(calculateCountChange(previousMonthStats.getTotalVendorCount(), previousToPreviousMonthStats.getTotalVendorCount()));
//            stats.setTotalInvoiceCountInsight(calculateCountPercentageChange(previousMonthStats.getTotalVendorCount(), previousToPreviousMonthStats.getTotalVendorCount()));
//            stats.setTotalInvoiceAmountInsight(calculatePercentageChange(previousMonthStats.getTotalInvoiceAmount(), previousToPreviousMonthStats.getTotalInvoiceAmount()));
//            stats.setTotalTaxAmountInsight(calculatePercentageChange(previousMonthStats.getTotalTaxAmount(), previousToPreviousMonthStats.getTotalTaxAmount()));
//            stats.setTotalTaxableAmountInsight(calculatePercentageChange(previousMonthStats.getTotalTaxableAmount(), previousToPreviousMonthStats.getTotalTaxableAmount()));
            stats.setTotalVendorInsight(null);
            stats.setTotalInvoiceCountInsight(null);
            stats.setTotalInvoiceAmountInsight(null);
            stats.setTotalTaxAmountInsight(null);
            stats.setTotalTaxableAmountInsight(null);
        }

        logger.info("END getInvoiceStatistics | RequestId: {}", requestId);
        return stats;
    }

    private InvoicePayableStatisticsViewModel getInvoicePayableStatisticsViewModel(IAuthContextViewModel auth, List<Long> metadataIds, YearMonth targetMonthYear, Integer year) {
        BooleanBuilder builder = initBuilder(auth.getCompanyCode(), metadataIds).and(dac.reportStatus.in(StaticDataRegistry.CONSUMED));
        BooleanBuilder dateBuilder = getDateBuilder(targetMonthYear, year);

        NumberExpression<BigDecimal> taxAmount = Expressions.cases()
                .when(invoiceItem.taxPercentage.isNotNull())
                .then(invoiceItem.assessableAmount
                        .multiply(invoiceItem.taxPercentage.multiply(0.01)))
                .otherwise(BigDecimal.ZERO);

        Tuple result = queryFactory
                .select(
                        invoiceHeader.supplier.countDistinct(),
                        invoiceHeader.countDistinct(),
                        invoiceItem.assessableAmount.coalesce(BigDecimal.ZERO).sum(),
                        invoiceItem.totalWithTax.coalesce(BigDecimal.ZERO).sum(),
                        taxAmount.sum()
                )
                .from(invoiceHeader)
                .join(invoiceHeader.document, doc)
                .join(doc.documentApprovalContainer, dac)
                .leftJoin(invoiceHeader.invoiceItems, invoiceItem)
                .where(builder.and(dateBuilder)
                )
                .fetchOne();

        Long vendorCount = result.get(0, Long.class);  // Vendor count
        Long invoiceCount = result.get(1, Long.class);  // Invoice count
        BigDecimal totalAssessableAmount = result.get(2, BigDecimal.class);  // Total Assessable Amount
        BigDecimal totalInvoiceAmount = result.get(3, BigDecimal.class);  // Total Invoice Amount
        BigDecimal totalTaxAmount = result.get(4, BigDecimal.class);

        return new InvoicePayableStatisticsViewModel(
                vendorCount,
                invoiceCount,
                totalAssessableAmount,
                totalInvoiceAmount,
                totalTaxAmount
        );
    }

    private BooleanBuilder getDateBuilder(YearMonth targetMonthYear, Integer year) {
        if (targetMonthYear != null) {
            // Filter for a specific month
            LocalDate startOfMonth = targetMonthYear.atDay(1);
            LocalDate endOfMonth = targetMonthYear.atEndOfMonth();
            return new BooleanBuilder().and(doc.documentDate.between(startOfMonth, endOfMonth));
        } else if (year != null) {
            // Filter for the whole financial year (April to March)
            LocalDate startOfYear = LocalDate.of(year, 4, 1);  // Start in April
            LocalDate endOfYear = LocalDate.of(year + 1, 3, 31);
            return new BooleanBuilder().and(doc.documentDate.between(startOfYear, endOfYear));
        } else {
            // Default to the current financial year (April to March)
            int currentYear = LocalDate.now().getYear();
            LocalDate startOfYear = LocalDate.now().isBefore(LocalDate.of(currentYear, 4, 1)) ?
                    LocalDate.of(currentYear - 1, 4, 1) :
                    LocalDate.of(currentYear, 4, 1);
            LocalDate endOfYear = startOfYear.plusYears(1).withMonth(3).withDayOfMonth(31);
            return new BooleanBuilder().and(doc.documentDate.between(startOfYear, endOfYear));
        }
    }

    private String calculatePercentageChange(BigDecimal current, BigDecimal previous) {
        BigDecimal result;
        previous = null == previous ? BigDecimal.ZERO : previous;
        current = null == current ? BigDecimal.ZERO : current;
        if (previous.equals(BigDecimal.ZERO)) {
            result = current.equals(BigDecimal.ZERO) ? BigDecimal.ZERO : BigDecimal.valueOf(100);
            return result + "%";
        }
        result = (current.subtract(previous)).divide(previous, MathContext.DECIMAL32).multiply(BigDecimal.valueOf(100));
        return result + "%";
    }

    private String calculateCountPercentageChange(Long current, Long previous) {
        Long result;
        previous = null == previous ? 0l : previous;
        current = null == current ? 0l : current;
        if (previous.equals(0l)) {
            result = current.equals(0l) ? 0l : Long.valueOf(100);
            return result + "%";
        }
        result = ((current - previous) / previous) * (Long.valueOf(100));
        return result + "%";
    }

    private String calculateCountChange(Long current, Long previous) {
        previous = null == previous ? 0l : previous;
        current = null == current ? 0l : current;
        return String.valueOf(current - previous);
    }
}
