package in.taxgenie.pay_expense_pvv.repositories.implementations;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.core.types.ConstructorExpression;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.budget.QBudget;
import in.taxgenie.pay_expense_pvv.entities.budget.QBudgetDocumentAction;
import in.taxgenie.pay_expense_pvv.entities.budget.QBudgetStructureMaster;
import in.taxgenie.pay_expense_pvv.entities.budget.QBudgetSyncMaster;
import in.taxgenie.pay_expense_pvv.entities.gl_master.QGlMaster;
import in.taxgenie.pay_expense_pvv.entities.invoice.QInvoiceItem;
import in.taxgenie.pay_expense_pvv.entities.po.QPurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.entities.po.QPurchaseOrderItem;
import in.taxgenie.pay_expense_pvv.entities.pr.ProcurementStatus;
import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestItem;
import in.taxgenie.pay_expense_pvv.entities.pr.QPurchaseRequestHeader;
import in.taxgenie.pay_expense_pvv.entities.pr.QPurchaseRequestItem;
import in.taxgenie.pay_expense_pvv.repositories.pr.IPurchaseRequestHeaderRepository;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.ConsumedItemAvailableCountViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.QueueStatisticsGenericViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.QueueStatisticsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.ConsumptionDetailsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.syncmaster.BudgetSyncMasterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderItemDetailsViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.PRHeaderWithConsumptionCount;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.ProcurementViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.PurchaseRequestContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.PurchaseRequestItemDetailsView;
import jakarta.persistence.EntityManager;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

@Data
@Component
public class CustomDocumentApprovalContainerRepository {

    // QueryDSL dependencies
    private final JPAQueryFactory queryFactory;
    QReportState reportState = QReportState.reportState;
    private static final QDocumentMetadata documentMetadata = QDocumentMetadata.documentMetadata;
    private static final QDocumentApprovalContainer dac = QDocumentApprovalContainer.documentApprovalContainer;
    private static final QDocument doc = QDocument.document;
    private static final QDocumentSubgroup docSub = QDocumentSubgroup.documentSubgroup1;
    private static final QInvoiceHeader invoiceHeader = QInvoiceHeader.invoiceHeader;
    private static final QInvoiceReceived invoiceReceived = QInvoiceReceived.invoiceReceived;
    private static final QCompany company = QCompany.company;
    private static final QUsers user = QUsers.users;
    private static final QBudget budget = QBudget.budget;
    private static final QBudgetStructureMaster bsm = QBudgetStructureMaster.budgetStructureMaster;
    private static final QPurchaseRequestHeader pr = QPurchaseRequestHeader.purchaseRequestHeader;
    private static final QPurchaseOrderHeader po = QPurchaseOrderHeader.purchaseOrderHeader;
    private static final QPurchaseRequestItem prItem = QPurchaseRequestItem.purchaseRequestItem;
    private static final QPurchaseOrderItem poItem = QPurchaseOrderItem.purchaseOrderItem;
    private static final QInvoiceItem invoiceItem = QInvoiceItem.invoiceItem;
    private static final QBudgetDocumentAction bda = QBudgetDocumentAction.budgetDocumentAction;
    private static final QItemMaster itemMaster = QItemMaster.itemMaster;
    private static final QGlMaster glm = QGlMaster.glMaster;
    private static final QPayment payment = QPayment.payment;
    QDocumentStorage documentStorage = QDocumentStorage.documentStorage;
    private static final QBudgetSyncMaster budgetSyncMaster = QBudgetSyncMaster.budgetSyncMaster;
    private final Logger logger;
    private final IPurchaseRequestHeaderRepository purchaseRequestHeaderRepository;

    public CustomDocumentApprovalContainerRepository(EntityManager em, IPurchaseRequestHeaderRepository purchaseRequestHeaderRepository) {
        this.queryFactory = new JPAQueryFactory(em);
        this.purchaseRequestHeaderRepository = purchaseRequestHeaderRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public Page<InvoiceContainerViewModel> getInvoiceQueue(long companyCode, Long userId, List<Long> metadataIds, QueueFilterViewModel filterValues, Pageable pageable, String orgKey, List<String> rbacValues) {
        // removed userId from filter as now RBAC is implemented
        BooleanBuilder builder = initBuilder(companyCode, metadataIds);
        JPQLQuery<Integer> documentCount = getSupportingDocumentsCountForInvoice();
        // Get RBAC values

        // Filter DocumentApprovalContainer (dac) by matching keys and values
        if (!StaticDataRegistry.isNullOrEmptyOrWhitespace(orgKey) && !rbacValues.isEmpty()) {
            builder.and(applyRbac(orgKey, rbacValues));
        } else {
            // handle scenario - when user does not have value against the key for which hierarchy is created - TODO: re-check this.
            builder.and(dac.creatingUserId.eq(userId));
        }


        // With requested filters
//        createFilterQuery(filterValues, builder);
        createFilterQueryByDocument(StaticDataRegistry.LOOKUP_VALUE_INVOICE, filterValues, builder);

        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);
        // Add further sorting below if required

        ConstructorExpression<InvoiceContainerViewModel> constructorExpression = Projections.constructor(InvoiceContainerViewModel.class,
                dac.id, dac.approvalContainerClaimAmount, dac.documentIdentifier, dac.createdTimestamp,
                user.firstName.concat(" ").concat(user.lastName),
                dac.reportStatus, dac.submitDate, doc.documentDate, dac.endDate, dac.currentApproverFirstName,
                dac.currentApproverLastName, dac.currentApproverSystemIdCode,
                dac.currentApproverEmployeeCode, dac.firstName, dac.middleName,
                dac.lastName, dac.employeeCode, dac.employeeGrade, dac.updatedTimestamp,
                doc.originatorDocumentIdentifier, doc.documentSubgroupId, doc.source,
                doc.docNo,
                company.vendorCode, company.gstinStatus, company.msmeStatus, company.gst, company.supplierCompanyName, company.addr1, company.addr2,
                company.loc, company.pin, company.phoneNo, company.emailId, docSub.documentSubgroup, documentCount, docSub.documentMetadata.applicableExpenseType, Expressions.cases()
                        .when(docSub.hasPrecedingDocument.isTrue()).then("PO Based")
                        .otherwise("Non-PO Based"), docSub.documentMetadata.documentGroup, company.gst, invoiceReceived.invoiceReceivedId, dac.erpRemarks, payment.paymentReference, dac.statusRemarks, dac.processMethod, dac.paymentDate);

        QueryResults<InvoiceContainerViewModel> results = queryFactory.select(constructorExpression)
                .from(dac)
                .leftJoin(dac.documents, doc)
                .leftJoin(doc.documentSubgroup, docSub)
                .leftJoin(doc.invoiceHeader, invoiceHeader)
                .leftJoin(invoiceHeader.supplier, company)
                .leftJoin(invoiceHeader.invoiceReceived, invoiceReceived)
                .leftJoin(payment).on(dac.id.eq(payment.documentApprovalContainerId))
                .join(user).on(dac.creatingUserId.eq(user.id.longValue()))
                .where(builder)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    private BooleanBuilder applyRbac(String rbacKey, List<String> rbacValues) {
        BooleanBuilder dacKeyValueFilter = new BooleanBuilder();
        if (!rbacValues.isEmpty() && rbacKey != null) {
            dacKeyValueFilter.or(
                    Expressions.anyOf(
                            dac.key01.eq(rbacKey).and(dac.value01.in(rbacValues)),
                            dac.key02.eq(rbacKey).and(dac.value02.in(rbacValues)),
                            dac.key03.eq(rbacKey).and(dac.value03.in(rbacValues)),
                            dac.key04.eq(rbacKey).and(dac.value04.in(rbacValues)),
                            dac.key05.eq(rbacKey).and(dac.value05.in(rbacValues)),
                            dac.key06.eq(rbacKey).and(dac.value06.in(rbacValues)),
                            dac.key07.eq(rbacKey).and(dac.value07.in(rbacValues)),
                            dac.key08.eq(rbacKey).and(dac.value08.in(rbacValues)),
                            dac.key09.eq(rbacKey).and(dac.value09.in(rbacValues)),
                            dac.key10.eq(rbacKey).and(dac.value10.in(rbacValues))
                    )
            );
        }
        return dacKeyValueFilter;
    }


    public Page<PurchaseRequestContainerViewModel> getPRQueue(long companyCode, long userId, List<Long> metadataIds, QueueFilterViewModel filterValues, Pageable pageable, String orgKey, List<String> rbacValues) {
        // removed userId from filter as now RBAC is implemented
        BooleanBuilder builder = initBuilder(companyCode, metadataIds);

        JPQLQuery<Integer> documentCount = getSupportingDocumentsCountForPurchaseRequest();

        if (!StaticDataRegistry.isNullOrEmptyOrWhitespace(orgKey) && !rbacValues.isEmpty()) {
            builder.and(applyRbac(orgKey, rbacValues));
        } else {
            // handle scenario - when user does not have value against the key for which hierarchy is created - TODO: re-check this.
            builder.and(dac.creatingUserId.eq(userId));
        }

        ConstructorExpression<PurchaseRequestContainerViewModel> queueConstructorExpression = Projections.constructor(PurchaseRequestContainerViewModel.class,
                dac.id,
                doc.claimAmount,
                doc.claimAmount.subtract(doc.consumedAmount.coalesce(BigDecimal.ZERO)),
                dac.documentIdentifier,
                dac.createdTimestamp, doc.documentDate, dac.reportStatus, dac.submitDate,
                dac.currentApproverFirstName, dac.currentApproverLastName, dac.currentApproverSystemIdCode, dac.currentApproverEmployeeCode,
                dac.firstName, dac.middleName, dac.lastName,
                dac.employeeCode, dac.employeeGrade,
                dac.updatedTimestamp,
                doc.originatorDocumentIdentifier, doc.documentSubgroupId,
                dac.employeeDepartment,
                pr.expectedDeliveryDate,
                pr.purchaseReason,
                doc.docNo,
                pr.prType.value,
                doc.claimAmount, doc.claimAmount,
                documentCount, docSub.documentMetadata.applicableExpenseType,
                docSub.documentMetadata.documentGroup, doc.documentSubgroup.documentSubgroup);

        // With requested filters
        createFilterQueryByDocument(StaticDataRegistry.LOOKUP_VALUE_PR, filterValues, builder);

        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);

        QueryResults<PurchaseRequestContainerViewModel> results = queryFactory.select(queueConstructorExpression)
                .from(dac)
                .leftJoin(dac.documents, doc)
                .innerJoin(doc.documentSubgroup, docSub)
                .leftJoin(doc.prHeader, pr)
                .leftJoin(budget).on(budget.id.eq(docSub.budgetId))
                .where(builder)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    public Page<PurchaseOrderContainerViewModel> getPOQueue(long companyCode, long userId, List<Long> metadataIds, QueueFilterViewModel filterValues, Pageable pageable, String orgKey, List<String> rbacValues) {
        // removed userId from filter as now RBAC is implemented
        BooleanBuilder builder = initBuilder(companyCode, metadataIds);

        JPQLQuery<Integer> documentCount = getSupportingDocumentsCountForPurchaseOrder();
        // Filter DocumentApprovalContainer (dac) by matching keys and values
        if (!StaticDataRegistry.isNullOrEmptyOrWhitespace(orgKey) && !rbacValues.isEmpty()) {
            builder.and(applyRbac(orgKey, rbacValues));
        } else {
            // handle scenario - when user does not have value against the key for which hierarchy is created - TODO: re-check this.
            builder.and(dac.creatingUserId.eq(userId));
        }
        ConstructorExpression<PurchaseOrderContainerViewModel> queueConstructorExpression = Projections.constructor(PurchaseOrderContainerViewModel.class, dac.id, dac.documentIdentifier, dac.createdTimestamp, doc.documentDate,
                dac.reportStatus,
                dac.submitDate, dac.currentApproverFirstName, dac.currentApproverLastName, dac.currentApproverSystemIdCode,
                dac.currentApproverEmployeeCode, dac.firstName, dac.middleName, dac.lastName, dac.employeeCode, dac.employeeGrade,
                dac.updatedTimestamp, doc.docNo, po.deliveryDate, po.orderCreationTime, doc.claimAmount,
                doc.claimAmount.subtract(doc.consumedAmount.coalesce(BigDecimal.ZERO)),
                po.poStatus, budget.budgetCode, doc.originatorDocumentIdentifier, doc.documentSubgroupId, doc.source, company.supplierCompanyName,
                company.vendorCode, company.gst, company.msmeStatus, company.gstinStatus, documentCount, docSub.documentMetadata.documentGroup,
                doc.documentSubgroup.documentSubgroup, docSub.documentMetadata.applicableExpenseType, Expressions.cases()
                        .when(docSub.hasPrecedingDocument.isTrue()).then("PR Based")
                        .otherwise("Non-PR Based"), dac.erpRemarks, dac.statusRemarks);

        // With requested filters
        createFilterQueryByDocument(StaticDataRegistry.LOOKUP_VALUE_PO, filterValues, builder);

        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);

        QueryResults<PurchaseOrderContainerViewModel> results = queryFactory.select(queueConstructorExpression)
                .from(dac)
                .leftJoin(dac.documents, doc)
                .innerJoin(doc.documentSubgroup, docSub)
                .leftJoin(doc.poHeader, po)
                .leftJoin(company).on(po.vendorId.eq(company.companyId))
                .leftJoin(budget).on(budget.id.eq(docSub.budgetId))
                .where(builder)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    public BudgetContainerViewModel getBudgetQueue(long companyCode, long userId, long id) {

        BooleanBuilder builder = new BooleanBuilder().and(dac.companyCode.eq(companyCode)).and(dac.id.eq(id));

        ConstructorExpression<BudgetContainerViewModel> queueConstructorExpression = Projections.constructor(BudgetContainerViewModel.class, dac.id,
                doc.claimAmount, doc.totalAmountGSTInclusive, dac.documentIdentifier, dac.createdTimestamp, dac.reportStatus, dac.submitDate,
                dac.currentApproverFirstName, dac.currentApproverLastName, dac.currentApproverSystemIdCode, dac.currentApproverEmployeeCode, dac.firstName,
                dac.middleName, dac.lastName, dac.employeeCode, dac.employeeGrade, dac.updatedTimestamp, doc.originatorDocumentIdentifier, doc.documentSubgroupId,
                dac.employeeDepartment, doc.docNo, dac.documentMetadata.actionType.stringValue(), bsm.id, budget.budgetCode, bsm.financialYear,
                bsm.structureName, bsm.startMonthIndex, bsm.isApproved);

        // Execute the query
        QueryResults<BudgetContainerViewModel> results = queryFactory.select(queueConstructorExpression)
                .from(dac)
                .leftJoin(dac.documents, doc)
                .innerJoin(doc.documentSubgroup, docSub)
                .leftJoin(doc.budgetDocumentAction, bda)
                .leftJoin(budget).on(budget.id.eq(bda.budgetId))

                .leftJoin(bsm).on(bsm.id.eq(bda.budgetStructureMasterId))

//                .leftJoin(budget).on(budget.id.eq(docSub.budgetId))
                .where(builder)
                .fetchResults();

        // Check if results are empty and handle accordingly
        if (results.isEmpty()) {
            return null; // or throw an exception, or return an empty BudgetContainerViewModel, depending on the use case
        }

        // Return the first result (assuming one result is expected based on the criteria)
        return results.getResults().get(0);
    }

    private JPQLQuery<Integer> getSupportingDocumentsCountForInvoice() {
        return JPAExpressions
                .select(documentStorage.id.count().intValue())
                .from(documentStorage)
                .where(documentStorage.entityId.eq(invoiceHeader.invoiceHeaderId).and(dac.documentMetadata.documentCategoryId.eq(documentStorage.categoryId)));
    }

    private JPQLQuery<Integer> getSupportingDocumentsCountForPurchaseRequest() {
        return JPAExpressions
                .select(documentStorage.id.count().intValue())
                .from(documentStorage)
                .where(documentStorage.entityId.eq(pr.id).and(dac.documentMetadata.documentCategoryId.eq(documentStorage.categoryId)));
    }

    private JPQLQuery<Integer> getSupportingDocumentsCountForPurchaseOrder() {
        return JPAExpressions
                .select(documentStorage.id.count().intValue())
                .from(documentStorage)
                .where(documentStorage.entityId.eq(po.id).and(dac.documentMetadata.documentCategoryId.eq(documentStorage.categoryId)));
    }

    public Page<ProcurementViewModel> getProcurementData(long companyCode, long userId, List<Long> metadataIds, QueueFilterViewModel filterValues, Pageable pageable, boolean isStat) {
        QueryResults<ProcurementViewModel> results = getProcurementViewModelQueryResults(companyCode, metadataIds, filterValues, pageable, isStat);

        List<ProcurementViewModel> viewModels = results.getResults();
        List<Long> prIds = viewModels.stream().map(ProcurementViewModel::getPrId).toList();

        // get items by prIds
        List<Long> prItemIds = purchaseRequestHeaderRepository.findByIdsIn(prIds).stream()
                .flatMap(prh -> prh.getPrItems().stream())
                .map(PurchaseRequestItem::getId).collect(Collectors.toList());

        logger.trace("getting consumption map for item quantity");
        Map<Long, List<ConsumedItemAvailableCountViewModel>> consumedItemQuantityByPurchaseReqestHeaderAvailableMap = new HashMap<>();
        List<ConsumedItemAvailableCountViewModel> consumedItems = getPRItemsConsumedAndNotConsumedInPOsWithCounts(prItemIds);

        // Logic to create map which will be used for amount calculations based on status
        Map<Long, List<ReportStatus>> idToReportStatusMap = consumedItems.stream()
                .collect(Collectors.groupingBy(
                        ConsumedItemAvailableCountViewModel::getId,
                        Collectors.mapping(ConsumedItemAvailableCountViewModel::getReportStatus, Collectors.toList())
                ));
        // logic end
        // avoid modifying references
        List<ConsumedItemAvailableCountViewModel> processConsumedItems = getCopiedList(consumedItems);
        populateConsumedItemsByPurchaseRequestHeaderMap(processConsumedItems, consumedItemQuantityByPurchaseReqestHeaderAvailableMap);


        Map<Long, BigDecimal> prIdToInProcessAmountMap = new HashMap<>();
        double originalQuantity = 0;
        for (ProcurementViewModel viewModel : viewModels) {
            double remainingQuantity = 0;
            ReportStatus reportStatus = null;
            List<ConsumedItemAvailableCountViewModel> consumedItemViewModels = consumedItemQuantityByPurchaseReqestHeaderAvailableMap.get(viewModel.getPrId());
            if (consumedItemViewModels == null) {
                viewModel.setProcurementStatus(ProcurementStatus.READY_TO_CONVERT);
                continue;
            }
            // sort consumedItemViewModels items with status accepted>submitted>sent_back so that sent_back will always come as last and last reportStatus will be set accordingly in below for-lp.
            List<ConsumedItemAvailableCountViewModel> sortedConsumedItemViewModel = sortConsumedItemViewModel(consumedItems, consumedItemViewModels);

            for (ConsumedItemAvailableCountViewModel consumedItemViewModel : sortedConsumedItemViewModel) {
                // TODO : commenting below if as on submitted quantity will be considered as consumed
//                if (consumedItemViewModel.getReportStatus() == ReportStatus.SUBMITTED) {
//                    // On submit quantity will not be considered as consumed. - assigning quantity to get the correct status.
//                    remainingQuantity = consumedItemViewModel.getQuantity();
//                }else
                if (consumedItemViewModel.getReportStatus() == ReportStatus.ACCEPTED) { // handle sent back case
                    remainingQuantity = remainingQuantity + (consumedItemViewModel.getInitialQuantity() - consumedItemViewModel.getQuantity());
                }


                // logic for amount calculation as per PO status -
                // same item can be consumed in different po having both status submitted and accepted, handle InProcessOfBeingApprovedInPOAmount.
                List<ReportStatus> reportStatuses = idToReportStatusMap.get(consumedItemViewModel.getId());
                if (reportStatuses != null && reportStatuses.contains(ReportStatus.SUBMITTED)) {
                    ConsumedItemAvailableCountViewModel itemToGetQuantity = consumedItems.stream().
                            filter(i -> (i.getId().equals(consumedItemViewModel.getId()) && i.getReportStatus() == ReportStatus.SUBMITTED))
                            .findFirst().orElse(null);
                    double quantity = itemToGetQuantity != null ? itemToGetQuantity.getQuantity() : 0.0;
                    remainingQuantity = remainingQuantity + quantity; // to get submitted quantity
                    BigDecimal totalAmount = calculateTotalAmountByPrIdAndPrItemId(consumedItemViewModel.getPrId(), consumedItemViewModel.getId(), quantity);
                    prIdToInProcessAmountMap.merge(viewModel.getPrId(), totalAmount, BigDecimal::add);
                } else if (reportStatuses != null && reportStatuses.contains(ReportStatus.SENT_BACK)) {
                    ConsumedItemAvailableCountViewModel itemToGetQuantity = consumedItems.stream().
                            filter(i -> (i.getId().equals(consumedItemViewModel.getId()) && i.getReportStatus() == ReportStatus.SENT_BACK))
                            .findFirst().orElse(null);
                    double quantity = itemToGetQuantity != null ? itemToGetQuantity.getQuantity() : 0.0;
                    remainingQuantity = remainingQuantity + quantity; // to get sent back quantity
                }
                // logic end

                // set status as per the highest precedence - which is already done in populateConsumedItemsByPurchaseRequestHeaderMap method;
                if (consumedItemViewModel.getReportStatus() != null) {
                    reportStatus = consumedItemViewModel.getReportStatus();
                } else {
                    // handle in case of non-consumed item with report status as null
                    remainingQuantity = remainingQuantity + consumedItemViewModel.getInitialQuantity();
                }
            }
            if (null != reportStatus) {
                viewModel.setProcurementStatus(switch (reportStatus) {
                    case SUBMITTED -> ProcurementStatus.IN_PROGRESS;
                    case SENT_BACK -> ProcurementStatus.SENT_BACK;
                    case ACCEPTED ->
                            remainingQuantity == 0 ? ProcurementStatus.FULLY_CONVERTED : ProcurementStatus.PARTIALLY_CONVERTED;
                    case DECLINED ->
                            remainingQuantity == 0 ? ProcurementStatus.CLOSED : ProcurementStatus.PARTIALLY_CLOSED;
                    default -> viewModel.getProcurementStatus();
                });
            } else {
                viewModel.setProcurementStatus(ProcurementStatus.READY_TO_CONVERT);
            }
//            if (viewModel.getProcurementStatus() == ProcurementStatus.IN_PROGRESS) {
//                prIdToInProcessAmountMap.merge(viewModel.getPrId(), viewModel.getPrAmount(), BigDecimal::add);
//            }
            // set amount in process of being approved
            viewModel.setInProcessOfBeingApprovedInPOAmount(prIdToInProcessAmountMap.getOrDefault(viewModel.getPrId(), BigDecimal.ZERO));

            // handle "consumption on PO submitted amount instead of PO accepted issue"
            viewModel.setApprovedInPOAmount(
                    Optional.ofNullable(viewModel.getApprovedInPOAmount()).orElse(BigDecimal.ZERO)
                            .subtract(Optional.ofNullable(viewModel.getInProcessOfBeingApprovedInPOAmount()).orElse(BigDecimal.ZERO))
            );
        }
        // set amount in process of being approved
//        viewModels.forEach(viewModel -> {
//            if (viewModel.getProcurementStatus() == ProcurementStatus.IN_PROGRESS) {
//                viewModel.setInProcessOfBeingApprovedInPOAmount(prIdToInProcessAmountMap.getOrDefault(viewModel.getPrId(), BigDecimal.ZERO));
//            }
//        });


        return isStat ? new PageImpl<>(viewModels) : PageableExecutionUtils.getPage(viewModels, pageable, results::getTotal);

    }

    public static List<ConsumedItemAvailableCountViewModel> sortConsumedItemViewModel(
            List<ConsumedItemAvailableCountViewModel> consumedItem,
            List<ConsumedItemAvailableCountViewModel> consumedItemViewModel) {

        // Define custom priority for ReportStatus
        Map<ReportStatus, Integer> priorityMap = Map.of(
                ReportStatus.SENT_BACK, 3,
                ReportStatus.SUBMITTED, 2,
                ReportStatus.ACCEPTED, 1
        );

        // Create a map of id to highest priority ReportStatus in consumedItem
        Map<Long, Integer> idToHighestPriority = consumedItem.stream()
                .filter(item -> item.getReportStatus() != null)
                .collect(Collectors.toMap(
                        ConsumedItemAvailableCountViewModel::getId,
                        item -> priorityMap.get(item.getReportStatus()),
                        Math::min // Keep the highest priority (lowest value)
                ));

        // Sort consumedItemViewModel based on the priority from the map
        return consumedItemViewModel.stream()
                .sorted(Comparator.comparingInt(
                        item -> idToHighestPriority.getOrDefault(item.getId(), Integer.MAX_VALUE)
                ))
                .collect(Collectors.toList());
    }

    private List<ConsumedItemAvailableCountViewModel> getCopiedList(List<ConsumedItemAvailableCountViewModel> consumedItems) {
        List<ConsumedItemAvailableCountViewModel> newList = new ArrayList<>();
        for (ConsumedItemAvailableCountViewModel item : consumedItems) {
            newList.add(new ConsumedItemAvailableCountViewModel(item));
        }
        return newList;
    }

    public QueryResults<ProcurementViewModel> getProcurementViewModelQueryResults(long companyCode, List<Long> metadataIds, QueueFilterViewModel filterValues, Pageable pageable, boolean isStat) {
        BooleanBuilder builder = initBuilder(companyCode, metadataIds).and(dac.reportStatus.in(ReportStatus.ACCEPTED));
        ConstructorExpression<ProcurementViewModel> queueConstructorExpression = Projections.constructor(ProcurementViewModel.class,
                dac.id,
                pr.id,
                doc.claimAmount, // total
                Expressions.cases()
                        .when(dac.reportStatus.eq(ReportStatus.ACCEPTED))
                        .then(doc.consumedAmount.coalesce(BigDecimal.ZERO))
                        .otherwise(BigDecimal.ZERO), // approved amount
                dac.documentIdentifier,
                dac.createdTimestamp, doc.documentDate, dac.reportStatus, dac.submitDate,
                dac.currentApproverFirstName, dac.currentApproverLastName, dac.currentApproverSystemIdCode, dac.currentApproverEmployeeCode,
                dac.firstName, dac.middleName, dac.lastName,
                dac.employeeCode, dac.employeeGrade,
                dac.updatedTimestamp,
                doc.originatorDocumentIdentifier, doc.documentSubgroupId,
                dac.employeeDepartment,
                pr.expectedDeliveryDate,
                pr.purchaseReason,
                doc.docNo,
                pr.prType.value,
                Expressions.constant(BigDecimal.valueOf(0.0)), // set amount of being in process later
                doc.claimAmount.subtract(doc.consumedAmount.coalesce(BigDecimal.ZERO))); // balance amount

        // With requested filters
        createFilterQuery(filterValues, builder);
        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(isStat ? Pageable.unpaged() : pageable);

        JPAQuery<ProcurementViewModel> query = queryFactory.select(queueConstructorExpression)
                .from(dac)
                .leftJoin(dac.documents, doc)
                .leftJoin(doc.prHeader, pr)
                .where(builder)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]));

        // Apply pagination only if not stat (isStat = false)
        if (!isStat) {
            query.offset((long) pageable.getPageSize() * pageable.getPageNumber())
                    .limit(pageable.getPageSize());
        }

        QueryResults<ProcurementViewModel> results = query.fetchResults();
        return results;
    }

    private void populateConsumedItemsByPurchaseRequestHeaderMap(List<ConsumedItemAvailableCountViewModel> consumedItems, Map<Long, List<ConsumedItemAvailableCountViewModel>> consumedItemQuantityAvailableMap) {
        // This aggregates consumed items and map to its purchase request header
        // map of vendorIds as diffrent item may have diffeernt vendor
        Map<Long, Set<Long>> prIdToVendorIdsMap = new HashMap<>();

        for (ConsumedItemAvailableCountViewModel item : consumedItems) {
            Long prId = item.getPrId();
            Long vendorId = item.getVendorId();

            logger.debug("Processing item with id: {}, prId: {}, vendorId: {}, quantity: {}, initialQuantity: {}",
                    item.getId(), item.getPrId(), item.getVendorId(), item.getQuantity(), item.getInitialQuantity());

            // Initialize or retrieve the set of vendor IDs associated with this prId
            Set<Long> vendorIds = prIdToVendorIdsMap.computeIfAbsent(prId, k -> new HashSet<>());

            // Check if the prId already exists in the map
            if (consumedItemQuantityAvailableMap.containsKey(prId)) {
                // Retrieve the existing list of items for this prId
                List<ConsumedItemAvailableCountViewModel> existingItems = consumedItemQuantityAvailableMap.get(prId);

                // Find the item with the same ID in the list, if it exists
                ConsumedItemAvailableCountViewModel existingViewModel = existingItems.stream()
                        .filter(existingItem -> existingItem.getId().equals(item.getId()))
                        .findFirst()
                        .orElse(null);

                if (existingViewModel != null) {
                    // Update existing item: sum quantities, update initialQuantity and reportStatus if necessary
                    existingViewModel.setQuantity(existingViewModel.getQuantity() + item.getQuantity());

                    // Update initialQuantity only if the item belongs to a different vendor
                    if (!vendorIds.contains(vendorId)) {
                        existingViewModel.setInitialQuantity(existingViewModel.getInitialQuantity() + item.getInitialQuantity());
                        vendorIds.add(vendorId);
                    }

                    // Update reportStatus to the highest priority (ACCEPTED > SUBMITTED > null)
                    existingViewModel.setReportStatus(determineHigherPriorityStatus(existingViewModel.getReportStatus(), item.getReportStatus()));

                    logger.debug("Updated prId: {} with total quantity: {} and total initialQuantity: {}",
                            prId, existingViewModel.getQuantity(), existingViewModel.getInitialQuantity());
                } else {
                    // No existing item with the same ID, so add this item to the list
                    existingItems.add(item);
                    if (!vendorIds.contains(vendorId)) {
                        vendorIds.add(vendorId);
                    }
                    logger.debug("Added new item to existing prId: {} with quantity: {} and initialQuantity: {}",
                            prId, item.getQuantity(), item.getInitialQuantity());
                }
            } else {
                // If prId doesn't exist, create a new list for this prId and add the item to it
                List<ConsumedItemAvailableCountViewModel> newList = new ArrayList<>();
                newList.add(new ConsumedItemAvailableCountViewModel(
                        item.getId(), prId, item.getVendorId(), item.getQuantity(), item.getInitialQuantity(), item.getReportStatus()));
                consumedItemQuantityAvailableMap.put(prId, newList);
                vendorIds.add(vendorId);

                logger.debug("Added new entry for prId: {} with quantity: {} and initialQuantity: {}",
                        prId, item.getQuantity(), item.getInitialQuantity());
            }
        }
    }

    /**
     * Determines the higher priority between two report statuses.
     * ACCEPTED has the highest priority, followed by SUBMITTED, SENT_BACK and then null.
     */
    private ReportStatus determineHigherPriorityStatus(ReportStatus existingStatus, ReportStatus newStatus) {
        if (newStatus == ReportStatus.ACCEPTED) {
            return ReportStatus.ACCEPTED;  // Highest priority
        } else if (newStatus == ReportStatus.SUBMITTED &&
                (existingStatus != ReportStatus.ACCEPTED && existingStatus != ReportStatus.SUBMITTED)) {
            return ReportStatus.SUBMITTED; // Higher than SENT_BACK and null
        } else if (newStatus == ReportStatus.SENT_BACK &&
                (existingStatus == null || existingStatus == ReportStatus.SENT_BACK)) {
            return ReportStatus.SENT_BACK; // Higher than null but lower than SUBMITTED
        } else if (existingStatus == null) {
            return newStatus;  // Set status if existing status is null
        }
        return existingStatus;  // Keep the existing status if it's already higher
    }

    public BigDecimal calculateTotalAmountByPrIdAndPrItemId(Long prId, Long prItemId, double itemQuantity) {
        NumberExpression<BigDecimal> totalAmountPerItem = prItem.unitRate
                .multiply(itemQuantity)
                .add(
                        prItem.unitRate
                                .multiply(itemQuantity)
                                .multiply(prItem.taxPercentage.coalesce(0.0).divide(Expressions.constant(100)))
                );

        // Execute query to fetch total amount for specific PR and PR item
        BigDecimal totalAmount = queryFactory
                .select(totalAmountPerItem)
                .from(pr)
                .join(pr.prItems, prItem)
                .where(pr.id.eq(prId)
                        .and(prItem.id.eq(prItemId)))
                .fetchOne();

        // Return total amount or BigDecimal.ZERO if null
        return totalAmount != null ? totalAmount : BigDecimal.ZERO;
    }

    public Page<BudgetContainerViewModel> getBudgetQueue(long companyCode, long userId, Long budgetStructureMasterId, List<Long> metadataIds, QueueFilterViewModel filterValues, Pageable pageable, String orgKey, List<String> rbacValues) {
        // removed userId from filter as now RBAC is implemented
        BooleanBuilder builder = initBuilder(companyCode, metadataIds);

        // Filter DocumentApprovalContainer (dac) by matching keys and values
        if (!StaticDataRegistry.isNullOrEmptyOrWhitespace(orgKey) && !rbacValues.isEmpty()) {
            builder.and(applyRbac(orgKey, rbacValues));
        }
        ConstructorExpression<BudgetContainerViewModel> queueConstructorExpression;
        if (budgetStructureMasterId != null) {
            builder.and(bsm.id.eq(budgetStructureMasterId));
        }
        queueConstructorExpression = Projections.constructor(BudgetContainerViewModel.class, dac.id,
                doc.claimAmount, budget.treasury.subtract(budget.consumed.add(budget.locked)), dac.documentIdentifier, dac.createdTimestamp, dac.reportStatus, dac.submitDate,
                dac.currentApproverFirstName, dac.currentApproverLastName, dac.currentApproverSystemIdCode, dac.currentApproverEmployeeCode, dac.firstName,
                dac.middleName, dac.lastName, dac.employeeCode, dac.employeeGrade, dac.updatedTimestamp, doc.originatorDocumentIdentifier, doc.documentSubgroupId,
                dac.employeeDepartment, doc.docNo, doc.documentApprovalContainer.documentMetadata.actionType.stringValue(), bsm.id, budget.budgetCode, bsm.financialYear,
                bsm.structureName, bsm.startMonthIndex, bsm.isApproved);

        createFilterQueryByDocument(StaticDataRegistry.LOOKUP_VALUE_BUDGET, filterValues, builder);
        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);

        QueryResults<BudgetContainerViewModel> results = queryFactory.select(queueConstructorExpression)
                .from(dac)
                .leftJoin(dac.documents, doc)
                .innerJoin(doc.documentSubgroup, docSub)
                .leftJoin(doc.budgetDocumentAction, bda)
                .leftJoin(budget).on(budget.id.eq(bda.budgetId))
                .leftJoin(bsm).on(bda.budgetStructureMasterId.eq(bsm.id))
                .where(builder)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    public List<PurchaseRequestItemDetailsView> getReadyToConvertPrItems(long companyCode, Integer vendorId, List<Long> prIds) {
        List<PurchaseRequestItemDetailsView> prItems = new ArrayList<>();
        if (null != prIds && !prIds.isEmpty()) {
            logger.trace("getReadyToConvertPrItems: Getting PRItems by PRIds:\t{}", prIds);
            prItems = getPRItemsByPRs(companyCode, vendorId, prIds);
        } else if (null != vendorId) {
            logger.trace("getReadyToConvertPrItems: Getting PRItems by vendorId:\t{}", vendorId);
            prItems = getPRItemsForVendor(companyCode, vendorId);
        }

        List<Long> prItemIds = prItems.stream().map(PurchaseRequestItemDetailsView::getId).toList();

        logger.trace("getReadyToConvertPrItems: Creating consumption map for item quantity");
        Map<Long, ConsumedItemAvailableCountViewModel> consumedItemQuantityAvailableMap = new HashMap<>();
        getPRItemsConsumedInPOsWithCounts(prItemIds).forEach(i -> consumedItemQuantityAvailableMap.put(i.getId(), i));

        logger.trace("getReadyToConvertPrItems: Returning with updating values according assumption");
        return prItems
                .stream()
                .filter(i -> !consumedItemQuantityAvailableMap.containsKey(i.getId()) || consumedItemQuantityAvailableMap.get(i.getId()).getQuantity() > 0)
                .map(i -> {
                    if (consumedItemQuantityAvailableMap.containsKey(i.getId())) {
                        // Initially quantity and selected quantity will be same (client will always select whole quantity and then reduce from there)
                        ConsumedItemAvailableCountViewModel viewModel = consumedItemQuantityAvailableMap.get(i.getId());
                        double quantity = (viewModel.getInitialQuantity() - viewModel.getQuantity());
                        handleQuantityChange(quantity, i);
                    }
                    return i;
                })
                .toList();

    }

    private void handleQuantityChange(double quantity, PurchaseRequestItemDetailsView prItem) {
        double currentTaxRate = (prItem.getTaxPercentage() == null ? 0.0 : prItem.getTaxPercentage()) * 0.01; // Todo: Change field name from tax rate to tax percentage
        // Set Quantity
        prItem.setQuantity(quantity);
        prItem.setSelectedQuantity(quantity);
        // Update amounts
        prItem.setAmountWithoutGst(prItem.getUnitRate().multiply(BigDecimal.valueOf(quantity)));
        prItem.setTotalGstAmount(prItem.getAmountWithoutGst().multiply(BigDecimal.valueOf(currentTaxRate)));
        prItem.setTotal(prItem.getAmountWithoutGst().add(prItem.getTotalGstAmount()));
    }

    private void handleQuantityChange(double quantity, PurchaseOrderItemDetailsViewModel poItem) {
        double currentTaxPercentage = poItem.getTaxPercentage() * 0.01;
        // Set Quantity
        poItem.setQuantity(quantity);
        poItem.setSelectedQuantity(quantity);
        // Update amounts
        poItem.setAmountWithoutGst(poItem.getUnitRate().multiply(BigDecimal.valueOf(quantity)));
        poItem.setTotalGstAmount(poItem.getAmountWithoutGst().multiply(BigDecimal.valueOf(currentTaxPercentage)));
        poItem.setTotal(poItem.getAmountWithoutGst().add(poItem.getTotalGstAmount()));
    }

    // Todo : Move below in seperate dedicated repository

    public QueueStatisticsViewModel getStatistics(long companyCode, long userId, List<Long> metadataIds, String orgKey, List<String> rbacValues) {
        // removed userId from filter as now RBAC is implemented
        BooleanBuilder searchQueryBuilder = initBuilder(companyCode, metadataIds);
        if (!StaticDataRegistry.isNullOrEmptyOrWhitespace(orgKey) && !rbacValues.isEmpty()) {
            searchQueryBuilder.and(applyRbac(orgKey, rbacValues));
        } else {
            // handle scenario - when user does not have value against the key for which hierarchy is created - TODO: re-check this.
            searchQueryBuilder.and(dac.creatingUserId.eq(userId));
        }

        return getQueueStatisticsViewModel(searchQueryBuilder);
    }

    public QueueStatisticsViewModel getStatisticsForWidgetDashboard(long companyCode, long userId, List<Long> metadataIds,
                                                                    String orgKey, List<String> rbacValues,
                                                                    YearMonth targetMonthYear, Integer month) {

        BooleanBuilder searchQueryBuilder = initBuilder(companyCode, metadataIds);
        BooleanBuilder dateBuilder = getDateBuilder(targetMonthYear, month);

        if (!StaticDataRegistry.isNullOrEmptyOrWhitespace(orgKey) && !rbacValues.isEmpty()) {
            searchQueryBuilder.and(applyRbac(orgKey, rbacValues));
        } else {
            // handle scenario - when user does not have value against the key for which hierarchy is created - TODO: re-check this.
            searchQueryBuilder.and(dac.creatingUserId.eq(userId));
        }
        return getQueueStatisticsViewModel(searchQueryBuilder.and(dateBuilder));
    }


    private QueueStatisticsViewModel getQueueStatisticsViewModel(BooleanBuilder searchQueryBuilder) {
        ConstructorExpression<QueueStatisticsGenericViewModel> ce = Projections.constructor(QueueStatisticsGenericViewModel.class,
                dac.count(), dac.approvalContainerClaimAmount.sum(), dac.reportStatus);

        Map<ReportStatus, QueueStatisticsGenericViewModel> resultSet = new HashMap<>();
        queryFactory.select(ce)
                .from(doc)
                .join(doc.documentApprovalContainer, dac)
                .where(searchQueryBuilder)
                .groupBy(dac.reportStatus)
                .fetch().forEach(i -> resultSet.put(i.getStatus(), i));

        QueueStatisticsGenericViewModel defaultView = new QueueStatisticsGenericViewModel(0l, BigDecimal.valueOf(0.0));

        return new QueueStatisticsViewModel(
                resultSet.getOrDefault(ReportStatus.DRAFT, defaultView).getCount(),
                resultSet.getOrDefault(ReportStatus.SUBMITTED, defaultView).getCount(),
                resultSet.getOrDefault(ReportStatus.ACCEPTED, defaultView).getCount(),
                resultSet.getOrDefault(ReportStatus.REVOKED, defaultView).getCount(),
                resultSet.getOrDefault(ReportStatus.POSTED_TO_DESTINATION_SYSTEM, defaultView).getCount(),
                resultSet.getOrDefault(ReportStatus.PAID, defaultView).getCount(),
                resultSet.getOrDefault(ReportStatus.SENT_BACK, defaultView).getCount(),
                resultSet.getOrDefault(ReportStatus.PARKED, defaultView).getCount(),
                resultSet.getOrDefault(ReportStatus.DRAFT, defaultView).getAmount(),
                resultSet.getOrDefault(ReportStatus.SUBMITTED, defaultView).getAmount(),
                resultSet.getOrDefault(ReportStatus.ACCEPTED, defaultView).getAmount(),
                resultSet.getOrDefault(ReportStatus.REVOKED, defaultView).getAmount(),
                resultSet.getOrDefault(ReportStatus.POSTED_TO_DESTINATION_SYSTEM, defaultView).getAmount(),
                resultSet.getOrDefault(ReportStatus.PAID, defaultView).getAmount(),
                resultSet.getOrDefault(ReportStatus.SENT_BACK, defaultView).getAmount(),
                resultSet.getOrDefault(ReportStatus.PARKED, defaultView).getAmount()
        );
    }

    // --- Helper methods ---

    public Double getConsumedPRItemCount(long prItemId, long companyCode) {

        List<Long> prItemIds = Collections.singletonList(prItemId);
        List<ConsumedItemAvailableCountViewModel> results = getPRItemsConsumedInPOsWithCounts(prItemIds);
        ConsumedItemAvailableCountViewModel viewModel = results.isEmpty() ? new ConsumedItemAvailableCountViewModel(prItemId, fetchPRQuantity(prItemId)) : results.get(0);
        // results.isEmpty() means no item is consumed then fetch quantity as it is and set, in such case 3rd param set to null
        return null == viewModel.getQuantity() ? 0 : viewModel.getQuantity(); // If consumed amount is null then that PR item hasn't been consumed yet.
    }

    private Double fetchPRQuantity(long prItemId) {
        Double poItemQuantity = queryFactory.select(prItem.quantity)
                .from(prItem)
                .where(prItem.id.eq(prItemId))
                .fetchOne();
        return null == poItemQuantity ? 0 : poItemQuantity;
    }

    private List<PurchaseRequestItemDetailsView> getPRItemsForVendor(long companyCode, Integer vendorId) {
        BooleanBuilder searchByReadyItemsByVendor = new BooleanBuilder()
                .and(dac.companyCode.eq(companyCode))
                .and(dac.reportStatus.eq(ReportStatus.ACCEPTED))
                .and(company.companyId.eq(vendorId));

        ConstructorExpression<PurchaseRequestItemDetailsView> ce = Projections.constructor(PurchaseRequestItemDetailsView.class,
                prItem.id, doc.docNo, prItem.type, prItem.name, prItem.hsn, prItem.itemCode, prItem.unitOfMeasure,
                prItem.description, prItem.quantity, prItem.unitRate, prItem.taxPercentage, prItem.amountWithoutGst, prItem.totalGstAmount, prItem.total, Expressions.numberTemplate(Integer.class, "0"), company.companyId,
                new CaseBuilder().when(company.supplierCompanyName.isNull()).then(Expressions.constant("N/A")).otherwise(company.supplierCompanyName), prItem.budgetNode.id, prItem.budgetNode.budgetCode, user.firstName.concat(" ").concat(user.lastName), prItem.createdAt);

        return queryFactory.select(ce)
                .from(prItem)
                .innerJoin(prItem.purchaseRequestHeader, pr)
                .innerJoin(prItem.vendor, company)
                .innerJoin(pr.document, doc)
                .innerJoin(doc.documentApprovalContainer, dac)
                .where(searchByReadyItemsByVendor)
                .fetch();
    }

    private List<PurchaseRequestItemDetailsView> getPRItemsByPRs(long companyCode, Integer vendorId, List<Long> prIds) {

        ConstructorExpression<PurchaseRequestItemDetailsView> ce = Projections.constructor(PurchaseRequestItemDetailsView.class,
                prItem.id, doc.docNo, prItem.type, prItem.name, prItem.hsn, prItem.itemCode, prItem.unitOfMeasure,
                prItem.description, prItem.quantity, prItem.unitRate, prItem.taxPercentage, prItem.amountWithoutGst, prItem.totalGstAmount, prItem.total, Expressions.numberTemplate(Integer.class, "0"), company.companyId,
                new CaseBuilder().when(company.supplierCompanyName.isNull()).then(Expressions.constant("N/A")).otherwise(company.supplierCompanyName),
                new CaseBuilder().when(budget.id.isNull()).then(Expressions.constant(0L)).otherwise(budget.id),
                new CaseBuilder().when(budget.id.isNull()).then(Expressions.constant("N/A")).otherwise(budget.budgetCode),
                user.firstName.concat(" ").concat(user.lastName),
                prItem.createdAt, doc.requesterRemark);

        BooleanBuilder builder = new BooleanBuilder()
                .and(dac.companyCode.eq(companyCode))
                .and(dac.reportStatus.eq(ReportStatus.ACCEPTED))
                .and(prItem.purchaseRequestHeaderId.in(prIds));

        // Include PR items with matching vendorId or without any vendor
        builder.andAnyOf(
                company.companyId.eq(vendorId), // PR items with the provided vendor
                company.companyId.isNull()      // PR items without any vendor
        );

        return queryFactory.select(ce)
                .from(prItem)
                .innerJoin(prItem.purchaseRequestHeader, pr)
                .leftJoin(prItem.vendor, company)
                .innerJoin(pr.document, doc)
                .innerJoin(pr.createdBy, user)
                .innerJoin(doc.documentApprovalContainer, dac)
                .leftJoin(prItem.budgetNode, budget)
                .where(builder)
//                        .and(company.companyId.eq(vendorId)) // for non-vendor pr selection on continue-to-po default selected vendor id is coming in request - no need of vendor if we have the list of prids
                // PR can either have any vendor or do not have any vendors at all - so non-vendor pr ids can be in list
                .fetch();
    }

    public List<ConsumedItemAvailableCountViewModel> getPRItemsConsumedInPOsWithCounts(List<Long> prItemsIds) {
        BooleanBuilder searchQuery = new BooleanBuilder().and(prItem.id.in(prItemsIds)).and(dac.reportStatus.in(StaticDataRegistry.ITEM_CONSUMED_STATES));

        ConstructorExpression<ConsumedItemAvailableCountViewModel> ce = Projections.constructor(
                ConsumedItemAvailableCountViewModel.class, prItem.id, pr.id, prItem.vendorId, poItem.quantity.sum(), prItem.quantity);

        return queryFactory.select(ce)
                .from(prItem) // Addition joins handled by above statement and the fact that using result as input to the "in" query
                .innerJoin(poItem).on(poItem.purchaseRequestItemId.eq(prItem.id))
                .innerJoin(poItem.purchaseOrderHeader, po)
                .innerJoin(po.document, doc)
                .innerJoin(doc.documentApprovalContainer, dac)
                .where(searchQuery)
                .groupBy(prItem.id, prItem.vendorId, prItem.quantity)
                .fetch();
    }

    public List<ConsumedItemAvailableCountViewModel> getPRItemsConsumedAndNotConsumedInPOsWithCounts(List<Long> prItemsIds) {
        BooleanBuilder searchQuery = new BooleanBuilder().and(prItem.id.in(prItemsIds));

        ConstructorExpression<ConsumedItemAvailableCountViewModel> ce = Projections.constructor(
                ConsumedItemAvailableCountViewModel.class, prItem.id, pr.id, prItem.vendorId, poItem.quantity.sum().coalesce(Double.valueOf(0)), prItem.quantity.coalesce(Double.valueOf(0)), dac.reportStatus);

        return queryFactory.select(ce)
                .from(pr)
                .innerJoin(prItem).on(prItem.purchaseRequestHeaderId.eq(pr.id))
                // Addition joins handled by above statement and the fact that using result as input to the "in" query
                .leftJoin(poItem).on(poItem.purchaseRequestItemId.eq(prItem.id))
                .leftJoin(poItem.purchaseOrderHeader, po)
                .leftJoin(po.document, doc)
                .leftJoin(doc.documentApprovalContainer, dac).on(dac.reportStatus.in(StaticDataRegistry.ITEM_CONSUMED_STATES_PROCUREMENT))
                .where(searchQuery)
                .groupBy(prItem.id, pr.id, prItem.vendorId, prItem.quantity, dac.reportStatus)
                .fetch();
    }

    private List<ConsumedItemAvailableCountViewModel> getPOItemsConsumedInInvoicesWithCounts(List<Long> poItemsIds) {

        BooleanBuilder searchQuery = new BooleanBuilder().and(poItem.id.in(poItemsIds)).and(dac.reportStatus.in(StaticDataRegistry.ITEM_CONSUMED_STATES));

//        NumberExpression<Double> availableQuantity = poItem.quantity.subtract(invoiceItem.quantity.sum()); // PO item will be duplicate across invoice items (consumed multiple times) so we only take the actual quantity against the sum of the used quantity in invoice

//        ConstructorExpression<ConsumedItemAvailableCountViewModel> ce = Projections.constructor(
//                ConsumedItemAvailableCountViewModel.class, poItem.id, availableQuantity);

        ConstructorExpression<ConsumedItemAvailableCountViewModel> ce = Projections.constructor(
                ConsumedItemAvailableCountViewModel.class, poItem.id, po.id, poItem.vendorId, invoiceItem.quantity.sum(), poItem.quantity);

        return queryFactory.select(ce)
                .from(poItem) // Addition joins handled by above statement and the fact that using result as input to the "in" query
                .innerJoin(invoiceItem).on(invoiceItem.purchaseOrderItemId.eq(poItem.id))
                .innerJoin(invoiceItem.invoiceHeader, invoiceHeader)
                .innerJoin(invoiceHeader.document, doc)
                .innerJoin(doc.documentApprovalContainer, dac)
                .where(searchQuery)
                .groupBy(poItem.id, poItem.vendorId, poItem.quantity)
                .fetch();
    }

    public List<PRHeaderWithConsumptionCount> getAvailablePRHeaderWithCounts(long companyCode, long vendorId) {

        ConstructorExpression<PRHeaderWithConsumptionCount> ce = Projections.constructor(PRHeaderWithConsumptionCount.class,
                pr.id, prItem.id, prItem.quantity.subtract(poItem.quantity.sum()));

        return queryFactory.select(ce)
                .from(poItem)
                .innerJoin(poItem.purchaseOrderHeader, po)
                .innerJoin(poItem.purchaseRequestItem, prItem)
                .innerJoin(prItem.purchaseRequestHeader, pr)
                .innerJoin(po.document, doc)
                .innerJoin(doc.documentApprovalContainer, dac)
                .where(new BooleanBuilder()
                        .and(dac.companyCode.eq(companyCode))
                        .and(dac.reportStatus.in(StaticDataRegistry.ITEM_CONSUMED_STATES))
                        .and(poItem.vendorId.eq(vendorId)))
                .groupBy(pr.id, prItem.id, prItem.quantity)
                .fetch();

    }

    public List<PRHeaderWithConsumptionCount> getAvailablePOHeaderWithCounts(long companyCode, Long vendorId) {

        ConstructorExpression<PRHeaderWithConsumptionCount> ce = Projections.constructor(PRHeaderWithConsumptionCount.class,
                po.id, poItem.id, poItem.quantity.subtract(invoiceItem.quantity.sum().coalesce(0d))
        );
// new query
//        return queryFactory.select(ce)
//                .from(poItem)
//                .innerJoin(poItem.purchaseOrderHeader, po)
//                .leftJoin(invoiceItem)
//                .on(invoiceItem.purchaseOrderItem.eq(poItem)) // Explicit join condition
//                .leftJoin(invoiceItem.invoiceHeader, invoiceHeader)
//                .leftJoin(invoiceHeader.document, doc)
//                .leftJoin(doc.documentApprovalContainer, dac)
//                .where(new BooleanBuilder()
//                        .and(dac.companyCode.eq(companyCode))
//                        .andAnyOf(
//                                dac.reportStatus.in(StaticDataRegistry.ITEM_CONSUMED_STATES), // For consumed items
//                                invoiceItem.isNull() // For non-consumed items
//                        )
//                        .and(po.vendorId.eq(vendorId.intValue()))) // Ensure filtering is done on PO vendorId
//                .groupBy(po.id, poItem.id, poItem.quantity)
//                .fetch();
// -- previos query
        return queryFactory.select(ce)
                .from(invoiceItem)
                .innerJoin(invoiceItem.invoiceHeader, invoiceHeader)
                .innerJoin(invoiceItem.purchaseOrderItem, poItem)
                .innerJoin(poItem.purchaseOrderHeader, po)
                .innerJoin(invoiceHeader.document, doc)
                .innerJoin(doc.documentApprovalContainer, dac)
                .where(new BooleanBuilder()
                        .and(dac.companyCode.eq(companyCode))
                        .and(dac.reportStatus.in(StaticDataRegistry.ITEM_CONSUMED_STATES))
                        .and(invoiceItem.vendorId.eq(vendorId)))
                .groupBy(po.id, poItem.id, poItem.quantity)
                .fetch();

    }

    public ConsumedItemAvailableCountViewModel getConsumedPOItemCount(long poItemId, long companyCode) {
        List<Long> poItemIds = Collections.singletonList(poItemId);
        List<ConsumedItemAvailableCountViewModel> results = getPOItemsConsumedInInvoicesWithCounts(poItemIds);
        ConsumedItemAvailableCountViewModel viewModel = results.isEmpty() ? new ConsumedItemAvailableCountViewModel(poItemId, fetchPOQuantity(poItemId)) : results.get(0);
        // results.isEmpty() means no item is consumed then fetch quantity as it is and set, in such case 3rd param set to null
        if (viewModel.getQuantity() == null)
            viewModel.setQuantity(0.0);
        return viewModel; // If consumed amount is null then that PR item hasn't been consumed yet.
    }

    private Double fetchPOQuantity(long poItemId) {
        Double poItemQuantity = queryFactory.select(poItem.quantity)
                .from(poItem)
                .where(poItem.id.eq(poItemId))
                .fetchOne();
        return null == poItemQuantity ? 0 : poItemQuantity;
    }

    public List<ReportStatus> getReportStatusForConsumedItem(Long prItemId) {
        return queryFactory.select(dac.reportStatus)
                .from(poItem)
                .innerJoin(poItem.purchaseOrderHeader, po)
                .innerJoin(po.document, doc)
                .innerJoin(doc.documentApprovalContainer, dac)
                .where(poItem.purchaseRequestItemId.eq(prItemId))
                .fetch();
    }

    public Map<Long, ReportStatus> getReportStatusForPRItemsByPRHeader(List<Long> prHeaderIds) {
        return queryFactory
                .select(poItem.purchaseRequestItemId, dac.reportStatus)
                .from(poItem)
                .innerJoin(poItem.purchaseOrderHeader, po)
                .innerJoin(po.document, doc)
                .innerJoin(doc.documentApprovalContainer, dac)
                .where(poItem.purchaseRequestItem.purchaseRequestHeader.id.in(prHeaderIds))
                .fetch()
                .stream()
                .collect(Collectors.toMap(
                        tuple -> tuple.get(poItem.purchaseRequestItemId), // Key: PR item ID
                        tuple -> tuple.get(dac.reportStatus)              // Value: Report status
                ));
    }

    private List<OrderSpecifier<?>> createStandardOrderSpecifier(Pageable pageable) {
        List<OrderSpecifier<?>> defaultOrderSpecifier = pageable.getSort().stream()
                .map(order -> switch (order.getProperty()) {
                    case "createdDate" ->
                            order.getDirection().isAscending() ? dac.createdDate.asc() : dac.createdDate.desc();
                    case "approvalContainerTitle" ->
                            order.getDirection().isAscending() ? dac.approvalContainerTitle.asc() : dac.approvalContainerTitle.desc();
                    case "approvalContainerClaimAmount" ->
                            order.getDirection().isAscending() ? dac.approvalContainerClaimAmount.asc() : dac.approvalContainerClaimAmount.desc();
                    case "updatedTimestamp" ->
                            order.getDirection().isAscending() ? dac.updatedTimestamp.asc() : dac.updatedTimestamp.desc();
                    case "lastActionAt" ->
                            order.getDirection().isAscending() ? dac.lastActionedAt.asc() : dac.lastActionedAt.desc();
                    case "reportStatus" ->
                            order.getDirection().isAscending() ? dac.reportStatus.asc() : dac.reportStatus.desc();
                    case "firstName" ->
                            order.getDirection().isAscending() ? user.firstName.asc() : user.firstName.desc();
                    default -> dac.id.desc();
                }).collect(Collectors.toList());
        if (defaultOrderSpecifier.isEmpty())
            defaultOrderSpecifier.add(dac.id.desc());
        return defaultOrderSpecifier;
    }

    private void createFilterQuery(QueueFilterViewModel filterValues, BooleanBuilder builder) {
        Map<String, List<String>> filters = filterValues.getSearchCriteria() == null ? new HashMap<>() : filterValues.getSearchCriteria();
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            BooleanBuilder innerBuilder = new BooleanBuilder();
            switch (searchCriteria) {
                case StringConstants.EMPLOYEE_NAME:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(dac.firstName.toLowerCase().contains(caseInsensitive).or(dac.lastName.toLowerCase().contains(caseInsensitive)));
                        }
                    });
                    break;
                case StringConstants.EMPLOYEE_CODE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(dac.employeeCode.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;
                case StringConstants.APPROVER_NAME:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(dac.currentApproverFirstName.toLowerCase().contains(caseInsensitive).or(dac.currentApproverLastName.toLowerCase().contains(caseInsensitive)));
                        }
                    });
                    break;
                case StringConstants.APPROVER_EMPLOYEE_CODE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(dac.currentApproverEmployeeCode.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;
                case StringConstants.DOCUMENT_IDENTIFIER:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(dac.documentIdentifier.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;
                case StringConstants.REPORT_TITLE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(dac.approvalContainerTitle.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;
                case StringConstants.CREATED_DATE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            LocalDate searchDate = null;
                            try {
                                searchDate = LocalDate.parse(value);
                                innerBuilder.or(dac.createdDate.eq(searchDate));
                            } catch (DateTimeParseException e) {
                                logger.error(String.format("createFilterQuery: Could not convert {%s} to Date", value));
                            } catch (Exception e) {
                                logger.error(String.format("%s: %s", StaticDataRegistry.ERROR_MESSAGE_INVOICE_QUEUE_UNHANDLED, e));
                            }
                        }
                    });
                    break;
                case StringConstants.REPORT_CLAIM_AMOUNT:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            try {
                                innerBuilder.or(dac.approvalContainerClaimAmount.eq(new BigDecimal(value)));
                            } catch (NumberFormatException e) {
                                logger.error(String.format("createFilterQuery: Could not convert {%s} to BigDecimal", value));
                            } catch (Exception e) {
                                logger.error(String.format("%s: %s", StaticDataRegistry.ERROR_MESSAGE_INVOICE_QUEUE_UNHANDLED, e));
                            }

                        }
                    });
                    break;
                case StringConstants.REPORT_STATUS:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            try {
                                innerBuilder.or(dac.reportStatus.eq(ReportStatus.valueOf(value)));
                            } catch (IllegalArgumentException e) {
                                logger.error(String.format("createFilterQuery: Could not convert {%s} to Report Status", value));
                            } catch (Exception e) {
                                logger.error(String.format("%s: %s", StaticDataRegistry.ERROR_MESSAGE_INVOICE_QUEUE_UNHANDLED, e));
                            }

                        }
                    });
                    break;
                case StringConstants.HAS_PRECEDING_DOCUMENT:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            if (Boolean.parseBoolean(value)) {
                                innerBuilder.or(docSub.hasPrecedingDocument.eq(true));
                            } else {
                                innerBuilder.or(docSub.hasPrecedingDocument.eq(false));
                            }
//                            switch (value.toLowerCase()) {
//                                case "po based", "pr based":
//                                    innerBuilder.or(docSub.hasPrecedingDocument.eq(true));
//                                    break;
//                                case "non-po based", "non-pr based":
//                                    innerBuilder.or(docSub.hasPrecedingDocument.eq(false));
//                                    break;
//                                default:
//                            }
                        }
                    });
                    break;
                default:
            }
            builder.and(innerBuilder);
        }
    }

    private void createFilterQueryByDocument(String documentType, QueueFilterViewModel filterValues, BooleanBuilder builder) {
        Map<String, List<String>> filters = filterValues.getSearchCriteria() == null ? new HashMap<>() : filterValues.getSearchCriteria();
        addGlobalFilters(filters, builder);
        switch (documentType) {
            case StaticDataRegistry.LOOKUP_VALUE_PO -> addPurchaseOrderFilters(filters, builder);
            case StaticDataRegistry.LOOKUP_VALUE_PR -> addPurchaseRequestFilters(filters, builder);
            case StaticDataRegistry.LOOKUP_VALUE_BUDGET -> addBudgetFilters(filters, builder);
            default -> addInvoiceFilters(filters, builder);
        }
    }

    private void addInvoiceFilters(Map<String, List<String>> filters, BooleanBuilder builder) {
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            BooleanBuilder innerBuilder = new BooleanBuilder();
            switch (searchCriteria) {
                case StringConstants.SUPPLIER_LEGAL_NAME:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(invoiceHeader.supplier.legalName.toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.VENDOR_CODE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(invoiceHeader.supplier.vendorCode.toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.UPLOADED_BY:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or((user.firstName.toLowerCase().concat(" ").concat(user.lastName.toLowerCase())).contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.GSTIN:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(invoiceHeader.supplier.gst.toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.GSTIN_STATUS:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(invoiceHeader.supplier.gstinStatus.toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                default:
            }
            builder.and(innerBuilder);
        }
    }

    private void addPurchaseOrderFilters(Map<String, List<String>> filters, BooleanBuilder builder) {
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            BooleanBuilder innerBuilder = new BooleanBuilder();
            switch (searchCriteria) {
                case StringConstants.VENDOR_NAME:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(company.supplierCompanyName.toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.VENDOR_CODE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(company.vendorCode.toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.GSTIN:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(po.vendor.gst.toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.GSTIN_STATUS:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(po.vendor.gstinStatus.toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.MSME_STATUS:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(po.vendor.msmeStatus.stringValue().toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                default:
            }
            builder.and(innerBuilder);
        }
    }


    private void addPurchaseRequestFilters(Map<String, List<String>> filters, BooleanBuilder builder) {
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            BooleanBuilder innerBuilder = new BooleanBuilder();
            switch (searchCriteria) {
                default:
            }
        }
    }

    private void addBudgetFilters(Map<String, List<String>> filters, BooleanBuilder builder) {
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            BooleanBuilder innerBuilder = new BooleanBuilder();
            switch (searchCriteria) {
                case StringConstants.BUDGET_CODE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(budget.budgetCode.toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.BUDGET_STATUS:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            if (Boolean.parseBoolean(value)) { // ACTIVATE
                                innerBuilder.or(budget.isActive.eq(true));
                            } else { // DEACTIVATE
                                innerBuilder.or(budget.isActive.eq(false));
                            }
                        }
                    });
                    break;
                default:
            }
            builder.and(innerBuilder);
        }
    }

    private void addGlobalFilters(Map<String, List<String>> filters, BooleanBuilder builder) {
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            BooleanBuilder innerBuilder = new BooleanBuilder();
            switch (searchCriteria) {
                case StringConstants.EMPLOYEE_NAME:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(dac.firstName.toLowerCase().contains(caseInsensitive).or(dac.lastName.toLowerCase().contains(caseInsensitive)));
                        }
                    });
                    break;
                case StringConstants.EMPLOYEE_CODE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(dac.employeeCode.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;
                case StringConstants.APPROVER_NAME, StringConstants.APPROVER:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(dac.currentApproverFirstName.toLowerCase().contains(caseInsensitive).or(dac.currentApproverLastName.toLowerCase().contains(caseInsensitive)));
                        }
                    });
                    break;
                case StringConstants.APPROVER_EMPLOYEE_CODE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(dac.currentApproverEmployeeCode.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;
                case StringConstants.DOCUMENT_IDENTIFIER:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(dac.documentIdentifier.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;
                case StringConstants.REPORT_TITLE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            innerBuilder.or(dac.approvalContainerTitle.toLowerCase().contains(value.toLowerCase()));
                        }
                    });
                    break;
                case StringConstants.CREATED_DATE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            LocalDate searchDate = null;
                            try {
                                searchDate = LocalDate.parse(value);
                                innerBuilder.or(dac.createdDate.eq(searchDate));
                            } catch (DateTimeParseException e) {
                                logger.error(String.format("createFilterQuery: Could not convert {%s} to Date", value));
                            } catch (Exception e) {
                                logger.error(String.format("%s: %s", StaticDataRegistry.ERROR_MESSAGE_INVOICE_QUEUE_UNHANDLED, e));
                            }
                        }
                    });
                    break;
                case StringConstants.REPORT_CLAIM_AMOUNT, StringConstants.PURCHASE_ORDER_AMOUNT,
                     StringConstants.PURCHASE_REQUEST_AMOUNT:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            try {
                                innerBuilder.or(dac.approvalContainerClaimAmount.eq(new BigDecimal(value)));
                            } catch (NumberFormatException e) {
                                logger.error(String.format("createFilterQuery: Could not convert {%s} to BigDecimal", value));
                            } catch (Exception e) {
                                logger.error(String.format("%s: %s", StaticDataRegistry.ERROR_MESSAGE_INVOICE_QUEUE_UNHANDLED, e));
                            }

                        }
                    });
                    break;
                case StringConstants.REPORT_STATUS:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            try {
                                innerBuilder.or(dac.reportStatus.eq(ReportStatus.valueOf(value)));
                            } catch (IllegalArgumentException e) {
                                logger.error(String.format("createFilterQuery: Could not convert {%s} to Report Status", value));
                            } catch (Exception e) {
                                logger.error(String.format("%s: %s", StaticDataRegistry.ERROR_MESSAGE_INVOICE_QUEUE_UNHANDLED, e));
                            }

                        }
                    });
                    break;
                case StringConstants.HAS_PRECEDING_DOCUMENT:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            if (Boolean.parseBoolean(value)) {
                                innerBuilder.or(docSub.hasPrecedingDocument.eq(true));
                            } else {
                                innerBuilder.or(docSub.hasPrecedingDocument.eq(false));
                            }
//                            switch (value.toLowerCase()) {
//                                case "po based", "pr based":
//                                    innerBuilder.or(docSub.hasPrecedingDocument.eq(true));
//                                    break;
//                                case "non-po based", "non-pr based":
//                                    innerBuilder.or(docSub.hasPrecedingDocument.eq(false));
//                                    break;
//                                default:
//                            }
                        }
                    });
                    break;

                // Todo: Confirm date fields work
                case StringConstants.DOCUMENT_DATE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(doc.documentDate.stringValue().toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.SUBMISSION_DATE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(dac.submitDate.stringValue().toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.EXPENSE_TYPE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(dac.documentMetadata.applicableExpenseType.stringValue().toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.DOCUMENT_GROUP, StringConstants.DOCUMENT_GROUP_SHORT:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(dac.documentMetadata.documentGroup.toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.DOCUMENT_SUBGROUP, StringConstants.DOCUMENT_SUBGROUP_SHORT:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(doc.documentSubgroup.documentSubgroup.toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.DOCUMENT_TYPE:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(dac.documentMetadata.documentType.toLowerCase().contains(caseInsensitive));
                        }
                    });
                    break;
                case StringConstants.DOCUMENT_NUMBER:
                    entry.getValue().forEach(value -> {
                        if (!isNullOrEmpty(value)) {
                            String caseInsensitive = value.toLowerCase();
                            innerBuilder.or(doc.docNo.isNotNull().and(doc.docNo.toLowerCase().contains(caseInsensitive)));
                        }
                    });
                    break;
                default:
            }
            builder.and(innerBuilder);
        }
    }


    private BooleanBuilder initBuilder(long companyCode, long userId, List<Long> metadataIds) {
        return new BooleanBuilder().and(dac.companyCode.eq(companyCode)).and(dac.creatingUserId.eq(userId)).and(dac.documentMetadataId.in(metadataIds));
    }

    private BooleanBuilder initBuilder(long companyCode, List<Long> metadataIds) {
        return new BooleanBuilder().and(dac.companyCode.eq(companyCode)).and(dac.documentMetadataId.in(metadataIds));
    }

    private BooleanBuilder initBuilder(long companyCode) {
        return new BooleanBuilder().and(dac.companyCode.eq(companyCode));
    }

    private BooleanBuilder getDateBuilder(YearMonth targetMonthYear, Integer year) {
        if (targetMonthYear != null) {
            // Filter for a specific month
            LocalDate startOfMonth = targetMonthYear.atDay(1);
            LocalDate endOfMonth = targetMonthYear.atEndOfMonth();
            return new BooleanBuilder().and(doc.documentDate.between(startOfMonth, endOfMonth));
        } else if (year != null) {
            // Filter for the whole financial year (April to March)
            LocalDate startOfYear = LocalDate.of(year, 4, 1);  // Start in April
            LocalDate endOfYear = LocalDate.of(year + 1, 3, 31);
            return new BooleanBuilder().and(doc.documentDate.between(startOfYear, endOfYear));
        } else {
            // Default to the current financial year (April to March)
            int currentYear = LocalDate.now().getYear();
            LocalDate startOfYear = LocalDate.now().isBefore(LocalDate.of(currentYear, 4, 1)) ?
                    LocalDate.of(currentYear - 1, 4, 1) :
                    LocalDate.of(currentYear, 4, 1);
            LocalDate endOfYear = startOfYear.plusYears(1).withMonth(3).withDayOfMonth(31);
            return new BooleanBuilder().and(doc.documentDate.between(startOfYear, endOfYear));
        }
    }

    private static boolean isNullOrEmpty(String value) {
        return value == null || value.isEmpty();
    }

    public List<PurchaseRequestItemDetailsView> getLineItemsReadyToConvertByPR(long companyCode, Integer vendorId, GenericListRequestViewModel<Long> prIds) {
        return getReadyToConvertPrItems(companyCode, vendorId, prIds.getIds());
    }

    public List<PurchaseOrderItemDetailsViewModel> getReadyToConvertPoItems(long companyCode, Integer vendorId, List<Long> purchaseOrderIds) {

        List<PurchaseOrderItemDetailsViewModel> poItems = new ArrayList<>();

        poItems = getPOItemsByPOs(companyCode, vendorId, purchaseOrderIds);

        List<Long> purchaseOrderItemsIds = poItems.stream().map(PurchaseOrderItemDetailsViewModel::getId).toList();

        Map<Long, ConsumedItemAvailableCountViewModel> consumedItemQuantityAvailableMap = new HashMap<>();
        getPOItemsConsumedInInvoicesWithCounts(purchaseOrderItemsIds).forEach(i -> consumedItemQuantityAvailableMap.put(i.getId(), i));

        return poItems
                .stream()
                .filter(i -> !consumedItemQuantityAvailableMap.containsKey(i.getId()) || consumedItemQuantityAvailableMap.get(i.getId()).getQuantity() > 0)
                .map(i -> {
                    // If not in map, means not yet consumed so use quantity directly from PO.
                    if (consumedItemQuantityAvailableMap.containsKey(i.getId())) {
//                        Double quantity = consumedItemQuantityAvailableMap.get(i.getId()).getQuantity();
                        ConsumedItemAvailableCountViewModel viewModel = consumedItemQuantityAvailableMap.get(i.getId());
                        double quantity = (viewModel.getInitialQuantity() - viewModel.getQuantity());
                        handleQuantityChange(quantity, i);
                    }
                    return i;
                }).toList();
    }

    private List<PurchaseOrderItemDetailsViewModel> getPOItemsByPOs(long companyCode, Integer vendorId, List<Long> purchaseOrderIds) {
        BooleanBuilder searchByReadyItemsByVendor = new BooleanBuilder()
                .and(dac.companyCode.eq(companyCode))
                .and(dac.reportStatus.eq(ReportStatus.RELEASED))
                .and(company.companyId.eq(vendorId))
                .and(poItem.purchaseOrderHeaderId.in(purchaseOrderIds));

        ConstructorExpression<PurchaseOrderItemDetailsViewModel> ce = Projections.constructor(PurchaseOrderItemDetailsViewModel.class,
                poItem.id, doc.docNo, poItem.type, poItem.name, poItem.hsn, poItem.itemCode,
                poItem.description, poItem.quantity, poItem.unitRate, poItem.taxPercentage, poItem.amountWithoutGst, poItem.totalGstAmount, poItem.total, Expressions.numberTemplate(Integer.class, "0"), company.companyId,
                new CaseBuilder().when(company.supplierCompanyName.isNull()).then(Expressions.constant("N/A")).otherwise(company.supplierCompanyName),
                new CaseBuilder().when(budget.id.isNull()).then(Expressions.constant(0L)).otherwise(budget.id),
                new CaseBuilder().when(budget.id.isNull()).then(Expressions.constant("N/A")).otherwise(budget.budgetCode),
                user.firstName.concat(" ").concat(user.lastName), poItem.createdAt, doc.requesterRemark, glm.id, glm.gl, glm.glName, poItem.unitOfMeasure, poItem.purchaseOrderHeaderId);

        return queryFactory.select(ce)
                .from(poItem)
                .innerJoin(poItem.purchaseOrderHeader, po)
                .innerJoin(poItem.vendor, company)
                .innerJoin(po.document, doc)
                .innerJoin(po.createdBy, user)
                .innerJoin(doc.documentApprovalContainer, dac)
                .leftJoin(poItem.budgetNode, budget)
                .leftJoin(poItem.glMaster, glm)
                .where(searchByReadyItemsByVendor)
                .fetch();
    }


    public Page<DocumentApprovalContainerViewModel> getAdminQueueV3(long companyCode, QueueFilterViewModel filterValues, Pageable pageable) {
        QReportState rs2 = new QReportState("rs2");
        JPQLQuery<Integer> subquery = JPAExpressions
                .selectOne()
                .from(rs2)
                .where(rs2.documentApprovalContainerId.eq(reportState.documentApprovalContainerId)
                        .and(rs2.status.eq(reportState.status))
                        .and(rs2.level.lt(reportState.level)));

//        ConstructorExpression<AdminQueueViewModelV2> ce = Projections.constructor(AdminQueueViewModelV2.class,
//                reportState.approverEmployeeCode, reportState.approverFirstName, reportState.level, reportState.approverLastName, dac.id,
//                dac.documentIdentifier, dac.createdDate, dac.submitDate, dac.startDate, dac.endDate,
//                dac.approvalContainerTitle, dac.description, dac.purpose, dac.approvalContainerClaimAmount, dac.reportStatus,
//                dac.actionLevel, dac.containsDeviation, dac.deviationRemarks, dac.approvalContainerSgstAmount,
//                dac.approvalContainerCgstAmount, dac.approvalContainerIgstAmount, dac.approvalContainerTaxableAmount, dac.firstName,
//                dac.middleName, dac.lastName, dac.employeeEmail, dac.employeeCode, dac.employeeGrade,
//                dac.employeeSystemId, dac.expenseType, dac.documentMetadataId, documentMetadata.documentGroup,
//                documentMetadata.documentType, dac.sendBackRemarks, dac.rejectRemarks, dac.delegationRemarks,
//                dac.defaultApproverRemarks, dac.containsSentBack, dac.glPostingDate, dac.paymentDate, dac.updatedTimestamp);

        ConstructorExpression<DocumentApprovalContainerViewModel> ce = Projections.constructor(DocumentApprovalContainerViewModel.class,
                dac.id,                                  // long id
                dac.documentIdentifier,                  // String documentIdentifier
                documentMetadata.documentCategory,                    // String documentCategory
                dac.createdDate,                         // LocalDate createdDate
                dac.submitDate,                          // LocalDate submitDate
                dac.startDate,                           // LocalDate startDate
                dac.endDate,                             // LocalDate endDate
                dac.approvalContainerTitle,              // String approvalContainerTitle
                dac.description,                         // String description
                dac.purpose,                             // String purpose
                reportState.level,                       // Integer level
                dac.approvalContainerClaimAmount,        // BigDecimal approvalContainerClaimAmount
                dac.reportStatus,                        // ReportStatus reportStatus
                dac.actionLevel,                         // int actionLevel
                dac.containsDeviation,                   // boolean containsDeviation
                dac.deviationRemarks,                    // String deviationRemarks
                dac.approvalContainerSgstAmount,         // BigDecimal approvalContainerSgstAmount
                dac.approvalContainerCgstAmount,         // BigDecimal approvalContainerCgstAmount
                dac.approvalContainerIgstAmount,         // BigDecimal approvalContainerIgstAmount
                dac.approvalContainerTaxableAmount,      // BigDecimal approvalContainerTaxableAmount
                dac.firstName,                           // String firstName
                dac.middleName,                          // String middleName
                dac.lastName,                            // String lastName
                dac.employeeEmail,                       // String employeeEmail
                dac.employeeCode,                        // String employeeCode
                dac.employeeGrade,                       // String employeeGrade
                dac.employeeSystemId,                    // Long employeeSystemId
                dac.expenseType,                         // ExpenseType expenseType (Enum expected)
                dac.documentMetadataId,                  // long documentMetadataId
                documentMetadata.documentType,           // String documentType
                documentMetadata.documentGroup,          // String documentGroup
                dac.sendBackRemarks,                     // String sendBackRemarks
                dac.rejectRemarks,                       // String rejectRemarks
                dac.delegationRemarks,                   // String delegationRemarks
                dac.defaultApproverRemarks,              // String defaultApproverRemarks
                reportState.approverFirstName,           // String currentApproverFirstName
                reportState.approverLastName,            // String currentApproverLastName
                dac.currentApproverSystemIdCode,        // String currentApproverSystemIdCode
                reportState.approverEmployeeCode,        // String currentApproverEmployeeCode
                dac.containsSentBack,                    // boolean containsSentBack
                dac.glPostingDate,                       // LocalDate glPostingDate
                dac.paymentDate,                         // LocalDate paymentDate
                documentMetadata.id,                  // Long documentSubgroupId
                payment.paymentReference                            // String utrNumber
        );

        BooleanBuilder conditions = filtersForAdminQueue(companyCode, filterValues, dac, reportState);
        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);
        QueryResults<DocumentApprovalContainerViewModel> results = queryFactory.select(ce).from(dac)
                .innerJoin(doc).on(dac.id.eq(doc.documentApprovalContainer.id))
                .innerJoin(docSub).on(docSub.id.eq(doc.documentSubgroup.id))
                .innerJoin(documentMetadata).on(dac.documentMetadata.id.eq(documentMetadata.id))
                .leftJoin(payment).on(dac.id.eq(payment.documentApprovalContainer.id))
                .leftJoin(reportState)
                .on(
                        reportState.documentApprovalContainerId.eq(dac.id)
                                .and(reportState.status.eq(ExpenseActionStatus.UNACTIONED))
                                .and(subquery.notExists())
                )
                .where(conditions)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    private BooleanBuilder filtersForAdminQueue(long companyCode, QueueFilterViewModel filterValues, QDocumentApprovalContainer er1, QReportState rs) {
        BooleanBuilder builder = new BooleanBuilder();
        for (Map.Entry<String, List<String>> entry : filterValues.getSearchCriteria().entrySet()) {
            String key = entry.getKey();
            List<String> values = entry.getValue();
            if (values == null || values.isEmpty()) continue;

            BooleanBuilder innerBuilder = new BooleanBuilder();

            for (String value : values) {
                if (value == null || value.isBlank()) continue;
                String likeValue = "%" + value + "%";

                switch (key) {
                    case StringConstants.EMPLOYEE_NAME:
                        innerBuilder.or(Expressions.stringTemplate("concat({0}, ' ', {1})", er1.firstName, er1.lastName)
                                .likeIgnoreCase(likeValue));
                        break;

                    case StringConstants.EMPLOYEE_EMAIL:
                        innerBuilder.or(er1.employeeEmail.likeIgnoreCase(likeValue));
                        break;

                    case StringConstants.EMPLOYEE_CODE:
                        innerBuilder.or(er1.employeeCode.likeIgnoreCase(likeValue));
                        break;

                    case StringConstants.APPROVER_NAME:
                        innerBuilder.or(Expressions.stringTemplate("concat({0}, ' ', {1})", rs.approverFirstName, rs.approverLastName)
                                .likeIgnoreCase(likeValue));
                        break;

                    case StringConstants.APPROVER_EMPLOYEE_CODE:
                        innerBuilder.or(rs.approverEmployeeCode.likeIgnoreCase(likeValue));
                        break;

                    case StringConstants.DOCUMENT_IDENTIFIER:
                        innerBuilder.or(er1.documentIdentifier.likeIgnoreCase(likeValue));
                        break;

                    case StringConstants.REPORT_TITLE:
                        innerBuilder.or(er1.approvalContainerTitle.likeIgnoreCase(likeValue));
                        break;

                    case StringConstants.CREATED_DATE:
                        // Assuming YYYY-MM-DD format
                        innerBuilder.or(er1.createdDate.stringValue().like(value + "%"));
                        break;

                    case StringConstants.REPORT_CLAIM_AMOUNT:
                        innerBuilder.or(er1.approvalContainerClaimAmount.stringValue().like(value + "%"));
                        break;

                    case StringConstants.REPORT_STATUS:
                        ReportStatus status = ReportStatus.valueOf(getStatus(value.toUpperCase()));
                        innerBuilder.or(er1.reportStatus.eq(status));
                        break;

                    default:
                        break;
                }
            }
            builder.and(innerBuilder);
        }
        builder.and(er1.companyCode.eq(companyCode));

        return builder;
    }

    private String getStatus(String matchStatus) {
        return Arrays.stream(ReportStatus.values())
                .map(Enum::name)
                .filter(name -> name.equalsIgnoreCase(matchStatus))
                .findFirst()
                .orElse(ReportStatus.ALL.name());
    }

    public Page<ConsumptionDetailsViewModel> getBudgetConsumptionDetailsForPurchaseOrder(Long budgetId, long companyCode, long userId, QueueFilterViewModel filterValues, Pageable pageable, String orgKey, List<String> rbacValues) {
        BooleanBuilder builder = new BooleanBuilder().and(dac.companyCode.eq(companyCode));

        // Filter DocumentApprovalContainer (dac) by matching keys and values
        if (!StaticDataRegistry.isNullOrEmptyOrWhitespace(orgKey) && !rbacValues.isEmpty()) {
            builder.and(applyRbac(orgKey, rbacValues));
        } else {
            // handle scenario - when user does not have value against the key for which hierarchy is created - TODO: re-check this.
            builder.and(dac.creatingUserId.eq(userId));
        }

        // on basis of budget id
        builder.and(poItem.budgetNode.id.eq(budgetId));
        // only submitted, accepted
        builder.and(dac.reportStatus.in(StaticDataRegistry.BUDGET_CONSUMED_STATES));

        ConstructorExpression<ConsumptionDetailsViewModel> queueConstructorExpression =
                Projections.constructor(ConsumptionDetailsViewModel.class,
                        dac.id, dac.documentIdentifier, doc.docNo, docSub.hasPrecedingDocument, dac.createdDate, dac.reportStatus, dac.submitDate,
                        dac.updatedTimestamp, doc.claimAmount, doc.claimAmount.subtract(doc.consumedAmount.coalesce(BigDecimal.ZERO)),
                        doc.documentSubgroupId, doc.source,
                        company.supplierCompanyName, company.vendorCode, company.gst, company.msmeStatus, company.gstinStatus, doc.documentDate
                );

        // With requested filters
        createFilterQuery(filterValues, builder);

        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);

        QueryResults<ConsumptionDetailsViewModel> results = queryFactory.select(queueConstructorExpression)
                .from(poItem)
                .innerJoin(poItem.purchaseOrderHeader, po)
                .innerJoin(po.document, doc)
                .innerJoin(doc.documentApprovalContainer, dac)
                .innerJoin(doc.documentSubgroup, docSub)
                .leftJoin(poItem.vendor, company)
                .where(builder)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    public Page<ConsumptionDetailsViewModel> getBudgetConsumptionDetailsForInvoice(Long budgetId, long companyCode, long userId, QueueFilterViewModel filterValues, Pageable pageable, String orgKey, List<String> rbacValues) {

        BooleanBuilder builder = new BooleanBuilder().and(dac.companyCode.eq(companyCode));

        if (!StaticDataRegistry.isNullOrEmptyOrWhitespace(orgKey) && !rbacValues.isEmpty()) {
            builder.and(applyRbac(orgKey, rbacValues));
        } else {
            // handle scenario - when user does not have value against the key for which hierarchy is created - TODO: re-check this.
            builder.and(dac.creatingUserId.eq(userId));
        }
        // on basis of budget id
        builder.and(invoiceItem.budgetNode.id.eq(budgetId));
        // only submitted, accepted
        builder.and(dac.reportStatus.in(StaticDataRegistry.BUDGET_CONSUMED_STATES));

        ConstructorExpression<ConsumptionDetailsViewModel> queueConstructorExpression =
                Projections.constructor(ConsumptionDetailsViewModel.class,
                        dac.id, dac.documentIdentifier, doc.docNo, docSub.hasPrecedingDocument, dac.createdDate, dac.reportStatus, dac.submitDate,
                        dac.updatedTimestamp, doc.claimAmount, doc.claimAmount.subtract(doc.consumedAmount.coalesce(BigDecimal.ZERO)),
                        doc.documentSubgroupId, doc.source,
                        company.supplierCompanyName, company.vendorCode, company.gst, company.msmeStatus, company.gstinStatus, doc.documentDate
                );

        // With requested filters
        createFilterQuery(filterValues, builder);

        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);

        QueryResults<ConsumptionDetailsViewModel> results = queryFactory.selectDistinct(queueConstructorExpression)
                .from(invoiceItem)
                .innerJoin(invoiceItem.invoiceHeader, invoiceHeader)
                .innerJoin(invoiceHeader.document, doc)
                .innerJoin(doc.documentApprovalContainer, dac)
                .innerJoin(doc.documentSubgroup, docSub)
                .leftJoin(invoiceItem.vendor, company)
                .where(builder)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    public Page<BudgetSyncMasterViewModel> getCustomAllBudgetSyncMaster(long companyCode,
                                                                        long budgetMasterId, Boolean isActive,
                                                                        QueueFilterViewModel filterValues,
                                                                        Pageable pageable) {

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(budgetSyncMaster.companyCode.eq(companyCode));

        QUsers creatingUser = new QUsers("creatingUserAlias");
        QUsers updatingUser = new QUsers("updatingUserAlias");
        QUsers deletingUser = new QUsers("deletingUserAlias");

        if (budgetMasterId > 0) {
            builder.and(budgetSyncMaster.budgetMasterId.eq(budgetMasterId));
        }
        if (isActive != null) {
            if (Boolean.TRUE.equals(isActive)) // this requires as per old implementation
                builder.and(QBudgetSyncMaster.budgetSyncMaster.isActive.eq(isActive));
        }

        Map<String, List<String>> filters = filterValues.getSearchCriteria() == null
                ? new HashMap<>()
                : filterValues.getSearchCriteria();

        addGlobalFiltersForMasterData(filterValues.getSearchString(), builder);

        ConstructorExpression<BudgetSyncMasterViewModel> budgetSyncMasterConstructor = Projections.constructor(
                BudgetSyncMasterViewModel.class,
                budgetSyncMaster.id,
                budgetSyncMaster.uuid,
                budgetSyncMaster.companyCode,
                budgetSyncMaster.code,
                budgetSyncMaster.description,
                budgetSyncMaster.address,
                budgetSyncMaster.contactDetails,
                budgetSyncMaster.sourceId,
                budgetSyncMaster.source,
                budgetSyncMaster.budgetMasterId,
                budgetSyncMaster.isActive,

                new CaseBuilder()
                        .when(creatingUser.isNotNull())
                        .then(creatingUser.firstName.append(" ").append(creatingUser.lastName))
                        .otherwise(""),
                budgetSyncMaster.createdTimestamp,

                new CaseBuilder()
                        .when(updatingUser.isNotNull())
                        .then(updatingUser.firstName.append(" ").append(updatingUser.lastName))
                        .otherwise(""),
                budgetSyncMaster.updatedTimestamp,

                new CaseBuilder()
                        .when(deletingUser.isNotNull())
                        .then(deletingUser.firstName.append(" ").append(deletingUser.lastName))
                        .otherwise(""),
                budgetSyncMaster.deletedTimestamp

        );

        // Execute the query with filtering and pagination
        QueryResults<BudgetSyncMasterViewModel> queryResults = queryFactory
                .select(budgetSyncMasterConstructor)
                .from(budgetSyncMaster)
                .leftJoin(budgetSyncMaster.creatingUser, creatingUser)
                .leftJoin(budgetSyncMaster.updatingUser, updatingUser)
                .leftJoin(budgetSyncMaster.deletingUser, deletingUser)
                .where(builder)
                .orderBy(budgetSyncMaster.description.asc())
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();
        logger.info("QueryBuilder: " + builder.toString());
        // Return the paged results as a Page object
        return new PageImpl<>(queryResults.getResults(), pageable, queryResults.getTotal());
    }

    private void addGlobalFiltersForMasterData(String searchString, BooleanBuilder builder) {
        if (StaticDataRegistry.isNullOrEmptyOrWhitespace(searchString))
            return;
        BooleanBuilder innerBuilder = new BooleanBuilder();

        innerBuilder.or(budgetSyncMaster.description.containsIgnoreCase(searchString));
        innerBuilder.or(budgetSyncMaster.code.containsIgnoreCase(searchString));
        innerBuilder.or(budgetSyncMaster.address.containsIgnoreCase(searchString));

        builder.and(innerBuilder);

    }

    public Page<DocumentApprovalContainerViewModel> getApproverQueueDataV3(StateChannel stateChannel, long companyCode, String userEmail, QueueFilterViewModel filterValues, Pageable pageable) {

        ConstructorExpression<DocumentApprovalContainerViewModel> ce = Projections.constructor(DocumentApprovalContainerViewModel.class,
                dac.id,                                  // long id
                dac.documentIdentifier,                  // String documentIdentifier
                documentMetadata.documentCategory,                    // String documentCategory
                dac.createdDate,                         // LocalDate createdDate
                dac.submitDate,                          // LocalDate submitDate
                dac.startDate,                           // LocalDate startDate
                dac.endDate,                             // LocalDate endDate
                dac.approvalContainerTitle,              // String approvalContainerTitle
                dac.description,                         // String description
                dac.purpose,                             // String purpose
                reportState.level,                       // Integer level
                dac.approvalContainerClaimAmount,        // BigDecimal approvalContainerClaimAmount
                dac.reportStatus,                        // ReportStatus reportStatus
                dac.actionLevel,                         // int actionLevel
                dac.containsDeviation,                   // boolean containsDeviation
                dac.deviationRemarks,                    // String deviationRemarks
                dac.approvalContainerSgstAmount,         // BigDecimal approvalContainerSgstAmount
                dac.approvalContainerCgstAmount,         // BigDecimal approvalContainerCgstAmount
                dac.approvalContainerIgstAmount,         // BigDecimal approvalContainerIgstAmount
                dac.approvalContainerTaxableAmount,      // BigDecimal approvalContainerTaxableAmount
                dac.firstName,                           // String firstName
                dac.middleName,                          // String middleName
                dac.lastName,                            // String lastName
                dac.employeeEmail,                       // String employeeEmail
                dac.employeeCode,                        // String employeeCode
                dac.employeeGrade,                       // String employeeGrade
                dac.employeeSystemId,                    // Long employeeSystemId
                dac.expenseType,                         // ExpenseType expenseType (Enum expected)
                dac.documentMetadataId,                  // long documentMetadataId
                documentMetadata.documentType,           // String documentType
                documentMetadata.documentGroup,          // String documentGroup
                dac.sendBackRemarks,                     // String sendBackRemarks
                dac.rejectRemarks,                       // String rejectRemarks
                dac.delegationRemarks,                   // String delegationRemarks
                dac.defaultApproverRemarks,              // String defaultApproverRemarks
                reportState.approverFirstName,           // String currentApproverFirstName
                reportState.approverLastName,            // String currentApproverLastName
                dac.currentApproverSystemIdCode,        // String currentApproverSystemIdCode
                reportState.approverEmployeeCode,        // String currentApproverEmployeeCode
                dac.containsSentBack,                    // boolean containsSentBack
                dac.glPostingDate,                       // LocalDate glPostingDate
                dac.paymentDate,                         // LocalDate paymentDate
                documentMetadata.id,                  // Long documentSubgroupId
                payment.paymentReference                            // String utrNumber
        );
        BooleanBuilder conditions = filtersForAdminQueue(companyCode, filterValues, dac, reportState);
        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);
        QueryResults<DocumentApprovalContainerViewModel> results = queryFactory.select(ce).from(reportState)
                .innerJoin(dac).on(reportState.documentApprovalContainerId.eq(dac.id)
                        .and(reportState.level.eq(dac.actionLevel)))
                .innerJoin(documentMetadata).on(dac.documentMetadataId.eq(documentMetadata.id))
                .innerJoin(doc).on(dac.id.eq(doc.documentApprovalContainerId))
                .innerJoin(docSub).on(doc.documentSubgroupId.eq(docSub.id))
                .leftJoin(payment).on(dac.id.eq(payment.documentApprovalContainerId))
                .where(conditions,
                        reportState.companyCode.eq(companyCode),
                        reportState.channel.eq(stateChannel),
                        reportState.status.eq(ExpenseActionStatus.UNACTIONED),
                        reportState.approver.eq(userEmail))
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    public Page<DocumentApprovalContainerViewModel> getUserQueueV3(long companyCode, long userId, QueueFilterViewModel filterValues, Pageable pageable) {

        QReportState rs2 = new QReportState("rs2");
        JPQLQuery<Integer> subquery = JPAExpressions
                .selectOne()
                .from(rs2)
                .where(rs2.documentApprovalContainerId.eq(reportState.documentApprovalContainerId)
                        .and(rs2.status.eq(reportState.status))
                        .and(rs2.level.lt(reportState.level)));

        CaseBuilder caseBuilder = new CaseBuilder();
        ConstructorExpression<DocumentApprovalContainerViewModel> ce = Projections.constructor(DocumentApprovalContainerViewModel.class,
                caseBuilder.when(dac.reportStatus.eq(ReportStatus.SUBMITTED)).then(reportState.approverEmployeeCode).otherwise(StringConstants.EMPTY).as("approverEmployeeCode"),
                caseBuilder.when(dac.reportStatus.eq(ReportStatus.SUBMITTED)).then(reportState.approverFirstName).otherwise(StringConstants.EMPTY).as("approverFirstName"),
                reportState.level,
                caseBuilder.when(dac.reportStatus.eq(ReportStatus.SUBMITTED)).then(reportState.approverLastName).otherwise(StringConstants.EMPTY).as("approverLastName"),

                dac.id,
                dac.documentIdentifier,
                dac.createdDate,
                dac.submitDate,
                dac.startDate,
                dac.endDate,
                dac.approvalContainerTitle,
                dac.description,
                dac.purpose,
                dac.approvalContainerClaimAmount,
                dac.reportStatus,
                dac.actionLevel,
                dac.containsDeviation,
                dac.deviationRemarks,
                dac.approvalContainerSgstAmount,
                dac.approvalContainerCgstAmount,
                dac.approvalContainerIgstAmount,
                dac.approvalContainerTaxableAmount,
                dac.firstName,
                dac.middleName,
                dac.lastName,
                dac.employeeEmail,
                dac.employeeCode,
                dac.employeeGrade,
                dac.employeeSystemId,
                dac.expenseType,
                dac.documentMetadataId,
                documentMetadata.documentGroup,
                documentMetadata.documentType,
                dac.sendBackRemarks,
                dac.rejectRemarks,
                dac.delegationRemarks,
                dac.defaultApproverRemarks,
                dac.containsSentBack,
                dac.glPostingDate,
                dac.paymentDate
        );

        BooleanBuilder conditions = filtersForAdminQueue(companyCode, filterValues, dac, reportState);
        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);
        QueryResults<DocumentApprovalContainerViewModel> results = queryFactory.select(ce).from(dac)
                .innerJoin(documentMetadata).on(dac.documentMetadata.id.eq(documentMetadata.id))
                .leftJoin(reportState)
                .on(
                        reportState.documentApprovalContainerId.eq(dac.id)
                                .and(reportState.status.eq(ExpenseActionStatus.UNACTIONED))
                                .and(subquery.notExists())
                )
                .where(conditions, dac.creatingUserId.eq(userId))
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    public Optional<ReportState> getMaxLevelApprovedState(long companyCode, long documentApprovalContainerId, ExpenseActionStatus status) {
        ReportState result = queryFactory
                .selectFrom(reportState)
                .where(
                        reportState.companyCode.eq(companyCode),
                        reportState.documentApprovalContainerId.eq(documentApprovalContainerId),
                        reportState.status.eq(status)
                )
                .orderBy(
                        reportState.level.desc(),
                        reportState.id.desc()
                )
                .fetchFirst();

        return Optional.ofNullable(result);
    }
}
