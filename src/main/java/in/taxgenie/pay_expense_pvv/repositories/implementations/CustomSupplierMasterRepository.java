package in.taxgenie.pay_expense_pvv.repositories.implementations;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.core.types.ConstructorExpression;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SupplierMasterCombinedViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SupplierMastersGstinViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SupplierMastersViewModel;
import jakarta.persistence.EntityManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class CustomSupplierMasterRepository {
    private final JPAQueryFactory queryFactory;
    QReportState reportState = QReportState.reportState;
    QDocumentMetadata documentMetadata = QDocumentMetadata.documentMetadata;
    private static final QDocumentApprovalContainer dac = QDocumentApprovalContainer.documentApprovalContainer;
    private static final QDocument doc = QDocument.document;
    private static final QCompany company = QCompany.company;
    private static final QCompanyMaster companyMaster = QCompanyMaster.companyMaster;
    QUsers createdByUser = new QUsers("createdByUser");
    QUsers updatedByUser = new QUsers("updatedByUser");
    private final Logger logger;

    public CustomSupplierMasterRepository(EntityManager em) {
        this.queryFactory = new JPAQueryFactory(em);
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public Page<SupplierMastersViewModel> getSupplierMasterQueue(long companyCode, QueueFilterViewModel filterValues, Pageable pageable) {
        BooleanBuilder builder = new BooleanBuilder();
        createFilterQuery(filterValues, builder);
        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);

        ConstructorExpression<SupplierMastersViewModel> ce =
                Projections.constructor(SupplierMastersViewModel.class,
                        companyMaster.companyMasterId.longValue(),
                        companyMaster.panNumber,
                        getGstinCount(), // GSTIN count for each CompanyMaster
                        Expressions.asBoolean(true).isTrue(), //TODO: change later to isSupplierConnectRequestEnable
                        companyMaster.createdAt.stringValue(), //TODO: change later to supplierConnectRequestDate
                        companyMaster.paymentTerms,
                        companyMaster.cinNo,
                        companyMaster.cinStatus,
                        companyMaster.tan,
                        companyMaster.tds, // TODO: below - empty case can be handled companyMaster.companies.isEmpty() ? null :
//                        companyMaster.companyList.get(0).supplierCompanyName,
//                        companyMaster.companyList.get(0).vendorCode,
//                        companyMaster.companyList.get(0).msmeStatus,
//                        companyMaster.companyList.get(0).contactPersonEmail,
//                        companyMaster.companyList.get(0).contactPersonName,
//                        companyMaster.companyList.get(0).contactPersonPhone,
//                        companyMaster.companyList.get(0).addr1.concat("").concat(companyMaster.companyList.get(0).addr2),
//                        Expressions.stringTemplate("COALESCE({0}, '')",
//                                JPAExpressions.select(company.supplierCompanyName)
//                                        .from(company)
//                                        .where(company.vendorCode.eq(companyMaster.vendorCode))
//                                        .orderBy(company.createdAt.asc())
//                                        .limit(1)),
                        Expressions.stringTemplate("COALESCE({0}, '')",companyMaster.legalName),

                        Expressions.stringTemplate("COALESCE({0}, '')",
                                JPAExpressions.selectDistinct(company.vendorCode)
                                        .from(company)
                                        .where(company.vendorCode.eq(companyMaster.vendorCode))
//                                        .orderBy(company.createdAt.asc())
                                        .limit(1)),
                        Expressions.booleanTemplate("COALESCE({0}, {1})",
                                JPAExpressions.selectDistinct(company.msmeStatus)
                                        .from(company)
                                        .where(company.vendorCode.eq(companyMaster.vendorCode))
//                                        .orderBy(company.createdAt.asc())
                                        .limit(1), Boolean.FALSE),
                        Expressions.stringTemplate("COALESCE({0}, '')",
                                JPAExpressions.selectDistinct(company.contactPersonEmail)
                                        .from(company)
                                        .where(company.vendorCode.eq(companyMaster.vendorCode))
//                                        .orderBy(company.createdAt.asc())
                                        .limit(1)),
                        Expressions.stringTemplate("COALESCE({0}, '')",
                                JPAExpressions.selectDistinct(company.contactPersonName)
                                        .from(company)
                                        .where(company.vendorCode.eq(companyMaster.vendorCode))
//                                        .orderBy(company.createdAt.asc())
                                        .limit(1)),
                        Expressions.stringTemplate("COALESCE({0}, '')",
                                JPAExpressions.selectDistinct(company.contactPersonPhone)
                                        .from(company)
                                        .where(company.vendorCode.eq(companyMaster.vendorCode))
//                                        .orderBy(company.createdAt.asc())
                                        .limit(1)),
                        Expressions.stringTemplate("COALESCE({0}, '')",
                                JPAExpressions.selectDistinct(company.addr1.concat(" ").concat(company.addr2))
                                        .from(company)
                                        .where(company.vendorCode.eq(companyMaster.vendorCode))
//                                        .orderBy(company.createdAt.asc())
                                        .limit(1)),// company.contactPersonEmail
                        createdByUser.userName,
                        updatedByUser.userName,
                        Expressions.stringTemplate("TO_CHAR({0}, 'YYYY-MM-DD HH12:MI AM')", companyMaster.createdAt),
                        Expressions.stringTemplate("TO_CHAR({0}, 'YYYY-MM-DD HH12:MI AM')", companyMaster.updatedAt)
                );

        QueryResults<SupplierMastersViewModel> results = queryFactory.select(ce)
                .from(companyMaster)
                .leftJoin(createdByUser).on(companyMaster.createdBy.eq(createdByUser.id))
                .leftJoin(updatedByUser).on(companyMaster.updatedBy.eq(updatedByUser.id))
                .where(builder)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();


        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    private JPQLQuery<Integer> getGstinCount() {
        return JPAExpressions
                .select(company.companyId.count().intValue())
                .from(company)
                .where(company.companyMaster.companyMasterId.eq(companyMaster.companyMasterId));
    }

    public List<SupplierMastersGstinViewModel> getAllGSTSuppliersByPan(long companyCode, String pan) {
        BooleanBuilder builder = new BooleanBuilder().and(company.camCompanyId.eq(companyCode));

        ConstructorExpression<SupplierMastersGstinViewModel> ce =
                Projections.constructor(SupplierMastersGstinViewModel.class,
                        company.companyId.longValue(),
                        company.gst,
                        company.gstinStatus,
                        Expressions.cases()
                                .when(company.eInvoiceApplicable.isNull()).then(false)   // Null case defaults to false
                                .when(company.eInvoiceApplicable.eq(1)).then(true)
                                .otherwise(false),
                        company.gstComplianceRating.coalesce(0.0),
                        Expressions.cases()
                                .when(company.itcRiskAmount.isNull()).then("No")         // Null case defaults to "No"
                                .when(
                                        company.itcRiskAmount.isNotNull()
                                                .and(company.itcRiskAmount.ne(0))
                                ).then("Yes")
                                .otherwise("No"),
                        company.bankName,
                        company.bankAccountNumber,
                        company.ifscCode,
                        Expressions.constant("Onboarded"), // TODO: change to correct ReportStatus once vendor onboarding approval flow is implemented
                        company.supplierCompanyName,
                        company.vendorCode,
                        company.msmeStatus,
                        company.contactPersonEmail,
                        company.contactPersonName,
                        company.contactPersonPhone,
                        company.addr1, company.addr2,
                        createdByUser.userName,
                        updatedByUser.userName,
                        Expressions.stringTemplate("TO_CHAR({0}, 'YYYY-MM-DD HH12:MI AM')", company.createdAt),
                        Expressions.stringTemplate("TO_CHAR({0}, 'YYYY-MM-DD HH12:MI AM')", company.updatedAt),
                        company.paymentTerms
                );

        return queryFactory.select(
                        ce)
                .from(company)
                .join(company.companyMaster, companyMaster)
                .leftJoin(createdByUser).on(company.createdBy.eq(createdByUser.id))
                .leftJoin(updatedByUser).on(company.updatedBy.eq(updatedByUser.id))
                .where(builder, companyMaster.panNumber.eq(pan)) // .and(company.gst.notEqualsIgnoreCase("Not--Applicable")))
                .orderBy(company.companyId.asc())
                .fetch();
    }

    private void createFilterQuery(QueueFilterViewModel filterValues, BooleanBuilder builder) {
        Map<String, List<String>> filters = filterValues.getSearchCriteria() == null ? new HashMap<>() : filterValues.getSearchCriteria();
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            List<String> values = entry.getValue();

            BooleanBuilder innerBuilder = new BooleanBuilder();

            switch (searchCriteria) {

                case StringConstants.SupplierMaster.VENDOR_CODE:
                    applyOrCondition(innerBuilder, values, companyMaster.vendorCode::containsIgnoreCase);
                    break;

                case StringConstants.SupplierMaster.VENDOR_NAME:
                    applyOrCondition(innerBuilder, values, companyMaster.legalName::containsIgnoreCase);
                    break;

                case StringConstants.SupplierMaster.GST:
                    applyOrCondition(innerBuilder, values, companyMaster.gstin::containsIgnoreCase);
                    break;

                case StringConstants.SupplierMaster.PAN:
                    applyOrCondition(innerBuilder, values, companyMaster.panNumber::containsIgnoreCase);
                    break;
//                case StringConstants.REPORT_STATUS:
//                    applyOrCondition(innerBuilder, values, value -> {
//                        try {
//                            return dac.reportStatus.eq(ReportStatus.valueOf(value));
//                        } catch (IllegalArgumentException e) {
//                            logger.error(String.format("createFilterQuery: Could not convert {%s} to Report Status", value));
//                            return null;
//                        }
//                    });
//                    break;
                case StringConstants.SupplierMaster.CONTACT_PERSON_EMAIL:
                    applyOrCondition(innerBuilder, values, companyMaster.emailId1::containsIgnoreCase);
                    break;

//                case StringConstants.SupplierMaster.STATUS:
//                    applyOrCondition(innerBuilder, values, value -> companyMaster.status.eq(value));
//                    break;

                case StringConstants.SupplierMaster.CONTACT_PERSON_NAME:
                    applyOrCondition(innerBuilder, values, companyMaster.contactPerson1::containsIgnoreCase);
                    break;

                case StringConstants.SupplierMaster.PAYMENT_TERMS:
                    applyOrCondition(innerBuilder, values, companyMaster.paymentTerms::containsIgnoreCase);
                    break;

                default:
                    logger.warn("Unhandled search criteria: " + searchCriteria);
                    break;
            }

            builder.and(innerBuilder);
        }

    }

    private void applyOrCondition(BooleanBuilder innerBuilder, List<String> values, Function<String, BooleanExpression> expressionFunction) {
        for (String value : values) {
            if (!isNullOrEmpty(value)) {
                BooleanExpression expression = expressionFunction.apply(value);
                if (expression != null) {
                    innerBuilder.or(expression);
                }
            }
        }
    }

    private List<OrderSpecifier<?>> createStandardOrderSpecifier(org.springframework.data.domain.Pageable pageable) {
        List<OrderSpecifier<?>> defaultOrderSpecifier = pageable.getSort().stream()
                .map(order -> {
                    return switch (order.getProperty()) {
                        case "panNumber" ->
                                order.getDirection().isAscending() ? companyMaster.panNumber.asc() : companyMaster.panNumber.desc();
                        default -> companyMaster.companyMasterId.desc();
                    };
                }).collect(Collectors.toList());
        return defaultOrderSpecifier;

    }

    private static boolean isNullOrEmpty(String value) {
        return value == null || value.isEmpty();
    }

    public List<SupplierMasterCombinedViewModel> getAllSupplierMasterDetails(IAuthContextViewModel auth) {
        ConstructorExpression<SupplierMasterCombinedViewModel> ce =
                Projections.constructor(SupplierMasterCombinedViewModel.class,
                        company.companyId.longValue(),
                        companyMaster.panNumber,
                        Expressions.asBoolean(true).isTrue(), //TODO: change later to isSupplierConnectRequestEnable
                        companyMaster.createdAt.stringValue(), //TODO: change later to supplierConnectRequestDate
                        companyMaster.paymentTerms,
                        companyMaster.cinNo,
                        companyMaster.cinStatus,
                        companyMaster.tan,
                        companyMaster.tds,
                        company.gst,
                        company.gstinStatus,
                        Expressions.cases()
                                .when(company.eInvoiceApplicable.isNull()).then(false)   // Null case defaults to false
                                .when(company.eInvoiceApplicable.eq(1)).then(true)
                                .otherwise(false),
                        company.gstComplianceRating.coalesce(0.0),
                        Expressions.cases()
                                .when(company.itcRiskAmount.isNull()).then("No")         // Null case defaults to "No"
                                .when(
                                        company.itcRiskAmount.isNotNull()
                                                .and(company.itcRiskAmount.ne(0))
                                ).then("Yes")
                                .otherwise("No"),
                        company.bankName,
                        company.bankAccountNumber,
                        company.ifscCode,
                        Expressions.constant("Onboarded"), // TODO: change to correct ReportStatus once vendor onboarding approval flow is implemented
                        company.supplierCompanyName,
                        company.vendorCode,
                        company.msmeStatus,
                        company.contactPersonEmail,
                        company.contactPersonName,
                        company.contactPersonPhone,
                        company.addr1.concat(" ").concat(company.addr2),
                        createdByUser.userName,
                        updatedByUser.userName,
                        Expressions.stringTemplate("TO_CHAR({0}, 'YYYY-MM-DD HH12:MI AM')", company.createdAt),
                        Expressions.stringTemplate("TO_CHAR({0}, 'YYYY-MM-DD HH12:MI AM')", company.updatedAt)
                );
        return queryFactory.select(
                        ce)
                .from(company)
                .join(company.companyMaster, companyMaster)
                .leftJoin(createdByUser).on(company.createdBy.eq(createdByUser.id))
                .leftJoin(updatedByUser).on(company.updatedBy.eq(updatedByUser.id))
//                .where(company.gstinStatus.equalsIgnoreCase("Active").and(company.gst.notEqualsIgnoreCase("Not--Applicable")))
                .orderBy(company.companyId.asc())
                .fetch();
    }
}
