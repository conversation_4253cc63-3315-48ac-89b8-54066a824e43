package in.taxgenie.pay_expense_pvv.repositories.implementations;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.core.types.ConstructorExpression;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQueryFactory;
import in.taxgenie.pay_expense_pvv.entities.QUsers;
import in.taxgenie.pay_expense_pvv.entities.invoice.QMailroom;
import in.taxgenie.pay_expense_pvv.entities.mailroom.QInvoiceSyncReport;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.mailroom.InvoiceMailroomDataViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.mailroom.InvoiceMailroomQueueViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.mailroom.InvoiceSyncReportQueueViewModel;
import jakarta.persistence.EntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
public class CustomMailroomRepository {

    private final JPAQueryFactory queryFactory;

    private static final QInvoiceSyncReport invoiceSyncReport = QInvoiceSyncReport.invoiceSyncReport;
    private static final QMailroom mailroom = QMailroom.mailroom;

    public CustomMailroomRepository(EntityManager em) {
        this.queryFactory = new JPAQueryFactory(em);
    }

    // Utility method to check for null or empty strings
    private static boolean isNullOrEmpty(String value) {
        return value == null || value.isEmpty();
    }

    /**
     * Apply filters to the BooleanBuilder dynamically
     */
    private void addQueueFilters(Map<String, List<String>> filters, BooleanBuilder builder) {
        if (filters == null || filters.isEmpty()) return;

        filters.forEach((criteria, values) -> {
            BooleanBuilder innerBuilder = new BooleanBuilder();
            values.stream()
                    .filter(value -> !isNullOrEmpty(value))
                    .forEach(value -> {
                        value = value.toLowerCase();
                        switch (criteria) {
                            case StringConstants.MAILROOM_INVOICE_SUPPILER_NAME:
                                innerBuilder.or(mailroom.supplierName.containsIgnoreCase(value));
                                break;
                            case StringConstants.MAILROOM_INVOICE_GSTIN:
                                innerBuilder.or(mailroom.gstinNo.containsIgnoreCase(value));
                                break;
                            case StringConstants.MAILROOM_INVOICE_NUMBER:
                                innerBuilder.or(mailroom.invoiceNumber.containsIgnoreCase(value));
                                break;
                            case StringConstants.MAILROOM_INVOICE_IRN:
                                innerBuilder.or(mailroom.irn.containsIgnoreCase(value));
                                break;
                            case StringConstants.MAILROOM_INVOICE_DATE:
                                innerBuilder.or(mailroom.documentDate.stringValue().contains(value));
                                break;
                            case StringConstants.MAILROOM_INVOICE_SYNC_DATE:
                                innerBuilder.or(mailroom.syncDate.stringValue().contains(value));
                                break;
                            case StringConstants.MAILROOM_INVOICE_SYNC_DATE_FROM:
                                LocalDate fromDate = parseToLocalDate(value);
                                if (fromDate != null)
                                    innerBuilder.and(mailroom.syncDate.goe(fromDate));
                                break;
                            case StringConstants.MAILROOM_INVOICE_SYNC_DATE_TO:
                                LocalDate toDate = parseToLocalDate(value);
                                if (toDate != null)
                                    innerBuilder.and(mailroom.syncDate.loe(toDate));
                                break;
                            case StringConstants.MAILROOM_INVOICE_SYNC_FINANCIAL_DATE_FROM:
                                Integer fromFY = extractYear(value);
                                if (fromFY != null) {
                                    LocalDate fyStart = LocalDate.of(fromFY, Month.APRIL, 1);
                                    innerBuilder.and(mailroom.documentDate.goe(fyStart));
                                }
                                break;
                            case StringConstants.MAILROOM_INVOICE_SYNC_FINANCIAL_DATE_TO:
                                Integer toFY = extractYear(value);
                                if (toFY != null) {
                                    // End of the financial year is March 31 of the next year
                                    LocalDate fyEnd = LocalDate.of(toFY + 1, Month.MARCH, 31);
                                    innerBuilder.and(mailroom.documentDate.loe(fyEnd));
                                }
                                break;
                            default:
                                // Unknown criteria
                        }
                    });

            builder.and(innerBuilder);
        });
    }
    private LocalDate parseToLocalDate(String dateStr) {
        try {
            return LocalDate.parse(dateStr); // Assumes ISO format. Otherwise, use DateTimeFormatter
        } catch (DateTimeParseException e) {
            // Log or ignore
            return null;
        }
    }

    private Integer extractYear(String value) {
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * Fetch Mailroom Invoice with pagination and filters
     */
    public Page<InvoiceMailroomQueueViewModel> getMailroomInvoice(long companyCode, QueueFilterViewModel filterValues, Pageable pageable) {

        Map<String, List<String>> filters = Optional.ofNullable(filterValues.getSearchCriteria()).orElse(new HashMap<>());

        BooleanBuilder builder = new BooleanBuilder()
                .and(mailroom.companyCode.eq(companyCode))
                .and(mailroom.type.eq("Invoice"))
                .and(mailroom.consumed.isFalse());

        addQueueFilters(filters, builder);

        ConstructorExpression<InvoiceMailroomQueueViewModel> constructorExpression = Projections
                .constructor(InvoiceMailroomQueueViewModel.class,
                        mailroom.id,
                        mailroom.supplierName,
                        mailroom.irn,
                        mailroom.invoiceNumber,
                        mailroom.totalAmount,
                        mailroom.invoiceType
                );

        QueryResults<InvoiceMailroomQueueViewModel> results = queryFactory
                .select(constructorExpression)
                .from(mailroom)
                .where(builder)
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    /**
     * Fetch Mailroom Invoice data by id
     */
    public InvoiceMailroomDataViewModel getMailroomInvoiceById(long companyCode, long id) {

        BooleanBuilder builder = new BooleanBuilder()
                .and(mailroom.companyCode.eq(companyCode))
                .and(mailroom.id.eq(id))
                .and(mailroom.type.eq("Invoice"));
        ConstructorExpression<InvoiceMailroomDataViewModel> constructor = Projections
                .constructor(InvoiceMailroomDataViewModel.class,
                        mailroom.id,
                        mailroom.supplierName,
                        mailroom.supplierCode,
                        mailroom.gstinNo,
                        mailroom.activeStatus,
                        mailroom.msme,
                        mailroom.eInvoice,
                        mailroom.irn,
                        mailroom.invoiceNumber,
                        mailroom.description,
                        mailroom.documentDate,
                        mailroom.totalAmount,
                        mailroom.syncDate,
                        mailroom.invoiceType
                );

        return queryFactory
                .select(constructor)
                .from(mailroom)
                .where(builder)
                .fetchOne();
    }
    /**
     * Fetch Invoice Sync Report for a specific company with filters and pagination
     */
    public Page<InvoiceSyncReportQueueViewModel> getInvoiceSyncReportForCompany(long companyCode, QueueFilterViewModel filterValues, Pageable pageable) {

        Map<String, List<String>> filters = Optional.ofNullable(filterValues.getSearchCriteria()).orElse(new HashMap<>());

        QUsers createdByUser = new QUsers("createdByUser");

        BooleanBuilder builder = new BooleanBuilder().and(invoiceSyncReport.companyCode.eq(companyCode));

        ConstructorExpression<InvoiceSyncReportQueueViewModel> constructorExpression = Projections
                .constructor(InvoiceSyncReportQueueViewModel.class,
                        invoiceSyncReport.requestId,
                        invoiceSyncReport.irnCount,
                        invoiceSyncReport.gstin,
                        invoiceSyncReport.financialYear,
                        invoiceSyncReport.message,
                        invoiceSyncReport.status,
                        Expressions.stringTemplate("({0} || ' ' || {1})", createdByUser.firstName, createdByUser.lastName),
                        Expressions.stringTemplate("TO_CHAR({0}, 'YYYY-MM-DD')", invoiceSyncReport.createdTimestamp),
                        Expressions.stringTemplate("TO_CHAR({0}, 'HH24:MI:SS')", invoiceSyncReport.createdTimestamp)
                );

        QueryResults<InvoiceSyncReportQueueViewModel> results = queryFactory
                .select(constructorExpression)
                .from(invoiceSyncReport)
                .leftJoin(invoiceSyncReport.creatingUser, createdByUser)
                .where(builder)
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }
}

