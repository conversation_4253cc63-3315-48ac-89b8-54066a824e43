package in.taxgenie.pay_expense_pvv.repositories.implementations;

import in.taxgenie.pay_expense_pvv.entities.LineItemsReport;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.util.List;

public interface ILineItemsReportRepository extends JpaRepository<LineItemsReport, Long> {
    @Query(" select lir FROM LineItemsReport lir " +
            " where lir.companyCode = ?1 and  lir.expenseCreatedDate between ?2 and  ?3   " +
            " and lir.voucherStatus not in (?4, ?5) " +
            " and UPPER(TRIM(lir.entity)) like ?6 order by lir.expenseReportId , lir.expenseCreatedDate ")

    List<LineItemsReport> getExpenseLineItemsForAll(long companyCode, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatue, String entity);
    @Query(" select lir FROM LineItemsReport lir " +
            " where lir.companyCode = ?1 and  lir.expenseCreatedDate between ?2 and  ?3   " +
            " and lir.voucherStatus =  ?4 " +
            " and UPPER(TRIM(lir.entity)) like ?5 order by lir.expenseReportId , lir.expenseCreatedDate ")
    List<LineItemsReport> getExpenseLineItems(long companyCode, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, String entity);
}
