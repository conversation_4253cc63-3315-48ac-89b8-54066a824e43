package in.taxgenie.pay_expense_pvv.repositories.implementations;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryResults;
import com.querydsl.core.types.ConstructorExpression;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQueryFactory;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.rbac.QKeyValueMapping;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.rbac.KeyValueMappingViewModel;
import jakarta.persistence.EntityManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class CustomInvoiceTatReportRepository {
    private final JPAQueryFactory queryFactory;
    private final Logger logger;
    private static final QInvoiceTatReport invoiceTatReport = QInvoiceTatReport.invoiceTatReport;
    private static final QDocumentApprovalContainer dac = QDocumentApprovalContainer.documentApprovalContainer;
    private static final QCompany company = QCompany.company;
    private static final QKeyValueMapping kvm = QKeyValueMapping.keyValueMapping;
    QUsers creatingUser = new QUsers("creatingUser");
    QUsers updatingUser = new QUsers("updatingUser");

    private static final int DEFAULT_MIN_TAT = 0;
    private static final int DEFAULT_MAX_TAT = 10000;

    public CustomInvoiceTatReportRepository(EntityManager em) {
        this.queryFactory = new JPAQueryFactory(em);
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public Page<KeyValueMappingViewModel> getKeyValueMappingQueue(long companyCode, QueueFilterViewModel filterValues, Pageable pageable) {
        BooleanBuilder builder = new BooleanBuilder();
        createFilterQuery(filterValues, builder);
        List<OrderSpecifier<?>> orderSpecifiers = createStandardOrderSpecifier(pageable);

        ConstructorExpression<KeyValueMappingViewModel> ce =
                Projections.constructor(KeyValueMappingViewModel.class, kvm.id, kvm.key, kvm.value, kvm.label,
                        kvm.createdTimestamp, kvm.updatedTimestamp, kvm.source);

        QueryResults<KeyValueMappingViewModel> results = queryFactory.select(ce)
                .from(kvm)
                .where(builder)
                .orderBy(orderSpecifiers.toArray(new OrderSpecifier[0]))
                .offset((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .fetchResults();

        return PageableExecutionUtils.getPage(results.getResults(), pageable, results::getTotal);
    }

    public List<String> getDocumentIdentifiersForIntervalAndApprovalType(Integer minTat, Integer maxTat, ApproverType approverType, Map<String,String> metadataFilters, Long companyCode) {

        logger.debug("Entered getDocumentIdentifiersForIntervalAndApprovalType");

        BooleanBuilder builder = new BooleanBuilder();
        ConstructorExpression<String> ce = Projections.constructor(String.class, dac.documentIdentifier);

        if (minTat == null) {
            logger.warn("minTat is null, setting to default: {}", DEFAULT_MIN_TAT);
            minTat = DEFAULT_MIN_TAT;
        }

        if (maxTat == null) {
            logger.warn("maxTat is null, setting to default: {}", DEFAULT_MAX_TAT);
            maxTat = DEFAULT_MAX_TAT;
        }

        builder.and(invoiceTatReport.tatTimeDays.between(minTat, maxTat))
                .and(invoiceTatReport.approverType.stringValue().eq(approverType.name()));

        // Convert metadataFilters to JSON string and apply JSONB filtering
//        if (metadataFilters != null && !metadataFilters.isEmpty()) {
//            for (Map.Entry<String, String> entry : metadataFilters.entrySet()) {
//                String key = entry.getKey();
//                String value = entry.getValue();
//                builder.and(Expressions.booleanTemplate(
//                        "jsonb_extract_path_text({0}, {1}) = {2}",
//                        invoiceTatReport.metadata, key, value
//                ));
//            }
//        }
//
//        logger.debug("Executing query with minTat={}, maxTat={}, companyCode={}, metadataFilter={} ", minTat, maxTat, companyCode, metadataFilters);
//
//        return queryFactory.select(ce)
//                .from(invoiceTatReport)
//                .innerJoin(invoiceTatReport.documentApprovalContainer, dac)
//                .where(builder)
//                .fetch();
        if (metadataFilters != null && !metadataFilters.isEmpty()) {
            for (Map.Entry<String, String> entry : metadataFilters.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                builder.and(Expressions.booleanTemplate(
                        "jsonb_extract_path_text({0}, {1}) = {2}",
                        invoiceTatReport.metadata, key, value
                ));
            }
        } else {
            // Equivalent to '{}'::jsonb = '{}'::jsonb
            builder.and(Expressions.booleanTemplate("'{}'::jsonb = '{}'::jsonb"));
        }

        logger.debug("Executing grouped query with HAVING SUM(tatTimeDays) between {} and {}", minTat, maxTat);

        return queryFactory
                .select(dac.documentIdentifier)
                .from(invoiceTatReport)
                .innerJoin(invoiceTatReport.documentApprovalContainer, dac)
                .where(builder.and(invoiceTatReport.companyCode.eq(companyCode)))
                .groupBy(invoiceTatReport.approverType, dac.documentIdentifier)
                .having(invoiceTatReport.tatTimeDays.sum().between(minTat, maxTat))
                .fetch();
    }

    // Todo: Add companyCode to initBuilder function
    private BooleanBuilder initBuilder(long companyCode) {
        return new BooleanBuilder();
    }

    private void createFilterQuery(QueueFilterViewModel filterValues, BooleanBuilder builder) {
        Map<String, List<String>> filters = filterValues.getSearchCriteria() == null ? new HashMap<>() : filterValues.getSearchCriteria();
        for (Map.Entry<String, List<String>> entry : filters.entrySet()) {
            String searchCriteria = entry.getKey();
            List<String> values = entry.getValue();

            BooleanBuilder innerBuilder = new BooleanBuilder();

            switch (searchCriteria) {

                case StringConstants.SupplierMaster.VENDOR_CODE:
//                    applyOrCondition(innerBuilder, values, companyMaster.vendorCode::containsIgnoreCase);
                    break;


                default:
                    logger.warn("Unhandled search criteria: " + searchCriteria);
                    break;
            }

            builder.and(innerBuilder);
        }
    }

    private void applyOrCondition(BooleanBuilder innerBuilder, List<String> values, Function<String, BooleanExpression> expressionFunction) {
        for (String value : values) {
            if (!isNullOrEmpty(value)) {
                BooleanExpression expression = expressionFunction.apply(value);
                if (expression != null) {
                    innerBuilder.or(expression);
                }
            }
        }
    }

    private List<OrderSpecifier<?>> createStandardOrderSpecifier(Pageable pageable) {
        List<OrderSpecifier<?>> defaultOrderSpecifier = pageable.getSort().stream()
                .map(order -> switch (order.getProperty()) {
                    case "key" -> order.getDirection().isAscending() ? kvm.key.asc() : kvm.key.desc();
                    case "value" -> order.getDirection().isAscending() ? kvm.value.asc() : kvm.value.desc();
                    case "label" -> order.getDirection().isAscending() ? kvm.label.asc() : kvm.label.desc();
                    default -> kvm.id.desc();
                }).collect(Collectors.toList());
        return defaultOrderSpecifier;
    }

    private static boolean isNullOrEmpty(String value) {
        return value == null || value.isEmpty();
    }

}
