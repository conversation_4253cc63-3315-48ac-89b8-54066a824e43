package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.Document;
import in.taxgenie.pay_expense_pvv.entities.ReportStatus;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.IExpenseValidationAggregates;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface IDocumentRepository extends JpaRepository<Document, Long> {
    Optional<Document> findByCompanyCodeAndId(long companyCode, long id);

    @Query("select e from Document e where e.companyCode = ?1 and e.documentSubgroupId = ?2 and e.documentSubgroup.isDateRangeApplicable = ?3 and e.documentApprovalContainer.reportStatus in ?4 and e.documentApprovalContainer.employeeEmail = ?5")
    List<Document> getByDateRange(long companyCode, long documentMetadataId, boolean isDateRangeApplicable, List<ReportStatus> documentStatuses, String creatingUserEmail);

    boolean existsByCompanyCodeAndIdAndCreatingUserId(long companyCode, long id, long userId);

    List<Document> findAllByCompanyCodeAndDocumentApprovalContainerId(long companyCode, long documentApprovalContainerId);

    @Query("select sum(e.claimAmount) from Document e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.documentSubgroup.documentMetadataId = ?3 and e.documentDate = ?4")
    BigDecimal getDocumentsOnDay(long companyCode, long userId, long metadataId, LocalDate documentDate);

    @Query("select sum(e.claimAmount) from Document e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.documentSubgroup.documentMetadataId = ?3 and e.documentApprovalContainer.reportStatus in ?4 and e.documentDate >= ?5 and e.documentDate <= ?6")
    BigDecimal getDocumentsBetween(long companyCode, long userId, long metadataId, List<ReportStatus> statusList, LocalDate start, LocalDate end);

//    @Query("select sum(e.claimAmount) as sum, count(e) as count, e.documentDate as documentDate from Document e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.documentSubgroup.documentMetadataId = ?3 and e.documentApprovalContainer.reportStatus in ?4 and e.documentDate >= ?5 and e.documentDate <= ?6 group by function('to_char', e.documentDate, '%Y-%m')")
    @Query("select sum(e.claimAmount) as sum, count(e) as count, e.documentDate as expenseDate, date_trunc('month', e.documentDate) as documentMonth from Document e where e.companyCode = ?1 and e.creatingUserId = ?2 and e.documentSubgroup.documentMetadataId = ?3 and e.documentApprovalContainer.reportStatus in ?4 and e.documentDate >= ?5 and e.documentDate <= ?6 group by documentMonth, e.documentDate")
    List<IExpenseValidationAggregates> getDocumentsAggregatesByMonth(long companyCode, long userId, long metadataId, List<ReportStatus> statusList, LocalDate start, LocalDate end);


    @Query(" select ex FROM Document ex " +
            " where ex.companyCode = ?1 and  ex.documentDate between ?2 and  ?3   " +
            " and ex.documentApprovalContainer.reportStatus =  ?4 " +
            " and UPPER(TRIM(ex.documentApprovalContainer.value03)) like ?5 order by ex.documentApprovalContainer.id , ex.documentDate ")

    List<Document> getDocumentLineItems(long companyCode, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, String entity);

    @Query(" select ex FROM Document ex " +
            " where ex.companyCode = ?1 and  ex.documentDate between ?2 and  ?3   " +
            " and ex.documentApprovalContainer.reportStatus not in (?4, ?5) " +
            " and UPPER(TRIM(ex.documentApprovalContainer.value03)) like ?6 order by ex.documentApprovalContainer.id , ex.documentDate ")

    List<Document> getDocumentLineItemsForAll(long companyCode, LocalDate fromDate, LocalDate toDate, ReportStatus voucherStatus, ReportStatus revokeStatue, String entity);
    @Query("select sum(ex.invoiceAmount) from Document ex where  ex.companyCode = ?1 and ex.documentApprovalContainer = ?2")
    Optional<BigDecimal> getTotalDocuments(long companyCode, long documentApprovalContainerId);

    Document findByCompanyCodeAndDocumentApprovalContainerId(long companyCode, Long documentApprovalContainerId);

    boolean existsByDocNoAndCompanyCodeAndId(String docNo, long companyCode, long id);

    boolean existsByDocNoAndCompanyCodeAndDocumentApprovalContainer_ReportStatusNotInAndDocumentApprovalContainer_DocumentMetadata_DocumentCategoryIdAndIdNot(String docNo, long companyCode, List<ReportStatus> excludedStatuses, Integer documentCategoryId, long id);
}
