package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.Location;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ILocationRepository extends JpaRepository<Location, Long> {
    Optional<Location> findByCompanyCodeAndId(long companyCode, long id);
    List<Location> findByCompanyCode(long companyCode);
    List<Location> findByCompanyCodeAndCountryCode(long companyCode, String countryCode);

    List<Location> findByCompanyCodeAndCountryCodeNot(long companyCode, String countryCode);

    @Query("select count(l) from Location l where l.companyCode = ?1 and upper(l.countryCode) = upper(?2) and  upper(l.location) = upper(?3)")
    long getCountByParameters(long companyCode, String countryCode, String location);
}
