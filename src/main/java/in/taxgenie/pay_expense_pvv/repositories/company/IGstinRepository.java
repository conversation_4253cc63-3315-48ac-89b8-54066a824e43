package in.taxgenie.pay_expense_pvv.repositories.company;

import in.taxgenie.pay_expense_pvv.entities.company.GstinDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IGstinRepository extends JpaRepository<GstinDetail, Long> {
    List<GstinDetail> findByCompanyCode(long companyCode);
    Optional<GstinDetail> findByCompanyCodeAndGstinNo(long companyCode, String gstin);
    List<GstinDetail> findByCompanyCodeAndStateCodeIn(long companyCode, List<String> stateCodes);
    List<GstinDetail> findByCompanyCodeAndIdIn(long companyCode, List<Long> ids);
}
