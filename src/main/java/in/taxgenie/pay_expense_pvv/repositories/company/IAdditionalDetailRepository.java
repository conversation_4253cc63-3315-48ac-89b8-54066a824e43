package in.taxgenie.pay_expense_pvv.repositories.company;

import in.taxgenie.pay_expense_pvv.entities.company.AdditionalDetails;
import in.taxgenie.pay_expense_pvv.entities.company.GstinDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IAdditionalDetailRepository extends JpaRepository<AdditionalDetails, Long>  {
    List<AdditionalDetails> findByCompanyCode(long companyCode);

}
