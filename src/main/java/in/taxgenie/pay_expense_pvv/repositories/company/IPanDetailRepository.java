package in.taxgenie.pay_expense_pvv.repositories.company;

import in.taxgenie.pay_expense_pvv.entities.company.PanDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface IPanDetailRepository extends JpaRepository<PanDetail, Long> {
    Optional<PanDetail> findByCompanyCode(long companyCode);
}
