package in.taxgenie.pay_expense_pvv.repositories;

import in.taxgenie.pay_expense_pvv.entities.Payment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface IPaymentRepository extends JpaRepository<Payment, Long> {

    Optional<Payment> findFirstByCompanyCodeAndDocumentApprovalContainerIdOrderByCreatedTimestampDesc(long companyCode, long documentApprovalContainerId);

    Optional<Payment> findByDocumentApprovalContainerId(long documentApprovalContainerId);
    Optional<Payment> findByCompanyCodeAndDocumentApprovalContainerId(long companyCode, long documentApprovalContainerId);
}
