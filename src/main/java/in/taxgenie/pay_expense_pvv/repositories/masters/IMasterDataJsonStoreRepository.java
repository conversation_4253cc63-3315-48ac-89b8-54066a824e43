package in.taxgenie.pay_expense_pvv.repositories.masters;

import in.taxgenie.pay_expense_pvv.entities.MasterDataJsonStore;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface IMasterDataJsonStoreRepository  extends JpaRepository<MasterDataJsonStore, Long> {
    // Todo: Replace with newer version below.
    @Query("SELECT jsonStore.body FROM MasterDataJsonStore jsonStore " +
            "LEFT JOIN BudgetSyncMaster syncMaster ON syncMaster.id = :syncMasterId " +
            "WHERE jsonStore.id = syncMaster.masterDataJsonStore.id")
    String findBodyBySyncMasterId(@Param("syncMasterId") Long syncMasterId);

    @Query("SELECT jsonStore FROM BudgetSyncMaster syncMaster JOIN syncMaster.masterDataJsonStore jsonStore WHERE syncMaster.id = :syncMasterId")
    Optional<MasterDataJsonStore> findJsonStoreBySyncMasterId(@Param("syncMasterId") Long syncMasterId);

}
