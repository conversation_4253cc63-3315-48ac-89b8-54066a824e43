package in.taxgenie.pay_expense_pvv.repositories.masters;

import in.taxgenie.pay_expense_pvv.entities.masters.RatioCategoryConfig;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.MasterResponseViewModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

@Repository
public interface IRatioCategoryConfigRepository extends JpaRepository<RatioCategoryConfig, Integer> {
    @Query("SELECT NEW in.taxgenie.pay_expense_pvv.viewmodels.masters.MasterResponseViewModel(rc.id, rc.masterId, bm.name, bm.displayName) FROM BudgetMaster bm " +
            "JOIN RatioCategoryConfig rc ON rc.masterId = bm.id WHERE rc.companyCode = :companyCode")
    List<MasterResponseViewModel> findByCompanyCode(long companyCode);

    void deleteAllByCompanyCode(long companyCode);
}
