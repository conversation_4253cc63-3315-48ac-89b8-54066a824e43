package in.taxgenie.pay_expense_pvv.repositories.masters;

import in.taxgenie.pay_expense_pvv.entities.masters.MappingSegmentRatioToEntity;
import in.taxgenie.pay_expense_pvv.viewmodels.GenericListRequestViewModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IMappingSegmentRatioToEntityRepository extends JpaRepository<MappingSegmentRatioToEntity, Long> {
    Optional<MappingSegmentRatioToEntity> findByDocumentTypeIdAndEntityId(Integer documentTypeId, Long entityId);

    @Query("SELECT m FROM MappingSegmentRatioToEntity m WHERE m.entityId IN :entityIds AND m.documentTypeId = :documentTypeId")
    List<MappingSegmentRatioToEntity> findByDocumentTypeIdAndEntityIds(@Param("documentTypeId") Integer documentTypeId, @Param("entityIds") List<Long> entityIds);
}
