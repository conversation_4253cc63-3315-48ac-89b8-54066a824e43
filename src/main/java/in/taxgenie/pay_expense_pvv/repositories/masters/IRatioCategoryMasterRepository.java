package in.taxgenie.pay_expense_pvv.repositories.masters;

import in.taxgenie.pay_expense_pvv.entities.masters.RatioCategoryMaster;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

@Repository
public interface IRatioCategoryMasterRepository extends JpaRepository<RatioCategoryMaster, Integer> {
    List<RatioCategoryMaster> findAllByCompanyCode(long companyCode);

    Optional<RatioCategoryMaster> findByCompanyCodeAndId(long companyCode, Integer id);

    @Query("SELECT r FROM RatioCategoryMaster r WHERE r.companyCode = :companyCode AND r.id IN " +
            "(SELECT MIN(r2.id) FROM RatioCategoryMaster r2 WHERE r2.companyCode = :companyCode GROUP BY r2.name)")
    List<RatioCategoryMaster> findDistinctByNameAndCompanyCode(@Param("companyCode") long companyCode);

    Optional<RatioCategoryMaster> findFirstByNameIgnoreCaseAndCompanyCode(String name, long companyCode);

    @Modifying
    void deleteByCompanyCodeAndId(long companyCode, Long id);

    @Modifying
    void deleteAllByCompanyCode(long companyCode);

    boolean existsByNameAndMasterJsonAndCompanyCode(String name, String masterJson, long companyCode);

    List<RatioCategoryMaster> findAllByCompanyCodeAndNameIgnoreCase(long companyCode, String name);
}
