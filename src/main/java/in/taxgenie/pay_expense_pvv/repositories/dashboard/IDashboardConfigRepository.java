package in.taxgenie.pay_expense_pvv.repositories.dashboard;

import in.taxgenie.pay_expense_pvv.entities.dashboard.DashboardConfiguration;
import in.taxgenie.pay_expense_pvv.viewmodels.dashboard.DashboardConfigurationViewModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IDashboardConfigRepository extends JpaRepository<DashboardConfiguration, Long> {
@Query("SELECT new in.taxgenie.pay_expense_pvv.viewmodels.dashboard.DashboardConfigurationViewModel(dc.id, w.id, w.name, " +
        "dc.sequenceId, dc.isActive, dc.isMandatory, dc.companyCode) FROM DashboardConfiguration dc INNER JOIN Widget w ON w.id = dc.widgetId " +
        "WHERE dc.companyCode =:companyCode AND dc.isAuthorised = true ORDER BY dc.sequenceId")
public List<DashboardConfigurationViewModel> findByCompanyCodeOrderById(@Param("companyCode") long comanyCode);

    List<DashboardConfiguration> findByCompanyCodeAndIdIn(Long companyCode, List<Long> ids);
}
