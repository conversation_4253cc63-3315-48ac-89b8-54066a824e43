package in.taxgenie.pay_expense_pvv.audit_logs;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import in.taxgenie.pay_expense_pvv.auth.AppJwtAuthentication;
import in.taxgenie.pay_expense_pvv.auth.IJwtFacilities;
import in.taxgenie.pay_expense_pvv.auth.TransactionContextHolder;
import in.taxgenie.pay_expense_pvv.entities.AuditLog;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.repositories.IAuditLogRepository;
import in.taxgenie.pay_expense_pvv.utils.DateTimeUtils;
import jakarta.persistence.PostPersist;
import jakarta.persistence.PostRemove;
import jakarta.persistence.PostUpdate;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Map;

@Component
public class EntityAuditListener {

    private static IAuditLogRepository auditLogRepository;
    private static IJwtFacilities jwtFacilities;
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final Logger logger;

    static {
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

    }


    public static void setAuditLogRepository(IAuditLogRepository repository, IJwtFacilities facilities) {
        auditLogRepository = repository;
        jwtFacilities = facilities;
    }

    public EntityAuditListener() {
        this.logger = LoggerFactory.getLogger(this.getClass());

    }

    @PostPersist
    public void auditEntityPostPersist(Object entity) {
        auditEntity(entity, "CREATE");
    }

    @PostUpdate
    public void auditEntityPostUpdate(Object entity) {
        auditEntity(entity, "UPDATE");
    }

    @PostRemove
    public void auditEntityPostRemove(Object entity) {
        auditEntity(entity, "DELETE");
    }

    private void auditEntity(Object entity, String operation) {
        if (entity == null) {
            logger.warn("Entity is null. Skipping audit for operation: {}", operation);
            return;
        }

        try {

            String entityName = entity.getClass().getSimpleName();
            String entityId = fetchEntityId(entity);
            String afterState = objectMapper.writeValueAsString(entity);

            AuditLog auditLog = new AuditLog();
            auditLog.setTimestamp(DateTimeUtils.getCurrentTimestamp().toLocalDateTime());
            auditLog.setUserId(getCurrentUserId());
            auditLog.setEntityName(entityName);
            auditLog.setEntityId(entityId);
            auditLog.setTransactionId(TransactionContextHolder.getTransactionId());
            auditLog.setOperation(operation);
            auditLog.setAfterState(afterState);
            auditLog.setApiCallee(getCurrentApiCallee());
            auditLog.setApiUrl(getCurrentApiUrl());

            logger.info("Saving audit log: {}", auditLog);
            auditLogRepository.save(auditLog);
            logger.info("Audit log saved successfully for entity: {} with operation: {}", entityName, operation);
        } catch (Exception e) {
            logger.error("Failed to log audit entry for entity: {} during operation: {}", entity.getClass().getName(), operation, e);
            throw new DomainInvariantException("Failed to log audit entry for entity: " + entity.getClass().getName() + " during operation: " + operation);
        }
    }

    private String fetchEntityId(Object entity) {
        final Map<String, String> ENTITY_ID_FIELDS = Map.of(
                "InvoiceHeader", "invoiceHeaderId",
                "InvoiceReceived", "invoiceReceivedId"
                // add if there are any other such classes
        );
        try {
            String className = entity.getClass().getSimpleName();
            String idFieldName = ENTITY_ID_FIELDS.getOrDefault(className, "id"); // Default to "id" if not explicitly mapped

            var idField = entity.getClass().getDeclaredField(idFieldName);
            idField.setAccessible(true);
            Object idValue = idField.get(entity);
            if (idValue == null) {
                logger.warn("Entity ID is null for entity: {}", entity.getClass().getName());
                return "unknown-id";
            }
            return idValue.toString();
        } catch (NoSuchFieldException e) {
            logger.error("ID field not found in entity: {}", entity.getClass().getName(), e);
        } catch (IllegalAccessException e) {
            logger.error("Unable to access ID field for entity: {}", entity.getClass().getName(), e);
        }
        return "unknown-id";
    }

    private String getCurrentUserId() {
        try {
            var authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null) {
                return String.valueOf(jwtFacilities.getUserId(((AppJwtAuthentication) authentication).getJwt()));
            }
            logger.warn("SecurityContext authentication is null. Returning unknown-user.");
        } catch (Exception e) {
            logger.error("Error fetching current user ID from SecurityContext", e);
        }
        return "unknown-user";
    }

    private String getCurrentApiCallee() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String apiCallee = request.getHeader("X-API-Caller");
                if (apiCallee != null) {
                    return apiCallee;
                }
                logger.warn("X-API-Caller header not found. Falling back to remote address.");
                return request.getRemoteAddr();
            }
        } catch (Exception e) {
            logger.error("Error fetching current API callee", e);
        }
        return "unknown-api-callee";
    }

    private String getCurrentApiUrl() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return request.getRequestURI();
            }
        } catch (Exception e) {
            logger.error("Error fetching current API URL", e);
        }
        return "unknown-api-url";
    }
}
