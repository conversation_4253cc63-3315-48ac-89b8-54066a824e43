package in.taxgenie.pay_expense_pvv.audit_logs;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
public class ApiLoggingInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String apiEndpoint = request.getRequestURI();
        request.setAttribute("api_callee", apiEndpoint);
        return true;
    }
}