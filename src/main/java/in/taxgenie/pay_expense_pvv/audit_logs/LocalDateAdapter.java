package in.taxgenie.pay_expense_pvv.audit_logs;

import com.google.gson.*;

import java.lang.reflect.Type;
import java.time.LocalDate;

class LocalDateAdapter implements JsonSerializer<LocalDate>, JsonDeserializer<LocalDate> {

    // Serialize LocalDate to JSON (ISO-8601 format)
    @Override
    public JsonElement serialize(LocalDate src, Type typeOfSrc, JsonSerializationContext context) {
        return new JsonPrimitive(src.toString()); // Format as "yyyy-MM-dd"
    }

    // Deserialize JSON back to LocalDate
    @Override
    public LocalDate deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        return LocalDate.parse(json.getAsString()); // Parse ISO-8601 date string
    }
}