package in.taxgenie.pay_expense_pvv.api;

import in.taxgenie.pay_expense_pvv.auth.AppJwtAuthenticationManager;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextFactory;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.auth.IJwtFacilities;
import in.taxgenie.pay_expense_pvv.response.interfaces.factory.IServerResponseFactory;
import in.taxgenie.pay_expense_pvv.services.InvoiceOCRService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentApprovalContainerUserService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IFileIOService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IInvoiceService;
import in.taxgenie.pay_expense_pvv.utils.DummyServerResponse;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentCreateResponse;
import in.taxgenie.pay_expense_pvv.invoice.request.InvoiceDetailsReq;
import in.taxgenie.pay_expense_pvv.viewmodels.ExistenceResultViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.InvoiceApprovalContainerFromIRNViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice.*;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice_ocr.BuyerDocIdRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice_ocr.StatusUpdateViewModel;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(InvoiceApi.class)
@AutoConfigureMockMvc(addFilters = false)
class InvoiceApiTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IInvoiceService invoiceService;

    @MockBean
    private IAuthContextFactory authContextFactory;

    @MockBean
    private IServerResponseFactory serverResponseFactory;

    @MockBean
    private IFileIOService fileIOService;

    @MockBean
    private IDocumentApprovalContainerUserService documentApprovalContainerUserService;

    @MockBean
    private InvoiceOCRService invoiceOCRService;

    // Internal dependencies
    @MockBean
    private AppJwtAuthenticationManager jwtAuthenticationManager;

    @MockBean
    private IJwtFacilities jwtFacilities;

    @Test
    void testGetById() throws Exception {
        Long invoiceId = 1L;
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        InvoiceDetailsReq mockInvoiceDetails = new InvoiceDetailsReq();

        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.getById(invoiceId, mockAuth)).thenReturn(mockInvoiceDetails);

        mockMvc.perform(get("/api/v1/invoice/{invoiceId}", invoiceId))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(invoiceService, times(1)).getById(invoiceId, mockAuth);
    }


    // ----------------------------------------------------------------------------------
    // EXAMPLE 2: Get an existing Invoice by documentContainerId
    // ----------------------------------------------------------------------------------
    @Test
    void testGetByDocumentContainerId() throws Exception {
        Long containerId = 999L;
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        InvoiceDetailsReq mockResult = new InvoiceDetailsReq();

        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.getByDocumentContainerId(containerId, mockAuth)).thenReturn(mockResult);

        mockMvc.perform(get("/api/v1/invoice/by-document-container-id/{documentContainerId}", containerId))
                .andExpect(status().isOk());

        verify(invoiceService, times(1)).getByDocumentContainerId(containerId, mockAuth);
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 3: Create Invoice (Multipart)
    // ----------------------------------------------------------------------------------
    @Test
    void testCreateInvoice() throws Exception {
        Long metadataId = 123L;
        Long subgroupId = 456L;
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);

        // Mock files
        MockMultipartFile invoiceFile = new MockMultipartFile(
                "invoice",     // part name
                "test-invoice.pdf",
                MediaType.APPLICATION_PDF_VALUE,
                "Fake PDF Content".getBytes()
        );
        MockMultipartFile supportingFile = new MockMultipartFile(
                "supportingDocuments",
                "support-doc.txt",
                MediaType.TEXT_PLAIN_VALUE,
                "Support doc content".getBytes()
        );

        // Mock the JSON data for invoice details
        String invoiceDetails = "{\"field\":\"value\"}";

        MockMultipartFile invoiceDetailsPart = new MockMultipartFile(
                "invoiceDetails",                // name must match @RequestPart
                "",                              // original filename (optional)
                MediaType.APPLICATION_JSON_VALUE, // or text/plain if appropriate
                invoiceDetails.getBytes()        // content bytes
        );

        // Our service method returns a DocumentCreateResponse
        DocumentCreateResponse mockResponse = new DocumentCreateResponse();
        mockResponse.setMessage("Invoice Created");

        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.create(eq(metadataId), eq(subgroupId), anyString(), anyList(), eq(mockAuth)))
                .thenReturn(mockResponse);

        mockMvc.perform(
                        multipart("/api/v1/invoice/create/{metadataId}/{subgroupId}", metadataId, subgroupId)
                                .file(invoiceFile)
                                .file(supportingFile)
                                .file(invoiceDetailsPart)
                                // The content type for multipart
                                .contentType(MediaType.MULTIPART_FORM_DATA)
                )
                .andExpect(status().isOk());

        verify(invoiceService, times(1))
                .create(eq(metadataId), eq(subgroupId), eq(invoiceDetails), anyList(), eq(mockAuth));
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 3.b. : Create Invoice (Multipart), with Document create providing Null.
    // ----------------------------------------------------------------------------------
    @Test
    void testCreateInvoice_NullMessage() throws Exception {
        Long metadataId = 123L;
        Long subgroupId = 456L;
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);

        // Mock files
        MockMultipartFile invoiceFile = new MockMultipartFile(
                "invoice",     // part name
                "test-invoice.pdf",
                MediaType.APPLICATION_PDF_VALUE,
                "Fake PDF Content".getBytes()
        );
        MockMultipartFile supportingFile = new MockMultipartFile(
                "supportingDocuments",
                "support-doc.txt",
                MediaType.TEXT_PLAIN_VALUE,
                "Support doc content".getBytes()
        );
        // Create the invoiceDetails part as a multipart file.
        String invoiceDetails = "{\"field\":\"value\"}";
        MockMultipartFile invoiceDetailsPart = new MockMultipartFile(
                "invoiceDetails",
                "",
                MediaType.APPLICATION_JSON_VALUE,
                invoiceDetails.getBytes()
        );

        // Create a response with a null message.
        DocumentCreateResponse mockResponse = new DocumentCreateResponse();
        // Do not set any message so that mockResponse.getMessage() returns null.

        // Create a dummy server response that the serverResponseFactory will return.
        // You may adjust this dummy response to match your actual response structure.
        // Here, we assume the JSON produced contains a "message" field.
        DummyServerResponse dummyServerResponse = new DummyServerResponse(
                StaticDataRegistry.HTTP_RESPONSE_OK,
                StaticDataRegistry.HTTP_MESSAGE_OK,
                true,
                mockResponse
        );

        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.create(eq(metadataId), eq(subgroupId), anyString(), anyList(), eq(mockAuth)))
                .thenReturn(mockResponse);
        // Stub the serverResponseFactory to return our dummy response.
        when(serverResponseFactory.getServerResponseWithBody(
                eq(StaticDataRegistry.HTTP_RESPONSE_OK),
                eq(StaticDataRegistry.HTTP_MESSAGE_OK),  // because response.getMessage() is null, default is used
                eq(true),
                eq(mockResponse)
        )).thenReturn(dummyServerResponse);

        mockMvc.perform(
                        multipart("/api/v1/invoice/create/{metadataId}/{subgroupId}", metadataId, subgroupId)
                                .file(invoiceFile)
                                .file(supportingFile)
                                .file(invoiceDetailsPart)
                                .contentType(MediaType.MULTIPART_FORM_DATA)
                )
                .andExpect(status().isOk())
                // Verify that the JSON response has the expected message.
                .andExpect(jsonPath("$.message").value(StaticDataRegistry.HTTP_MESSAGE_OK));

        verify(invoiceService, times(1))
                .create(eq(metadataId), eq(subgroupId), eq(invoiceDetails), anyList(), eq(mockAuth));
    }


    // ----------------------------------------------------------------------------------
    // EXAMPLE 4: Create Invoice (Post-upload) - JSON Body
    // ----------------------------------------------------------------------------------
    @Test
    void testCreateInvoicePostUpload() throws Exception {
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        QueueBulkInvoiceUploadDataViewModel requestBody = new QueueBulkInvoiceUploadDataViewModel();
        // Populate requestBody as needed

        DocumentCreateResponse mockResponse = new DocumentCreateResponse();
        mockResponse.setMessage("Created via post-upload");

        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.createInvoicePostUpload(eq(requestBody), eq(mockAuth)))
                .thenReturn(mockResponse);

        mockMvc.perform(post("/api/v1/invoice/create/post-upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"someField\":\"someValue\"}") // or use an object mapper
                )
                .andExpect(status().isOk());

        verify(invoiceService, times(1)).createInvoicePostUpload(any(), eq(mockAuth));
    }

    // -------------------------------------------------------------------------
    // EXAMPLE 4.b.: Create Invoice (Post-upload) - JSON Body Test for createInvoicePostUpload branch where response.getMessage() is null.
    // -------------------------------------------------------------------------
    @Test
    void testCreateInvoicePostUpload_NullMessage() throws Exception {
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        QueueBulkInvoiceUploadDataViewModel requestBody = new QueueBulkInvoiceUploadDataViewModel();
        // Populate requestBody as needed for your test

        // Create a response with a null message
        DocumentCreateResponse mockResponse = new DocumentCreateResponse();
        // Do not set any message so that mockResponse.getMessage() returns null.

        // Create a dummy server response that the serverResponseFactory will return.
        DummyServerResponse dummyServerResponse = new DummyServerResponse(
                StaticDataRegistry.HTTP_RESPONSE_OK,
                StaticDataRegistry.HTTP_MESSAGE_OK,  // default message substituted
                true,
                mockResponse
        );

        // Stub the necessary calls:
        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.createInvoicePostUpload(eq(requestBody), eq(mockAuth))).thenReturn(mockResponse);
        when(serverResponseFactory.getServerResponseWithBody(
                eq(StaticDataRegistry.HTTP_RESPONSE_OK),
                eq(StaticDataRegistry.HTTP_MESSAGE_OK),  // because mockResponse.getMessage() is null
                eq(true),
                eq(mockResponse)
        )).thenReturn(dummyServerResponse);

        // Perform the POST request with a JSON body.
        mockMvc.perform(post("/api/v1/invoice/create/post-upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"someField\":\"someValue\"}")  // adjust as needed
                )
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value(StaticDataRegistry.HTTP_MESSAGE_OK));

        verify(invoiceService, times(1)).createInvoicePostUpload(any(), eq(mockAuth));
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 5: Check if Invoice already exists by IRN (Multipart param)
    // ----------------------------------------------------------------------------------
    @Test
    void testGetByIRN() throws Exception {
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);

        // Mock file
        MockMultipartFile invoiceFile = new MockMultipartFile(
                "invoice",
                "irn-invoice.pdf",
                MediaType.APPLICATION_PDF_VALUE,
                "Fake IRN content".getBytes()
        );

        InvoiceApprovalContainerFromIRNViewModel mockViewModel = new InvoiceApprovalContainerFromIRNViewModel();

        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.getByIRN(any(MultipartFile.class), eq(mockAuth)))
                .thenReturn(mockViewModel);

        mockMvc.perform(multipart("/api/v1/invoice/irn/already-exists")
                        .file(invoiceFile)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk());

        verify(invoiceService, times(1)).getByIRN(any(MultipartFile.class), eq(mockAuth));
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 6: Save Invoice (PUT with JSON body)
    // ----------------------------------------------------------------------------------
    @Test
    void testSaveInvoice() throws Exception {
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        InvoiceDetailsReq req = new InvoiceDetailsReq();
        // set fields if needed

        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.save(any(InvoiceDetailsReq.class), eq(mockAuth))).thenReturn(true);

        mockMvc.perform(put("/api/v1/invoice/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"invoiceField\":\"invoiceValue\"}") // test JSON
                )
                .andExpect(status().isOk());

        verify(invoiceService, times(1)).save(any(InvoiceDetailsReq.class), eq(mockAuth));
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 7: Save Invoice OCR JSON (POST with JSON body)
    // ----------------------------------------------------------------------------------
    @Test
    void testSaveInvoiceOCRJson() throws Exception {
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.saveInvoiceOCRJson(any(), eq(mockAuth))).thenReturn(true);

        mockMvc.perform(post("/api/v1/invoice/save/ocr_json")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"irn\":\"12345\"}") // minimal JSON
                )
                .andExpect(status().isOk());

        verify(invoiceService, times(1)).saveInvoiceOCRJson(any(), eq(mockAuth));
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 8: Save Invoice Buyer Doc ID (PUT with JSON)
    // ----------------------------------------------------------------------------------
    @Test
    void testSaveInvoiceBuyerDocId() throws Exception {
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.saveInvoiceBuyerDocId(any(BuyerDocIdRequestViewModel.class), eq(mockAuth)))
                .thenReturn(true);

        mockMvc.perform(put("/api/v1/invoice/save/buyer_doc_id")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"buyerDocId\":\"DOC123\"}")
                )
                .andExpect(status().isOk());

        verify(invoiceService, times(1)).saveInvoiceBuyerDocId(any(BuyerDocIdRequestViewModel.class), eq(mockAuth));
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 9: Save Invoice Buyer Doc ID Status Update (PUT with JSON)
    // ----------------------------------------------------------------------------------
    @Test
    void testSaveInvoiceBuyerDocIdStatusUpdate() throws Exception {
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.saveInvoiceBuyerDocIdStatusUpdate(any(StatusUpdateViewModel.class), eq(mockAuth)))
                .thenReturn(true);

        mockMvc.perform(put("/api/v1/invoice/save/buyer_doc_id/status_update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"status\":\"UPDATED\"}")
                )
                .andExpect(status().isOk());

        verify(invoiceService, times(1)).saveInvoiceBuyerDocIdStatusUpdate(any(StatusUpdateViewModel.class), eq(mockAuth));
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 10: Delete Invoice by ID (DELETE)
    // ----------------------------------------------------------------------------------
    @Test
    void testDeleteById() throws Exception {
        Long invoiceId = 99L;
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);

        doNothing().when(invoiceService).delete(invoiceId, mockAuth);
        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);

        mockMvc.perform(delete("/api/v1/invoice/{invoiceId}", invoiceId))
                .andExpect(status().isOk());

        verify(invoiceService, times(1)).delete(invoiceId, mockAuth);
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 11: Get existing line items (GET /{invoiceId}/lineItem)
    // ----------------------------------------------------------------------------------
    @Test
    void testGetLineItemsByInvoiceId() throws Exception {
        Long invoiceId = 101L;
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        InvoiceLineItemViewModel mockResponse = new InvoiceLineItemViewModel();

        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.getByInvoiceId(invoiceId, mockAuth)).thenReturn(mockResponse);

        mockMvc.perform(get("/api/v1/invoice/{invoiceId}/lineItem", invoiceId))
                .andExpect(status().isOk());

        verify(invoiceService, times(1)).getByInvoiceId(invoiceId, mockAuth);
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 12: Delete LineItem by ID(s) (DELETE with request param)
    // ----------------------------------------------------------------------------------
    @Test
    void testDeleteLineItemById() throws Exception {
        Long invoiceId = 202L;
        List<Long> lineItemIds = List.of(11L, 22L);
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);

        doNothing().when(invoiceService).deleteLineItem(invoiceId, lineItemIds, mockAuth);
        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);

        mockMvc.perform(delete("/api/v1/invoice/{invoiceId}/lineItem", invoiceId)
                        .param("lineItemId", "11")
                        .param("lineItemId", "22")
                )
                .andExpect(status().isOk());

        verify(invoiceService, times(1)).deleteLineItem(eq(invoiceId), eq(lineItemIds), eq(mockAuth));
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 13: Check whether invoice number exists
    // GET /api/v1/invoice/exists/{supplierId}/invoice/{invoiceNo}?id=someId
    // ----------------------------------------------------------------------------------
    @Test
    void testCheckExistenceByInvoiceNo() throws Exception {
        String invoiceNo = "INV123";
        Long supplierId = 300L;
        Long someId = 400L; // optional param

        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        ExistenceResultViewModel mockExistResult = new ExistenceResultViewModel(true, "Invoice");

        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.checkExistenceByInvoiceNo(supplierId, invoiceNo, someId, mockAuth))
                .thenReturn(true);

        mockMvc.perform(get("/api/v1/invoice/exists/{supplierId}/invoice/{invoiceNo}", supplierId, invoiceNo)
                        .param("id", "400")
                )
                .andExpect(status().isOk());

        verify(invoiceService, times(1))
                .checkExistenceByInvoiceNo(supplierId, invoiceNo, someId, mockAuth);
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 14: Perform OCR on Invoice (GET with Multipart - tricky scenario)
    // Normally, multipart + GET isn't standard. If your method truly uses GET,
    // this might fail. We'll show a typical approach with POST or PUT.
    // For demonstration, we'll match the endpoint signature given.
    // ----------------------------------------------------------------------------------
    @Test
    void testPerformOcr() throws Exception {
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);

        // Mock file
        MockMultipartFile invoiceFile = new MockMultipartFile(
                "file",
                "ocr-invoice.pdf",
                MediaType.APPLICATION_PDF_VALUE,
                "Fake content".getBytes()
        );

        InvoiceDetailsReq mockInvoiceDetails = new InvoiceDetailsReq();
        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceOCRService.performOcrAndFetchDetails(any(MultipartFile.class), eq("22AAAAA0000A1Z5"), eq(mockAuth)))
                .thenReturn(mockInvoiceDetails);

        // Because it's a GET endpoint, you might do something like:
        mockMvc.perform(
                        multipart("/api/v1/invoice/ocr")
                                .file(invoiceFile)
                                .param("buyer_gstin", "22AAAAA0000A1Z5")
                                .with(request -> {
                                    request.setMethod("GET"); // Force GET if truly a GET
                                    return request;
                                })
                )
                .andExpect(status().isOk());

        verify(invoiceOCRService, times(1))
                .performOcrAndFetchDetails(any(MultipartFile.class), eq("22AAAAA0000A1Z5"), eq(mockAuth));
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 15: Get Invoice PDF Base64 (GET /pdf/by-document-id/{documentId})
    // ----------------------------------------------------------------------------------
    @Test
    void testGetInvoicePDFBase64ByDocumentIdentifier() throws Exception {
        String documentId = "DOC-111";
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        String mockBase64 = "ZmFrZUJhc2U2NA==";

        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.getInvoicePDFBase64ByDocumentIdentifier(documentId, mockAuth))
                .thenReturn(mockBase64);

        mockMvc.perform(get("/api/v1/invoice/pdf/by-document-id/{documentId}", documentId))
                .andExpect(status().isOk());

        verify(invoiceService, times(1)).getInvoicePDFBase64ByDocumentIdentifier(documentId, mockAuth);
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 16: Test an API that checks for Company Token (testCompanyToken)
    // ----------------------------------------------------------------------------------
    @Test
    void testTestAPI() throws Exception {
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);

        when(authContextFactory.getCompanyAuthContext(any())).thenReturn(mockAuth);

        mockMvc.perform(get("/api/v1/invoice/testCompanyToken"))
                .andExpect(status().isOk());

        verify(authContextFactory, times(1)).getCompanyAuthContext(any());
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 17: Create a new signed URL
    // ----------------------------------------------------------------------------------
    @Test
    void testGetSignedUrl() throws Exception {
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        SignedUrlResponseViewModel mockSignedUrl = new SignedUrlResponseViewModel(List.of());
//        mockSignedUrl.setSignedUrl("http://fake-signed-url");

        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.createSignedUrl(any(GetSignedUrlRequestViewModel.class), eq(mockAuth)))
                .thenReturn(mockSignedUrl);

        mockMvc.perform(post("/api/v1/invoice/get/signed-url")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"filename\":\"test.pdf\"}")
                )
                .andExpect(status().isOk());

        verify(invoiceService, times(1))
                .createSignedUrl(any(GetSignedUrlRequestViewModel.class), eq(mockAuth));
    }

    // ----------------------------------------------------------------------------------
    // EXAMPLE 18: Bulk upload invoice (POST with JSON)
    // ----------------------------------------------------------------------------------
    @Test
    void testInvoiceBulkUpload() throws Exception {
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        BulkInvoiceUploadResponseViewModel mockResponse = new BulkInvoiceUploadResponseViewModel();

        when(authContextFactory.getAuthContext(any())).thenReturn(mockAuth);
        when(invoiceService.invoiceBulkUpload(any(BulkInvoiceUploadRequestViewModel.class), eq(mockAuth)))
                .thenReturn(mockResponse);

        mockMvc.perform(post("/api/v1/invoice/bulk_upload")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"bulkField\":\"bulkValue\"}")
                )
                .andExpect(status().isOk());

        verify(invoiceService, times(1))
                .invoiceBulkUpload(any(BulkInvoiceUploadRequestViewModel.class), eq(mockAuth));
    }

}
