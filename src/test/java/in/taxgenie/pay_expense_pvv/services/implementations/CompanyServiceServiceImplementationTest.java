package in.taxgenie.pay_expense_pvv.services.implementations;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel;

import java.util.List;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Company;
import in.taxgenie.pay_expense_pvv.repositories.ICompanyRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.ICompanyService;
import in.taxgenie.pay_expense_pvv.utils.DummyAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.LookupDataModel;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.jdbc.SqlConfig;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.PostgreSQLContainer;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@DataJpaTest
@ComponentScan(basePackages = {
        "in.taxgenie.pay_expense_pvv.repositories.implementations",
        "in.taxgenie.pay_expense_pvv.services.implementations"
})
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Sql(
        scripts = {"/test-data/test-companies.sql"},
        config = @SqlConfig(commentPrefix = "--", separator = ";")
)
class CompanyServiceServiceImplementationTest {

    @Autowired
    private ICompanyRepository companyRepository;

    @Autowired
    private ICompanyService companyService;

    // Testcontainers-managed PostgreSQL
    private static final PostgreSQLContainer<?> postgreSQLContainer =
            new PostgreSQLContainer<>("postgres:13.3")
                    .withDatabaseName("testdb")
                    .withUsername("test")
                    .withPassword("test")
                    .withInitScript("schema.sql");

    @BeforeAll
    static void init() {
        // Start container and set up system props for Spring
        postgreSQLContainer.start();
        System.setProperty("spring.datasource.url", postgreSQLContainer.getJdbcUrl());
        System.setProperty("spring.datasource.username", postgreSQLContainer.getUsername());
        System.setProperty("spring.datasource.password", postgreSQLContainer.getPassword());
    }

    @BeforeEach
    void setUp() {
        // any additional setup needed
    }

//    @Test
//    void testGetAllSupplierNames_shouldReturnOnlyNonNullStateCodeCompanies() {
//        // GIVEN
//        IAuthContextViewModel auth = DummyAuthContextViewModel.createDummyAuthContext();
//
//
//        // WHEN
//        List<LookupDataModel> result = companyService.getAllSupplierNames(auth);
//
//        // THEN
//        // We expect only the companies with a non-null stateCode to appear.
//        // test-companies.sql inserted companyId=100 and 101 with valid stateCode,
//        // and companyId=102 with NULL stateCode => 102 should NOT be returned.
//        assertThat(result).isNotEmpty();
//        // Let's check that the result doesn't contain the one with null stateCode
//        List<Integer> returnedIds = result.stream().map(LookupDataModel::getId).toList();
//        assertThat(returnedIds).contains(100, 101);
//        assertThat(returnedIds).doesNotContain(102);
//
//        // We can also fetch from the DB to confirm stateCode is not null
//        for (Integer companyId : returnedIds) {
//            Company c = companyRepository.findById(companyId).orElse(null);
//            assertThat(c).isNotNull();
//            assertThat(c.getStateCode()).isNotNull();  // <--- critical check
//        }
//    }

}