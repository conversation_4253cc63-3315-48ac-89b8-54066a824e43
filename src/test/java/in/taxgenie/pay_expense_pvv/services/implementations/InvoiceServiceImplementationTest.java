package in.taxgenie.pay_expense_pvv.services.implementations;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.storage.HttpMethod;
import com.google.pubsub.v1.PubsubMessage;
import com.taxgenie.utils.api.MultiServiceClient;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cloud.GcpCSFileIOProvider;
import in.taxgenie.pay_expense_pvv.cloud.interfaces.IGcpCSFileIOProvider;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.invoice.InvoiceItem;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderItem;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.DuplicateRecordFoundException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.govrndto.*;
import in.taxgenie.pay_expense_pvv.invoice.mapper.InvoiceHeaderMapper;
import in.taxgenie.pay_expense_pvv.invoice.message.CompanyDetails;
import in.taxgenie.pay_expense_pvv.invoice.message.InvoiceDocumentDetails;
import in.taxgenie.pay_expense_pvv.invoice.repository.IInvoiceHeaderRepository;
import in.taxgenie.pay_expense_pvv.invoice.request.InvoiceDetailsReq;
import in.taxgenie.pay_expense_pvv.repositories.*;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetRepository;
import in.taxgenie.pay_expense_pvv.repositories.company.IGstinRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.invoice.IInvoiceItemRepository;
import in.taxgenie.pay_expense_pvv.repositories.mailroom.IMailRoomReposiitory;
import in.taxgenie.pay_expense_pvv.repositories.po.IPurchaseOrderItemRepository;
import in.taxgenie.pay_expense_pvv.services.implementations.budget.BudgetServiceImplementation;
import in.taxgenie.pay_expense_pvv.services.interfaces.*;
import in.taxgenie.pay_expense_pvv.services.interfaces.documents.IDocumentManagementService;
import in.taxgenie.pay_expense_pvv.services.interfaces.dynamic.form.IDynamicFormService;
import in.taxgenie.pay_expense_pvv.services.interfaces.masters.IRatioCategoryMasterService;
import in.taxgenie.pay_expense_pvv.utils.MultiServiceClientFactory;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.ConsumedItemAvailableCountViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice.*;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice.GetSignedUrlRequestViewModel.*;

import in.taxgenie.pay_expense_pvv.viewmodels.invoice_ocr.BuyerDocIdRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice_ocr.StatusUpdateViewModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Example test suite covering the major public methods in InvoiceServiceImplementation.
 * These are illustrative stubs. Customize them for your actual use cases.
 */
@ExtendWith(MockitoExtension.class)
class InvoiceServiceImplementationTest {

    // ----------------------------------------------------------
    // Mock dependencies
    // ----------------------------------------------------------
    @Mock private IDocumentRepository documentRepository;
    @Mock private IDocumentApprovalContainerRepository reportRepository;
    @Mock private IMailRoomReposiitory mailRoomReposiitory;
    @Mock private IDocumentRuleRepository ruleRepository;
    @Mock private IDocumentSubgroupRepository subgroupRepository;
    @Mock private IEmployeeMasterDataService employeeService;
    @Mock private ILocationRepository locationRepository;
    @Mock private IMetadataLimitRuleRepository metadataLimitRuleRepository;
    @Mock private IFileIOService fileIOService;
    @Mock private IInvoiceHeaderRepository invoiceHeaderRepository;
    @Mock private IInvoiceItemRepository invoiceItemRepository;
    @Mock private IItemMasterRepository itemMasterRepository;
    @Mock private IPurchaseOrderItemRepository purchaseOrderItemRepository;
    @Mock private IInvoiceReceivedRepository invoiceReceivedRepository;
    @Mock private ILookupRepository lookupRepository;
    @Mock private IDocumentApprovalContainerUserService documentApprovalContainerUserService;
    @Mock private ICompanyRepository companyRepository;
    @Mock private IGstinRepository gstinRepository;
    @Mock private ILineItemDetailsRepository lineItemDetailsRepository;
    @Mock private IBudgetRepository budgetRepository;
    @Mock private IGlMasterRepository glMasterRepository;
    @Mock private IDMRService idmrService;
    @Mock private CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository;
    @Mock private BudgetServiceImplementation budgetServiceImplementation;
    @Mock private IDocumentManagementService documentManagementService;
    @Mock private IDynamicFormService dynamicFormService;
    @Mock private IRatioCategoryMasterService ratioCategoryMasterService;
    @Mock private GcpCSFileIOProvider gsFileIOProvider;
    @Mock private MultiServiceClientFactory multiServiceClientFactory;
    @Mock private IPubSubPublisherService pubSubPublisherService;
    @Mock private IEnvironmentDataProvider environmentDataProvider;
    @Mock private IGcpCSFileIOProvider gcpCSFileIOProvider;
    @Mock private ObjectMapper objectMapper;
    @Mock private InvoiceHeaderMapper invoiceHeaderMapper; // if you use it directly
    @Mock private WebClient.Builder webClientBuilder;

    // ----------------------------------------------------------
    // System Under Test (SUT)
    // ----------------------------------------------------------
    @InjectMocks
    private InvoiceServiceImplementation invoiceService;

    @BeforeEach
    void setUp() {
        // For instance, set a default value for 'uploadEnv'
        ReflectionTestUtils.setField(invoiceService, "uploadEnv", "testUploadEnv");
    }

    // -------------------------------------------------------------------------
    // 1) create(long metadataId, ...)
    // -------------------------------------------------------------------------
    @Test
    @MockitoSettings(strictness = Strictness.LENIENT)
    void testCreate_HappyPath() throws Exception {
        // Arrange
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        when(auth.getCompanyCode()).thenReturn(111L);
        when(auth.getToken()).thenReturn("dummyToken");

        InvoiceDetailsReq reqParsed = new InvoiceDetailsReq();
        reqParsed.setIsJson(false); // for PDF upload branch
        when(objectMapper.readValue(anyString(), eq(InvoiceDetailsReq.class)))
                .thenReturn(reqParsed);

        // We need a DocumentSubgroup with at least one rule.
        DocumentSubgroup subgroup = new DocumentSubgroup();
        subgroup.setId(222L);
        DocumentRule dummyRule = new DocumentRule();
        dummyRule.setId(777L);
        dummyRule.setFrozen(false);
        subgroup.setRules(List.of(dummyRule));
        when(subgroupRepository.findByCompanyCodeAndId(eq(111L), eq(222L)))
                .thenReturn(Optional.of(subgroup));

        // We also need a DocumentApprovalContainer
        DocumentApprovalContainer dac = new DocumentApprovalContainer();
        dac.setId(333L);

        DocumentApprovalContainerViewModel mockViewModel = new DocumentApprovalContainerViewModel();
        mockViewModel.setId(333L);

        when(documentApprovalContainerUserService.create(anyLong(), eq(auth)))
                .thenReturn(mockViewModel);
        when(reportRepository.findByCompanyCodeAndId(eq(111L), eq(333L)))
                .thenReturn(Optional.of(dac));

        // Stub the necessary calls for success
        when(lookupRepository.findByTypeAndValue(eq(StaticDataRegistry.LOOKUP_TYPE_DOCUMENT),
                eq(StaticDataRegistry.LOOKUP_VALUE_INVOICE)))
                .thenReturn(Optional.of(new LookupData()));

        // If needed, mock file uploads
        MockMultipartFile file = new MockMultipartFile("invoice", "invoice.pdf",
                "application/pdf", "fakeContent".getBytes());
        List<MultipartFile> invoices = List.of(file);

        // Stub file handling
        when(fileIOService.handleInvoiceUpload(file, 111L))
                .thenReturn("dummy/path/invoice.pdf");
        when(gsFileIOProvider.getSignedUrl("dummy/path/invoice.pdf"))
                .thenReturn(new URL("http://dummy-signed-url"));

        // --- Inject a dummy MultiServiceClient into webClientHelper ---
        MultiServiceClient dummyClient = mock(MultiServiceClient.class);
        lenient().when(dummyClient.makeRequest(
                        anyInt(),
                        any(),
                        anyString(),
                        any(),
                        any(),
                        any(),
                        any(),
                        any()))
                .thenReturn(Mono.just("{}"));
        ReflectionTestUtils.setField(invoiceService, "webClientHelper", dummyClient);

        // Act
        DocumentCreateResponse response =
                invoiceService.create(100L, 222L, "{}", invoices, auth);

        // Assert
        assertNotNull(response);
        assertEquals(333L, response.getContainerId());
    }

    // -------------------------------------------------------------------------
    // 2) getByIRN(MultipartFile invoice, IAuthContextViewModel auth)
    // -------------------------------------------------------------------------
    @Test
    void testGetByIRN_HappyPath() throws Exception {
        // Arrange
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        when(auth.getCompanyCode()).thenReturn(123L); // Added this line
        when(auth.getToken()).thenReturn("dummyToken");

        MockMultipartFile file = new MockMultipartFile("invoice", "test.pdf",
                "application/pdf", "fakePdfData".getBytes());

        // Create a stub QR response using the expected type:
        QRResponseDTO stubQR = new QRResponseDTO();
        Map<String, QRGovDtlsDTO> qrData = new HashMap<>();
        QRGovDtlsDTO govDtls = new QRGovDtlsDTO();  // Ensure this type exists and has a setter for IRN
        govDtls.setIrn("IRN-ABC");
        qrData.put("0", govDtls);
        stubQR.setQrData(qrData);

        // Stub objectMapper to return the stub QR response when given any String.
        when(objectMapper.readValue(anyString(), eq(QRResponseDTO.class)))
                .thenReturn(stubQR);

        // Inject a dummy MultiServiceClient so that webClientHelper.makeRequest(...) returns a valid JSON string.
        MultiServiceClient dummyClient = mock(MultiServiceClient.class);
        when(dummyClient.makeRequest(
                anyInt(),
                any(),
                anyString(),
                any(),
                any(),
                any(),
                eq(String.class),
                anyString()))
                .thenReturn(Mono.just("{\"qrData\":{\"0\":{\"irn\":\"IRN-ABC\"}}}"));
        ReflectionTestUtils.setField(invoiceService, "webClientHelper", dummyClient);

        // Stub the repository to find an existing invoice with that IRN.
        InvoiceReceived invoiceReceived = new InvoiceReceived();
        InvoiceHeader invoiceHeader = new InvoiceHeader();
        Document doc = new Document();
        // Set a dummy DocumentSubgroup (to avoid later NPEs in getByIRN)
        DocumentSubgroup subgroup = new DocumentSubgroup();
        subgroup.setSubgroupFieldsJson("{}");
        doc.setDocumentSubgroup(subgroup);
        invoiceHeader.setDocument(doc);
        invoiceReceived.setInvoiceHeader(invoiceHeader);

        when(invoiceReceivedRepository.findByCompanyIdAndIrn(anyLong(), eq("IRN-ABC")))
                .thenReturn(Optional.of(invoiceReceived));

        // Act
        InvoiceApprovalContainerFromIRNViewModel result = invoiceService.getByIRN(file, auth);

        // Assert
        assertNotNull(result, "Expected a non-null result");
        assertNotNull(result.getSubgroupFieldsJson(), "Expected subgroupFieldsJson to be set");
    }


    // -------------------------------------------------------------------------
    // 3) getById(Long invoiceHeaderId, IAuthContextViewModel auth)
    // -------------------------------------------------------------------------
    //@Test
//    void testGetById_HappyPath() {
//        Long invoiceHeaderId = 100L;
//        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
//
//        // Mock an InvoiceHeader + Document
//        InvoiceHeader mockInvoiceHeader = new InvoiceHeader();
//        mockInvoiceHeader.setInvoiceHeaderId(invoiceHeaderId);
//
//        Document mockDocument = new Document();
//        mockDocument.setId(999L);
//
//        DocumentSubgroup mockSubgroup = new DocumentSubgroup();
//        mockSubgroup.setBudgetId(888L);
//        mockDocument.setDocumentSubgroup(mockSubgroup);
//
//        DocumentApprovalContainer mockDAC = new DocumentApprovalContainer();
//        mockDAC.setId(777L);
//        mockDocument.setDocumentApprovalContainer(mockDAC);
//
//        mockInvoiceHeader.setDocument(mockDocument);
//
//        // Create a dummy ItemType and assign it to the InvoiceItem
//        ItemType dummyType = mock(ItemType.class);
//        when(dummyType.getFormattedName()).thenReturn("Dummy Type");
//
//        PurchaseOrderItem purchaseOrderItem = new PurchaseOrderItem();
//        purchaseOrderItem.setPurchaseOrderHeader(new PurchaseOrderHeader());
//
//        // Suppose invoiceHeader has a line item
//        InvoiceItem mockLineItem = new InvoiceItem();
//        mockLineItem.setInvoiceHeader(mockInvoiceHeader);  // set parent reference
//        mockLineItem.setType(dummyType);
//        mockLineItem.setPurchaseOrderItem(purchaseOrderItem);// set dummy type
//
//        mockInvoiceHeader.setInvoiceItems(List.of(mockLineItem));
//
//        when(invoiceHeaderRepository.findById(invoiceHeaderId))
//                .thenReturn(Optional.of(mockInvoiceHeader));
//        when(documentRepository.findById(999L))
//                .thenReturn(Optional.of(mockDocument));
//        // Stub budgetServiceImplementation for getBudgetNodeForDocument:
//        when(budgetServiceImplementation.getBudgetNode(eq(888L), eq(mockAuth)))
//                .thenReturn(null);
//
//        // Act
//        InvoiceDetailsReq result = invoiceService.getById(invoiceHeaderId, mockAuth);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(1, result.getItemList().size());
//    }

    // -------------------------------------------------------------------------
    // 4) getByDocumentContainerId(Long documentContainerId, IAuthContextViewModel auth)
    // -------------------------------------------------------------------------
    @Test
    void testGetByDocumentContainerId_HappyPath() {
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);
        when(mockAuth.getCompanyCode()).thenReturn(999L);

        // Prepare the container
        DocumentApprovalContainer container = new DocumentApprovalContainer();
        container.setId(111L);

        when(reportRepository.findByCompanyCodeAndId(999L, 111L))
                .thenReturn(Optional.of(container));

        // Prepare the single Document
        Document mockDoc = new Document();
        mockDoc.setId(222L);
        DocumentSubgroup mockSubgroup = new DocumentSubgroup();
        mockDoc.setDocumentSubgroup(mockSubgroup);
        mockDoc.setDocumentApprovalContainer(container);

        when(documentRepository.findAllByCompanyCodeAndDocumentApprovalContainerId(999L, 111L))
                .thenReturn(List.of(mockDoc));

        // Prepare the InvoiceHeader
        InvoiceHeader header = new InvoiceHeader();
        header.setInvoiceHeaderId(333L);
        header.setDocument(mockDoc);
        header.setInvoiceItems(new ArrayList<>());  // Initialize an empty list to avoid NPE
        when(invoiceHeaderRepository.findByDocumentId(222L)).thenReturn(Optional.of(header));


        // Act
        InvoiceDetailsReq result = invoiceService.getByDocumentContainerId(111L, mockAuth);

        // Assert
        assertNotNull(result);
        // Container is found, doc is found, invoice header is found...
    }

    // -------------------------------------------------------------------------
    // 5) invoiceItemToDto(InvoiceItem, IAuthContextViewModel)
    // -------------------------------------------------------------------------
    //@Test
    void testInvoiceItemToDto_HappyPath() {
        IAuthContextViewModel mockAuth = mock(IAuthContextViewModel.class);

        // Create an InvoiceItem with a PO using many-to-many relationship
        InvoiceItem item = new InvoiceItem();
        item.setCreatedAt(LocalDateTime.now());

        // Build the minimal chain for the PO details
        PurchaseOrderHeader poHeader = new PurchaseOrderHeader();
        Document doc = new Document();
        doc.setDocNo("PO123");
        poHeader.setDocument(doc);
        Users users = new Users();
        users.setFirstName("Test");
        users.setLastName("Test");
        poHeader.setCreatedBy(users);

        PurchaseOrderItem poItem = new PurchaseOrderItem();
        poItem.setId(10L);
        poItem.setPurchaseOrderHeader(poHeader);

        // Stub customDocumentApprovalContainerRepository call
        when(customDocumentApprovalContainerRepository.getConsumedPOItemCount(10L, 0L))
                .thenReturn(new ConsumedItemAvailableCountViewModel(1L,10L,0L,5.0,10.0)); // Suppose company code is 0

        // Add the PO item to the many-to-many relationship
        item.getPurchaseOrderItems().add(poItem);

        // Create a dummy InvoiceHeader
        InvoiceHeader dummyHeader = new InvoiceHeader();
        dummyHeader.setInvoiceHeaderId(555L);
        // Set a dummy supplier so that getSupplier() returns a non-null value.
        Company dummySupplier = new Company();
        dummySupplier.setCompanyId(777);
        dummyHeader.setSupplier(dummySupplier);
        // **Crucial step**: Set the Document on the InvoiceHeader.
        dummyHeader.setDocument(doc);

        // Set the dummy InvoiceHeader on the InvoiceItem
        item.setInvoiceHeader(dummyHeader);

        // Set a non-null quantity to avoid other NPEs
        item.setQuantity(5.0);

        // Set a dummy ItemType to avoid NPE when calling getFormattedName()
        ItemType dummyType = mock(ItemType.class);
        when(dummyType.getFormattedName()).thenReturn("Dummy Formatted Name");
        item.setType(dummyType);

        // Act: ensure auth returns a non-null company code (e.g., 0L)
        when(mockAuth.getCompanyCode()).thenReturn(0L);
        InvoiceItemDetailsViewModel dto = invoiceService.invoiceItemToDto(item, mockAuth);

        // Assert
        assertNotNull(dto);
        assertEquals("PO123", dto.getDocNo());
        assertEquals(5.0, dto.getQuantity());
    }


    // -------------------------------------------------------------------------
    // 6) getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks(...)
    // -------------------------------------------------------------------------
    @Test
    void testGetDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks_HappyPath() {
        when(documentRepository.findAllByCompanyCodeAndDocumentApprovalContainerId(123L, 456L))
                .thenReturn(List.of(new Document()));

        Document doc = invoiceService.getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks(123L, 456L);
        assertNotNull(doc);
    }

    @Test
    void testGetDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks_MultipleDocs() {
        // If multiple documents come back, the method throws DomainInvariantException
        when(documentRepository.findAllByCompanyCodeAndDocumentApprovalContainerId(123L, 456L))
                .thenReturn(List.of(new Document(), new Document()));

        assertThrows(DomainInvariantException.class,
                () -> invoiceService.getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks(123L, 456L));
    }

    // -------------------------------------------------------------------------
    // 7) getInvoicePDFBase64ByDocumentIdentifier(...)
    // -------------------------------------------------------------------------
    @Test
    void testGetInvoicePDFBase64ByDocumentIdentifier_HappyPath() throws IOException {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        when(auth.getCompanyCode()).thenReturn(111L);

        // Suppose the container is found
        DocumentApprovalContainer dac = new DocumentApprovalContainer();
        dac.setId(987L);
        List<DocumentApprovalContainer> containerList = List.of(dac);

        when(reportRepository.findByCompanyCodeAndDocumentIdentifier(111L, "ABC123"))
                .thenReturn(containerList);

        Document doc = new Document();
        doc.setId(999L);
        // single doc
        when(documentRepository.findAllByCompanyCodeAndDocumentApprovalContainerId(111L, 987L))
                .thenReturn(List.of(doc));

        // Invoice header with invoiceReceived
        InvoiceHeader header = new InvoiceHeader();
        InvoiceReceived rec = new InvoiceReceived();
        rec.setDocumentUrl("gcs://some/path");
        header.setInvoiceReceived(rec);
        when(invoiceHeaderRepository.findByDocumentId(999L)).thenReturn(Optional.of(header));

        // Mock the gsFileIOProvider
        when(gsFileIOProvider.downloadFileAndGetBase64("gcs://some/path"))
                .thenReturn("BASE64DATA");

        // Act
        String base64 = invoiceService.getInvoicePDFBase64ByDocumentIdentifier("ABC123", auth);

        // Assert
        assertEquals("BASE64DATA", base64);
    }

    // -------------------------------------------------------------------------
    // 8) createSignedUrl(...)
    // -------------------------------------------------------------------------
    @Test
    @MockitoSettings(strictness = Strictness.LENIENT)
    void testCreateSignedUrl_HappyPath() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        when(auth.getCompanyCode()).thenReturn(123L);

        GetSignedUrlRequestViewModel request = new GetSignedUrlRequestViewModel();
        SignedUrlRequestData data = new SignedUrlRequestData();
        data.setName("test.pdf");
        data.setContentType("application/pdf");
        request.setData(List.of(data));

        // Stub the GcpCSFileIOProvider using lenient stubbing.
        lenient().when(gcpCSFileIOProvider.createSignedURL(
                        anyString(),
                        anyString(),
                        any(com.google.cloud.storage.HttpMethod.class)))
                .thenReturn("http://signed.url");

        SignedUrlResponseViewModel response = invoiceService.createSignedUrl(request, auth);
        assertNotNull(response);
        assertFalse(response.getData().isEmpty());
//        assertEquals("http://signed.url", response.getData().get(0).getSignedUrls());
    }

    // -------------------------------------------------------------------------
    // 9) invoiceBulkUpload(...)
    // -------------------------------------------------------------------------
    @Test
    @MockitoSettings(strictness = Strictness.LENIENT)
    void testInvoiceBulkUpload_HappyPath() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        when(auth.getCompanyCode()).thenReturn(100L);
        lenient().when(environmentDataProvider.getProductId()).thenReturn("dummyProduct");
        BulkInvoiceUploadRequestViewModel req = new BulkInvoiceUploadRequestViewModel();
        SignedUrlResponseData fileData = new SignedUrlResponseData();
        fileData.setName("invoice.pdf");
        req.setData(List.of(fileData));

        when(pubSubPublisherService.publishMessage(anyString(), eq("Bulk_Invoice_Upload"), any(PubsubMessage.class)))
                .thenReturn("success");

        BulkInvoiceUploadResponseViewModel response = invoiceService.invoiceBulkUpload(req, auth);
        assertTrue(response.getSuccess());
        assertEquals("Invoice uploaded successfully", response.getMessage());
        assertEquals(1, response.getData().size());
        assertTrue(response.getData().get(0).getIsUpload());
    }

    // -------------------------------------------------------------------------
    // 10) createInvoicePostUpload(...)
    // -------------------------------------------------------------------------
    @Test
    void testCreateInvoicePostUpload_HappyPath() throws IOException {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        when(auth.getCompanyCode()).thenReturn(111L);

        // Prepare request
        QueueBulkInvoiceUploadDataViewModel dataReq = new QueueBulkInvoiceUploadDataViewModel();
        dataReq.setGroup(10L);
        dataReq.setSubGroup(20L);
        dataReq.setFilePath("temp/file.pdf");
        dataReq.setName("file.pdf");

        // Stub fileIOService
        when(fileIOService.handleMoveInvoice("temp/file.pdf", "file.pdf", 111L))
                .thenReturn("final/path/file.pdf");
        when(fileIOService.getSignedUrl("final/path/file.pdf"))
                .thenReturn(new URL("http://fake-signed-url"));

        // Mock creation of container
        DocumentApprovalContainerViewModel containerVM = new DocumentApprovalContainerViewModel();
        containerVM.setId(999L);
        when(documentApprovalContainerUserService.create(eq(10L), eq(auth)))
                .thenReturn(containerVM);

        DocumentApprovalContainer container = new DocumentApprovalContainer();
        container.setId(999L);
        when(reportRepository.findByCompanyCodeAndId(111L, 999L))
                .thenReturn(Optional.of(container));

        // Prepare subgroup with a dummy rule so that getApplicableRule doesn't throw
        DocumentSubgroup subgroup = new DocumentSubgroup();
        subgroup.setId(20L);
        DocumentRule dummyRule = new DocumentRule();
        dummyRule.setId(555L);
        dummyRule.setFrozen(false);
        // You can leave any other properties as defaults if isRuleMatch is simple.
        subgroup.setRules(List.of(dummyRule));
        when(subgroupRepository.findByCompanyCodeAndId(111L, 20L))
                .thenReturn(Optional.of(subgroup));

        // Act
        DocumentCreateResponse resp = invoiceService.createInvoicePostUpload(dataReq, auth);

        // Assert
        assertNotNull(resp);
        assertEquals(999L, resp.getContainerId());
    }

    // -------------------------------------------------------------------------
    // 11) getInvoiceHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks(...)
    // -------------------------------------------------------------------------
    @Test
    void testGetInvoiceHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks_HappyPath() {
        Document doc = new Document();
        doc.setId(123L);

        InvoiceHeader header = new InvoiceHeader();
        when(invoiceHeaderRepository.findByDocumentId(123L))
                .thenReturn(Optional.of(header));

        InvoiceHeader result = invoiceService.getInvoiceHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks(0L, doc);
        assertNotNull(result);
        assertSame(header, result);
    }

    @Test
    void testGetInvoiceHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks_NotFound() {
        Document doc = new Document();
        doc.setId(123L);
        when(invoiceHeaderRepository.findByDocumentId(123L))
                .thenReturn(Optional.empty());

        assertThrows(DomainInvariantException.class,
                () -> invoiceService.getInvoiceHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks(0L, doc));
    }

    // -------------------------------------------------------------------------
    // 12) delete(long id, IAuthContextViewModel auth)
    // -------------------------------------------------------------------------
    @Test
    void testDelete_NotImplementedYet() {
        invoiceService.delete(999L, mock(IAuthContextViewModel.class));
    }

    // -------------------------------------------------------------------------
    // 13) saveInvoiceBuyerDocIdStatusUpdate(...)
    // -------------------------------------------------------------------------
    @Test
    void testSaveInvoiceBuyerDocIdStatusUpdate_HappyPath() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        StatusUpdateViewModel statusVM = new StatusUpdateViewModel();
        statusVM.setRequestId("REQ-789");

        InvoiceReceived received = new InvoiceReceived();
        InvoiceHeader header = new InvoiceHeader();
        Document doc = new Document();
        DocumentApprovalContainer container = new DocumentApprovalContainer();
        doc.setDocumentApprovalContainer(container);
        header.setDocument(doc);
        received.setInvoiceHeader(header);

        when(invoiceReceivedRepository.findByDmrReferenceId("REQ-789"))
                .thenReturn(Optional.of(received));

        boolean result = invoiceService.saveInvoiceBuyerDocIdStatusUpdate(statusVM, auth);
        assertTrue(result);
        verify(reportRepository, times(1)).saveAndFlush(container);
    }

    @Test
    void testSaveInvoiceBuyerDocIdStatusUpdate_NotFound() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        StatusUpdateViewModel statusVM = new StatusUpdateViewModel();
        statusVM.setRequestId("REQ-404");

        when(invoiceReceivedRepository.findByDmrReferenceId("REQ-404"))
                .thenReturn(Optional.empty());

        boolean result = invoiceService.saveInvoiceBuyerDocIdStatusUpdate(statusVM, auth);
        assertFalse(result);
    }

    // -------------------------------------------------------------------------
    // 14) saveInvoiceBuyerDocId(...)
    // -------------------------------------------------------------------------
    @Test
    void testSaveInvoiceBuyerDocId_HappyPath() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        BuyerDocIdRequestViewModel vm = new BuyerDocIdRequestViewModel();
        vm.setInvoiceReceivedId(101L);
        vm.setHasSubscription(true);
        vm.setBuyerDocId("DOC-123");

        InvoiceReceived received = new InvoiceReceived();
        when(invoiceReceivedRepository.findById(101L))
                .thenReturn(Optional.of(received));

        boolean result = invoiceService.saveInvoiceBuyerDocId(vm, auth);
        assertTrue(result);
        // Should store the docId and flush
        assertEquals("DOC-123", received.getDmrReferenceId());
    }

    @Test
    void testSaveInvoiceBuyerDocId_NotFound() {
        var vm = new BuyerDocIdRequestViewModel();
        vm.setInvoiceReceivedId(9999L);

        when(invoiceReceivedRepository.findById(9999L))
                .thenReturn(Optional.empty());

        boolean result = invoiceService.saveInvoiceBuyerDocId(vm, mock(IAuthContextViewModel.class));
        assertFalse(result);
    }

    // -------------------------------------------------------------------------
    // 15) saveInvoiceOCRJson(...) // Todo: Review this function then update the test
    // -------------------------------------------------------------------------
//    @Test
//    void testSaveInvoiceOCRJson_HappyPath() {
//        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
//        when(auth.getCompanyCode()).thenReturn(555L);
//
//        // Prepare the input DTO with proper custom details.
//        InvoiceDeatilsByIrnDTO dto = new InvoiceDeatilsByIrnDTO();
//        CustomDetails custDetails = new CustomDetails();
//        custDetails.setRequestId("REQ-222");
//        custDetails.setOcrStatus("SUCCESS");
//        dto.setCustomDetails(custDetails);
//
//        // Prepare the invoice JSON with an IRN and seller details.
//        InvoiceDTO invoiceJson = new InvoiceDTO();
//        invoiceJson.setIrn("IRN-111");
//        // Create and set seller details with a valid GST value.
//        CompanyDtlsDTO sellerDtls = new CompanyDtlsDTO();
//        sellerDtls.setGst("GST123");  // Ensure GST is non-null and non-empty
//        invoiceJson.setSellerDtls(sellerDtls);
//
//        // **Initialize the line item list** to prevent NPE.
//        invoiceJson.setItemList(new ArrayList<>());
//
//        dto.setInvoiceJson(invoiceJson);
//
//        // Prepare a dummy InvoiceReceived with an initialized InvoiceHeader and Document.
//        InvoiceReceived received = new InvoiceReceived();
//        InvoiceHeader header = new InvoiceHeader();
//        header.setInvoiceHeaderId(555L);
//        header.setInvoiceItems(new ArrayList<>());  // Initialize to avoid NPE later
//
//        // Create a Document and set a dummy DocumentSubgroup
//        Document doc = new Document();
//        DocumentSubgroup subgroup = new DocumentSubgroup();
//        subgroup.setSubgroupFieldsJson("{}"); // Provide a dummy JSON string
//        subgroup.setBudgetId(999L);  // Set a dummy budget ID
//        doc.setDocumentSubgroup(subgroup);
//        header.setDocument(doc);
//        DocumentApprovalContainer container = new DocumentApprovalContainer();
//        doc.setDocumentApprovalContainer(container);
//        received.setInvoiceHeader(header);
//
//        // Stub repository call to return our dummy InvoiceReceived.
//        when(invoiceReceivedRepository.findByDmrReferenceIdAndCompanyId("REQ-222", 555L))
//                .thenReturn(Optional.of(received));
//
//        // Stub objectMapper.writeValueAsString(invoiceJson) so it returns a dummy JSON.
//        try {
//            doReturn("{}").when(objectMapper).writeValueAsString(invoiceJson);
//        } catch (JsonProcessingException e) {
//            fail("Unexpected JsonProcessingException");
//        }
//
//        // Stub companyRepository to return a dummy Company for the seller.
//        Company dummySeller = new Company();
//        dummySeller.setCompanyId(1000);
//        when(companyRepository.findByCamCompanyIdAndGst(555L, "GST123"))
//                .thenReturn(List.of(dummySeller));
//
//        LookupData invoiceLookupStub = new LookupData();
//        invoiceLookupStub.setId(0);
//        invoiceLookupStub.setType("INVOICE");
//
//        when(lookupRepository.findByTypeAndValue(StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_INVOICE)).thenReturn(Optional.of(invoiceLookupStub));
//
//        // Act
//        boolean result = invoiceService.saveInvoiceOCRJson(dto, auth);
//
//        // Assert
//        assertTrue(result);
//    }


    @Test
    void testSaveInvoiceOCRJson_NoInvoiceReceived() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        when(auth.getCompanyCode()).thenReturn(555L);

        InvoiceDeatilsByIrnDTO dto = new InvoiceDeatilsByIrnDTO();
        CustomDetails custDetails = new CustomDetails();
        custDetails.setRequestId("REQ-999");
        dto.setCustomDetails(custDetails);

        when(invoiceReceivedRepository.findByDocumentIdentifierIdAndCompanyId("REQ-999",555L))
                .thenReturn(Optional.empty());

        boolean result = invoiceService.saveInvoiceOCRJson(dto, auth);
        assertFalse(result);
    }

    // -------------------------------------------------------------------------
    // 16) save(InvoiceDetailsReq, IAuthContextViewModel)
    // -------------------------------------------------------------------------
    @Test
    void testSave_HappyPath() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        when(auth.getCompanyCode()).thenReturn(123L);

        InvoiceDetailsReq req = new InvoiceDetailsReq();
        req.setInvoiceHeaderId(555L);

        InvoiceDocumentDetails docDetails = new InvoiceDocumentDetails();
        docDetails.setDocNo("INV-ABC");
        docDetails.setDocDate("2023-01-01");
        req.setDocumentDetails(docDetails);

        // Provide dummy seller details to avoid NPE in updateSellerDetails
        CompanyDetails sellerDetails = new CompanyDetails();
        sellerDetails.setId(10);
        req.setSellerDetails(sellerDetails);

        // Create InvoiceHeader and initialize invoiceItems list and hasPrecedingDocument
        InvoiceHeader header = new InvoiceHeader();
        header.setInvoiceHeaderId(555L);
        header.setInvoiceItems(new ArrayList<>());
        header.setHasPrecedingDocument(false);
        header.setDocTypeId(1);

        Document doc = new Document();
        DocumentApprovalContainer dac = new DocumentApprovalContainer();
        DocumentMetadata meta = new DocumentMetadata();
        meta.setDocumentCategoryId(99);
        dac.setDocumentMetadata(meta);
        doc.setDocumentApprovalContainer(dac);
        header.setDocument(doc);

        when(invoiceHeaderRepository.findById(555L)).thenReturn(Optional.of(header));

        // Stub companyRepository to return a dummy Company when queried with sellerDetails' id
        Company dummySeller = new Company();
        dummySeller.setCompanyId(10);
        when(companyRepository.findById(10)).thenReturn(Optional.of(dummySeller));

        // Act
        boolean result = invoiceService.save(req, auth);

        // Assert
        assertTrue(result);
        // Todo: Vishal check why this is happening.
        verify(invoiceHeaderRepository, times(2)).saveAndFlush(header);

    }

    @Test
    void testSave_DuplicateInvoiceNumber() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        when(auth.getCompanyCode()).thenReturn(123L);

        InvoiceDetailsReq req = new InvoiceDetailsReq();
        req.setInvoiceHeaderId(555L);
        InvoiceDocumentDetails docDetails = new InvoiceDocumentDetails();
        docDetails.setDocNo("DUPLICATE");
        req.setDocumentDetails(docDetails);

        InvoiceHeader header = new InvoiceHeader();
        header.setInvoiceHeaderId(555L);

        Document doc = new Document();
        DocumentApprovalContainer dac = new DocumentApprovalContainer();
        DocumentMetadata meta = new DocumentMetadata();
        meta.setDocumentCategoryId(99);
        dac.setDocumentMetadata(meta);
        doc.setDocumentApprovalContainer(dac);

        header.setDocument(doc);

        when(invoiceHeaderRepository.findById(555L)).thenReturn(Optional.of(header));

        // Return true so the service thinks there's a duplicate
        when(documentRepository.existsByDocNoAndCompanyCodeAndDocumentApprovalContainer_ReportStatusNotInAndDocumentApprovalContainer_DocumentMetadata_DocumentCategoryIdAndIdNot(
                eq("DUPLICATE"), eq(123L), anyList(), eq(99), anyLong()))
                .thenReturn(true);

        assertThrows(DuplicateRecordFoundException.class, () -> invoiceService.save(req, auth));
    }

    // -------------------------------------------------------------------------
    // 17) deleteLineItem(long invoiceHeaderId, List<Long> lineItemIds, IAuthContextViewModel auth)
    // -------------------------------------------------------------------------
    @Test
    void testDeleteLineItem_HappyPath() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        long invoiceHeaderId = 100L;
        List<Long> lineItemIds = List.of(11L, 12L);

        InvoiceItem item1 = new InvoiceItem();
        item1.setId(11L);
        InvoiceItem item2 = new InvoiceItem();
        item2.setId(12L);

        when(invoiceItemRepository.findById(11L)).thenReturn(Optional.of(item1));
        when(invoiceItemRepository.findById(12L)).thenReturn(Optional.of(item2));

        invoiceService.deleteLineItem(invoiceHeaderId, lineItemIds, auth);

        verify(invoiceItemRepository).delete(item1);
        verify(invoiceItemRepository).delete(item2);
        verify(invoiceItemRepository).flush();
    }

    @Test
    void testDeleteLineItem_ItemNotFound() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        List<Long> lineItemIds = List.of(999L);

        // If the repository returns empty
        when(invoiceItemRepository.findById(999L)).thenReturn(Optional.empty());

        assertThrows(RecordNotFoundException.class,
                () -> invoiceService.deleteLineItem(100L, lineItemIds, auth));
    }

    // -------------------------------------------------------------------------
    // 18) getByInvoiceId(Long invoiceHeaderId, IAuthContextViewModel auth)
    // -------------------------------------------------------------------------
    @Test
    void testGetByInvoiceId_HappyPath() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        // Option 1: Remove this line if not needed
        // or Option 2: Mark it as lenient:
        lenient().when(auth.getCompanyCode()).thenReturn(123L);

        // Create a dummy InvoiceHeader with a non-null ID.
        InvoiceHeader dummyHeader = new InvoiceHeader();
        dummyHeader.setInvoiceHeaderId(888L);

        // Create two InvoiceItems, set IDs, quantity, and assign the dummy InvoiceHeader.
        InvoiceItem item1 = new InvoiceItem();
        item1.setId(1L);
        item1.setInvoiceHeader(dummyHeader);
        item1.setQuantity(10.0);

        InvoiceItem item2 = new InvoiceItem();
        item2.setId(2L);
        item2.setInvoiceHeader(dummyHeader);
        item2.setQuantity(20.0);

        List<InvoiceItem> items = List.of(item1, item2);
        when(invoiceItemRepository.findByInvoiceHeaderInvoiceHeaderId(999L)).thenReturn(items);

        InvoiceLineItemViewModel result = invoiceService.getByInvoiceId(999L, auth);
        assertNotNull(result);
        assertEquals(2, result.getItemList().size());
    }


    @Test
    void testGetByInvoiceId_NoItems() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        when(invoiceItemRepository.findByInvoiceHeaderInvoiceHeaderId(999L)).thenReturn(Collections.emptyList());

        InvoiceLineItemViewModel result = invoiceService.getByInvoiceId(999L, auth);
        assertNotNull(result);
        assertEquals(0, result.getItemList().size());
    }

    // -------------------------------------------------------------------------
    // 19) checkExistenceByInvoiceNo(...)
    // -------------------------------------------------------------------------
    @Test
    void testCheckExistenceByInvoiceNo_HappyPath() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        when(invoiceHeaderRepository.getCountByDocNoAndInvoiceHeaderId(1L, "INV-123", 999L))
                .thenReturn(1L); // means it exists

        boolean exists = invoiceService.checkExistenceByInvoiceNo(1L, "INV-123", 999L, auth);
        assertTrue(exists);
    }

    @Test
    void testCheckExistenceByInvoiceNo_NotFound() {
        IAuthContextViewModel auth = mock(IAuthContextViewModel.class);
        when(invoiceHeaderRepository.getCountByDocNoAndInvoiceHeaderId(1L, "INV-XYZ", 999L))
                .thenReturn(0L);

        boolean exists = invoiceService.checkExistenceByInvoiceNo(1L, "INV-XYZ", 999L, auth);
        assertFalse(exists);
    }
}
