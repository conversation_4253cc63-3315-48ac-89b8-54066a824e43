package in.taxgenie.pay_expense_pvv.repositories.implementations;

import static org.junit.jupiter.api.Assertions.*;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.rbac.IOrgHierarchyRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentApprovalContainerApproverService;
import in.taxgenie.pay_expense_pvv.services.interfaces.IDocumentApprovalContainerUserService;
import in.taxgenie.pay_expense_pvv.utils.DummyAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistry;
import in.taxgenie.pay_expense_pvv.utils.StaticDataRegistryForTest;
import in.taxgenie.pay_expense_pvv.utils.StringConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.InvoiceContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.QueueFilterViewModel;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.jdbc.SqlConfig;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.ActiveProfiles;
import org.testcontainers.containers.PostgreSQLContainer;
import jakarta.persistence.EntityManager;

import java.util.*;

@Sql(
        scripts = {"/lookup_data.sql", "/users.sql", "/data.sql"},
        config = @SqlConfig(commentPrefix = "--", separator = ";")
)
@DataJpaTest
@ExtendWith(SpringExtension.class)
@ComponentScan(basePackages = {"in.taxgenie.pay_expense_pvv.repositories.implementations"})
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class CustomDocumentApprovalContainerRepositoryTest {

    @Autowired
    private CustomDocumentApprovalContainerRepository repository;
    @Autowired
    private IOrgHierarchyRepository orgHierarchyRepository;


    @Autowired
    private IDocumentApprovalContainerRepository reportRepository;

    @Autowired
    private EntityManager entityManager;

    private QueueFilterViewModel filterValues;
    IAuthContextViewModel auth;


    // Static PostgreSQL container for the whole test class
    private static PostgreSQLContainer<?> postgreSQLContainer = new PostgreSQLContainer<>("postgres:13.3")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test")
            .withInitScript("schema.sql"); // This line will run the schema creation script when the container starts

    @BeforeAll
    static void startContainer() {
        postgreSQLContainer.start();
        System.setProperty("spring.datasource.url", postgreSQLContainer.getJdbcUrl());
        System.setProperty("spring.datasource.username", postgreSQLContainer.getUsername());
        System.setProperty("spring.datasource.password", postgreSQLContainer.getPassword());
    }

    @BeforeEach
    void setUp() {
        filterValues = new QueueFilterViewModel();
        IAuthContextViewModel auth = DummyAuthContextViewModel.createDummyAuthContext();
    }

//    @Test
//    void testGetInvoiceQueue_AllInvoices_shouldReturnOne() {
//        // Arrange
//        Map<String, List<String>> searchCriteria = new HashMap<>();
//        filterValues.setSearchCriteria(searchCriteria);
//        Pageable pageable = PageRequest.of(0, 10);
//        List<Long> metadataIds = List.of(324L);
//
//        String key = orgHierarchyRepository.findDistinctKeyByCompanyCode(StaticDataRegistryForTest.COMPANY_CODE);
//        // Act
//        Page<InvoiceContainerViewModel> result = repository.getInvoiceQueue(StaticDataRegistryForTest.COMPANY_CODE, StaticDataRegistryForTest.USER_ID, metadataIds, filterValues, pageable, StaticDataRegistry.isNullOrEmptyOrWhitespace(key) ? "Division" : key, List.of("TG01"));
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(1, result.getTotalElements());
//    }

//    @Test
//    void testGetInvoiceQueue_withSupplierLegalName_shouldReturnFilteredResults() {
//        // Arrange
//        Map<String, List<String>> searchCriteria = new HashMap<>();
//        searchCriteria.put(StringConstants.SUPPLIER_LEGAL_NAME, Arrays.asList("Supplier A", "Supplier B"));
//        filterValues.setSearchCriteria(searchCriteria);
//        Pageable pageable = PageRequest.of(0, 10);
//
//        // Act
//        Page<InvoiceContainerViewModel> result = repository.getInvoiceQueue(123L, 456L, Collections.emptyList(), filterValues, pageable, "", new ArrayList<>());
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(0, result.getTotalElements()); // Assuming no data is present in the in-memory DB
//    }
}