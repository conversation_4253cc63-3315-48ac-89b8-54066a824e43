package in.taxgenie.pay_expense_pvv.utils;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

public class DummyAuthContextViewModel implements IAuthContextViewModel {

    @Override
    public long getUserId() {
        return StaticDataRegistryForTest.USER_ID;
    }

    @Override
    public String getUserEmail() {
        return StaticDataRegistryForTest.EMAIL_ID;
    }

    @Override
    public long getCompanyCode() {
        return StaticDataRegistryForTest.COMPANY_CODE;
    }

    @Override
    public String getUserClientCode() {
        return "CLIENT123";
    }

    @Override
    public String getToken() {
        return "Bearer " + UUID.randomUUID().toString();
    }

    @Override
    public List<String> getAuthorities() {
        return Arrays.asList("ROLE_USER", "ROLE_ADMIN");
    }

    @Override
    public long getSenderProductId() {
        return 111L;
    }

    @Override
    public long getReceiverProductId() {
        return 222L;
    }

    @Override
    public String getCompanyPan() {
        return "**********";
    }

    @Override
    public boolean isCompanyToken() {
        return true;
    }

    // Static method to create a dummy auth context
    public static IAuthContextViewModel createDummyAuthContext() {
        return new DummyAuthContextViewModel();
    }
}