package in.taxgenie.pay_expense_pvv.utils;

import in.taxgenie.pay_expense_pvv.response.interfaces.infra.IServerResponseWithBody;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentCreateResponse;

public class DummyServerResponse implements IServerResponseWithBody<DocumentCreateResponse> {
    private int status;
    private String message;
    private boolean success;
    private DocumentCreateResponse body;

    public DummyServerResponse(int status, String message, boolean success, DocumentCreateResponse body) {
        this.status = status;
        this.message = message;
        this.success = success;
        this.body = body;
    }

    // Getters for JSON serialization (or you may use public fields if your project allows)
    public int getStatus() { return status; }

    @Override
    public int getResponseCode() {
        return 0;
    }

    public String getMessage() { return message; }

    @Override
    public boolean isOk() {
        return false;
    }

    public boolean isSuccess() { return success; }
    public DocumentCreateResponse getBody() { return body; }
}

