-- Insert a few rows into state_codes
INSERT INTO state_codes (id, code, name)
VALUES (1, 'KA', 'Karnataka'),
       (2, 'TN', 'Tamil Nadu');

-- Insert companies that have non-null stateCode
INSERT INTO company (company_id, cam_company_id, gst, gstin_status, state_code_id, supplier_company_name, vendor_code)
VALUES
  (100, 13049, 'AAA111XXXZZZ', 'Active', 1, 'Supplier A', 'V001'),  -- valid (non-null stateCode)
  (101, 13049, 'BBB222XXXZZZ', 'Active', 2, 'Supplier B', 'V002'),  -- valid (non-null stateCode)

-- Insert a company with null stateCode
INSERT INTO company (company_id, cam_company_id, gst, gstin_status, state_code_id, supplier_company_name, vendor_code)
VALUES
  (102, 13049, 'CCC333XXXZZZ', 'Active', NULL, 'Supplier C', 'V003');  -- stateCode is null