-- Metadata: 324
INSERT INTO sch_payinvoice_p2p.document_metadata (id,company_code,created_timestamp,creating_user_id,definitions_count,description,document_category,document_category_id,document_group,document_group_prefix,document_type,document_type_prefix,is_frozen,limit_days,purpose_required,rules_count,subgroups_count,updated_timestamp,updating_user_id,"uuid",action_type,applicable_expense_type) VALUES
	 (324, 13049,'2024-11-18 11:38:39.516',25361,1,'Admin Test Capex','Invoice',1,'AdminTestCapex','admcap','AdminTestCap','admtest',false,120,false,1,3,'2024-11-28 16:12:52.305',25420,NULL,'SUBMIT','CAPEX');

-- : 295
INSERT INTO sch_payinvoice_p2p.document_metadata (id, company_code,created_timestamp,creating_user_id,definitions_count,description,document_category,document_category_id,document_group,document_group_prefix,document_type,document_type_prefix,is_frozen,limit_days,purpose_required,rules_count,subgroups_count,updated_timestamp,updating_user_id,uuid,action_type,applicable_expense_type) VALUES
	 (295, 13049,'2024-10-16 10:11:53.228',25361,1,'Test Budget','Budget',4,'TestBudget','T-submit','TestBudget-submit','Testbud-submit',false,NULL,false,1,1,NULL,NULL,'80f2b3c2-386f-4b27-a8cf-377984e1aab6','SUBMIT','CAPEX');

-- Budget Master: 52
INSERT INTO sch_payinvoice_p2p.budget_master (id, company_code,created_timestamp,creating_user_id,deleted_timestamp,deleting_user_id,is_active,updated_timestamp,updating_user_id,"uuid","name",display_name) VALUES
	 (52, 13049,'2024-08-27 11:25:43.930444+05:30',25361,NULL,NULL,true,NULL,NULL,'d08a368e-bee8-43a6-945e-d67ec3fceffa'::uuid,'Department',NULL);

-- BudgetStructureMaster: 282
INSERT INTO sch_payinvoice_p2p.budget_structure_master (id, company_code,created_timestamp,creating_user_id,deleted_timestamp,deleting_user_id,is_active,updated_timestamp,updating_user_id,"uuid",structure_name,start_month_index,financial_year,current_step,lookup_data_id,document_id,document_metadata_id,is_approved,status) VALUES
	 (282, 13049,'2024-10-16 11:09:19.225',25440,NULL,NULL,true,NULL,NULL,'*************-451e-bcd6-25b78ec1af98'::uuid,'Struct161024',4,'2024-2025','STEP4',20,NULL,295,true,'ACCEPTED');

-- BudgetSyncMaster: 300
INSERT INTO sch_payinvoice_p2p.budget_sync_master (id, company_code,created_timestamp,creating_user_id,deleted_timestamp,deleting_user_id,is_active,updated_timestamp,updating_user_id,"uuid",address,budget_master_id,code,contact_details,description,"source",source_id,parent_id,master_data_json_store_id) VALUES
	 (300, 13049,'2024-09-03 11:06:21.795',25361,NULL,NULL,true,NULL,NULL,'57c1d15d-242d-482f-ace6-37d1691a6e60'::uuid,'',52,'Dept-005','','Testing',NULL,NULL,NULL,NULL);


-- Budget: 4610
INSERT INTO sch_payinvoice_p2p.budget (id, company_code,created_timestamp,creating_user_id,deleted_timestamp,deleting_user_id,is_active,updated_timestamp,updating_user_id,"uuid",budget_sync_master_id,"locked",soft_locked,total,treasury,parent_id,budget_code,description,budget_structure_master_id,consumed,is_budget_locked) VALUES
	 (4610, 13049,'2024-10-16 11:16:38.926',NULL,NULL,NULL,true,NULL,NULL,'157c3660-c9fc-4f9c-b3a2-5ad35c911985'::uuid,300,0.00,0.00,2250000.00,0.00, NULL,'Test16',NULL,282,0.00,false);


-- Subgroup: 363
INSERT INTO sch_payinvoice_p2p.document_subgroup (id, company_code,created_timestamp,creating_user_id,description,document_code,document_genre,document_genre_prefix,document_metadata_id,document_subgroup,document_subgroup_prefix,frequency,gl_account_code,is_date_range_applicable,is_destination_location_applicable,is_document_identifier_applicable,is_frozen,is_gst_entry_allowed,is_location_required,is_mobility_descriptor_applicable,is_source_location_applicable,is_standard_deduction_applicable,is_transport_descriptor_applicable,is_travel_descriptor_applicable,merchant_required,rules_count,subgroup_fields_json,updated_timestamp,updating_user_id,budget_id,has_preceding_document,applicable_expense_type) VALUES
	 (363, 13049,'2024-11-18 11:41:13.180',25361,'','I002','','',324,'NonPOBasedCap','nonpo',NULL,'8767867',false,false,false,false,false,false,false,false,false,false,false,false,1,'{"itemDetails": {"isSelected": true}, "buyerDetails": {"pin": 2, "email": 2, "gstin": 2, "address1": 2, "location": 2, "legalName": 2, "tradeName": 2, "isSelected": true, "phoneNumber": 2, "stateCodeId": 2}, "amountDetails": {"isSelected": true, "totalAssValue": 2, "roundoffAmount": 2, "totalCgstValue": 2, "totalIgstValue": 2, "totalSgstValue": 2, "finalInvoiceValue": 2, "totalInvoiceValue": 2}, "budgetSummary": {"isSelected": true}, "exportDetails": {"portCode": 2, "exportDuty": 2, "isSelected": true, "countryCode": 2, "refundClaim": 2, "shippingBillNo": 2, "foreignCurrency": 2, "shippingBillDate": 2}, "sellerDetails": {"pin": 2, "email": 2, "gstin": 2, "address1": 2, "location": 2, "legalName": 2, "tradeName": 2, "isSelected": true, "vendorCode": 2, "phoneNumber": 2, "stateCodeId": 2, "supplierCompanyName": 2}, "shipToDetails": {"pin": 2, "email": 2, "address1": 2, "location": 2, "isSelected": true, "phoneNumber": 2, "stateCodeId": 2}, "paymentDetails": {"ifscCode": 2, "payeeName": 2, "creditDays": 2, "isSelected": true, "paidAmount": 2, "paymentDue": 2, "directDebit": 2, "paymentTerm": 2, "modeOfPayment": 2, "accountDetails": 2, "creditTransfer": 2, "paymentInstruction": 2}, "businessDetails": {"gL": 2, "org": 2, "state": 2, "branch": 2, "hrushi": 2, "region": 2, "branch2": 2, "company": 2, "hrushi1": 2, "project": 2, "domestic": 2, "expenses": 2, "subBranch": 2, "department": 2, "isSelected": true, "hrushiTest2": 2, "subExpenses": 2, "testHrushi2": 2, "testHrushi3": 2, "plant_master": 2, "lineOfBusiness": 2, "lineofbusiness": 2, "aBSTC_Costcenters": 2, "thisisatestmasterdatawhichhasverylongnameforcheckingdisturbedUI": 2}, "dispatchDetails": {"pin": 2, "email": 2, "address1": 2, "location": 2, "isSelected": true, "phoneNumber": 2, "stateCodeId": 2}, "documentDetails": {"docNo": 2, "docDate": 2, "otherRef": 2, "isSelected": true, "invoiceType": 2, "requesterName": 2, "invoiceEndDate": 2, "invoiceSubType": 2, "invoiceStartDate": 2, "precedingInvoiceNo": 2, "invoiceDocumentType": 2, "precedingInvoiceDate": 2}, "ewayBillDetails": {"vehicleNo": 2, "isSelected": true, "transporter": 2, "odcOrRegular": 2, "transporterName": 2, "transporterDocNo": 2, "transporterDocDate": 2, "distOfTransportation": 2, "modeOfTransportation": 2}, "supportingDocument": {"isSelected": true}}',NULL,NULL,4610,false,NULL);

-- Rules
INSERT INTO sch_payinvoice_p2p.document_rule (branch_code,can_exceed_limit,company_code,cost_center_code,created_timestamp,creating_user_id,department_code,document_subgroup_id,employee_grade,employee_type,end_date,invoice_required_threshold,is_frozen,is_invoice_required,is_per_diem_allowed,is_unit_rate_applicable,limit_amount,location_category,maximum_amount,per_diem_amount,standard_deduction_rate,start_date,unit_of_measure,unit_rate,unit_rate_type,updated_timestamp,updating_user_id) VALUES
	 (NULL,false,13049,NULL,'2024-11-18 11:41:42.934',25361,NULL,363,NULL,NULL,'2099-12-31',NULL,false,false,false,false,700000.00,NULL,700000.00,NULL,NULL,'2024-11-01',NULL,NULL,NULL,NULL,NULL);

-- Document Approval Container: 3242
INSERT INTO sch_payinvoice_p2p.document_approval_container (id, action_level,action_status,company_code,contains_deviation,contains_sent_back,created_timestamp,creating_user_id,current_approver_employee_code,current_approver_first_name,current_approver_last_name,default_approver_remarks,delegation_remarks,deviation_remarks,document_type,employee_email,key01,key02,key03,key04,key05,key06,key07,key08,key09,key10,reject_remarks,report_status,send_back_remarks,updated_timestamp,updating_user_id,value01,value02,value03,value04,value05,value06,value07,value08,value09,value10,acknowledged_by_erp,approval_container_cgst_amount,approval_container_claim_amount,approval_container_igst_amount,approval_container_sgst_amount,approval_container_taxable_amount,approval_container_title,created_date,current_approver_system_id_code,description,dimension01,dimension02,dimension03,dimension04,dimension05,dimension06,dimension07,dimension08,dimension09,dimension10,document_identifier,document_metadata_id,employee_branch,employee_code,employee_cost_center,employee_department,employee_gl_main_account_code,employee_gl_sub_account_code,employee_grade,employee_hrms_code,employee_profit_center,employee_system_id,employee_type,end_date,erp_acknowledgement_timestamp,first_name,gl_document_reference,gl_posting_date,is_posted_to_gl,last_name,middle_name,paid_status,payment_date,purpose,start_date,submit_date,total_paid_amount,total_tds_amount,last_actioned_at,expense_type,document_approval_code,approved_date,erp_validated_date,holding_date,is_erp_validated,is_hold,is_parked,parking_date,sent_to_erp,erp_remarks) VALUES
	 (3242,1,NULL,13049,false,false,'2024-12-04 14:08:53.092',25361,NULL,NULL,NULL,NULL,NULL,NULL,0,'<EMAIL>','Department','Branch','Division',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,'2024-12-04 14:08:53.297',NULL,'P01','H01','TG',NULL,NULL,NULL,NULL,NULL,NULL,NULL,false,0.00,0.00,0.00,0.00,0.00,'AdminTestCap/AdminTestCapex Expense','2024-12-04',NULL,NULL,NULL,'W03',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'admtest-***********',324,'100','TG000007','CC-2','P01','10007',NULL,'002',NULL,'PP-2',NULL,NULL,'2024-12-04',NULL,'Rahul',NULL,NULL,false,'Pandey',NULL,NULL,NULL,NULL,'2024-12-04',NULL,0.00,0.00,'2024-12-04 08:38:53.300','CAPEX','4f02e13a-2d41-4d2c-bdfa-032702dd627f'::uuid,NULL,NULL,NULL,NULL,NULL,NULL,NULL,false,NULL);

-- Document: 2863
INSERT INTO sch_payinvoice_p2p."document" (id, applicable_amount,cgst_amount,cgst_rate,claim_amount,claim_amount_in_native_currency,company_code,created_date,created_timestamp,creating_user_id,description,destination_location,deviation_remarks,document1upload_content_type,document1upload_marker,document1upload_url,document1upload_uuid,document2upload_content_type,document2upload_marker,document2upload_url,document2upload_uuid,document3upload_content_type,document3upload_marker,document3upload_url,document3upload_uuid,document_approval_container_id,document_date,document_rule_id,document_subgroup_id,employee_email,end_date,frequency,gstin,identifier,igst_amount,igst_rate,invoice_amount,is_applicable_amount_lesser,is_deviated,limit_amount,"location",location_category,merchant_details,mobility_descriptor,originator_document_amount,originator_document_date,originator_document_identifier,quantity,sgst_amount,sgst_rate,source_location,standard_deduction_amount,standard_deduction_rate,start_date,taxable_amount,total_amountgstinclusive,transport_descriptor,travel_descriptor,unit_of_measure,unit_rate,updated_timestamp,updating_user_id,with_holding_tax_amount,withholding_tax_rate,"source",reference_details,terms_and_condition,doc_no,consumed_amount,discount_amount,export_duty_amount,other_charges_amount,remaining_amount) VALUES
	 (2863, 0.00,0.00,NULL,0.00,0.00,13049,NULL,'2024-12-04 14:08:53.378',25361,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,3242,NULL,324,363,'<EMAIL>',NULL,NULL,NULL,NULL,0.00,NULL,0.00,false,false,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0.00,NULL,NULL,NULL,NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'PDF',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);


-- Invoice Header: 336
INSERT INTO sch_payinvoice_p2p.invoice_header (invoice_header_id, account_details,additional_parameters,buyer_gstin_id,contract_ref_no,created_at,creating_user_id,credit_days,credit_transfer,date_of_receipt_advice,deleted_at,direct_debit,dist_of_transportation,doc_type_id,gstin_of_operator,any_other_ref,ifsc_code,grn_srn_status,has_preceding_document,hsn_sac_code,igst_on_intra,invoice_remarks,invoice_document_type_id,invoice_end_date,invoice_start_date,invoice_sub_type_id,invoice_type_id,is_delivery_address_same,is_dispatch_address_same,is_preceding_doc,mode_of_payment,no_of_line_items,odc_or_regular,other_refe,payee_name,payment_instruction,payment_term,po_ref_date,po_ref_no,preceding_invoice_date,preceding_invoice_no,project_ref,recept_advice_no,refund_claim,reverse_charge,remarks,seller_id,ship_address_1,ship_address_2,shipping_bill_date,shipping_bill_no,ship_email,ship_gstin,ship_legal_name,ship_location,ship_phone_number,ship_pin,ship_tradename,transaction_catogory,tax_schema,lot_ref_no,transporter_doc_date,transporter_doc_no,transporter,mode_of_transportation,transporter_name,updated_at,vehicle_no,"version",country_code,contact_person_id,document_id,foreign_currency,invoice_status_id,port_code,ship_state_code,dynamic_fields_json,address1,address2,buyer_contact_person,buyer_legal_name,buyer_trade_name,district,email,gstin,loc,phone_no,pin,state_code_id,street_name,sync_attempt,priority,currency,dispatch_address_1,dispatch_address_2,dispatch_email,dispatch_location,dispatch_name,dispatch_phone_number,dispatch_pin,exchange_rate,itc_type,pos,tds_value,dispatch_state_code) VALUES
	 (336, NULL,NULL,NULL,NULL,'2024-12-04 08:38:54.47',25361,NULL,NULL,NULL,NULL,NULL,NULL,1,NULL,NULL,NULL,NULL,false,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,2863,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,0,'INR',NULL,NULL,NULL,NULL,NULL,NULL,NULL,1.0,NULL,NULL,0.00,NULL);

-- Invoice Item