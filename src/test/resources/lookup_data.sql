INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (1,NULL,NULL,NULL,NULL,'DocumentType',NULL,'Invoice'),
	 (2,NULL,NULL,NULL,NULL,'DocumentType',NULL,'Purchase Requisition'),
	 (3,NULL,NULL,NULL,NULL,'DocumentType',NULL,'Purchase Order'),
	 (4,NULL,NULL,NULL,NULL,'DocumentType',NULL,'Budget'),
	 (5,'1',NULL,NULL,NULL,'invoice_status',NULL,'DRAFT'),
	 (6,'json_pdf_map_status',NULL,NULL,'80b5f77b-ab68-473f-8616-e5ddadc66b83'::uuid,'json_pdf_map_status',NULL,'<PERSON>SO<PERSON> LINKED'),
	 (7,NULL,NULL,NULL,NULL,'pr_type',NULL,'General'),
	 (8,NULL,NULL,NULL,NULL,'priority',NULL,'High'),
	 (9,NULL,NULL,NULL,NULL,'priority',NULL,'Medium'),
	 (10,NULL,NULL,NULL,NULL,'priority',NULL,'Low');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (11,NULL,NULL,NULL,NULL,'payment_terms',NULL,'7 Days'),
	 (12,NULL,NULL,NULL,NULL,'payment_terms',NULL,'15 Days'),
	 (15,NULL,NULL,NULL,NULL,'BudgetPeriod',NULL,'MONTHLY'),
	 (16,NULL,NULL,NULL,NULL,'BudgetPeriod',NULL,'QUARTERLY'),
	 (17,NULL,NULL,NULL,NULL,'BudgetPeriod',NULL,'SEMI-YEARLY'),
	 (18,NULL,NULL,NULL,NULL,'BudgetPeriod',NULL,'YEARLY'),
	 (19,NULL,NULL,NULL,NULL,'YEARLY',NULL,'1'),
	 (20,NULL,NULL,NULL,NULL,'SEMI-YEARLY',NULL,'2'),
	 (21,NULL,NULL,NULL,NULL,'QUARTERLY',NULL,'4'),
	 (22,NULL,NULL,NULL,NULL,'MONTHLY',NULL,'12');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (23,NULL,NULL,NULL,NULL,'UploadSource',NULL,'System-PR'),
	 (24,NULL,NULL,NULL,NULL,'UploadSource',NULL,'System-PO'),
	 (25,NULL,NULL,NULL,NULL,'UploadSource',NULL,'PDF'),
	 (26,NULL,NULL,NULL,NULL,'UploadSource',NULL,'JSON'),
	 (27,NULL,NULL,NULL,NULL,'ExpenditureType',NULL,'BOTH'),
	 (28,NULL,NULL,NULL,NULL,'ExpenditureType',NULL,'OPEX'),
	 (29,NULL,NULL,NULL,NULL,'ExpenditureType',NULL,'CAPEX'),
	 (30,NULL,NULL,NULL,NULL,'FinancialYear',NULL,'2025-2026'),
	 (31,NULL,NULL,NULL,NULL,'FinancialYear',NULL,'2024-2025'),
	 (32,NULL,NULL,NULL,NULL,'FinancialYear',NULL,'2023-2024');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (33,NULL,NULL,NULL,NULL,'FinancialYear',NULL,'2022-2023'),
	 (34,NULL,NULL,NULL,NULL,'FinancialYear',NULL,'2021-2022'),
	 (77,'SUBMITTED',NULL,NULL,NULL,'ReportStatus',NULL,'Submitted'),
	 (14,NULL,NULL,NULL,NULL,'payment_terms',NULL,'45 Days'),
	 (78,'SENT_BACK',NULL,NULL,NULL,'ReportStatus',NULL,'Sent Back'),
	 (35,NULL,NULL,NULL,NULL,'payment_terms',NULL,'30 Days'),
	 (13,NULL,NULL,NULL,NULL,'payment_terms',NULL,'As per Agreed Terms'),
	 (36,NULL,NULL,NULL,NULL,'po_status',NULL,'Open'),
	 (37,NULL,NULL,NULL,NULL,'po_status',NULL,'Partially Open'),
	 (38,NULL,NULL,NULL,NULL,'po_status',NULL,'Closed');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (40,NULL,NULL,NULL,NULL,'po_type',NULL,'PR based'),
	 (79,'ACCEPTED',NULL,NULL,NULL,'ReportStatus',NULL,'Accepted'),
	 (42,NULL,NULL,NULL,NULL,'budget_type',NULL,'ADD'),
	 (43,NULL,NULL,NULL,NULL,'budget_type',NULL,'LESS'),
	 (44,NULL,NULL,NULL,NULL,'budget_type',NULL,'TRANSFER'),
	 (39,NULL,NULL,NULL,NULL,'po_type',NULL,'Non PR based'),
	 (46,NULL,NULL,NULL,NULL,'invoice_sub_type',NULL,'Regular'),
	 (45,NULL,NULL,NULL,NULL,'invoice_type',NULL,'B2B - [Bussiness To Bussiness]'),
	 (47,NULL,NULL,NULL,NULL,'invoice_type',NULL,'DNR - [Debit Notes Registered]'),
	 (48,NULL,NULL,NULL,NULL,'invoice_type',NULL,'DNUR');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (49,NULL,NULL,NULL,NULL,'invoice_type',NULL,'CNR'),
	 (50,NULL,NULL,NULL,NULL,'invoice_type',NULL,'CNUR'),
	 (51,NULL,NULL,NULL,NULL,'invoice_type',NULL,'CI-[Commercial Invoice]'),
	 (52,NULL,NULL,NULL,NULL,'invoice_type',NULL,'BOE - [Bill Of Entry]'),
	 (53,NULL,NULL,NULL,NULL,'invoice_type',NULL,'B2BUR -[Bussiness To Bussiness Unregistered]'),
	 (54,NULL,NULL,NULL,NULL,'invoice_type',NULL,'IMPS	'),
	 (55,NULL,NULL,NULL,NULL,'invoice_sub_type',NULL,'Not Applicable'),
	 (56,NULL,NULL,NULL,NULL,'invoice_document_type',NULL,'Debit Note'),
	 (57,NULL,NULL,NULL,NULL,'invoice_document_type',NULL,'Debit Note Unregistered'),
	 (58,NULL,NULL,NULL,NULL,'invoice_document_type',NULL,'Credit Note');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (59,NULL,NULL,NULL,NULL,'invoice_document_type',NULL,'Credit Note Unregistered'),
	 (60,NULL,NULL,NULL,NULL,'invoice_document_type',NULL,'Import of Goods'),
	 (61,NULL,NULL,NULL,NULL,'invoice_document_type',NULL,'Invoice'),
	 (62,NULL,NULL,NULL,NULL,'invoice_document_type',NULL,'Import Of Services'),
	 (63,NULL,NULL,NULL,NULL,'invoice_po_type',NULL,'PO based'),
	 (64,NULL,NULL,NULL,NULL,'invoice_po_type',NULL,'Non PO based'),
	 (41,NULL,NULL,NULL,NULL,'budget_type',NULL,'SUBMIT'),
	 (67,'employeeEmail',NULL,NULL,NULL,'SearchParametersQueue',NULL,'Employee Email'),
	 (68,'employeeCode',NULL,NULL,NULL,'SearchParametersQueue',NULL,'Employee Code'),
	 (69,'createdDate',NULL,NULL,NULL,'SearchParametersQueue',NULL,'Voucher Date');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (70,'reportTitle',NULL,NULL,NULL,'SearchParametersQueue',NULL,'Title'),
	 (71,'reportClaimAmount',NULL,NULL,NULL,'SearchParametersQueue',NULL,'Claim Amount'),
	 (72,'approverName',NULL,NULL,NULL,'SearchParametersQueue',NULL,'Pending At'),
	 (73,'approverEmployeeCode',NULL,NULL,NULL,'SearchParametersQueue',NULL,'Approver Code'),
	 (65,'documentIdentifier',NULL,NULL,NULL,'SearchParametersQueue',NULL,'Voucher No.'),
	 (117,'legalName',NULL,NULL,NULL,'SearchParametersQueue',NULL,'Supplier Legal Name'),
	 (88,'panNumber',NULL,NULL,NULL,'SearchParametersQueueSupplierMaster',NULL,'PAN No'),
	 (92,'email1',NULL,NULL,NULL,'SearchParametersQueueSupplierMaster',NULL,'Email ID'),
	 (91,'contactPerson1',NULL,NULL,NULL,'SearchParametersQueueSupplierMaster',NULL,'Contact Person'),
	 (94,'PROCESSING',NULL,NULL,NULL,'ReportStatus',NULL,'Processing');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (93,'paymentTerms',NULL,NULL,NULL,'SearchParametersQueueSupplierMasterGST',NULL,'Payment Terms'),
	 (87,'gstin',NULL,NULL,NULL,'SearchParametersQueueSupplierMasterGST',NULL,'Supplier GSTIN'),
	 (81,'POSTED_TO_DESTINATION_SYSTEM',NULL,NULL,NULL,'ReportStatus',NULL,'Posted'),
	 (74,'REGULAR',NULL,NULL,NULL,'ReportStatus',NULL,'Regular'),
	 (75,'ALL',NULL,NULL,NULL,'ReportStatus',NULL,'All'),
	 (76,'DRAFT',NULL,NULL,NULL,'ReportStatus',NULL,'Draft'),
	 (82,'PAID',NULL,NULL,NULL,'ReportStatus',NULL,'Paid'),
	 (85,'vendorCode',NULL,NULL,NULL,'SearchParametersQueueSupplierMaster',NULL,'Supplier Code'),
	 (89,'requestAcceptanceStatus',NULL,NULL,NULL,'SearchParametersQueueSupplierMaster',NULL,'Request Acceptance Status'),
	 (101,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'box');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (90,'status',NULL,NULL,NULL,'SearchParametersQueueSupplierMaster',NULL,'Supplier Status'),
	 (95,NULL,NULL,NULL,NULL,'ITEM_TYPE',NULL,'Goods'),
	 (86,'legalName',NULL,NULL,NULL,'SearchParametersQueueSupplierMaster',NULL,'Supplier Name'),
	 (80,'REVOKED',NULL,NULL,NULL,'ReportStatus',NULL,'Discarded'),
	 (104,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'KGS'),
	 (105,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'KGS-KILOGRAMS'),
	 (96,NULL,NULL,NULL,NULL,'ITEM_TYPE',NULL,'Service'),
	 (102,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'Kg'),
	 (103,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'Kgs'),
	 (106,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'MTR');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (97,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'Kg'),
	 (100,NULL,NULL,NULL,NULL,'GST',NULL,'5'),
	 (116,NULL,NULL,NULL,NULL,'GST',NULL,'12'),
	 (110,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'OTH'),
	 (111,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'OTH-Others'),
	 (112,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'OTH-OTHERS'),
	 (98,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'BAG-BAGS'),
	 (107,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'Nos'),
	 (108,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'NOS'),
	 (109,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'NOS-NUMBERS');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (113,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'UNT'),
	 (114,NULL,NULL,NULL,NULL,'UNIT_OF_MEASURE',NULL,'UNT-UNITS'),
	 (99,NULL,NULL,NULL,NULL,'GST',NULL,'18'),
	 (115,NULL,NULL,NULL,NULL,'GST',NULL,'28'),
	 (66,'employeeName',NULL,NULL,NULL,'SearchParametersQueue',NULL,'Employee Name'),
	 (143,'vendorCode',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'Supplier Code'),
	 (144,'gstin',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'GSTIN'),
	 (145,'gstinStatus',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'GSTIN Status'),
	 (146,'docType',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'Document Type'),
	 (147,'documentIdentifier',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'Document Identifier');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (148,'submitDate',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'Purchase Order Date'),
	 (149,'documentDate',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'Document Date'),
	 (150,'approvalContainerClaimAmount',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'Approval Container Claim Amount'),
	 (151,'expenseType',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'Expense Type'),
	 (152,'documentGroup',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'Document Group'),
	 (153,'documentSubgroup',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'Document Subgroup'),
	 (154,'currentApproverFirstName',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'Approver'),
	 (155,'vendorCode',NULL,NULL,NULL,'SearchParametersQueuePurchaseOrder',NULL,'Supplier Code'),
	 (156,'vendorName',NULL,NULL,NULL,'SearchParametersQueuePurchaseOrder',NULL,'Vendor Name'),
	 (157,'gstin',NULL,NULL,NULL,'SearchParametersQueuePurchaseOrder',NULL,'GSTIN');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (158,'gstinStatus',NULL,NULL,NULL,'SearchParametersQueuePurchaseOrder',NULL,'GSTIN Status'),
	 (159,'docType',NULL,NULL,NULL,'SearchParametersQueuePurchaseOrder',NULL,'Document Type'),
	 (160,'documentIdentifier',NULL,NULL,NULL,'SearchParametersQueuePurchaseOrder',NULL,'Document Identifier'),
	 (162,'documentDate',NULL,NULL,NULL,'SearchParametersQueuePurchaseOrder',NULL,'Document Date'),
	 (163,'poAmount',NULL,NULL,NULL,'SearchParametersQueuePurchaseOrder',NULL,'Purchase Order Amount'),
	 (165,'expenseType',NULL,NULL,NULL,'SearchParametersQueuePurchaseOrder',NULL,'Expense Type'),
	 (166,'group',NULL,NULL,NULL,'SearchParametersQueuePurchaseOrder',NULL,'Group'),
	 (167,'subGroup',NULL,NULL,NULL,'SearchParametersQueuePurchaseOrder',NULL,'Subgroup'),
	 (168,'approver',NULL,NULL,NULL,'SearchParametersQueuePurchaseOrder',NULL,'Approver'),
	 (169,'docNo',NULL,NULL,NULL,'SearchParametersQueuePurchaseRequest',NULL,'Document Number');
INSERT INTO sch_payinvoice_p2p.lookup_data (id,"attribute",created_at,deleted_at,lookup_data_code,"type",updated_at,value) VALUES
	 (170,'documentIdentifier',NULL,NULL,NULL,'SearchParametersQueuePurchaseRequest',NULL,'Document Identifier'),
	 (172,'documentDate',NULL,NULL,NULL,'SearchParametersQueuePurchaseRequest',NULL,'Document Date'),
	 (173,'prAmount',NULL,NULL,NULL,'SearchParametersQueuePurchaseRequest',NULL,'Purchase Request Amount'),
	 (175,'expectedDeliveryDate',NULL,NULL,NULL,'SearchParametersQueuePurchaseRequest',NULL,'Expected Delivery Date'),
	 (142,'legalName',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'Supplier Legal Name'),
	 (176,'approverName',NULL,NULL,NULL,'SearchParametersQueuePurchaseRequest',NULL,'Approver Name'),
	 (177,'docNo',NULL,NULL,NULL,'SearchParametersQueueInvoice',NULL,'Document Number'),
	 (178,'docNo',NULL,NULL,NULL,'SearchParametersQueuePurchaseOrder',NULL,'Document Number');
