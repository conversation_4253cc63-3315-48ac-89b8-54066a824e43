#image: atlassian/default-image:3
options:
  docker: true

definitions:
  services:
    docker:
      memory: 2048
  steps:
    - step: &build-push-development
        name: build and push
        image: asia-south1-docker.pkg.dev/noble-stratum-393405/base-image/taxgenie-maven:3.9.6-jdk-17-gcloud #this image has kubectl,gcloud helm command and maven3.6.3
        caches:
          - maven
        script:
          - export TESTCONTAINERS_RYUK_DISABLED=true
          - echo $MVN_REPO_CREDS > /root/.m2/settings.xml
          - mvn clean install -Dskiptests #build the maven project
          - chmod +x ./get_version.sh
          - /bin/bash get_version.sh     #Geting version of maven project
          - VERSION=$(cat ver.txt)
          - echo $VERSION
          - export IMAGE_NAME=asia-south1-docker.pkg.dev/$GCP_PROJECT_ID/$IMAGE_REPO/development:$VERSION #Declaring backend image variable
          - echo $IMAGE_NAME
          - echo $GCLOUD_KEY_FILE_PREPROD > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json #gcloud sdk authentication using sa key
          - gcloud config set project $GCP_PROJECT_ID
          - gcloud auth configure-docker asia-south1-docker.pkg.dev #login gcp artifact registry
          # BUILD IMAGE
          - docker build -t $IMAGE_NAME . -f Dockerfile.gcp
          # PUBLISH IMAGE
          - docker push $IMAGE_NAME

    - step: &sonar-qube
        name: Deploy to sonar
        image: asia-south1-docker.pkg.dev/noble-stratum-393405/base-image/taxgenie-maven:3.9.6-jdk-17-gcloud #this image has kubectl,gcloud helm command and maven3.6.3
        caches:
          - maven
        script:
          - export TESTCONTAINERS_RYUK_DISABLED=true
          - echo $MVN_REPO_CREDS > /root/.m2/settings.xml
          - mvn clean install -Dskiptests #build the maven project
          - pipe: sonarsource/sonarqube-scan:2.0.1
            variables:
              SONAR_HOST_URL: ${SONAR_HOST_URL} # Get the value from the repository/workspace variable.
              SONAR_TOKEN: ${SONAR_TOKEN} # Get the value from the repository/workspace variable. You shouldn't set secret in clear text here.
              clone:
                depth:full       # Disables shallow clone and ensures full repository history is clone

    - step: &deploy-development
        name: deploy
        deployment: Dev
        image: asia-south1-docker.pkg.dev/noble-stratum-393405/base-image/taxgenie-maven:3.9.6-jdk-17-gcloud
        script:
          - chmod +x ./get_version.sh
          - /bin/bash get_version.sh     #Geting version of maven project
          - VERSION=$(cat ver.txt)
          - sed -i "s/image_tag/$VERSION/g" "./p2p-deployment/values-development.yaml"
          - sed -i "s/aws_access_key/$AWS_ACCESS_KEY_PREPROD/g" "./p2p-deployment/values-development.yaml"
          - sed -i "s/aws_secret_key/$AWS_SECRET_KEY_PREPROD/g" "./p2p-deployment/values-development.yaml"
          - sed -i "s/bitbucket-db-password/$DB_PASSWORD_DEVELOPMENT/g" "./p2p-deployment/values-development.yaml"
          # - sed -i "s/SONAR_HOST_URL/$SONAR_HOST_URL/g" "./p2p-deployment/values-development.yaml"
          # - sed -i "s/SONAR_TOKEN/${SONAR_TOKEN}|g" "./p2p-deployment/values-development.yaml"
          - echo $GCLOUD_KEY_FILE_PREPROD > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json
          - gcloud config set project $GCP_PROJECT_ID
          - gcloud auth configure-docker asia-south1-docker.pkg.dev
          - gcloud container clusters get-credentials $GKE_CLUSTER --zone=$GKE_CLUSTER_REGION --project $GCP_PROJECT_ID #authentication with gke cluster
          # - pipe: sonarsource/sonarqube-scan:2.0.1
          - helm upgrade -i payinvoice -f ./p2p-deployment/values-development.yaml  ./p2p-deployment --namespace development

    - step: &build-push-demo
        name: build and push
        image: asia-south1-docker.pkg.dev/noble-stratum-393405/base-image/taxgenie-maven:3.9.6-jdk-17-gcloud #this image has kubectl,gcloud helm command and maven3.6.3
        caches:
          - maven
        script:
          - export TESTCONTAINERS_RYUK_DISABLED=true
          - echo $MVN_REPO_CREDS > /root/.m2/settings.xml
          - mvn clean install -Dskiptests #build the maven project
          - chmod +x ./get_version.sh
          - /bin/bash get_version.sh     #Geting version of maven project
          - VERSION=$(cat ver.txt)
          - echo $VERSION
          - export IMAGE_NAME=asia-south1-docker.pkg.dev/$GCP_PROJECT_ID/$IMAGE_REPO/demo:$VERSION #Declaring backend image variable
          - echo $IMAGE_NAME
          - echo $GCLOUD_KEY_FILE_PREPROD > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json #gcloud sdk authentication using sa key
          - gcloud config set project $GCP_PROJECT_ID
          - gcloud auth configure-docker asia-south1-docker.pkg.dev #login gcp artifact registry
          # BUILD IMAGE
          - docker build -t $IMAGE_NAME . -f Dockerfile.gcp
          # PUBLISH IMAGE
          - docker push $IMAGE_NAME

    - step: &deploy-demo
        name: deploy
        image: asia-south1-docker.pkg.dev/noble-stratum-393405/base-image/taxgenie-maven:3.9.6-jdk-17-gcloud
        script:
          - chmod +x ./get_version.sh
          - /bin/bash get_version.sh     #Geting version of maven project
          - VERSION=$(cat ver.txt)
          - sed -i "s/image_tag/$VERSION/g" "./p2p-deployment/values-demo.yaml"
          - sed -i "s/aws_access_key/$AWS_ACCESS_KEY_PREPROD/g" "./p2p-deployment/values-demo.yaml" # Todo: Check if same key file can be used
          - sed -i "s/aws_secret_key/$AWS_SECRET_KEY_PREPROD/g" "./p2p-deployment/values-demo.yaml" # Todo: Check if same key file can be used
          - sed -i "s/bitbucket-db-password/$DB_PASSWORD_DEMO/g" "./p2p-deployment/values-demo.yaml"
          - echo $GCLOUD_KEY_FILE_PREPROD > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json
          - gcloud config set project $GCP_PROJECT_ID
          - gcloud auth configure-docker asia-south1-docker.pkg.dev
          - gcloud container clusters get-credentials $GKE_CLUSTER --zone=$GKE_CLUSTER_REGION --project $GCP_PROJECT_ID #authentication with gke cluster
          - helm upgrade -i payinvoice -f ./p2p-deployment/values-demo.yaml  ./p2p-deployment --namespace demo
            # - pipe: sonarsource/sonarqube-scan:2.0.1
            # variables:
          #   SONAR_HOST_URL: ${SONAR_HOST_URL} # Get the value from the repository/workspace variable.
          #   SONAR_TOKEN: ${SONAR_TOKEN} # Get the value from the repository/workspace variable. You shouldn't set secret in clear text here.

    - step: &build-push-uat
        name: build and push
        image: asia-south1-docker.pkg.dev/noble-stratum-393405/base-image/taxgenie-maven:3.9.6-jdk-17-gcloud #this image has kubectl,gcloud helm command and maven3.6.3
        caches:
          - maven
        script:
          - export TESTCONTAINERS_RYUK_DISABLED=true
          - echo $MVN_REPO_CREDS > /root/.m2/settings.xml
          - mvn clean install -Dskiptests #build the maven project
          - chmod +x ./get_version.sh
          - /bin/bash get_version.sh     #Geting version of maven project
          - VERSION=$(cat ver.txt)
          - echo $VERSION
          - export IMAGE_NAME=asia-south1-docker.pkg.dev/$GCP_PROJECT_ID/$IMAGE_REPO/uat:$VERSION #Declaring backend image variable
          - echo $IMAGE_NAME
          - echo $GCLOUD_KEY_FILE_PREPROD > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json #gcloud sdk authentication using sa key
          - gcloud config set project $GCP_PROJECT_ID
          - gcloud auth configure-docker asia-south1-docker.pkg.dev #login gcp artifact registry
          # BUILD IMAGE
          - docker build -t $IMAGE_NAME . -f Dockerfile.gcp
          # PUBLISH IMAGE
          - docker push $IMAGE_NAME

    - step: &deploy-uat
        name: deploy
        deployment: Uat
        image: asia-south1-docker.pkg.dev/noble-stratum-393405/base-image/taxgenie-maven:3.9.6-jdk-17-gcloud
        script:
          - chmod +x ./get_version.sh
          - /bin/bash get_version.sh     #Geting version of maven project
          - VERSION=$(cat ver.txt)
          - sed -i "s/image_tag/$VERSION/g" "./p2p-deployment/values-uat.yaml"
          - sed -i "s/aws_access_key/$AWS_ACCESS_KEY_PREPROD/g" "./p2p-deployment/values-uat.yaml" # Todo: Check if same key file can be used
          - sed -i "s/aws_secret_key/$AWS_SECRET_KEY_PREPROD/g" "./p2p-deployment/values-uat.yaml" # Todo: Check if same key file can be used
          - sed -i "s/bitbucket-db-password/$DB_PASSWORD_UAT/g" "./p2p-deployment/values-uat.yaml"
          - echo $GCLOUD_KEY_FILE_PREPROD > ~/.gcloud-key.json
          - gcloud auth activate-service-account --key-file ~/.gcloud-key.json
          - gcloud config set project $GCP_PROJECT_ID
          - gcloud auth configure-docker asia-south1-docker.pkg.dev
          - gcloud container clusters get-credentials $GKE_CLUSTER --zone=$GKE_CLUSTER_REGION --project $GCP_PROJECT_ID #authentication with gke cluster
          - helm upgrade -i payinvoice -f ./p2p-deployment/values-uat.yaml  ./p2p-deployment --namespace uat
            # - pipe: sonarsource/sonarqube-scan:2.0.1
          # variables:
          #   SONAR_HOST_URL: ${SONAR_HOST_URL} # Get the value from the repository/workspace variable.
          #   SONAR_TOKEN: ${SONAR_TOKEN} # Get the value from the repository/workspace variable. You shouldn't set secret in clear text here.

    - step: &initialize-and-test
        name: TestContainer Init
        image: asia-south1-docker.pkg.dev/noble-stratum-393405/base-image/taxgenie-maven:3.9.6-jdk-17-gcloud #this image has kubectl,gcloud helm command and maven3.6.3
        caches:
          - maven
        script:
          - export TESTCONTAINERS_RYUK_DISABLED=true
          - echo $MVN_REPO_CREDS > /root/.m2/settings.xml
          - mvn clean install
        services:
          - docker

pipelines:
  tags:
    sonar-qube-development-*:
      - step:
          caches:
            - maven
          <<: *sonar-qube
          name: Sonar qube analysis [development]
    development-*:
      - step:
          caches:
            - maven
          <<: *build-push-development
          name: Build and Push [development]
      #      - step:
      #          caches:
      #            - maven
      #          <<: *sonar-qube
      #          name: Perform Sonar Qube analysis [development]
      - step:
          <<: *deploy-development
          name: Deploy apps [development]

    demo-*:
      - step:
          caches:
            - maven
          <<: *build-push-demo
          name: Build and Push [demo]
      - step:
          caches:
            - maven
          # deployment: demo
          <<: *deploy-demo
          name: Deploy apps [demo]

    uat-*:
      - step:
          caches:
            - maven
          <<: *build-push-uat
          name: Build and Push [uat]
      - step:
          caches:
            - maven
          # deployment: uat
          <<: *deploy-uat
          name: Deploy apps [uat]



  branches:
    develop:
      - step:
          caches:
            - maven
          <<: *initialize-and-test
          name: Initialize and perform tests
      - step:
          caches:
            - maven
          <<: *build-push-development
          name: Build and Push [development]
      #      - step:
      #          caches:
      #            - maven
      #          <<: *sonar-qube
      #          name: Perform Sonar Qube analysis [development]
      - step:
          #          deployment: development
          <<: *deploy-development
          name: Deploy apps [development]