<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1747656363937-10">
        <createTable tableName="erp_grn">
            <column autoIncrement="true" name="id" startWith="14" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="erp_grn_pkey"/>
            </column>
            <column name="grn_number" type="TEXT"/>
            <column name="grn_date" type="date"/>
            <column name="grn_reference_id" type="TEXT"/>
            <column name="grn_qty" type="numeric(18, 2)"/>
            <column name="grn_unit" type="TEXT"/>
            <column name="grn_status" type="TEXT"/>
            <column name="grn_created_by" type="TEXT"/>
            <column name="grn_user_email" type="TEXT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="company_code" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-11">
        <createTable tableName="erp_grn_item">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="erp_grn_item_pkey"/>
            </column>
            <column name="grn_id" type="INTEGER"/>
            <column name="po_line_item_number" type="TEXT"/>
            <column name="grni_line_item_number" type="TEXT"/>
            <column name="grni_received_quantity" type="numeric(18, 2)"/>
            <column name="po_rate" type="numeric(18, 2)"/>
            <column name="grn_value" type="numeric(18, 2)"/>
            <column name="grni_unit_of_measure" type="TEXT"/>
            <column name="material_code" type="TEXT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-12">
        <createTable tableName="po_grn_mapping">
            <column autoIncrement="true" name="id" startWith="14" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="po_grn_mapping_pkey"/>
            </column>
            <column name="purchase_order_header_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="erp_grn_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="now()" name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-13">
        <addColumn tableName="tenant_details">
            <column name="cloud_access_key" type="JSONB"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-14">
        <addColumn tableName="tenant_details">
            <column name="bucket_name" type="VARCHAR"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-15">
        <addColumn tableName="invoice_item">
            <column name="erp_grn_id" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-16">
        <addColumn tableName="purchase_order_header">
            <column name="state_code_id" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-17">
        <addForeignKeyConstraint baseColumnNames="grn_id" baseTableName="erp_grn_item" constraintName="erp_grn_item_grn_id_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="erp_grn" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-18">
        <addForeignKeyConstraint baseColumnNames="erp_grn_id" baseTableName="invoice_item" constraintName="fk_invoice_item_to_erp_grn" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="erp_grn" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-19">
        <addForeignKeyConstraint baseColumnNames="erp_grn_id" baseTableName="po_grn_mapping" constraintName="fk_po_grn_mapping_to_purchase_erp_grn" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="erp_grn" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-20">
        <addForeignKeyConstraint baseColumnNames="purchase_order_header_id" baseTableName="po_grn_mapping" constraintName="fk_po_grn_mapping_to_purchase_order_header" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="purchase_order_header" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-1">
        <modifyDataType columnName="additional_supporting_document_id" newDataType="int" tableName="additional_supporting_documents_to_invoice"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-3">
        <addDefaultValue columnDataType="timestamp" columnName="created_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-4">
        <modifyDataType columnName="description" newDataType="varchar(255)" tableName="invoice_item"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-6">
        <modifyDataType columnName="invoice_received_id" newDataType="int" tableName="invoice_upload"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-7">
        <modifyDataType columnName="invoice_upload_id" newDataType="int" tableName="additional_supporting_documents_to_invoice"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-8">
        <modifyDataType columnName="supporting_document_category_id" newDataType="int" tableName="supporting_document_categories"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1747656363937-9">
        <addDefaultValue columnDataType="timestamp" columnName="updated_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
</databaseChangeLog>
