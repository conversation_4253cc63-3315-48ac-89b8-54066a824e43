<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1737951016571-88">
        <dropForeignKeyConstraint baseTableName="invoice_header" constraintName="fkgmw3v04u534aq80o40bsf366s"/>
    </changeSet>
    <changeSet author="vedantdube" id="create-approver-type-enum">
        <sql>
            CREATE TYPE approver_type_enum AS ENUM ('APPROVER', 'UPLOADER', 'PAYER');
        </sql>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-24">
        <createTable tableName="master_data_store">
            <column name="master_data_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="company_code" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="body" type="JSONB"/>
            <column name="description" type="VARCHAR(255)"/>
            <column name="created_timestamp" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="creating_user_id" type="BIGINT"/>
            <column name="updated_timestamp" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="updating_user_id" type="BIGINT"/>
            <column autoIncrement="true" name="id" startWith="9" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-25">
        <createTable tableName="budget_sync_mapping">
            <column autoIncrement="true" name="id" startWith="8" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="budget_sync_mapping_pkey"/>
            </column>
            <column name="budget_sync_parent_id" type="BIGINT"/>
            <column name="budget_sync_child_id" type="BIGINT"/>
            <column name="budget_master_parent_id" type="BIGINT"/>
            <column name="budget_master_child_id" type="BIGINT"/>
            <column defaultValueBoolean="true" name="is_active" type="BOOLEAN"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="deleted_by" type="BIGINT"/>
            <column defaultValueComputed="now()" name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-26">
        <createTable tableName="ratio_category_config">
            <column autoIncrement="true" name="id" startWith="98" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ratio_category_config_pkey"/>
            </column>
            <column name="master_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="company_code" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-27">
        <createTable tableName="ratio_category_master">
            <column autoIncrement="true" name="id" startWith="99" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ratio_category_master_pkey"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="ratio" type="FLOAT8"/>
            <column name="master_json" type="JSONB"/>
            <column name="company_code" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="uuid" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-28">
        <createTable tableName="mapping_segment_ratio_to_entity">
            <column autoIncrement="true" name="id" startWith="57" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="mapping_segment_ratio_to_entity_pkey"/>
            </column>
            <column name="entity_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="document_type_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="ratio_distribution_json" type="JSONB"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-29">
        <createTable tableName="pdf_templates">
            <column autoIncrement="true" name="id" startWith="4" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pdf_templates_pk"/>
            </column>
            <column name="type" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="pdf_template_file" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="mapping_json" type="JSONB"/>
            <column name="description" type="TEXT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>

    <changeSet author="vedantdube (generated)" id="1737951016571-31">
        <createIndex indexName="budget_mapping_master_unique_partial" tableName="budget_mapping_master" unique="true">
            <column name="budget_structure_master_id"/>
            <column name="budget_master_id"/>
            <column name="company_code"/>
            <column name="parent_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-32">
        <createTable tableName="invoice_tat_report">
            <column autoIncrement="true" name="id" startWith="46" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="invoice_tat_report_pkey"/>
            </column>
            <column name="approver" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="now()" name="entry_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="tat_time_days" type="INTEGER"/>
            <column name="status" type="VARCHAR(50)"/>
            <column name="doc_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="key" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="title" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="value" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="approver_type" type="APPROVER_TYPE_ENUM">
                <constraints nullable="false"/>
            </column>
            <column name="metadata" type="JSONB"/>
            <column name="document_date" type="date"/>
            <column name="company_code" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-33">
        <addUniqueConstraint columnNames="type" constraintName="pdf_templates_type_key" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-34">
        <createTable tableName="adm_user_details_temp">
            <column name="ud_user_id" type="INTEGER"/>
            <column name="ud_user_name" type="TEXT"/>
            <column name="ud_pwd" type="TEXT"/>
            <column name="ud_first_name" type="TEXT"/>
            <column name="ud_last_name" type="TEXT"/>
            <column name="ud_email_id" type="TEXT"/>
            <column name="ud_mobile_no" type="TEXT"/>
            <column name="ud_entity_id" type="INTEGER"/>
            <column name="ud_et_entity_type_id" type="INTEGER"/>
            <column name="ud_user_status" type="TEXT"/>
            <column name="ud_activity_status" type="TEXT"/>
            <column name="ud_designation" type="TEXT"/>
            <column name="ud_branch_code" type="TEXT"/>
            <column name="ud_state_code" type="TEXT"/>
            <column name="ud_otp" type="TEXT"/>
            <column name="ud_change_pwd_flag" type="TEXT"/>
            <column name="ud_last_pwd" type="TEXT"/>
            <column name="ud_last_pwd_change_tm" type="TEXT"/>
            <column name="ud_activity_status_tm" type="TEXT"/>
            <column name="ud_last_login_tm" type="TEXT"/>
            <column name="ud_email_verification" type="TEXT"/>
            <column name="ud_gcm_id" type="TEXT"/>
            <column name="ud_cr_uid" type="INTEGER"/>
            <column name="ud_cr_dt" type="TEXT"/>
            <column name="ud_upd_uid" type="INTEGER"/>
            <column name="ud_upd_dt" type="TEXT"/>
            <column name="ud_user_photo" type="TEXT"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-35">
        <createTable tableName="countries_copy">
            <column name="country_id" type="INTEGER"/>
            <column name="name" type="VARCHAR(52)"/>
            <column name="country_code" type="VARCHAR(2)"/>
            <column name="create_at" type="VARCHAR(19)"/>
            <column name="updated_at" type="VARCHAR(1)"/>
            <column name="deleted_at" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-36">
        <createTable tableName="item_master_abcdl_prod">
            <column name="product_id" type="INTEGER"/>
            <column name="companyID" type="INTEGER"/>
            <column name="subsidiary_id" type="INTEGER"/>
            <column name="item_id" type="VARCHAR(50)"/>
            <column name="item_name" type="VARCHAR(50)"/>
            <column name="HSN/SAC" type="INTEGER"/>
            <column name="description" type="VARCHAR(50)"/>
            <column name="rate" type="VARCHAR(50)"/>
            <column name="account" type="VARCHAR(50)"/>
            <column name="account_code" type="VARCHAR(50)"/>
            <column name="taxable" type="VARCHAR(50)"/>
            <column name="exemption_reason" type="VARCHAR(50)"/>
            <column name="product_type" type="VARCHAR(50)"/>
            <column name="intra_state_tax_name" type="VARCHAR(50)"/>
            <column name="intra_state_tax_rate" type="VARCHAR(50)"/>
            <column name="intra_state_tax_type" type="VARCHAR(50)"/>
            <column name="inter_state_tax_name" type="VARCHAR(50)"/>
            <column name="inter_state_tax_rate" type="VARCHAR(50)"/>
            <column name="inter_state_tax_type" type="VARCHAR(50)"/>
            <column name="source" type="VARCHAR(50)"/>
            <column name="reference_id" type="VARCHAR(50)"/>
            <column name="last_sync_time" type="VARCHAR(50)"/>
            <column name="status" type="VARCHAR(50)"/>
            <column name="usage_unit" type="VARCHAR(50)"/>
            <column name="purchase_rate" type="VARCHAR(50)"/>
            <column name="purchase_account" type="VARCHAR(50)"/>
            <column name="purchase_account_code" type="VARCHAR(50)"/>
            <column name="purchase_description" type="VARCHAR(50)"/>
            <column name="inventory_account" type="VARCHAR(50)"/>
            <column name="inventory_account_code" type="VARCHAR(50)"/>
            <column name="reorder_point" type="VARCHAR(50)"/>
            <column name="vendor" type="VARCHAR(50)"/>
            <column name="initial_stock" type="VARCHAR(50)"/>
            <column name="initial_stock_rate" type="VARCHAR(50)"/>
            <column name="stock_on_hand" type="VARCHAR(50)"/>
            <column name="item_type" type="VARCHAR(50)"/>
            <column name="function" type="VARCHAR(50)"/>
            <column name="territory" type="VARCHAR(50)"/>
            <column name="division" type="VARCHAR(50)"/>
            <column name="customer_segment" type="VARCHAR(50)"/>
            <column name="channel" type="VARCHAR(50)"/>
            <column name="cf_alias_name" type="VARCHAR(50)"/>
            <column name="cf_item_id" type="VARCHAR(50)"/>
            <column name="cf_product_view" type="VARCHAR(50)"/>
            <column name="currencyCode" type="VARCHAR(50)"/>
            <column name="createdTimeStamp" type="VARCHAR(50)"/>
            <column name="budgetCode" type="VARCHAR(50)"/>
            <column name="expenses_head_type" type="VARCHAR(50)"/>
            <column name="asset_age" type="VARCHAR(50)"/>
            <column name="depart_budget_amt" type="VARCHAR(50)"/>
            <column name="branch_allocated_budget" type="VARCHAR(50)"/>
            <column name="total_balance_amt" type="VARCHAR(50)"/>
            <column name="total_asset_amt_without_tax" type="VARCHAR(50)"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-37">
        <createTable tableName="users_backup">
            <column name="id" type="BIGINT"/>
            <column name="activity_status" type="VARCHAR(255)"/>
            <column name="activity_status_time" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="branch_code" type="VARCHAR(255)"/>
            <column name="chnage_pwd_flag" type="BOOLEAN"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="designation" type="VARCHAR(255)"/>
            <column name="email_id" type="VARCHAR(255)"/>
            <column name="first_name" type="VARCHAR(255)"/>
            <column name="is_email_verified" type="BOOLEAN"/>
            <column name="last_name" type="VARCHAR(255)"/>
            <column name="last_password" type="VARCHAR(255)"/>
            <column name="last_password_change_time" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="mobile_no" type="VARCHAR(255)"/>
            <column name="otp" type="VARCHAR(255)"/>
            <column name="pwd" type="VARCHAR(255)"/>
            <column name="state_code" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="user_id" type="INTEGER"/>
            <column name="user_name" type="VARCHAR(255)"/>
            <column name="user_photo" type="VARCHAR(255)"/>
            <column name="user_status" type="BOOLEAN"/>
            <column name="company_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-38">
        <createTable tableName="vendor_master">
            <column name="vm_seller_id" type="INTEGER"/>
            <column name="vm_supplier_fn" type="TEXT"/>
            <column name="vm_supplier_ln" type="TEXT"/>
            <column name="vm_supplier_code" type="INTEGER"/>
            <column name="vm_supplier_legal_name" type="TEXT"/>
            <column name="vm_supplier_company_name" type="TEXT"/>
            <column name="vm_supplier_pan" type="TEXT"/>
            <column name="vm_supplier_gstin" type="TEXT"/>
            <column name="vm_supplier_gstin_status" type="TEXT"/>
            <column name="vm_buyer_pan" type="TEXT"/>
            <column name="vm_parent_company_id" type="INTEGER"/>
            <column name="vm_buyer_fn" type="TEXT"/>
            <column name="vm_buyer_ln" type="TEXT"/>
            <column name="vm_buyer_contact_no" type="TEXT"/>
            <column name="vm_buyer_email_id" type="TEXT"/>
            <column name="vm_group_id" type="TEXT"/>
            <column name="vm_gst_cancellation_date" type="TEXT"/>
            <column name="vm_compliance_gstin_rating" type="TEXT"/>
            <column name="vm_itc_risk" type="TEXT"/>
            <column name="vm_msme_status" type="INTEGER"/>
            <column name="vm_e_invoice" type="TEXT"/>
            <column name="vm_hsn_mandatory" type="TEXT"/>
            <column name="vm_pan_aadhar_link" type="TEXT"/>
            <column name="vm_cin_no" type="TEXT"/>
            <column name="vm_cin_status" type="TEXT"/>
            <column name="vm_tan_no" type="TEXT"/>
            <column name="vm_supplier_portal_status" type="TEXT"/>
            <column name="vm_email_1" type="TEXT"/>
            <column name="vm_email_2" type="TEXT"/>
            <column name="vm_email_3" type="TEXT"/>
            <column name="vm_contact_no_1" type="TEXT"/>
            <column name="vm_contact_no_2" type="TEXT"/>
            <column name="vm_contact_no_3" type="TEXT"/>
            <column name="vm_supplier_addr_1" type="TEXT"/>
            <column name="vm_supplier_addr_2" type="TEXT"/>
            <column name="vm_zip_code" type="TEXT"/>
            <column name="vm_city" type="TEXT"/>
            <column name="vm_state" type="TEXT"/>
            <column name="vm_state_code" type="TEXT"/>
            <column name="vm_country" type="TEXT"/>
            <column name="vm_payment_term" type="TEXT"/>
            <column name="vm_acc_no" type="TEXT"/>
            <column name="vm_acc_name" type="TEXT"/>
            <column name="vm_ifsc_code" type="TEXT"/>
            <column name="vm_status" type="TEXT"/>
            <column name="vm_cr_uid" type="INTEGER"/>
            <column name="vm_cr_dt" type="TEXT"/>
            <column name="vm_upd_uid" type="INTEGER"/>
            <column name="vm_upd_dt" type="TEXT"/>
            <column name="vm_gstr1_filing_month" type="TEXT"/>
            <column name="vm_gstr1_filing_date" type="TEXT"/>
            <column name="vm_gstr1_due_date" type="TEXT"/>
            <column name="vm_gstr1_diff_date" type="TEXT"/>
            <column name="vm_gstr3b_filing_month" type="TEXT"/>
            <column name="vm_gstr3b_filing_date" type="TEXT"/>
            <column name="vm_gstr3b_due_date" type="TEXT"/>
            <column name="vm_gstr3b_diff_date" type="TEXT"/>
            <column name="vm_taxpayertype" type="TEXT"/>
            <column name="vm_gstr1_rating" type="TEXT"/>
            <column name="vm_gstr3b_rating" type="TEXT"/>
            <column name="vm_comp_code" type="INTEGER"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-39">
        <addColumn tableName="document_storage">
            <column name="creating_by" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-40">
        <addColumn tableName="purchase_request_item">
            <column name="hsn_sac_code" type="VARCHAR(8)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-41">
        <addColumn tableName="budget_document_action">
            <column name="consumed_amount" type="numeric(38, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-42">
        <addColumn tableName="budget_document_action">
            <column name="doc_no" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-43">
        <addColumn tableName="budget_master">
            <column name="users" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-44">
        <addColumn tableName="budget_structure_master">
            <column name="users" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-45">
        <addColumn tableName="budget_mapping_master">
            <column name="users" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-46">
        <addColumn tableName="budget">
            <column name="unallocated" type="numeric(38, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-47">
        <addColumn tableName="pan_detail">
            <column name="terms_and_conditions" type="TEXT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-48">
        <addColumn tableName="budget_frequency_mapping">
            <column name="unallocated" type="numeric(38, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-49">
        <addColumn tableName="budget_structure_master">
            <column name="metadata_id" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-50">
        <addColumn tableName="purchase_request_item">
            <column name="unit_of_measure_id" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-51">
        <addColumn tableName="budget_frequency_mapping">
            <column name="users" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-52">
        <addColumn tableName="budget_sync_master">
            <column name="users" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-53">
        <addColumn tableName="budget">
            <column name="users" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-54">
        <addColumn tableName="budget_frequency_mapping">
            <column name="allocated" type="numeric(38, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-55">
        <addColumn tableName="budget">
            <column name="budget_mapping_master_id" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-56">
        <addColumn tableName="budget">
            <column name="document_id" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-30-2">
        <addUniqueConstraint columnNames="document_id" constraintName="uk_ialgbn5v5lalbdccr6qrdq2vx" tableName="budget"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-57">
        <addColumn tableName="purchase_request_item">
            <column name="hsn_or_sac" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-58">
        <addColumn tableName="budget">
            <column name="allocated" type="numeric(38, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-59">
        <addColumn tableName="users">
            <column name="company_id" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-60">
        <addColumn tableName="purchase_order_item">
            <column name="hsn_or_sac" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-61">
        <addColumn tableName="item_master">
            <column name="suppiler_item_code" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-62">
        <addColumn tableName="item_master">
            <column name="suppiler_rate" type="numeric(38, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-63">
        <addColumn tableName="item_master">
            <column name="suppiler_name" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-64">
        <addColumn tableName="item_master">
            <column name="suppiler_quantity" type="FLOAT8"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-65">
        <addColumn tableName="invoice_item">
            <column name="hsn_or_sac" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-66">
        <addColumn tableName="purchase_order_header">
            <column name="po_type_id" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-67">
        <addColumn tableName="document">
            <column name="invoice_number" type="VARCHAR(50)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-68">
        <addPrimaryKey columnNames="company_code, code, master_data_type" constraintName="master_data_store_pkey" tableName="master_data_store"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-69">
        <addUniqueConstraint columnNames="request_acceptance_status" constraintName="uk_kao0gbwv8bkkgdv9tgmkf0p0s" tableName="company_handshake"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-70">
        <addForeignKeyConstraint baseColumnNames="users" baseTableName="budget_frequency_mapping" constraintName="fk68kayfre88astwu82gmxlahek" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-71">
        <addForeignKeyConstraint baseColumnNames="document_id" baseTableName="budget" constraintName="fk833ha1r5j126ma3rr1b56ryw5" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="document" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-72">
        <addForeignKeyConstraint baseColumnNames="users" baseTableName="budget_sync_master" constraintName="fk9f3osrbgxkj4tsy4yiqnqbmg7" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-73">
        <addForeignKeyConstraint baseColumnNames="budget_sync_child_id" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_mapping_to_sync_master_child" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="budget_sync_master" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-74">
        <addForeignKeyConstraint baseColumnNames="budget_sync_parent_id" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_mapping_to_sync_master_parent" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="budget_sync_master" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-75">
        <addForeignKeyConstraint baseColumnNames="budget_master_child_id" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_to_budget_child" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="budget_master" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-76">
        <addForeignKeyConstraint baseColumnNames="budget_master_parent_id" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_to_budget_master_parent" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="budget_master" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-77">
        <addForeignKeyConstraint baseColumnNames="created_by" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_to_users_createdby" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-78">
        <addForeignKeyConstraint baseColumnNames="deleted_by" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_to_users_deletedby" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-79">
        <addForeignKeyConstraint baseColumnNames="updated_by" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_to_users_updatedby" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-80">
        <addForeignKeyConstraint baseColumnNames="doc_id" baseTableName="invoice_tat_report" constraintName="fk_invoice_tat_report_document_approval_container" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="document_approval_container" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-81">
        <addForeignKeyConstraint baseColumnNames="users" baseTableName="budget_mapping_master" constraintName="fkbngnk17pg2pjlc72u62avmeyt" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-82">
        <addForeignKeyConstraint baseColumnNames="po_type_id" baseTableName="purchase_order_header" constraintName="fkbqda979ot32o71niw9brj3p6q" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="lookup_data" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-83">
        <addForeignKeyConstraint baseColumnNames="users" baseTableName="budget_structure_master" constraintName="fki6s81xrqu3cu21g1fm1mwv5ve" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-84">
        <addForeignKeyConstraint baseColumnNames="users" baseTableName="budget" constraintName="fkj0kkkheewt0bb8w5ox0wwiyno" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-85">
        <addForeignKeyConstraint baseColumnNames="widget_id" baseTableName="dashboard_configuration" constraintName="fkj3nor7gd8p4te655lxm2rolwi" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="widget" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-86">
        <addForeignKeyConstraint baseColumnNames="users" baseTableName="budget_master" constraintName="fkrpn752kdbex6cuf7xrs9npqa4" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-87">
        <addForeignKeyConstraint baseColumnNames="budget_mapping_master_id" baseTableName="budget" constraintName="fktdo3risaflubrmuvxn00vapbn" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="budget_mapping_master" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-89">
        <dropUniqueConstraint constraintName="uk_qcx5brd6bpje9yp2u9hmyb2x9" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-90">
        <dropColumn columnName="disatch_address_1" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-91">
        <dropColumn columnName="disatch_address_2" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-92">
        <dropColumn columnName="disatch_email" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-93">
        <dropColumn columnName="disatch_location" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-94">
        <dropColumn columnName="disatch_name" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-95">
        <dropColumn columnName="disatch_phone_number" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-96">
        <dropColumn columnName="disatch_pin" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-97">
        <dropColumn columnName="disatch_state_code" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-98">
        <dropSequence sequenceName="additional_details_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-99">
        <dropSequence sequenceName="additional_supporting_documen_additional_supporting_documen_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-100">
        <dropSequence sequenceName="approval_definition_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-101">
        <dropSequence sequenceName="approval_delegation_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-102">
        <dropSequence sequenceName="approver_audit_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-103">
        <dropSequence sequenceName="approver_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-104">
        <dropSequence sequenceName="budget_document_action_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-105">
        <dropSequence sequenceName="budget_frequency_mapping_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-106">
        <dropSequence sequenceName="budget_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-107">
        <dropSequence sequenceName="budget_mapping_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-108">
        <dropSequence sequenceName="budget_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-109">
        <dropSequence sequenceName="budget_structure_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-110">
        <dropSequence sequenceName="budget_sync_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-111">
        <dropSequence sequenceName="company_company_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-112">
        <dropSequence sequenceName="company_handshake_company_handshake_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-113">
        <dropSequence sequenceName="company_master_company_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-114">
        <dropSequence sequenceName="contact_person_contact_person_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-115">
        <dropSequence sequenceName="countries_country_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-116">
        <dropSequence sequenceName="dashboard_configuration_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-117">
        <dropSequence sequenceName="document_approval_container_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-118">
        <dropSequence sequenceName="document_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-119">
        <dropSequence sequenceName="document_identifier_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-120">
        <dropSequence sequenceName="document_metadata_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-121">
        <dropSequence sequenceName="document_rule_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-122">
        <dropSequence sequenceName="document_storage_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-123">
        <dropSequence sequenceName="document_subgroup_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-124">
        <dropSequence sequenceName="entitlement_details_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-125">
        <dropSequence sequenceName="excel_history_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-126">
        <dropSequence sequenceName="expense_vouchers_report_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-127">
        <dropSequence sequenceName="gl_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-128">
        <dropSequence sequenceName="gst_state_codes_gst_state_code_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-129">
        <dropSequence sequenceName="gstin_detail_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-130">
        <dropSequence sequenceName="invoice_header_invoice_header_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-131">
        <dropSequence sequenceName="invoice_item_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-132">
        <dropSequence sequenceName="invoice_upload_invoice_received_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-133">
        <dropSequence sequenceName="item_details_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-134">
        <dropSequence sequenceName="item_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-135">
        <dropSequence sequenceName="line_items_report_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-136">
        <dropSequence sequenceName="location_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-137">
        <dropSequence sequenceName="lookup_data_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-138">
        <dropSequence sequenceName="master_data_json_store_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-139">
        <dropSequence sequenceName="message_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-140">
        <dropSequence sequenceName="metadata_budget_structure_mapping_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-141">
        <dropSequence sequenceName="metadata_limit_rule_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-142">
        <dropSequence sequenceName="pan_detail_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-143">
        <dropSequence sequenceName="payment_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-144">
        <dropSequence sequenceName="ports_port_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-145">
        <dropSequence sequenceName="purchase_order_header_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-146">
        <dropSequence sequenceName="purchase_order_item_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-147">
        <dropSequence sequenceName="purchase_request_header_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-148">
        <dropSequence sequenceName="purchase_request_item_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-149">
        <dropSequence sequenceName="report_state_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-150">
        <dropSequence sequenceName="segment_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-151">
        <dropSequence sequenceName="supporting_document_categorie_supporting_document_category__seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-152">
        <dropSequence sequenceName="tat_report_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-153">
        <dropSequence sequenceName="user_company_mapping_user_company_mapping_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-154">
        <dropSequence sequenceName="widget_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-1">
        <addNotNullConstraint columnDataType="boolean" columnName="acknowledged_by_erp" tableName="document_approval_container" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-2">
        <addNotNullConstraint columnDataType="bigint" columnName="company_code" tableName="document_metadata" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-3">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="current_step" tableName="budget_structure_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-4">
        <modifyDataType columnName="description" newDataType="varchar(1000)" tableName="purchase_request_item"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-6">
        <addNotNullConstraint columnDataType="boolean" columnName="has_preceding_document" tableName="document_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-7">
        <modifyDataType columnName="invoice_received_id" newDataType="bigint" tableName="invoice_upload"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-8">
        <modifyDataType columnName="invoice_upload_id" newDataType="bigint" tableName="additional_supporting_documents_to_invoice"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-9">
        <dropNotNullConstraint columnDataType="boolean" columnName="is_active" tableName="budget"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-10">
        <addDefaultValue columnDataType="boolean" columnName="is_active" defaultValueBoolean="true" tableName="budget"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-11">
        <dropNotNullConstraint columnDataType="boolean" columnName="is_active" tableName="budget_frequency_mapping"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-12">
        <addDefaultValue columnDataType="boolean" columnName="is_active" defaultValueBoolean="true" tableName="budget_frequency_mapping"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-13">
        <dropNotNullConstraint columnDataType="boolean" columnName="is_active" tableName="budget_mapping_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-14">
        <addDefaultValue columnDataType="boolean" columnName="is_active" defaultValueBoolean="true" tableName="budget_mapping_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-15">
        <dropNotNullConstraint columnDataType="boolean" columnName="is_active" tableName="budget_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-16">
        <addDefaultValue columnDataType="boolean" columnName="is_active" defaultValueBoolean="true" tableName="budget_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-17">
        <dropNotNullConstraint columnDataType="boolean" columnName="is_active" tableName="budget_structure_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-18">
        <addDefaultValue columnDataType="boolean" columnName="is_active" defaultValueBoolean="true" tableName="budget_structure_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-19">
        <dropNotNullConstraint columnDataType="boolean" columnName="is_active" tableName="budget_sync_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-20">
        <addDefaultValue columnDataType="boolean" columnName="is_active" defaultValueBoolean="true" tableName="budget_sync_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-21">
        <addDefaultValue columnDataType="boolean" columnName="is_approved" defaultValueBoolean="false" tableName="budget_structure_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-22">
        <modifyDataType columnName="name" newDataType="varchar(100)" tableName="purchase_request_item"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737951016571-23">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="status" tableName="budget_structure_master"/>
    </changeSet>
</databaseChangeLog>
