<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="vedantdube (generated)" id="1738242189909-14">
        <addColumn tableName="predefined_ageing">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-13">
        <addUniqueConstraint columnNames="approver_type, ageing" constraintName="predefined_ageing_unique" tableName="predefined_ageing"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-15">
        <addColumn tableName="report_state">
            <column name="approval_match_value" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-16">
        <addColumn tableName="report_state">
            <column name="approval_matcher" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-17">
        <addColumn tableName="report_state">
            <column name="approval_title" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-1">
        <modifyDataType columnName="additional_supporting_document_id" newDataType="int" tableName="additional_supporting_documents_to_invoice"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-2">
        <modifyDataType columnName="approver_type" newDataType="varchar" tableName="invoice_tat_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-3">
        <addDefaultValue columnDataType="timestamp" columnName="created_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-5">
        <dropNotNullConstraint columnDataType="varchar(100)" columnName="key" tableName="invoice_tat_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-6">
        <modifyDataType columnName="supporting_document_category_id" newDataType="int" tableName="supporting_document_categories"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-7">
        <addNotNullConstraint columnDataType="int" columnName="tat_time_days" tableName="invoice_tat_report" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-8">
        <dropNotNullConstraint columnDataType="clob" columnName="title" tableName="invoice_tat_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-9">
        <addDefaultValue columnDataType="timestamp" columnName="updated_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-10">
        <dropNotNullConstraint columnDataType="clob" columnName="value" tableName="invoice_tat_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-11">
        <dropPrimaryKey tableName="predefined_ageing"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738242189909-12">
        <addPrimaryKey columnNames="id" constraintName="predefined_ageing_pkey" tableName="predefined_ageing"/>
    </changeSet>
</databaseChangeLog>
