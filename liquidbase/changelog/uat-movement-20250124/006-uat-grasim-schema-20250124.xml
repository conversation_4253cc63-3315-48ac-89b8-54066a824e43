<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1737964469568-94">
        <dropForeignKeyConstraint baseTableName="invoice_header" constraintName="fkgmw3v04u534aq80o40bsf366s"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-24">
        <createTable tableName="master_data_store">
            <column name="master_data_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="company_code" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="body" type="JSONB"/>
            <column name="description" type="VARCHAR(255)"/>
            <column name="created_timestamp" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="creating_user_id" type="BIGINT"/>
            <column name="updated_timestamp" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="updating_user_id" type="BIGINT"/>
            <column autoIncrement="true" name="id" startWith="9" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-25">
        <createTable tableName="budget_sync_mapping">
            <column autoIncrement="true" name="id" startWith="8" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="budget_sync_mapping_pkey"/>
            </column>
            <column name="budget_sync_parent_id" type="BIGINT"/>
            <column name="budget_sync_child_id" type="BIGINT"/>
            <column name="budget_master_parent_id" type="BIGINT"/>
            <column name="budget_master_child_id" type="BIGINT"/>
            <column defaultValueBoolean="true" name="is_active" type="BOOLEAN"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="deleted_by" type="BIGINT"/>
            <column defaultValueComputed="now()" name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-26">
        <createTable tableName="audit_logs">
            <column autoIncrement="true" name="id" startWith="2791" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_logs_pkey"/>
            </column>
            <column name="timestamp" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="entity_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="entity_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="transaction_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="operation" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="before_state" type="JSONB"/>
            <column name="after_state" type="JSONB"/>
            <column name="api_callee" type="VARCHAR(255)"/>
            <column name="api_url" type="VARCHAR"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-27">
        <createTable tableName="ratio_category_config">
            <column autoIncrement="true" name="id" startWith="100" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ratio_category_config_pkey"/>
            </column>
            <column name="master_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="company_code" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-28">
        <createTable tableName="ratio_category_master">
            <column autoIncrement="true" name="id" startWith="100" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ratio_category_master_pkey"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="ratio" type="FLOAT8"/>
            <column name="master_json" type="JSONB"/>
            <column name="company_code" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="uuid" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-29">
        <createTable tableName="mapping_segment_ratio_to_entity">
            <column autoIncrement="true" name="id" startWith="58" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="mapping_segment_ratio_to_entity_pkey"/>
            </column>
            <column name="entity_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="document_type_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="ratio_distribution_json" type="JSONB"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-30">
        <createTable tableName="pdf_templates">
            <column autoIncrement="true" name="id" startWith="4" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pdf_templates_pk"/>
            </column>
            <column name="type" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="pdf_template_file" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="mapping_json" type="JSONB"/>
            <column name="description" type="TEXT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>

    <changeSet author="vedantdube (generated)" id="1737964469568-32">
        <createIndex indexName="budget_mapping_master_unique_partial" tableName="budget_mapping_master" unique="true">
            <column name="budget_structure_master_id"/>
            <column name="budget_master_id"/>
            <column name="company_code"/>
            <column name="parent_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-33">
        <createIndex indexName="idx_audit_logs_timestamp" tableName="audit_logs">
            <column name="timestamp"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-34">
        <createIndex indexName="idx_audit_logs_user_id" tableName="audit_logs">
            <column name="user_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-35">
        <createIndex indexName="idx_audit_logs_transaction_id" tableName="audit_logs">
            <column name="transaction_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-36">
        <createIndex indexName="idx_audit_logs_entity" tableName="audit_logs">
            <column name="entity_name"/>
            <column name="entity_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-37">
        <createTable tableName="invoice_tat_report">
            <column autoIncrement="true" name="id" startWith="46" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="invoice_tat_report_pkey"/>
            </column>
            <column name="approver" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="now()" name="entry_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="tat_time_days" type="INTEGER"/>
            <column name="status" type="VARCHAR(50)"/>
            <column name="doc_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="key" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="title" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="value" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="approver_type" type="APPROVER_TYPE_ENUM">
                <constraints nullable="false"/>
            </column>
            <column name="metadata" type="JSONB"/>
            <column name="document_date" type="date"/>
            <column name="company_code" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-38">
        <createTable tableName="predefined_ageing">
            <column name="approver_type" type="APPROVER_TYPE_ENUM">
                <constraints nullable="false" primaryKey="true" primaryKeyName="predefined_ageing_pkey"/>
            </column>
            <column name="ageing" type="VARCHAR(20)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="predefined_ageing_pkey"/>
            </column>
            <column name="indicator" type="VARCHAR(7)">
                <constraints nullable="false"/>
            </column>
            <column name="sort_order" type="INTEGER">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-39">
        <addUniqueConstraint columnNames="type" constraintName="pdf_templates_type_key" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-40">
        <createTable tableName="adm_user_details_temp">
            <column name="ud_user_id" type="INTEGER"/>
            <column name="ud_user_name" type="TEXT"/>
            <column name="ud_pwd" type="TEXT"/>
            <column name="ud_first_name" type="TEXT"/>
            <column name="ud_last_name" type="TEXT"/>
            <column name="ud_email_id" type="TEXT"/>
            <column name="ud_mobile_no" type="TEXT"/>
            <column name="ud_entity_id" type="INTEGER"/>
            <column name="ud_et_entity_type_id" type="INTEGER"/>
            <column name="ud_user_status" type="TEXT"/>
            <column name="ud_activity_status" type="TEXT"/>
            <column name="ud_designation" type="TEXT"/>
            <column name="ud_branch_code" type="TEXT"/>
            <column name="ud_state_code" type="TEXT"/>
            <column name="ud_otp" type="TEXT"/>
            <column name="ud_change_pwd_flag" type="TEXT"/>
            <column name="ud_last_pwd" type="TEXT"/>
            <column name="ud_last_pwd_change_tm" type="TEXT"/>
            <column name="ud_activity_status_tm" type="TEXT"/>
            <column name="ud_last_login_tm" type="TEXT"/>
            <column name="ud_email_verification" type="TEXT"/>
            <column name="ud_gcm_id" type="TEXT"/>
            <column name="ud_cr_uid" type="INTEGER"/>
            <column name="ud_cr_dt" type="TEXT"/>
            <column name="ud_upd_uid" type="INTEGER"/>
            <column name="ud_upd_dt" type="TEXT"/>
            <column name="ud_user_photo" type="TEXT"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-41">
        <createTable tableName="countries_copy">
            <column name="country_id" type="INTEGER"/>
            <column name="name" type="VARCHAR(52)"/>
            <column name="country_code" type="VARCHAR(2)"/>
            <column name="create_at" type="VARCHAR(19)"/>
            <column name="updated_at" type="VARCHAR(1)"/>
            <column name="deleted_at" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-42">
        <createTable tableName="item_master_abcdl_prod">
            <column name="product_id" type="INTEGER"/>
            <column name="companyID" type="INTEGER"/>
            <column name="subsidiary_id" type="INTEGER"/>
            <column name="item_id" type="VARCHAR(50)"/>
            <column name="item_name" type="VARCHAR(50)"/>
            <column name="HSN/SAC" type="INTEGER"/>
            <column name="description" type="VARCHAR(50)"/>
            <column name="rate" type="VARCHAR(50)"/>
            <column name="account" type="VARCHAR(50)"/>
            <column name="account_code" type="VARCHAR(50)"/>
            <column name="taxable" type="VARCHAR(50)"/>
            <column name="exemption_reason" type="VARCHAR(50)"/>
            <column name="product_type" type="VARCHAR(50)"/>
            <column name="intra_state_tax_name" type="VARCHAR(50)"/>
            <column name="intra_state_tax_rate" type="VARCHAR(50)"/>
            <column name="intra_state_tax_type" type="VARCHAR(50)"/>
            <column name="inter_state_tax_name" type="VARCHAR(50)"/>
            <column name="inter_state_tax_rate" type="VARCHAR(50)"/>
            <column name="inter_state_tax_type" type="VARCHAR(50)"/>
            <column name="source" type="VARCHAR(50)"/>
            <column name="reference_id" type="VARCHAR(50)"/>
            <column name="last_sync_time" type="VARCHAR(50)"/>
            <column name="status" type="VARCHAR(50)"/>
            <column name="usage_unit" type="VARCHAR(50)"/>
            <column name="purchase_rate" type="VARCHAR(50)"/>
            <column name="purchase_account" type="VARCHAR(50)"/>
            <column name="purchase_account_code" type="VARCHAR(50)"/>
            <column name="purchase_description" type="VARCHAR(50)"/>
            <column name="inventory_account" type="VARCHAR(50)"/>
            <column name="inventory_account_code" type="VARCHAR(50)"/>
            <column name="reorder_point" type="VARCHAR(50)"/>
            <column name="vendor" type="VARCHAR(50)"/>
            <column name="initial_stock" type="VARCHAR(50)"/>
            <column name="initial_stock_rate" type="VARCHAR(50)"/>
            <column name="stock_on_hand" type="VARCHAR(50)"/>
            <column name="item_type" type="VARCHAR(50)"/>
            <column name="function" type="VARCHAR(50)"/>
            <column name="territory" type="VARCHAR(50)"/>
            <column name="division" type="VARCHAR(50)"/>
            <column name="customer_segment" type="VARCHAR(50)"/>
            <column name="channel" type="VARCHAR(50)"/>
            <column name="cf_alias_name" type="VARCHAR(50)"/>
            <column name="cf_item_id" type="VARCHAR(50)"/>
            <column name="cf_product_view" type="VARCHAR(50)"/>
            <column name="currencyCode" type="VARCHAR(50)"/>
            <column name="createdTimeStamp" type="VARCHAR(50)"/>
            <column name="budgetCode" type="VARCHAR(50)"/>
            <column name="expenses_head_type" type="VARCHAR(50)"/>
            <column name="asset_age" type="VARCHAR(50)"/>
            <column name="depart_budget_amt" type="VARCHAR(50)"/>
            <column name="branch_allocated_budget" type="VARCHAR(50)"/>
            <column name="total_balance_amt" type="VARCHAR(50)"/>
            <column name="total_asset_amt_without_tax" type="VARCHAR(50)"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-43">
        <createTable tableName="users_backup">
            <column name="id" type="BIGINT"/>
            <column name="activity_status" type="VARCHAR(255)"/>
            <column name="activity_status_time" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="branch_code" type="VARCHAR(255)"/>
            <column name="chnage_pwd_flag" type="BOOLEAN"/>
            <column name="created_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="deleted_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="designation" type="VARCHAR(255)"/>
            <column name="email_id" type="VARCHAR(255)"/>
            <column name="first_name" type="VARCHAR(255)"/>
            <column name="is_email_verified" type="BOOLEAN"/>
            <column name="last_name" type="VARCHAR(255)"/>
            <column name="last_password" type="VARCHAR(255)"/>
            <column name="last_password_change_time" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="mobile_no" type="VARCHAR(255)"/>
            <column name="otp" type="VARCHAR(255)"/>
            <column name="pwd" type="VARCHAR(255)"/>
            <column name="state_code" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="user_id" type="INTEGER"/>
            <column name="user_name" type="VARCHAR(255)"/>
            <column name="user_photo" type="VARCHAR(255)"/>
            <column name="user_status" type="BOOLEAN"/>
            <column name="company_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-44">
        <createTable tableName="vendor_master">
            <column name="vm_seller_id" type="INTEGER"/>
            <column name="vm_supplier_fn" type="TEXT"/>
            <column name="vm_supplier_ln" type="TEXT"/>
            <column name="vm_supplier_code" type="INTEGER"/>
            <column name="vm_supplier_legal_name" type="TEXT"/>
            <column name="vm_supplier_company_name" type="TEXT"/>
            <column name="vm_supplier_pan" type="TEXT"/>
            <column name="vm_supplier_gstin" type="TEXT"/>
            <column name="vm_supplier_gstin_status" type="TEXT"/>
            <column name="vm_buyer_pan" type="TEXT"/>
            <column name="vm_parent_company_id" type="INTEGER"/>
            <column name="vm_buyer_fn" type="TEXT"/>
            <column name="vm_buyer_ln" type="TEXT"/>
            <column name="vm_buyer_contact_no" type="TEXT"/>
            <column name="vm_buyer_email_id" type="TEXT"/>
            <column name="vm_group_id" type="TEXT"/>
            <column name="vm_gst_cancellation_date" type="TEXT"/>
            <column name="vm_compliance_gstin_rating" type="TEXT"/>
            <column name="vm_itc_risk" type="TEXT"/>
            <column name="vm_msme_status" type="INTEGER"/>
            <column name="vm_e_invoice" type="TEXT"/>
            <column name="vm_hsn_mandatory" type="TEXT"/>
            <column name="vm_pan_aadhar_link" type="TEXT"/>
            <column name="vm_cin_no" type="TEXT"/>
            <column name="vm_cin_status" type="TEXT"/>
            <column name="vm_tan_no" type="TEXT"/>
            <column name="vm_supplier_portal_status" type="TEXT"/>
            <column name="vm_email_1" type="TEXT"/>
            <column name="vm_email_2" type="TEXT"/>
            <column name="vm_email_3" type="TEXT"/>
            <column name="vm_contact_no_1" type="TEXT"/>
            <column name="vm_contact_no_2" type="TEXT"/>
            <column name="vm_contact_no_3" type="TEXT"/>
            <column name="vm_supplier_addr_1" type="TEXT"/>
            <column name="vm_supplier_addr_2" type="TEXT"/>
            <column name="vm_zip_code" type="TEXT"/>
            <column name="vm_city" type="TEXT"/>
            <column name="vm_state" type="TEXT"/>
            <column name="vm_state_code" type="TEXT"/>
            <column name="vm_country" type="TEXT"/>
            <column name="vm_payment_term" type="TEXT"/>
            <column name="vm_acc_no" type="TEXT"/>
            <column name="vm_acc_name" type="TEXT"/>
            <column name="vm_ifsc_code" type="TEXT"/>
            <column name="vm_status" type="TEXT"/>
            <column name="vm_cr_uid" type="INTEGER"/>
            <column name="vm_cr_dt" type="TEXT"/>
            <column name="vm_upd_uid" type="INTEGER"/>
            <column name="vm_upd_dt" type="TEXT"/>
            <column name="vm_gstr1_filing_month" type="TEXT"/>
            <column name="vm_gstr1_filing_date" type="TEXT"/>
            <column name="vm_gstr1_due_date" type="TEXT"/>
            <column name="vm_gstr1_diff_date" type="TEXT"/>
            <column name="vm_gstr3b_filing_month" type="TEXT"/>
            <column name="vm_gstr3b_filing_date" type="TEXT"/>
            <column name="vm_gstr3b_due_date" type="TEXT"/>
            <column name="vm_gstr3b_diff_date" type="TEXT"/>
            <column name="vm_taxpayertype" type="TEXT"/>
            <column name="vm_gstr1_rating" type="TEXT"/>
            <column name="vm_gstr3b_rating" type="TEXT"/>
            <column name="vm_comp_code" type="INTEGER"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-45">
        <addColumn tableName="document_storage">
            <column name="creating_by" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-46">
        <addColumn tableName="purchase_request_item">
            <column name="hsn_sac_code" type="VARCHAR(8)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-47">
        <addColumn tableName="budget_document_action">
            <column name="consumed_amount" type="numeric(38, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-48">
        <addColumn tableName="budget_document_action">
            <column name="doc_no" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-49">
        <addColumn tableName="budget_master">
            <column name="users" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-50">
        <addColumn tableName="budget_structure_master">
            <column name="users" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-51">
        <addColumn tableName="budget_mapping_master">
            <column name="users" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-52">
        <addColumn tableName="budget">
            <column name="unallocated" type="numeric(38, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-53">
        <addColumn tableName="pan_detail">
            <column name="terms_and_conditions" type="TEXT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-54">
        <addColumn tableName="budget_frequency_mapping">
            <column name="unallocated" type="numeric(38, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-55">
        <addColumn tableName="budget_structure_master">
            <column name="metadata_id" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-56">
        <addColumn tableName="purchase_request_item">
            <column name="unit_of_measure_id" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-57">
        <addColumn tableName="budget_frequency_mapping">
            <column name="users" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-58">
        <addColumn tableName="budget_sync_master">
            <column name="users" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-59">
        <addColumn tableName="budget">
            <column name="users" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-60">
        <addColumn tableName="budget_frequency_mapping">
            <column name="allocated" type="numeric(38, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-61">
        <addColumn tableName="budget">
            <column name="budget_mapping_master_id" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-62">
        <addColumn tableName="budget">
            <column name="document_id" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-63">
        <addColumn tableName="purchase_request_item">
            <column name="hsn_or_sac" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-64">
        <addColumn tableName="budget">
            <column name="allocated" type="numeric(38, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-65">
        <addColumn tableName="users">
            <column name="company_id" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-66">
        <addColumn tableName="purchase_order_item">
            <column name="hsn_or_sac" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-67">
        <addColumn tableName="item_master">
            <column name="suppiler_item_code" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-68">
        <addColumn tableName="item_master">
            <column name="suppiler_rate" type="numeric(38, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-69">
        <addColumn tableName="item_master">
            <column name="suppiler_name" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-70">
        <addColumn tableName="item_master">
            <column name="suppiler_quantity" type="FLOAT8"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-71">
        <addColumn tableName="invoice_item">
            <column name="hsn_or_sac" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-72">
        <addColumn tableName="purchase_order_header">
            <column name="po_type_id" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-73">
        <addColumn tableName="document">
            <column name="invoice_number" type="VARCHAR(50)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-74">
        <addPrimaryKey columnNames="company_code, code, master_data_type" constraintName="master_data_store_pkey" tableName="master_data_store"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-75">
        <addUniqueConstraint columnNames="request_acceptance_status" constraintName="uk_kao0gbwv8bkkgdv9tgmkf0p0s" tableName="company_handshake"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-76">
        <addForeignKeyConstraint baseColumnNames="users" baseTableName="budget_frequency_mapping" constraintName="fk68kayfre88astwu82gmxlahek" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-77">
        <addForeignKeyConstraint baseColumnNames="document_id" baseTableName="budget" constraintName="fk833ha1r5j126ma3rr1b56ryw5" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="document" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-78">
        <addForeignKeyConstraint baseColumnNames="users" baseTableName="budget_sync_master" constraintName="fk9f3osrbgxkj4tsy4yiqnqbmg7" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-79">
        <addForeignKeyConstraint baseColumnNames="budget_sync_child_id" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_mapping_to_sync_master_child" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="budget_sync_master" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-80">
        <addForeignKeyConstraint baseColumnNames="budget_sync_parent_id" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_mapping_to_sync_master_parent" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="budget_sync_master" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-81">
        <addForeignKeyConstraint baseColumnNames="budget_master_child_id" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_to_budget_child" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="budget_master" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-82">
        <addForeignKeyConstraint baseColumnNames="budget_master_parent_id" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_to_budget_master_parent" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="budget_master" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-83">
        <addForeignKeyConstraint baseColumnNames="created_by" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_to_users_createdby" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-84">
        <addForeignKeyConstraint baseColumnNames="deleted_by" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_to_users_deletedby" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-85">
        <addForeignKeyConstraint baseColumnNames="updated_by" baseTableName="budget_sync_mapping" constraintName="fk_budget_sync_to_users_updatedby" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-86">
        <addForeignKeyConstraint baseColumnNames="doc_id" baseTableName="invoice_tat_report" constraintName="fk_invoice_tat_report_document_approval_container" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="document_approval_container" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-87">
        <addForeignKeyConstraint baseColumnNames="users" baseTableName="budget_mapping_master" constraintName="fkbngnk17pg2pjlc72u62avmeyt" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-88">
        <addForeignKeyConstraint baseColumnNames="po_type_id" baseTableName="purchase_order_header" constraintName="fkbqda979ot32o71niw9brj3p6q" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="lookup_data" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-89">
        <addForeignKeyConstraint baseColumnNames="users" baseTableName="budget_structure_master" constraintName="fki6s81xrqu3cu21g1fm1mwv5ve" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-90">
        <addForeignKeyConstraint baseColumnNames="users" baseTableName="budget" constraintName="fkj0kkkheewt0bb8w5ox0wwiyno" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-91">
        <addForeignKeyConstraint baseColumnNames="widget_id" baseTableName="dashboard_configuration" constraintName="fkj3nor7gd8p4te655lxm2rolwi" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="widget" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-92">
        <addForeignKeyConstraint baseColumnNames="users" baseTableName="budget_master" constraintName="fkrpn752kdbex6cuf7xrs9npqa4" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-93">
        <addForeignKeyConstraint baseColumnNames="budget_mapping_master_id" baseTableName="budget" constraintName="fktdo3risaflubrmuvxn00vapbn" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="budget_mapping_master" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-95">
        <dropUniqueConstraint constraintName="uk_qcx5brd6bpje9yp2u9hmyb2x9" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-96">
        <dropTable tableName="gl_code_master_bkp"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-97">
        <dropColumn columnName="disatch_address_1" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-98">
        <dropColumn columnName="disatch_address_2" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-99">
        <dropColumn columnName="disatch_email" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-100">
        <dropColumn columnName="disatch_location" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-101">
        <dropColumn columnName="disatch_name" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-102">
        <dropColumn columnName="disatch_phone_number" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-103">
        <dropColumn columnName="disatch_pin" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-104">
        <dropColumn columnName="disatch_state_code" tableName="invoice_header"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-105">
        <dropSequence sequenceName="additional_details_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-106">
        <dropSequence sequenceName="additional_supporting_documen_additional_supporting_documen_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-107">
        <dropSequence sequenceName="approval_definition_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-108">
        <dropSequence sequenceName="approval_delegation_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-109">
        <dropSequence sequenceName="approver_audit_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-110">
        <dropSequence sequenceName="approver_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-111">
        <dropSequence sequenceName="budget_document_action_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-112">
        <dropSequence sequenceName="budget_frequency_mapping_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-113">
        <dropSequence sequenceName="budget_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-114">
        <dropSequence sequenceName="budget_mapping_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-115">
        <dropSequence sequenceName="budget_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-116">
        <dropSequence sequenceName="budget_structure_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-117">
        <dropSequence sequenceName="budget_sync_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-118">
        <dropSequence sequenceName="company_company_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-119">
        <dropSequence sequenceName="company_handshake_company_handshake_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-120">
        <dropSequence sequenceName="company_master_company_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-121">
        <dropSequence sequenceName="contact_person_contact_person_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-122">
        <dropSequence sequenceName="countries_country_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-123">
        <dropSequence sequenceName="dashboard_configuration_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-124">
        <dropSequence sequenceName="document_approval_container_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-125">
        <dropSequence sequenceName="document_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-126">
        <dropSequence sequenceName="document_identifier_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-127">
        <dropSequence sequenceName="document_metadata_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-128">
        <dropSequence sequenceName="document_rule_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-129">
        <dropSequence sequenceName="document_storage_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-130">
        <dropSequence sequenceName="document_subgroup_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-131">
        <dropSequence sequenceName="entitlement_details_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-132">
        <dropSequence sequenceName="excel_history_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-133">
        <dropSequence sequenceName="expense_vouchers_report_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-134">
        <dropSequence sequenceName="gl_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-135">
        <dropSequence sequenceName="gst_state_codes_gst_state_code_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-136">
        <dropSequence sequenceName="gstin_detail_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-137">
        <dropSequence sequenceName="invoice_header_invoice_header_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-138">
        <dropSequence sequenceName="invoice_item_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-139">
        <dropSequence sequenceName="invoice_upload_invoice_received_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-140">
        <dropSequence sequenceName="item_details_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-141">
        <dropSequence sequenceName="item_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-142">
        <dropSequence sequenceName="line_items_report_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-143">
        <dropSequence sequenceName="location_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-144">
        <dropSequence sequenceName="lookup_data_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-145">
        <dropSequence sequenceName="master_data_json_store_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-146">
        <dropSequence sequenceName="message_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-147">
        <dropSequence sequenceName="metadata_budget_structure_mapping_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-148">
        <dropSequence sequenceName="metadata_limit_rule_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-149">
        <dropSequence sequenceName="pan_detail_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-150">
        <dropSequence sequenceName="payment_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-151">
        <dropSequence sequenceName="ports_port_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-152">
        <dropSequence sequenceName="purchase_order_header_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-153">
        <dropSequence sequenceName="purchase_order_item_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-154">
        <dropSequence sequenceName="purchase_request_header_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-155">
        <dropSequence sequenceName="purchase_request_item_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-156">
        <dropSequence sequenceName="report_state_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-157">
        <dropSequence sequenceName="segment_master_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-158">
        <dropSequence sequenceName="supporting_document_categorie_supporting_document_category__seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-159">
        <dropSequence sequenceName="tat_report_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-160">
        <dropSequence sequenceName="user_company_mapping_user_company_mapping_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-161">
        <dropSequence sequenceName="widget_id_seq"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-1">
        <addNotNullConstraint columnDataType="boolean" columnName="acknowledged_by_erp" tableName="document_approval_container" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-2">
        <addNotNullConstraint columnDataType="bigint" columnName="company_code" tableName="document_metadata" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-3">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="current_step" tableName="budget_structure_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-4">
        <modifyDataType columnName="description" newDataType="varchar(1000)" tableName="purchase_request_item"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-6">
        <addNotNullConstraint columnDataType="boolean" columnName="has_preceding_document" tableName="document_subgroup" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-7">
        <modifyDataType columnName="invoice_received_id" newDataType="bigint" tableName="invoice_upload"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-8">
        <modifyDataType columnName="invoice_upload_id" newDataType="bigint" tableName="additional_supporting_documents_to_invoice"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-9">
        <dropNotNullConstraint columnDataType="boolean" columnName="is_active" tableName="budget"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-10">
        <addDefaultValue columnDataType="boolean" columnName="is_active" defaultValueBoolean="true" tableName="budget"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-11">
        <dropNotNullConstraint columnDataType="boolean" columnName="is_active" tableName="budget_frequency_mapping"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-12">
        <addDefaultValue columnDataType="boolean" columnName="is_active" defaultValueBoolean="true" tableName="budget_frequency_mapping"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-13">
        <dropNotNullConstraint columnDataType="boolean" columnName="is_active" tableName="budget_mapping_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-14">
        <addDefaultValue columnDataType="boolean" columnName="is_active" defaultValueBoolean="true" tableName="budget_mapping_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-15">
        <dropNotNullConstraint columnDataType="boolean" columnName="is_active" tableName="budget_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-16">
        <addDefaultValue columnDataType="boolean" columnName="is_active" defaultValueBoolean="true" tableName="budget_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-17">
        <dropNotNullConstraint columnDataType="boolean" columnName="is_active" tableName="budget_structure_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-18">
        <addDefaultValue columnDataType="boolean" columnName="is_active" defaultValueBoolean="true" tableName="budget_structure_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-19">
        <dropNotNullConstraint columnDataType="boolean" columnName="is_active" tableName="budget_sync_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-20">
        <addDefaultValue columnDataType="boolean" columnName="is_active" defaultValueBoolean="true" tableName="budget_sync_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-21">
        <addDefaultValue columnDataType="boolean" columnName="is_approved" defaultValueBoolean="false" tableName="budget_structure_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-22">
        <modifyDataType columnName="name" newDataType="varchar(100)" tableName="purchase_request_item"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-23">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="status" tableName="budget_structure_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1737964469568-31">
        <addUniqueConstraint columnNames="document_id" constraintName="uk_ialgbn5v5lalbdccr6qrdq2vx" tableName="budget"/>
    </changeSet>
</databaseChangeLog>
