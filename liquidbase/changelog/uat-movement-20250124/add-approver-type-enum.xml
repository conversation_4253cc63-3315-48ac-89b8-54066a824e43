<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">

    <changeSet author="vedantdube" id="create-approver-type-enum">
        <sql>
            CREATE TYPE approver_type_enum AS ENUM ('APPROVER', 'UPLOADER', 'PAYER');
        </sql>
    </changeSet>

</databaseChangeLog>