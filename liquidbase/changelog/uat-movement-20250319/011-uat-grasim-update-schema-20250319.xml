<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1742377478749-6">
        <addColumn tableName="gstin_detail">
            <column defaultValueBoolean="false" name="authorise" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1742377478749-7">
        <addColumn tableName="gstin_detail">
            <column name="user_id" type="VARCHAR"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1742377478749-8">
        <addColumn tableName="document">
            <column name="requester_remark" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1742377478749-9">
        <addForeignKeyConstraint baseColumnNames="gl_master_id" baseTableName="invoice_item" constraintName="fkk5tbsxgtqp9a6mau1b7urq27g" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="gl_master" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1742377478749-1">
        <modifyDataType columnName="additional_supporting_document_id" newDataType="int" tableName="additional_supporting_documents_to_invoice"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1742377478749-2">
        <addDefaultValue columnDataType="timestamp" columnName="created_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1742377478749-4">
        <modifyDataType columnName="supporting_document_category_id" newDataType="int" tableName="supporting_document_categories"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1742377478749-5">
        <addDefaultValue columnDataType="timestamp" columnName="updated_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
</databaseChangeLog>
