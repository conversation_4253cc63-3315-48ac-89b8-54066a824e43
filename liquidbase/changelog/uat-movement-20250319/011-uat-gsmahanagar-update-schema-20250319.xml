<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1742378235195-4">
        <createTable tableName="invoice_sync_report">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="invoice_sync_report_pkey"/>
            </column>
            <column name="request_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="irn_count" type="INTEGER"/>
            <column name="gstin" type="VARCHAR(15)">
                <constraints nullable="false"/>
            </column>
            <column name="financial_year" type="VARCHAR(6)"/>
            <column name="message" type="TEXT"/>
            <column name="status" type="VARCHAR(20)"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1742378235195-5">
        <addColumn tableName="gstin_detail">
            <column defaultValueBoolean="false" name="authorise" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1742378235195-6">
        <addColumn tableName="gstin_detail">
            <column name="user_id" type="VARCHAR"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1742378235195-7">
        <addColumn tableName="document">
            <column name="requester_remark" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1742378235195-1">
        <addDefaultValue columnDataType="timestamp" columnName="created_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1742378235195-3">
        <addDefaultValue columnDataType="timestamp" columnName="updated_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
</databaseChangeLog>
