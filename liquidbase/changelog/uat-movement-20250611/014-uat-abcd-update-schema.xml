<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1749648919639-11">
        <createTable tableName="match_configuration_data">
            <column autoIncrement="true" name="id" startWith="7" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="match_configuration_data_pkey"/>
            </column>
            <column name="is_2_way_match" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="lookup_perform_match_at" type="INTEGER"/>
            <column name="criteria_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="criteria_value" type="VARCHAR(255)"/>
            <column defaultValueBoolean="false" name="is_tolerance_allowed" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="lookup_default_operation" type="INTEGER"/>
            <column defaultValueBoolean="false" name="is_percentage" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="company_code" type="BIGINT"/>
            <column name="created_timestamp" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="creating_user_id" type="BIGINT"/>
            <column name="updated_timestamp" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="updating_user_id" type="BIGINT"/>
            <column name="criteria_map_value" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-12">
        <createTable tableName="invoice_item_purchase_order_item_mapping">
            <column name="invoice_item_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_invoice_item_purchase_order_item_mapping"/>
            </column>
            <column name="purchase_order_item_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_invoice_item_purchase_order_item_mapping"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-13">
        <createTable tableName="invoice_item_erp_grn_mapping">
            <column name="invoice_item_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_invoice_item_erp_grn_mapping"/>
            </column>
            <column name="erp_grn_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_invoice_item_erp_grn_mapping"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-14">
        <createIndex indexName="idx_match_config_company_code" tableName="match_configuration_data">
            <column name="company_code"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-15">
        <createIndex indexName="idx_match_config_lookup_perform" tableName="match_configuration_data">
            <column name="lookup_perform_match_at"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-16">
        <createIndex indexName="idx_match_config_criteria_name" tableName="match_configuration_data">
            <column name="criteria_name"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-17">
        <createIndex indexName="idx_invoice_item_mapping_invoice_item_id" tableName="invoice_item_purchase_order_item_mapping">
            <column name="invoice_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-18">
        <createIndex indexName="idx_invoice_item_mapping_purchase_order_item_id" tableName="invoice_item_purchase_order_item_mapping">
            <column name="purchase_order_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-19">
        <createIndex indexName="idx_invoice_item_erp_grn_mapping_invoice_item_id" tableName="invoice_item_erp_grn_mapping">
            <column name="invoice_item_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-20">
        <createIndex indexName="idx_invoice_item_erp_grn_mapping_erp_grn_id" tableName="invoice_item_erp_grn_mapping">
            <column name="erp_grn_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-21">
        <addForeignKeyConstraint baseColumnNames="erp_grn_id" baseTableName="invoice_item_erp_grn_mapping" constraintName="fk_invoice_item_erp_grn_mapping_to_erp_grn" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="erp_grn" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-22">
        <addForeignKeyConstraint baseColumnNames="invoice_item_id" baseTableName="invoice_item_erp_grn_mapping" constraintName="fk_invoice_item_erp_grn_mapping_to_invoice_item" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="invoice_item" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-23">
        <addForeignKeyConstraint baseColumnNames="invoice_item_id" baseTableName="invoice_item_purchase_order_item_mapping" constraintName="fk_invoice_item_mapping_to_invoice_item" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="invoice_item" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-24">
        <addForeignKeyConstraint baseColumnNames="purchase_order_item_id" baseTableName="invoice_item_purchase_order_item_mapping" constraintName="fk_invoice_item_mapping_to_purchase_order_item" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="purchase_order_item" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-25">
        <addForeignKeyConstraint baseColumnNames="lookup_default_operation" baseTableName="match_configuration_data" constraintName="match_configuration_data_lookup_default_operation_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="lookup_data" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-26">
        <addForeignKeyConstraint baseColumnNames="lookup_perform_match_at" baseTableName="match_configuration_data" constraintName="match_configuration_data_lookup_perform_match_at_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="lookup_data" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-1">
        <modifyDataType columnName="additional_supporting_document_id" newDataType="int" tableName="additional_supporting_documents_to_invoice"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-3">
        <addDefaultValue columnDataType="timestamp" columnName="created_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="erp_grn"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-4">
        <addDefaultValue columnDataType="timestamp" columnName="created_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="erp_grn_item"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-5">
        <addDefaultValue columnDataType="timestamp" columnName="created_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-7">
        <modifyDataType columnName="supporting_document_category_id" newDataType="int" tableName="supporting_document_categories"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-8">
        <addDefaultValue columnDataType="timestamp" columnName="updated_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="erp_grn"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-9">
        <addDefaultValue columnDataType="timestamp" columnName="updated_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="erp_grn_item"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1749648919639-10">
        <addDefaultValue columnDataType="timestamp" columnName="updated_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
</databaseChangeLog>
