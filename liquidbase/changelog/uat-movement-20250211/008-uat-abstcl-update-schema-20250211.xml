<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1739294231182-4">
        <addColumn tableName="document_approval_container">
            <column name="status_remarks" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1739294231182-5">
        <addColumn tableName="invoice_header">
            <column name="roundoff_amount" type="numeric(38, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1739294231182-1">
        <addDefaultValue columnDataType="timestamp" columnName="created_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1739294231182-2">
        <modifyDataType columnName="gl" newDataType="varchar" tableName="gl_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1739294231182-3">
        <addDefaultValue columnDataType="timestamp" columnName="updated_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
</databaseChangeLog>
