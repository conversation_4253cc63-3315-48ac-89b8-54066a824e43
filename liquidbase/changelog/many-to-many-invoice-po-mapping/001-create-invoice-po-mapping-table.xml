<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" 
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" 
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd 
                                       http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd 
                                       http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    
    <!-- Create junction table for many-to-many relationship between invoice_item and purchase_order_item -->
    <changeSet author="system-generated" id="create-invoice-po-mapping-table-001">
        <createTable tableName="invoice_item_purchase_order_item_mapping">
            <column name="invoice_item_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="purchase_order_item_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    
    <!-- Add primary key constraint -->
    <changeSet author="system-generated" id="create-invoice-po-mapping-pk-002">
        <addPrimaryKey 
            tableName="invoice_item_purchase_order_item_mapping" 
            columnNames="invoice_item_id,purchase_order_item_id" 
            constraintName="pk_invoice_item_purchase_order_item_mapping"/>
    </changeSet>
    
    <!-- Add foreign key constraint to invoice_item -->
    <changeSet author="system-generated" id="create-invoice-po-mapping-fk-invoice-003">
        <addForeignKeyConstraint 
            baseTableName="invoice_item_purchase_order_item_mapping"
            baseColumnNames="invoice_item_id"
            constraintName="fk_invoice_item_mapping_to_invoice_item"
            referencedTableName="invoice_item"
            referencedColumnNames="id"
            onDelete="CASCADE"
            onUpdate="NO ACTION"/>
    </changeSet>
    
    <!-- Add foreign key constraint to purchase_order_item -->
    <changeSet author="system-generated" id="create-invoice-po-mapping-fk-po-004">
        <addForeignKeyConstraint 
            baseTableName="invoice_item_purchase_order_item_mapping"
            baseColumnNames="purchase_order_item_id"
            constraintName="fk_invoice_item_mapping_to_purchase_order_item"
            referencedTableName="purchase_order_item"
            referencedColumnNames="id"
            onDelete="CASCADE"
            onUpdate="NO ACTION"/>
    </changeSet>
    
    <!-- Migrate existing data from invoice_item.purchase_order_item_id to the junction table -->
    <changeSet author="system-generated" id="migrate-existing-po-relationships-005">
        <sql>
            INSERT INTO invoice_item_purchase_order_item_mapping (invoice_item_id, purchase_order_item_id)
            SELECT id, purchase_order_item_id 
            FROM invoice_item 
            WHERE purchase_order_item_id IS NOT NULL;
        </sql>
    </changeSet>
    
    <!-- Drop the old foreign key constraint from invoice_item to purchase_order_item -->
    <changeSet author="system-generated" id="drop-old-po-fk-constraint-006">
        <dropForeignKeyConstraint 
            baseTableName="invoice_item" 
            constraintName="fk_invoice_item_to_purchase_order_item"/>
    </changeSet>
    
    <!-- Drop the old purchase_order_item_id column from invoice_item -->
    <changeSet author="system-generated" id="drop-old-po-column-007">
        <dropColumn tableName="invoice_item" columnName="purchase_order_item_id"/>
    </changeSet>
    
</databaseChangeLog>
