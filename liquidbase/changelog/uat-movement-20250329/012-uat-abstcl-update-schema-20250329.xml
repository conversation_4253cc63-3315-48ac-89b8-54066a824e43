<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="Vedant Dube (generated)" id="1743228278590-3">
        <addColumn tableName="purchase_order_header">
            <column defaultValueNumeric="0" name="sync_attempt" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="Vedant Dube (generated)" id="1743228278590-4">
        <addColumn tableName="purchase_order_header">
            <column defaultValueNumeric="0" name="priority" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="Vedant Dube (generated)" id="1743228278590-1">
        <addDefaultValue columnDataType="timestamp" columnName="created_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="Vedant Dube (generated)" id="1743228278590-2">
        <addDefaultValue columnDataType="timestamp" columnName="updated_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
</databaseChangeLog>
