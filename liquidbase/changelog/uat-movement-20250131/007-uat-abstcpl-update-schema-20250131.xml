<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1738316724660-8">
        <createTable tableName="predefined_ageing">
            <column autoIncrement="true" name="id" startWith="15" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="predefined_ageing_pkey"/>
            </column>
            <column name="approver_type" type="APPROVER_TYPE_ENUM">
                <constraints nullable="false"/>
            </column>
            <column name="ageing" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="indicator" type="VARCHAR(7)">
                <constraints nullable="false"/>
            </column>
            <column name="sort_order" type="INTEGER">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738316724660-9">
        <addUniqueConstraint columnNames="approver_type, ageing" constraintName="predefined_ageing_unique" tableName="predefined_ageing"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738316724660-10">
        <addColumn tableName="document_storage">
            <column name="file_hash" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738316724660-11">
        <addColumn tableName="report_state">
            <column name="approval_match_value" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738316724660-12">
        <addColumn tableName="report_state">
            <column name="approval_matcher" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738316724660-13">
        <addColumn tableName="report_state">
            <column name="approval_title" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738316724660-1">
        <modifyDataType columnName="approver_type" newDataType="varchar" tableName="invoice_tat_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738316724660-2">
        <addDefaultValue columnDataType="timestamp" columnName="created_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738316724660-3">
        <dropNotNullConstraint columnDataType="varchar(100)" columnName="key" tableName="invoice_tat_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738316724660-4">
        <addNotNullConstraint columnDataType="int" columnName="tat_time_days" tableName="invoice_tat_report" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738316724660-5">
        <dropNotNullConstraint columnDataType="clob" columnName="title" tableName="invoice_tat_report"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738316724660-6">
        <addDefaultValue columnDataType="timestamp" columnName="updated_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1738316724660-7">
        <dropNotNullConstraint columnDataType="clob" columnName="value" tableName="invoice_tat_report"/>
    </changeSet>
</databaseChangeLog>
