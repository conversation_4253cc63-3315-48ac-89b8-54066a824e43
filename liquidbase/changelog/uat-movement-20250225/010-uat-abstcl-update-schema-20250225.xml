<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1740474810747-3">
        <addUniqueConstraint columnNames="company_code, document_type_prefix, year, month, current_index" constraintName="unique_document_identifier" tableName="document_identifier"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740474810747-4">
        <addColumn tableName="document_identifier">
            <column defaultValueNumeric="0" name="version" type="INTEGER">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740474810747-5">
        <addColumn tableName="purchase_request_item">
            <column name="is_catalog" type="BOOLEAN"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740474810747-6">
        <addColumn tableName="purchase_order_item">
            <column name="is_catalog" type="BOOLEAN"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740474810747-7">
        <addColumn tableName="document_subgroup">
            <column name="document_type_id" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740474810747-8">
        <addForeignKeyConstraint baseColumnNames="document_type_id" baseTableName="document_subgroup" constraintName="fk_document_subgroup_document_type" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="lookup_data" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740474810747-1">
        <addDefaultValue columnDataType="timestamp" columnName="created_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740474810747-2">
        <addDefaultValue columnDataType="timestamp" columnName="updated_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
</databaseChangeLog>
