<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1740475096338-6">
        <addUniqueConstraint columnNames="company_code, document_type_prefix, year, month, current_index" constraintName="unique_document_identifier" tableName="document_identifier"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740475096338-7">
        <addColumn tableName="document_identifier">
            <column defaultValueNumeric="0" name="version" type="INTEGER">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740475096338-8">
        <addColumn tableName="purchase_request_item">
            <column name="is_catalog" type="BOOLEAN"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740475096338-9">
        <addColumn tableName="purchase_order_item">
            <column name="is_catalog" type="BOOLEAN"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740475096338-10">
        <addForeignKeyConstraint baseColumnNames="document_type_id" baseTableName="document_subgroup" constraintName="fk_document_subgroup_document_type" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="lookup_data" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740475096338-1">
        <modifyDataType columnName="additional_supporting_document_id" newDataType="int" tableName="additional_supporting_documents_to_invoice"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740475096338-2">
        <addDefaultValue columnDataType="timestamp" columnName="created_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740475096338-4">
        <modifyDataType columnName="supporting_document_category_id" newDataType="int" tableName="supporting_document_categories"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1740475096338-5">
        <addDefaultValue columnDataType="timestamp" columnName="updated_at" defaultValueComputed="CURRENT_TIMESTAMP" tableName="pdf_templates"/>
    </changeSet>
</databaseChangeLog>
