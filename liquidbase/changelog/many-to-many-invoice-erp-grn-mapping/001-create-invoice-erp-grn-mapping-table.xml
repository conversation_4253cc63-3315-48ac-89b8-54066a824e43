<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" 
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" 
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd 
                                       http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd 
                                       http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    
    <!-- Create junction table for many-to-many relationship between invoice_item and erp_grn -->
    <changeSet author="system-generated" id="create-invoice-erp-grn-mapping-table-001">
        <createTable tableName="invoice_item_erp_grn_mapping">
            <column name="invoice_item_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="erp_grn_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    
    <!-- Add primary key constraint -->
    <changeSet author="system-generated" id="create-invoice-erp-grn-mapping-pk-002">
        <addPrimaryKey 
            tableName="invoice_item_erp_grn_mapping" 
            columnNames="invoice_item_id,erp_grn_id" 
            constraintName="pk_invoice_item_erp_grn_mapping"/>
    </changeSet>
    
    <!-- Add foreign key constraint to invoice_item -->
    <changeSet author="system-generated" id="create-invoice-erp-grn-mapping-fk-invoice-003">
        <addForeignKeyConstraint 
            baseTableName="invoice_item_erp_grn_mapping"
            baseColumnNames="invoice_item_id"
            constraintName="fk_invoice_item_erp_grn_mapping_to_invoice_item"
            referencedTableName="invoice_item"
            referencedColumnNames="id"
            onDelete="CASCADE"
            onUpdate="NO ACTION"/>
    </changeSet>
    
    <!-- Add foreign key constraint to erp_grn -->
    <changeSet author="system-generated" id="create-invoice-erp-grn-mapping-fk-grn-004">
        <addForeignKeyConstraint 
            baseTableName="invoice_item_erp_grn_mapping"
            baseColumnNames="erp_grn_id"
            constraintName="fk_invoice_item_erp_grn_mapping_to_erp_grn"
            referencedTableName="erp_grn"
            referencedColumnNames="id"
            onDelete="CASCADE"
            onUpdate="NO ACTION"/>
    </changeSet>
    
    <!-- Migrate existing data from invoice_item.erp_grn_id to the junction table -->
    <changeSet author="system-generated" id="migrate-existing-erp-grn-relationships-005">
        <sql>
            INSERT INTO invoice_item_erp_grn_mapping (invoice_item_id, erp_grn_id)
            SELECT id, erp_grn_id 
            FROM invoice_item 
            WHERE erp_grn_id IS NOT NULL;
        </sql>
    </changeSet>
    
    <!-- Drop the old foreign key constraint from invoice_item to erp_grn -->
    <changeSet author="system-generated" id="drop-old-erp-grn-fk-constraint-006">
        <dropForeignKeyConstraint 
            baseTableName="invoice_item" 
            constraintName="fk_invoice_item_to_erp_grn"/>
    </changeSet>
    
    <!-- Drop the old erp_grn_id column from invoice_item -->
    <changeSet author="system-generated" id="drop-old-erp-grn-column-007">
        <dropColumn tableName="invoice_item" columnName="erp_grn_id"/>
    </changeSet>
    
</databaseChangeLog>
