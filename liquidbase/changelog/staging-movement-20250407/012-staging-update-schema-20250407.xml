<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vedantdube (generated)" id="1743999227235-3">
        <createTable tableName="invoice_sync_report">
            <column autoIncrement="true" name="id" startWith="18" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="invoice_sync_report_pkey"/>
            </column>
            <column name="request_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="irn_count" type="INTEGER"/>
            <column name="gstin" type="VARCHAR(15)">
                <constraints nullable="false"/>
            </column>
            <column name="financial_year" type="VARCHAR(6)"/>
            <column name="message" type="TEXT"/>
            <column name="status" type="VARCHAR(20)"/>
            <column name="company_code" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="creating_user_id" type="INTEGER"/>
            <column name="created_timestamp" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="updating_user_id" type="INTEGER"/>
            <column name="updated_timestamp" type="TIMESTAMP WITH TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-4">
        <createTable tableName="mailroom">
            <column autoIncrement="true" name="id" startWith="192" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="mailroom_pkey"/>
            </column>
            <column name="json_data" type="JSONB"/>
            <column name="request_json" type="JSONB"/>
            <column name="irn" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(500)"/>
            <column name="document_date" type="date"/>
            <column name="total_amount" type="numeric(18, 2)"/>
            <column name="sync_date" type="date"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_timestamp" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_timestamp" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="creating_user_id" type="BIGINT"/>
            <column name="updating_user_id" type="BIGINT"/>
            <column name="type" type="VARCHAR(500)"/>
            <column name="company_code" type="BIGINT"/>
            <column name="supplier_name" type="VARCHAR"/>
            <column name="gstin_no" type="VARCHAR"/>
            <column name="active_status" type="VARCHAR"/>
            <column name="msme" type="BOOLEAN"/>
            <column name="e_invoice" type="BOOLEAN"/>
            <column defaultValueBoolean="false" name="consumed" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="supplier_code" type="VARCHAR"/>
            <column name="invoice_number" type="VARCHAR"/>
            <column name="invoice_type" type="VARCHAR"/>
        </createTable>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-5">
        <addColumn tableName="gstin_detail">
            <column name="gstin_type" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-6">
        <addColumn tableName="gstin_detail">
            <column defaultValueBoolean="false" name="authorise" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-7">
        <addColumn tableName="gstin_detail">
            <column name="user_id" type="VARCHAR"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-8">
        <addColumn tableName="invoice_upload">
            <column name="json_source" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-9">
        <addColumn tableName="purchase_order_header">
            <column defaultValueNumeric="0" name="sync_attempt" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-10">
        <addColumn tableName="purchase_order_header">
            <column defaultValueNumeric="0" name="priority" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-11">
        <addColumn tableName="purchase_order_header">
            <column name="po_date" type="date"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-12">
        <addColumn tableName="document">
            <column name="requester_remark" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-13">
        <addColumn tableName="document_approval_container">
            <column name="process_method" type="VARCHAR"/>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-14">
        <addColumn tableName="document_approval_container">
            <column defaultValueBoolean="false" name="is_on_behalf" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-15">
        <addForeignKeyConstraint baseColumnNames="creating_user_id" baseTableName="invoice_sync_report" constraintName="fk_invoice_sync_report_to_users" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-16">
        <dropUniqueConstraint constraintName="uk_oj0o2i7h5d24n4utt7c25yd26" tableName="budget_sync_master"/>
    </changeSet>
    <changeSet author="vedantdube (generated)" id="1743999227235-2">
        <modifyDataType columnName="erp_remarks" newDataType="varchar(500)" tableName="document_approval_container"/>
    </changeSet>
</databaseChangeLog>
