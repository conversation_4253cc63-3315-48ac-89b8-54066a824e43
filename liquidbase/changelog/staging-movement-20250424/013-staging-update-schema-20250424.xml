<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="vishalmunde (generated)" id="1745488973939-5">
        <addColumn tableName="predefined_ageing">
            <column name="lower_bound" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vishalmunde (generated)" id="1745488973939-6">
        <addColumn tableName="predefined_ageing">
            <column name="upper_bound" type="INTEGER"/>
        </addColumn>
    </changeSet>
    <changeSet author="vishalmunde (generated)" id="1745488973939-7">
        <addColumn tableName="purchase_order_item">
            <column name="gl_master_id" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet author="vishalmunde (generated)" id="1745488973939-8">
        <addColumn tableName="company">
            <column name="gstin_status_last_sync" type="date"/>
        </addColumn>
    </changeSet>
    <changeSet author="vishalmunde (generated)" id="1745488973939-9">
        <addColumn tableName="company">
            <column defaultValueBoolean="false" name="is_seller_blocked" type="BOOLEAN"/>
        </addColumn>
    </changeSet>
    <changeSet author="vishalmunde (generated)" id="1745488973939-10">
        <addForeignKeyConstraint baseColumnNames="state_code_id" baseTableName="invoice_header" constraintName="fk_state_code_id_to_gst_state_code" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="gst_state_code_id" referencedTableName="gst_state_codes" validate="true"/>
    </changeSet>
    <changeSet author="vishalmunde (generated)" id="1745488973939-11">
        <dropColumn columnName="state_code_id" tableName="purchase_order_header"/>
    </changeSet>
    <changeSet author="vishalmunde (generated)" id="1745488973939-1">
        <addDefaultValue columnDataType="timestamp" columnName="created_timestamp" defaultValueComputed="CURRENT_TIMESTAMP" tableName="mailroom"/>
    </changeSet>
    <changeSet author="vishalmunde (generated)" id="1745488973939-2">
        <addDefaultValue columnDataType="uuid" columnName="document_approval_code" defaultValueComputed="uuid_generate_v4()" tableName="document_approval_container"/>
    </changeSet>
    <changeSet author="vishalmunde (generated)" id="1745488973939-3">
        <modifyDataType columnName="state_code" newDataType="varchar(3)" tableName="gstin_detail"/>
    </changeSet>
    <changeSet author="vishalmunde (generated)" id="1745488973939-4">
        <addDefaultValue columnDataType="timestamp" columnName="updated_timestamp" defaultValueComputed="CURRENT_TIMESTAMP" tableName="mailroom"/>
    </changeSet>
</databaseChangeLog>
