sudo aws ecr get-login-password --region ap-south-1 | sudo docker login --username AWS --password-stdin 561014279291.dkr.ecr.ap-south-1.amazonaws.com && sudo docker container rm --force pay-expense-backend && sudo docker image rm 561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:latest && sudo docker container run --detach --publish 9002:8080 --env DB_URL='**************************************************************************************' --env DB_USERNAME=taxgenie --env DB_PASSWORD='TaXg3nIeu$er@2022' --env FILE_UPLOAD_LIMIT=10MB --env JWT_SECRET=1E223539-0D5B-4279-8F9A-B3B13BDC7A29-DA01E40D-6806-40FA-B280-44ACB4902845 --env PAY_EXPENSE_PRODUCT_ID=4 --env DB_DIALECT=org.hibernate.dialect.MySQL5InnoDBDialect --env DB_SHOW_SQL=false --env DB_DDL_AUTO=update --env CEM_URL='https://api-cem-payexpense-uat.abfldirect.com/api/v1' --env DB_ENGINE=innodb --env AWS_ENDPOINT_URL='s3://tg-payinvoice-bucket/taxgenie_payexpense/' --env AWS_REGION='ap-south-1' --env AWS_ACCESS_KEY='********************' --env AWS_SECRET_KEY='7sRQ0X1QES5ikyISVgaAsTXMkFAPetrwJ5kiMO0Q' --env AWS_BUCKET_NAME='tg-payinvoice-bucket' --env ACTIVE_PROFILE='ses' --env AWS_ROOT_DIRECTORY='taxgenie_payexpense' --env ALLOWED_ORIGINS='http://localhost:4200,http://***********:9002,http://***********:10002,https://payexpense-uat.abfldirect.com' --env PAY_EXPENSE_CUSTOMER_ID=12741 --env COMMUNICATION_ENGINE_URL=https://api-comeng-payexpense-uat.abfldirect.com --env SENDBACK_EMAIL_TEMPLATE_ID=pe-sentback-format --env APPROVAL_EMAIL_TEMPLATE_ID=pe-approval-format --env ACKNOWLEDGEMENT_EMAIL_TEMPLATE_ID=pe-acknowledgement-format --env PAY_EXPENSE_SENDER_EMAIL='<EMAIL>' --env SENDBACK_EMAIL_TEMPLATE_ID=pe-sentback-for-creator --env SUBMIT_TO_CREATOR_EMAIL_TEMPLATE_ID=pe-submit-for-creator --env SUBMIT_TO_APPROVER_EMAIL_TEMPLATE_ID=pe-submit-for-approver --env APPROVED_TO_CREATOR_EMAIL_TEMPLATE_ID=pe-approved-for-creator --env APPROVED_TO_NEXT_APPROVER_EMAIL_TEMPLATE_ID=pe-approved-for-approver --env POSTED_EMAIL_TEMPLATE_ID=pe-posted-for-creator --env PAID_EMAIL_TEMPLATE_ID=pe-paid-for-creator --env NEW_USER_WELCOME_EMAIL_TEMPLATE_ID=pe-new-user-welcome --env SENDBACK_REMINDER_EMAIL_TEMPLATE_ID=pe-sent-back-reminder --env APPROVAL_REMINDER_EMAIL_TEMPLATE_ID=pe-approval-reminder --name pay-expense-backend 561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-expense-backend:latest && echo "Done: ABFL UAT"
