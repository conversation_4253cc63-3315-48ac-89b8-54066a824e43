replicaCount: 1

image:
  repository: asia-south1-docker.pkg.dev/payinvoice-p2p/payinvoice-p2p/backend-prod
  pullPolicy: Always
  tag: image_tag

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: false
  annotations: {}
  name: "payinvoice-p2p-deployment-prod"

podAnnotations: {}

podSecurityContext: {}

securityContext: {}

service:
  type: ClusterIP
  port: 8080

livenessProbe:
  httpGet:
    path: /health
    port: http
  periodSeconds: 45
  successThreshold: 1
  timeoutSeconds: 45

readinessProbe:
  httpGet:
    path: /health
    port: http
  periodSeconds: 45
  successThreshold: 1
  timeoutSeconds: 45

configMap:
  enabled: false
  data: {}

secret:
  enabled: true
  stringData:
    db-password: "bitbucket-db-password"
    aws_access_key: "aws_access_key"
    aws_secret_key: "aws_secret_key"

startupProbe: {}

containerPorts:
  - name: http
    containerPort: 8080
    protocol: TCP

env:
  - name: DB_URL
    value: "****************************************************"
  - name: DB_SCHEMA
    value: "sch_payinvoice_p2p"
  - name: DB_USERNAME
    value: "payinvoice_user"
  - name: DB_PASSWORD
    valueFrom:
      secretKeyRef:
        name: payinvoice-p2p-deployment
        key: db-password
  - name: gcp.bucket.name
    value: "payinvoice-p2p-production"
  - name: gcp.topic.name
    value: "pubsub_p2p_production"
  - name: FILE_UPLOAD_LIMIT
    value: "10MB"
  - name: JWT_SECRET_ENV
    value: "1E223539-0D5B-4279-8F9A-B3B13BDC7A29-DA01E40D-6806-40FA-B280-44ACB4902845"
  - name: PAY_EXPENSE_PRODUCT_ID_ENV
    value: "27"
  - name: DB_DIALECT
    value: "org.hibernate.dialect.PostgreSQLDialect"
  - name: DB_SHOW_SQL
    value: "false"
  - name: DB_DDL_AUTO
    value: "none"
  - name: DB_ENGINE
    value: "innodb"
  - name: CEM_URL_ENV
    value: "https://dmr.payinvoice.in/cem/api/v1"
  - name: API4BUSINESS_URL_ENV
    value: "https://api.api4business.com"
  - name: AWS_REGION
    value: "ap-south-1"
  - name: AWS_BUCKET_NAME
    value: "payinvoice-p2p"
  - name: SONAR_HOST_URL
    value: "https://sq-com.taxgenie.online"
  - name: SONAR_TOKEN
    value: "sqa_703d6283518f14d5608c33f1d8a849af76abf8f3"
  - name: ACTIVE_PROFILE
    value: "production-gcp"
  - name: BUCKET_NAME
    value: "payinvoice-p2p"
  - name: AWS_ROOT_DIRECTORY
    value: "payinvoice-p2p"
  - name: ALLOWED_ORIGINS_ENV
    value: "http://localhost:4200,https://grasimcorpfinance.payinvoice.online"
  - name: PAY_EXPENSE_CUSTOMER_ID_ENV
    value: "0"
  - name: COMMUNICATION_ENGINE_URL_ENV
    value: "https://gst.taxgenie.online/communication-engine"
  - name: APPROVAL_EMAIL_TEMPLATE_ID_ENV
    value: "pe-approval-format"
  - name: ACKNOWLEDGEMENT_EMAIL_TEMPLATE_ID_ENV
    value: "pe-acknowledgement-format"
  - name: PAY_EXPENSE_SENDER_EMAIL_ENV
    value: "<EMAIL>"
  - name: SENDBACK_EMAIL_TEMPLATE_ID_ENV
    value: "pe-sentback-for-creator"
  - name: SUBMIT_TO_CREATOR_EMAIL_TEMPLATE_ID_ENV
    value: "pe-submit-for-creator"
  - name: SUBMIT_TO_APPROVER_EMAIL_TEMPLATE_ID_ENV
    value: "pe-submit-for-approver"
  - name: APPROVED_TO_CREATOR_EMAIL_TEMPLATE_ID_ENV
    value: "pe-approved-for-creator"
  - name: APPROVED_TO_NEXT_APPROVER_EMAIL_TEMPLATE_ID_ENV
    value: "pe-approved-for-approver"
  - name: POSTED_EMAIL_TEMPLATE_ID_ENV
    value: "pe-posted-for-creator"
  - name: PAID_EMAIL_TEMPLATE_ID_ENV
    value: "pe-paid-for-creator"
  - name: NEW_USER_WELCOME_EMAIL_TEMPLATE_ID_ENV
    value: "pe-new-user-welcome"
  - name: SENDBACK_REMINDER_EMAIL_TEMPLATE_ID_ENV
    value: "pe-sent-back-reminder"
  - name: APPROVAL_REMINDER_EMAIL_TEMPLATE_ID_ENV
    value: "pe-approval-reminder"
  - name: GCP_BUCKET
    value: "payinvoice-p2p-production"
  - name: LOG_LOCATION
    value: "logs"
  - name: LOG_NAME
    value: "p2p_app"
  - name: APP_LOG_LEVEL
    value: "TRACE"
  - name: ROOT_LOG_LEVEL
    value: "INFO"
  - name: UPLOAD_ENVIRONMENT
    value: "production"
  - name: QR_READER_ENV
    value: "https://api.docproc.payindx.com"
  - name: OCR_SERVICE
    value: "https://api.docproc.payindx.com"
  - name: CAM_SERVICE
    value: "https://gst.taxgenie.online/cam-new/api/v1"
  - name: APIGEE_SERVICE
    value: "https://apigeeaccount.payindx.com"
  - name: API4BUSINESS_SERVICE
    value: "https://api.api4business.com"
  - name: BASE_URL_SERVICE
    value: "https://grasimcorpfinance.payinvoice.online/api/base"
  - name: MDM_SERVICE
    value: "https://gst.taxgenie.online/mdm-master"

resources: {}

resourceQuota: {}

networkPolicy:
  egressRules: {}

role:
  enabled: false

RoleBinding:
  enabled: false

autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
