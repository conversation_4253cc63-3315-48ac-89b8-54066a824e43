{{- if .Values.networkPolicy.enabled -}}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "p2p-deployment.fullname" . }}
  namespace: {{ .Values.namepsace }}
spec:
  podSelector:
    matchLabels: 
      {{- toYaml $.Values.networkPolicy.accessLabels | nindent 4 }}
  policyTypes:
  - Ingress
  - Egress
  ingress:
    {{- toYaml $.Values.networkPolicy.ingressRules | nindent 4 }}
  egress: 
    {{- toYaml $.Values.networkPolicy.egressRules | nindent 4 }}
{{- end }} 
