apiVersion: v1
kind: Service
metadata:
  name: {{ include "p2p-deployment.fullname" . }}
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    kubernetes.io/ingress.class: "gce"
  labels:
    {{- include "p2p-deployment.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "p2p-deployment.selectorLabels" . | nindent 4 }}
