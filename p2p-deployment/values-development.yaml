replicaCount: 1

image:
  repository: asia-south1-docker.pkg.dev/payinvoice-p2p/payinvoice-p2p/backend/development
  pullPolicy: Always
  tag: image_tag

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "payinvoice-p2p-deployment-dev"

podAnnotations: {}

podSecurityContext: {}
# fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
# runAsNonRoot: true
# runAsUser: 1000

service:
  type: ClusterIP
  port: 8080

livenessProbe:
  httpGet:
    path: /health
    port: http
  periodSeconds: 45
  successThreshold: 1
  timeoutSeconds: 45

readinessProbe:
  httpGet:
    path: /health
    port: http
  periodSeconds: 45
  successThreshold: 1
  timeoutSeconds: 45

configMap:
  enabled: false
  data: {}
    # property-like keys; each key maps to a simple value
    # player_initial_lives: "3"
    # ui_properties_file_name: "user-interface.properties"
    # # file-like keys
    # game.properties: |
    #   enemy.types=aliens,monsters
    #   player.maximum-lives=5
    # user-interface.properties: |
    #   color.good=purple
  #   color.bad=yellow
  #   allow.textmode=true

secret:
  enabled: enable
  stringData:
    db-password: "bitbucket-db-password"
    aws_access_key: "aws_access_key"
    aws_secret_key: "aws_secret_key"
    # auth-extra-groups: "system:bootstrappers:kubeadm:default-node-token"
    # expiration: "2020-09-13T04:39:10Z"
    # usage-bootstrap-authentication: "true"
    # usage-bootstrap-signing: "true"

# healthCheck:
#   checkIntervalSec: 15
#   port: 8080
#   type: HTTP
#   requestPath: /healthz

startupProbe: {}
  # failureThreshold: 30
  # httpGet:
  #   path: /actuator/health
  #   port: http
  #   scheme: HTTP
  # periodSeconds: 10
# successThreshold: 1
# timeoutSeconds: 1

containerPorts:
  - name: http
    containerPort: 8080
    protocol: TCP

env:
  - name: DB_URL
    value: "***************************************************"
  - name: DB_SCHEMA
    value: "sch_payinvoice_p2p"
  - name: DB_USERNAME
    value: "postgres"
  - name: DB_PASSWORD
    valueFrom:
      secretKeyRef:
        name: payinvoice-p2p-deployment #secret name is same as chart name
        key: db-password
  - name: gcp.bucket.name
    value: "payinvoice-p2p"
  - name: gcp.topic.name
    value: "syndicate_pubsub_p2p_dev"
  - name: FILE_UPLOAD_LIMIT
    value: "10MB"
  - name: JWT_SECRET_ENV
    value: "1E223539-0D5B-4279-8F9A-B3B13BDC7A29-DA01E40D-6806-40FA-B280-44ACB4902845"
  - name: PAY_EXPENSE_PRODUCT_ID_ENV
    value: "27"
  - name: DB_DIALECT
    value: "org.hibernate.dialect.PostgreSQLDialect"
  - name: DB_SHOW_SQL
    value: 'false'
  - name: DB_DDL_AUTO
    value: "none"
  - name: DB_ENGINE
    value: "innodb"
  - name: CEM_URL_ENV
    value: "https://dev.taxgenie.online/cem/api/v1"
  - name: API4BUSINESS_URL_ENV
    value: "https://dev.api.api4business.com"
  - name: AWS_ENDPOINT_URL
    value: "https://payexpense.s3.ap-south-1.amazonaws.com"
  - name: AWS_REGION
    value: "ap-south-1"
  - name: AWS_ACCESS_KEY
    valueFrom:
      secretKeyRef:
        name: payinvoice-p2p-deployment #secret name is same as chart name
        key: aws_access_key
  - name: AWS_SECRET_KEY
    valueFrom:
      secretKeyRef:
        name: payinvoice-p2p-deployment #secret name is same as chart name
        key: aws_secret_key
  - name: AWS_BUCKET_NAME
    value: "payinvoice-p2p"
  - name: SONAR_HOST_URL
    values: https://sq-com.taxgenie.online
  - name: SONAR_TOKEN
    values: sqa_703d6283518f14d5608c33f1d8a849af76abf8f3
  - name: ACTIVE_PROFILE
    value: "tg-internal-gcp"
  - name: BUCKET_NAME
    value: 'payinvoice-p2p'
  - name: AWS_ROOT_DIRECTORY
    value: "payinvoice-p2p"
  - name: ALLOWED_ORIGINS_ENV
    value: "http://localhost:4200,http://dev.payinvoice.online,https://dev.payinvoice.online,http://dev-p2p.payinvoice.in,https://dev-p2p.payinvoice.in"
  - name: PAY_EXPENSE_CUSTOMER_ID_ENV
    value: "0"
  - name: COMMUNICATION_ENGINE_URL_ENV
    value: "https://dev.taxgenie.online/communication-engine"
  - name: APPROVAL_EMAIL_TEMPLATE_ID_ENV
    value: "pe-approval-format"
  - name: ACKNOWLEDGEMENT_EMAIL_TEMPLATE_ID_ENV
    value: "pe-acknowledgement-format"
  - name: PAY_EXPENSE_SENDER_EMAIL_ENV
    value: "<EMAIL>"
  - name: SENDBACK_EMAIL_TEMPLATE_ID_ENV
    value: "pe-sentback-for-creator"
  - name: SUBMIT_TO_CREATOR_EMAIL_TEMPLATE_ID_ENV
    value: "pe-submit-for-creator"
  - name: SUBMIT_TO_APPROVER_EMAIL_TEMPLATE_ID_ENV
    value: "pe-submit-for-approver"
  - name: APPROVED_TO_CREATOR_EMAIL_TEMPLATE_ID_ENV
    value: "pe-approved-for-creator"
  - name: APPROVED_TO_NEXT_APPROVER_EMAIL_TEMPLATE_ID_ENV
    value: "pe-approved-for-approver"
  - name: POSTED_EMAIL_TEMPLATE_ID_ENV
    value: "pe-posted-for-creator"
  - name: PAID_EMAIL_TEMPLATE_ID_ENV
    value: "pe-paid-for-creator"
  - name: NEW_USER_WELCOME_EMAIL_TEMPLATE_ID_ENV
    value: "pe-new-user-welcome"
  - name: SENDBACK_REMINDER_EMAIL_TEMPLATE_ID_ENV
    value: "pe-sent-back-reminder"
  - name: APPROVAL_REMINDER_EMAIL_TEMPLATE_ID_ENV
    value: "pe-approval-reminder"
  - name: GCP_BUCKET
    value: "payinvoice-p2p"
  - name: LOG_LOCATION
    value: "logs"
  - name: LOG_NAME
    value: "p2p_app"
  - name: APP_LOG_LEVEL
    value: "TRACE"
  - name: ROOT_LOG_LEVEL
    value: "INFO"
  - name: UPLOAD_ENVIRONMENT
    value: "development"
    # Webclient External Services
  - name: QR_READER_ENV
    value: "https://dev.docproc.taxgenie.online"
  - name: OCR_SERVICE
    value: "https://dev.docproc.taxgenie.online"
  - name: CAM_SERVICE
    value: "https://dev.taxgenie.online/cam/api/v1"



resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
#   cpu: 100m
#   memory: 128Mi

resourceQuota: {}
  #Use this configuration to specify Resource Limit on a specific Namespace
  #Add the limits on CPU and Memory usage for the desired Namesapce.
  #If any resource try to utilize more resources speciffied here in that namespace, it will not allow that pod to run in the ns
  #To add quota on ns remove curly braces and set the value for enbaled to true
  #By default it is false, which means it will not create any quota Limit at ns level.
  #uncomment the lines below enabled to configure the desired quota limits.
  #  enabled: false
  #  namespace: customer1
  #  cpuLimit: "1"
  #  memLimit: "1Gi"
#  cpuReq: "0.5"
#  memRequest: "500Mi"


#specify network policy for the desired namespace.
networkPolicy:
  #To enable network policy set enable configuration below to true.
  # enabled: false
  # accessLabels:
  #   - app: data  # Pod Label to which you want to apply the network policy
  # ingressRules:  #Add the ingress network policy rules to be applied on Namespace
  # - from:
  #     - ipBlock:
  #         cidr: **********/16
  #         except:
  #           - **********/24
  #     - namespaceSelector:
  #         matchLabels:
  #           project: myproject
  #     - podSelector:
  #         matchLabels:
  #           role: frontend
  #   ports:
  #     - protocol: TCP
  #       port: 6379
  egressRules: {}  #Add egress network policy rules to be applied on Namespace.
    # - to:
    #     - ipBlock:
    #         cidr: 10.0.0.0/24
    #   ports:
  #     - protocol: TCP
  #       port: 5978


#Create a role into specified Namespace RBAC control for specified namespace
#Note give minimal permission to the user so that you can extend them as per your requirment
role:
  enabled: false # To enable the roles uncomment the rules block and set value of enabled to true
    # rules:
    # - apiGroups: [""]
    #   resources: ["pods", "services", "serviceaccounts"]
    #   verbs: ["update", "create", "delete", "get", "watch", "list"]
    # - apiGroups: ["apps"]
  #   resources: ["deployments"]
  #   verbs: ["update", "create", "delete", "get", "watch", "list"]

RoleBinding:
  enabled: false # Enable the rolebinding by setting this value to true or false.
  # subjects:
  # # You can specify more than one "subject"
  # - kind: User
  #   name: <EMAIL> # "name" is case sensitive and should be IAM user or SA name
  #   apiGroup: rbac.authorization.k8s.io
  # roleRef:
  # # "roleRef" specifies the binding to a Role
  #   kind: Role #this must be Role
  #   name: developer # this must match the name of the Role you wish to bind to
  #   apiGroup: rbac.authorization.k8s.io

autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
